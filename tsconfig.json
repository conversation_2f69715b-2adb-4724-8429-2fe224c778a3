{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "noImplicitAny": false, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "downlevelIteration": true, "jsx": "preserve", "baseUrl": "./src", "paths": {"@components/*": ["components/*"], "@components": ["components"], "@styles/*": ["styles/*"], "@styles": ["styles"], "@public/*": ["public/*"], "@public": ["public"], "@prisma": ["../prisma"], "@prisma/*": ["../prisma/*"], "@utils": ["utils"], "@utils/*": ["utils/*"], "@test/*": ["../test/*"]}, "incremental": true, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}