import * as gcp from '@pulumi/gcp';
import * as pulumi from '@pulumi/pulumi';

const config = new pulumi.Config();
const gcpConfig = new pulumi.Config('gcp');

const GCP_PROJECT_ID = gcpConfig.require('project');

/**
 * For full list of available GCP services, see:
 * `gcloud services list --available --filter="name:googleapis.com"`
 */
const ENABLED_SERVICES = [
  'compute.googleapis.com',
  'iap.googleapis.com',
  'secretmanager.googleapis.com',
  'cloudkms.googleapis.com',
];

for (let i = 0; i < ENABLED_SERVICES.length; i++) {
  new gcp.projects.Service(`project-service-${ENABLED_SERVICES[i]}`, {
    service: ENABLED_SERVICES[i],
    disableDependentServices: true,
  });
}

// Attach this project to the appropiate shared VPC based on environment
const sharedVPCAttachment = new gcp.compute.SharedVPCServiceProject('shared-vpc-service', {
  hostProject: config.require('vpcHostProject'),
  serviceProject: gcpConfig.require('project'),
});

// Create Artifact Registry repository
const repository = new gcp.artifactregistry.Repository(
  'sorcery-repository',
  {
    format: 'Docker',
    repositoryId: 'sorcery',
    project: config.require('deploymentHostProject'),
    location: 'us',
  },
  {
    protect: true,
  },
);

// Setup KMS keyring
const keyring = new gcp.kms.KeyRing('keyring', {
  name: `keyring-sorceryapp-${pulumi.getStack()}`,
  location: 'global',
});

const keys = new gcp.kms.CryptoKey('sops-key', {
  name: `key-sops-${pulumi.getStack()}`,
  keyRing: keyring.id,
  purpose: 'ENCRYPT_DECRYPT',
});

// const cloudrunService = new gcp.cloudrunv2.Service("default", {
//     location: "us-east4",
//     template: {
//         containers: [{
//             image: "user-docker.pkg.dev/"+config.require("deploymentHostProject")+"/sorcery/app"
//         }]
//     },
//     traffics: [
//         {
//             revision: "latest",
//             percent: 100
//         }
//     ]
// })
