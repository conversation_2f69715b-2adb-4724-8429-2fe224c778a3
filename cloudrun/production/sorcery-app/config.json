{"template_metadata": {"autoscaling.knative.dev/maxScale": "'5'", "autoscaling.knative.dev/minScale": "'2'", "run.googleapis.com/startup-cpu-boost": "'true'", "run.googleapis.com/client-name": "cloud-console", "run.googleapis.com/sessionAffinity": "'true'", "run.googleapis.com/vpc-access-connector": "projects/vpc-host-prod-beam/locations/us-central1/connectors/serverless-vpc-access-con", "run.googleapis.com/vpc-access-egress": "all-traffic", "run.googleapis.com/cloudsql-instances": "sorcery-prod-beam:us-central1:sorcery"}, "secret_manager": {"PLATFORM_DATABASE_URL": "PLATFORM_DATABASE_URL", "SORCERY_DATABASE_URL": "SORCERY_DATABASE_URL", "SCHEDULER_DATABASE_URL": "SCHEDULER_DATABASE_URL", "DOCUMENT_DATABASE_URL": "DOCUMENT_DATABASE_URL", "PAYMENT_DATABASE_URL": "PAYMENT_DATABASE_URL", "CONFIG_DATABASE_URL": "CONFIG_DATABASE_URL", "COMPLIANCE_DB_URL": "COMPLIANCE_DB_URL", "VERIFICATION_DATABASE_URL": "VERIFICATION_DATABASE_URL", "IMAGEKIT_PRIVATE_KEY": "IMAGEKIT_PRIVATE_KEY", "PLATFORM_ENCRYPTION_KEY": "PLATFORM_ENCRYPTION_KEY", "PW_PUSH_TOKEN": "PW_PUSH_TOKEN", "PW_PUSH_USER_EMAIL": "PW_PUSH_USER_EMAIL", "PAYMENTS_AUTH_SECRET": "PAYMENTS_AUTH_SECRET", "IDENTITY_DATABASE_URL": "IDENTITY_DATABASE_URL"}, "container": {"PORT": "8081"}, "labels": {"team": "'engineering'", "product": "'sorcery'", "service": "'sorcery-app'"}, "service_account": "<EMAIL>", "health_check_path": "/api/healthz", "health_check_timeout": "9", "health_check_attempts": "3", "health_check_period": "10", "container_concurrency": "80", "cpu_limit": "1000m", "memory_limit": "3Gi", "cloud_run_region": "us-central1"}