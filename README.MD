# Sorcery 🧙

Sorcery is the codename for Beam's dashboard that hosts internal customer support tools and services. The primary goal of this project is to provide self-service tools for our PST teams and engineers to support our organizational customers and applicants.

## Getting Started

At the core of the platform are:
* [Next.js](https://nextjs.org/)
* [refine](https://refine.dev/)
* [Postgraphile GraphQL API](https://www.graphile.org/postgraphile/)

Refine is a React-based framework for building data-intensive applications in no time ✨, it offers lots of out-of-the box functionality for rapid development, without compromising extreme customizability. Use-cases include, but are not limited to admin panels, B2B applications and dashboards.

Postgraphile & Next Crud help auto-generate APIs from Postgres schemas.

## Prerequisites
### Option 1

Use a local `.env.local` for your DB connection strings. You can copy the values from [`.env.example`](./.env.example).

Next has a few interesting gotchas on environment variables, especially when considering client vs server side variables. See more information [in their documentation](https://nextjs.org/docs/pages/building-your-application/configuring/environment-variables).

The most important bit to note here is that we do not have a way to add separate public variables for dev and prod at the moment, since we build the image once and deploy to both environments. We have a workaround set up with the [`getConfig`](./src/utils/config.ts) utility function that grabs any environment variable prefixed with `PUBLIC`, but these are not available immediately to the client and need to be fetched from the server, so some delay is expected.

Step 1: Setup local sorcery database with docker compose
- run `docker compose build db && docker compose up db`
- optionally include `docker compose down --remove-orphans --volumes` before the previous command to tear down any existing containers

Step 2: Setup local platform database with docker compose
- follow same steps as step 1 in the platform repo

Step 3: Migrate the schema to ensure it is up to date
- run `pnpm prisma:migrate`

Step 4: Generate the prisma clients for each DB
- run `pnpm prisma:generate`

Step 4: Profit $$$

### Option 2 

Use the following .env file for the Dev environment:<br>
https://start.1password.com/open/i?a=OVWZHXDBEJEF5EWE6HQZZAHBX4&v=cghncswga5rl6tbgvtlcael5ve&i=q7ngmsficlra3eskjwmd2ncheq&h=bybeam.1password.com

Setup a tunnel to the internal Sorcery DB by using [Cloud SQL Proxy](https://cloud.google.com/sql/docs/postgres/sql-proxy):<br>
```bash
./cloud-sql-proxy sorcery-dev-6265616d:us-central1:sorcery -p 5433 --private-ip
```

## Available Scripts

### Running the development server.

```bash
    pnpm install
    pnpm dev
```

### Building for production.

```bash
    pnpm build
```

### Running the production server.

```bash
    pnpm start
```

## Development
The app is built with Next.js, with Refine mostly serving as the framework for all of the data hooks and abastractions for the APIs.

What's going on in the folders:
```
├── deploy: See https://cloud.google.com/deploy/docs
├── graphile-server: See https://www.graphile.org/postgraphile/
└── src: 
    ├── components: shared, or non-page components that next.js complains about being in pages
    ├── lib: mostly providers
    └── pages: components here directly map to page paths
        └── api
            ├── graphql: auto-generated graphql API using postgraphile
            ├── internal: nextcrud auto-generated API for internal sorcery data only
            └── platform (deprecated): nextcrud API for the platform, replaced with graphql

```

## APIs

The main platform API is an auto-generated GraphQL API. There is a GraphiQL client is available at:<br>
[http://localhost:3000/api/graphiql](http://localhost:3000/api/graphiql)


-- Probably soon to be deprecated: --<br>
Nextcrud (the first auto-generated API attempt) also auto-generates RESTful API routes, available at:

http://localhost:3000/api/internal/* for the sorcery DB, only used for internal sorcery users accounts and audit logs for now

http://localhost:3000/api/platform/*: platform APIs, but better to use the GraphQL API moving forward.

## Deploy Process

1. create a new branch with your changes
2. open an MR to merge into main
3. on merge, the gitlab pipeline will build a new docker image and deploy it to development
4. to deploy to production, a manual step is required. when you see your new build in development [here](https://console.cloud.google.com/deploy/delivery-pipelines/us-central1/sorcery-app-pipeline?project=shared-infra-beam), click the `Promote` button to deploy to prod.

## Adding a new DB source

1. create a new `{database}-schema.prisma` file in `prisma/` (copy the `generator` and `datasource` blocks from an existing schema and update the output/url appropriately)
2. add an environment variable for the db's url to `.env`, `.env.example`, `docker.env`, `cloudrun/development/sorcery-app/config.json` and `cloudrun/production/sorcery-app/config.json`
3. add individual `prisma:dbpull:{database}` and `prisma:generate:{database}` and `prisma:db:format:{database}` commands to the `package.json` and add them to the rolled-up `prisma:dbpull` and `prisma:generate` commands
4. run `prisma:dbpull:{database}` and `prisma:db:format:{database}` and `prisma:generate:{database}`
5. add a new service for the db to `graphile-server/config.ts`
6. Add a new data provider in `src/pages/_app.tsx`

## Learn More

To learn more about **refine**, please check out the [Documentation](https://refine.dev/docs)
- **Material UI** [Docs](https://refine.dev/docs/examples/tutorial/mui-tutorial/)
- **Inferencer** [Docs](https://refine.dev/docs/packages/documentation/inferencer)
- **KBar Command Palette** [Docs](https://github.com/pankod/refine/tree/next/examples/commandPalette/kbar)
