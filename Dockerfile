# Install dependencies and build the app
FROM node:22.1.0-alpine AS base

# Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
WORKDIR /app
RUN npm install -g pnpm

COPY pnpm-lock.yaml ./
COPY package.json ./
RUN \
  if [ -f pnpm-lock.yaml ]; then pnpm install --frozen-lockfile; \
  else echo "Lockfile not found." && exit 1; \
  fi

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
RUN npm install -g pnpm

ENV NEXT_TELEMETRY_DISABLED 1

COPY --from=deps /app/node_modules ./node_modules
# Better to optimize this
COPY . .

RUN pnpm prisma:generate
RUN pnpm build

# Image: copy all the files and run next
FROM base AS runner
WORKDIR /app

RUN mkdir .next
ARG NODE_ENV

ENV NODE_ENV $NODE_ENV
ENV NEXT_TELEMETRY_DISABLED 1

COPY --from=builder /app/public ./public
# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder  /app/.next/standalone ./
COPY --from=builder  /app/.next/static ./.next/static
COPY --from=builder  /app/prisma/generated ./prisma/generated


ENV HOSTNAME "0.0.0.0"
EXPOSE 8080
ENV PORT 8080

CMD ["server.js"]