generator client {
  provider = "prisma-client-js"
  output   = "./generated/paymentClient"
}

datasource db {
  provider = "postgresql"
  url      = env("PAYMENT_DATABASE_URL")
}

model Accounts {
  id            String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  recipientId   String       @map("recipient_id") @db.Uuid
  referenceId   String       @map("reference_id") @db.VarChar
  type          Accounttype?
  keys          <PERSON><PERSON>
  createdAt     DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt     DateTime     @default(now()) @map("updated_at") @db.Timestamptz(6)
  deactivatedAt DateTime?    @map("deactivated_at") @db.Timestamptz(6)
  recipient     Recipients   @relation(fields: [recipientId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_recipient_account")
  payments      Payments[]
  schedules     Schedules[]
  transfers     Transfers[]

  @@map("accounts")
}

model Clients {
  id               String           @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  applicationName  String           @map("application_name") @db.VarChar
  clientId         String           @unique @map("client_id") @db.VarChar
  clientSecret     String           @map("client_secret") @db.VarChar
  role             Role?
  createdAt        DateTime         @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt        DateTime         @default(now()) @map("updated_at") @db.Timestamptz(6)
  clientSecretTemp String?          @map("client_secret_temp")
  fundingSources   FundingSources[]

  @@map("clients")
}

model FundingSources {
  id                       String                     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  referenceId              String                     @unique(map: "unique_reference_id_funding_sources") @map("reference_id") @db.VarChar
  clientId                 String                     @map("client_id") @db.Uuid
  provider                 Provider
  keys                     Json
  createdAt                DateTime                   @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt                DateTime                   @default(now()) @map("updated_at") @db.Timestamptz(6)
  client                   Clients                    @relation(fields: [clientId], references: [id], onUpdate: Restrict, map: "fk_funding_sources_client")
  RecipientsFundingSources RecipientsFundingSources[]
  Schedules                Schedules[]
  Transfers                Transfers[]

  @@map("funding_sources")
}

model Payments {
  id                String        @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  recipientId       String        @map("recipient_id") @db.Uuid
  accountId         String?       @map("account_id") @db.Uuid
  referenceId       String        @map("reference_id") @db.VarChar
  status            Status
  amount            Int?
  paymentMethod     Paymentmethod @map("payment_method")
  callbackUrl       String?       @map("callback_url") @db.VarChar
  keys              Json?
  transactionNumber BigInt        @default(autoincrement()) @map("transaction_number")
  createdAt         DateTime      @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt         DateTime      @default(now()) @map("updated_at") @db.Timestamptz(6)
  deactivatedAt     DateTime?     @map("deactivated_at") @db.Timestamptz(6)
  scheduleId        String?       @map("schedule_id") @db.Uuid
  fundingSourceId   String        @map("funding_source_id") @db.Uuid
  transferId        String?       @map("transfer_id") @db.Uuid
  account           Accounts?     @relation(fields: [accountId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_account_payment")
  schedule          Schedules?    @relation(fields: [scheduleId], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "fk_payments_schedule")
  recipient         Recipients    @relation(fields: [recipientId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_recipient_payment")
  transfer          Transfers?    @relation(fields: [transferId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_transfer_payment")

  @@map("payments")
}

model Recipients {
  id                       String                     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  referenceId              String                     @map("reference_id") @db.VarChar
  keys                     Json
  createdAt                DateTime                   @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt                DateTime                   @default(now()) @map("updated_at") @db.Timestamptz(6)
  deactivatedAt            DateTime?                  @map("deactivated_at") @db.Timestamptz(6)
  accounts                 Accounts[]
  payments                 Payments[]
  recipientsFundingSources RecipientsFundingSources[]
  schedules                Schedules[]
  transfers                Transfers[]

  @@map("recipients")
}

model RecipientsFundingSources {
  recipientId     String         @map("recipient_id") @db.Uuid
  fundingSourceId String         @map("funding_source_id") @db.Uuid
  fundingSource   FundingSources @relation(fields: [fundingSourceId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_funding_source")
  recipient       Recipients     @relation(fields: [recipientId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_recipient")

  @@unique([recipientId, fundingSourceId], map: "unique_recipient_id_funding_source_id_recipient_funding_sources")
  @@map("recipients_funding_sources")
}

model Schedules {
  id              String          @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  referenceId     String          @unique(map: "unique_reference_id_schedules") @map("reference_id") @db.VarChar
  accountId       String?         @map("account_id") @db.Uuid
  recipientId     String          @map("recipient_id") @db.Uuid
  paymentMethod   Paymentmethod   @map("payment_method")
  pattern         Paymentpattern
  amount          Int
  start           DateTime        @db.Timestamptz(6)
  count           Int
  createdAt       DateTime        @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt       DateTime        @default(now()) @map("updated_at") @db.Timestamptz(6)
  deactivatedAt   DateTime?       @map("deactivated_at") @db.Timestamptz(6)
  fundingSourceId String?         @map("funding_source_id") @db.Uuid
  callbackUrl     String?         @map("callback_url") @db.VarChar
  payload         Json            @default("{}")
  payments        Payments[]
  account         Accounts?       @relation(fields: [accountId], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "fk_schedules_account")
  recipient       Recipients      @relation(fields: [recipientId], references: [id], onUpdate: Restrict, map: "fk_schedules_recipient")
  fundingSource   FundingSources? @relation(fields: [fundingSourceId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("schedules")
}

model Transfers {
  id                String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  status            Status
  amount            Int
  keys              Json?
  transactionNumber BigInt         @default(autoincrement()) @map("transaction_number")
  fundingSourceId   String         @map("funding_source_id") @db.Uuid
  recipientId       String         @map("recipient_id") @db.Uuid
  accountId         String         @map("account_id") @db.Uuid
  createdAt         DateTime       @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt         DateTime       @default(now()) @map("updated_at") @db.Timestamptz(6)
  deactivatedAt     DateTime?      @map("deactivated_at") @db.Timestamptz(6)
  payments          Payments[]
  account           Accounts       @relation(fields: [accountId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_account_transfer")
  fundingSource     FundingSources @relation(fields: [fundingSourceId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_funding_source_transfer")
  recipient         Recipients     @relation(fields: [recipientId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_recipient_transfer")

  @@map("transfers")
}

enum Accounttype {
  ach
  zelle
  physicalCard
  virtualCard

  @@map("accounttype")
}

enum Paymentmethod {
  ach
  check
  physicalCard
  zelle
  virtualCard

  @@map("paymentmethod")
}

enum Paymentpattern {
  monthly
  semiMonthly
  biWeekly
  weekly
  oneTime

  @@map("paymentpattern")
}

enum Provider {
  jpmc

  @@map("provider")
}

enum Role {
  root

  @@map("role")
}

enum Status {
  initiated
  pending
  failed
  succeeded
  returned
  rejected

  @@map("status")
}
