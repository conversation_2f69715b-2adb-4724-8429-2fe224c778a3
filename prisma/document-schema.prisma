generator client {
  provider = "prisma-client-js"
  output   = "./generated/documentClient"
}

datasource db {
  provider = "postgresql"
  url      = env("DOCUMENT_DATABASE_URL")
}

model ActiveLabel {
  id                     String                 @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                   String
  modelVersionId         String                 @map("model_version_id") @db.Uuid
  createdAt              DateTime?              @default(now()) @map("created_at") @db.Timestamp(6)
  deactivatedAt          DateTime?              @map("deactivated_at") @db.Timestamp(6)
  displayName            String?                @map("display_name")
  modelVersion           ModelVersion           @relation(fields: [modelVersionId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  active_schema_xref     active_schema_xref[]
  feedbacks              Feedback[]
  labelCategories        LabelCategory[]
  predictions            Prediction[]
  programLabelCategories ProgramLabelCategory[]

  @@index([modelVersionId])
  @@map("active_label")
}

model Category {
  id                     String                 @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                   String
  modelVersionId         String                 @map("model_version_id") @db.Uuid
  createdAt              DateTime?              @default(now()) @map("created_at") @db.Timestamp(6)
  deactivatedAt          DateTime?              @map("deactivated_at") @db.Timestamp(6)
  modelVersion           ModelVersion           @relation(fields: [modelVersionId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  labelCategories        LabelCategory[]
  programLabelCategories ProgramLabelCategory[]

  @@index([modelVersionId])
  @@map("category")
}

model DeprecatedField {
  docId             String            @map("doc_id") @db.VarChar
  key               String            @db.VarChar
  value             String?           @db.VarChar
  valtype           String            @db.VarChar
  deprecatedSummary DeprecatedSummary @relation(fields: [docId], references: [docId], onDelete: NoAction, onUpdate: NoAction, map: "field_doc_id_fkey")

  @@id([docId, key], map: "field_pkey")
  @@map("deprecated_field")
}

model DeprecatedOcrresult {
  raw               Json?             @db.Json
  docId             String            @id(map: "ocrresult_pkey") @map("doc_id") @db.VarChar
  createdAt         DateTime          @map("created_at") @db.Timestamp(6)
  modelVersion      String            @map("model_version") @db.VarChar
  deprecatedSummary DeprecatedSummary @relation(fields: [docId], references: [docId], onDelete: NoAction, onUpdate: NoAction, map: "ocrresult_doc_id_fkey")

  @@map("deprecated_ocrresult")
}

model DeprecatedSummary {
  docId               String               @id(map: "summary_pkey") @map("doc_id") @db.VarChar
  createdAt           DateTime             @map("created_at") @db.Timestamp(6)
  updatedAt           DateTime             @map("updated_at") @db.Timestamp(6)
  modelVersion        String?              @map("model_version") @db.VarChar
  pred                String?              @db.VarChar
  predProb            Float?               @map("pred_prob")
  quality             Json?                @db.Json
  language            String?              @db.VarChar
  ocrComplete         Boolean              @map("ocr_complete")
  deprecatedFields    DeprecatedField[]
  deprecatedOcrresult DeprecatedOcrresult?

  @@map("deprecated_summary")
}

model Feedback {
  id               String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  predictionId     String       @map("prediction_id") @db.Uuid
  preferredLabelId String?      @map("preferred_label_id") @db.Uuid
  adminId          String       @map("admin_id") @db.Uuid
  createdAt        DateTime?    @default(now()) @map("created_at") @db.Timestamp(6)
  accurate         String
  deactivatedAt    DateTime?    @map("deactivated_at") @db.Timestamp(6)
  prediction       Prediction   @relation(fields: [predictionId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  activeLabel      ActiveLabel? @relation(fields: [preferredLabelId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([adminId])
  @@index([predictionId])
  @@index([preferredLabelId])
  @@map("feedback")
}

model LabelCategory {
  id          String      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  categoryId  String      @map("category_id") @db.Uuid
  labelId     String      @map("label_id") @db.Uuid
  category    Category    @relation(fields: [categoryId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  activeLabel ActiveLabel @relation(fields: [labelId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([categoryId])
  @@index([labelId])
  @@map("label_category")
}

model LabelSchema {
  id                 String               @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  category           String
  categoryDisplay    String               @map("category_display")
  label              String               @unique(map: "unique_label_name")
  labelDisplay       String               @map("label_display")
  description        String?
  createdAt          DateTime?            @default(now()) @map("created_at") @db.Timestamp(6)
  deactivatedAt      DateTime?            @map("deactivated_at") @db.Timestamp(6)
  active_schema_xref active_schema_xref[]
  training_label     TrainingLabel[]

  @@map("label_schema")
}

model ModelVersion {
  id                 String               @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  type               String
  name               String?
  description        String?
  createdAt          DateTime?            @default(now()) @map("created_at") @db.Timestamp(6)
  deactivatedAt      DateTime?            @map("deactivated_at") @db.Timestamp(6)
  raw_config         Json?
  activeLabels       ActiveLabel[]
  active_schema_xref active_schema_xref[]
  categories         Category[]
  ocrResults         OcrResult[]
  summaries          Summary[]

  @@map("model_version")
}

model OcrResult {
  id             String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  modelVersionId String       @map("model_version_id") @db.Uuid
  raw            Json?
  modelVersion   ModelVersion @relation(fields: [modelVersionId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  summaries      Summary[]

  @@index([modelVersionId])
  @@map("ocr_result")
}

model Prediction {
  id            String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  activeLabelId String?      @map("active_label_id") @db.Uuid
  probability   Float?
  createdAt     DateTime?    @default(now()) @map("created_at") @db.Timestamp(6)
  deactivatedAt DateTime?    @map("deactivated_at") @db.Timestamp(6)
  feedbacks     Feedback[]
  activeLabel   ActiveLabel? @relation(fields: [activeLabelId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  summaries     Summary[]

  @@index([activeLabelId])
  @@map("prediction")
}

model ProgramLabelCategory {
  id               String      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  programId        String      @map("program_id") @db.Uuid
  labelId          String      @map("label_id") @db.Uuid
  categoryId       String      @map("category_id") @db.Uuid
  categoryOverride String      @map("category_override")
  category         Category    @relation(fields: [categoryId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  activeLabel      ActiveLabel @relation(fields: [labelId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([categoryId])
  @@index([labelId])
  @@index([programId])
  @@map("program_label_category")
}

model Summary {
  id                String           @id(map: "summary_pkey1") @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  docId             String           @unique(map: "unique_doc_id") @map("doc_id") @db.Uuid
  modelVersionId    String           @map("model_version_id") @db.Uuid
  predictionId      String?          @map("prediction_id") @db.Uuid
  ocrId             String?          @map("ocr_id") @db.Uuid
  createdAt         DateTime?        @default(now()) @map("created_at") @db.Timestamp(6)
  partner_id        String?          @db.Uuid
  parent_partner_id String?          @db.Uuid
  parse_job_id      String?          @db.Uuid
  document_field    document_field[]
  document_tag      document_tag[]
  request_status    request_status[]
  modelVersion      ModelVersion     @relation(fields: [modelVersionId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  ocrResult         OcrResult?       @relation(fields: [ocrId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  parse_job         parse_job?       @relation(fields: [parse_job_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  prediction        Prediction?      @relation(fields: [predictionId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([docId])
  @@index([modelVersionId])
  @@index([ocrId])
  @@index([predictionId])
  @@map("summary")
}

model TrainingLabel {
  tags            String[]     @db.VarChar
  docId           String       @id(map: "label_pkey") @map("doc_id") @db.VarChar
  label           String       @db.VarChar
  createdAt       DateTime     @map("created_at") @db.Timestamp(6)
  createdBy       String       @map("created_by") @db.VarChar
  updatedAt       DateTime     @map("updated_at") @db.Timestamp(6)
  updatedBy       String       @map("updated_by") @db.VarChar
  updatedByType   String       @map("updated_by_type") @db.VarChar
  label_schema_id String?      @db.Uuid
  label_schema    LabelSchema? @relation(fields: [label_schema_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("training_label")
}

model active_schema_xref {
  id               String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  model_version_id String       @db.Uuid
  label_schema_id  String       @db.Uuid
  active_label_id  String       @db.Uuid
  active_label     ActiveLabel  @relation(fields: [active_label_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  label_schema     LabelSchema  @relation(fields: [label_schema_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  model_version    ModelVersion @relation(fields: [model_version_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([model_version_id, label_schema_id])
}

model document_field {
  id               String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  doc_id           String    @db.Uuid
  parse_job_id     String    @db.Uuid
  field_key        String
  field_value      String
  confidence       Float?
  start_index      Int?
  stop_index       Int?
  normalized_value String?
  valid            Boolean?
  created_at       DateTime? @default(now()) @db.Timestamp(6)
  deactivated_at   DateTime? @db.Timestamp(6)
  summary          Summary   @relation(fields: [doc_id], references: [docId], onDelete: NoAction, onUpdate: NoAction, map: "fk_df_doc_id")
  parse_job        parse_job @relation(fields: [parse_job_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_df_parse_job_id")
}

model document_tag {
  id             String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  doc_id         String    @db.Uuid
  tag_id         String    @db.Uuid
  tag_metadata   Json?
  created_at     DateTime? @default(now()) @db.Timestamp(6)
  deactivated_at DateTime? @db.Timestamp(6)
  summary        Summary   @relation(fields: [doc_id], references: [docId], onDelete: NoAction, onUpdate: NoAction, map: "fk_doc_id")
  tag            tag       @relation(fields: [tag_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fk_tag_id")

  @@unique([doc_id, tag_id], map: "unique_doc_tag")
  @@index([doc_id])
  @@index([tag_id])
}

model parse_job {
  id             String           @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  processor_id   String?
  status         String?
  created_at     DateTime?        @default(now()) @db.Timestamp(6)
  deactivated_at DateTime?        @db.Timestamp(6)
  document_field document_field[]
  summary        Summary[]
}

model request_status {
  id             String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  doc_id         String?   @db.Uuid
  work_requested Json?     @default("{}")
  status         String    @db.VarChar
  error_stage    String?   @db.VarChar
  error_message  String?   @db.VarChar
  created_at     DateTime? @default(now()) @db.Timestamp(6)
  deactivated_at DateTime? @db.Timestamp(6)
  summary        Summary?  @relation(fields: [doc_id], references: [docId], onDelete: NoAction, onUpdate: NoAction)

  @@index([doc_id])
  @@index([id])
}

model tag {
  id             String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name           String         @unique(map: "unique_tag_name")
  description    String?
  type           String
  visibility     String         @default("all")
  created_at     DateTime?      @default(now()) @db.Timestamp(6)
  deactivated_at DateTime?      @db.Timestamp(6)
  document_tag   document_tag[]

  @@index([id])
}
