generator client {
  provider = "prisma-client-js"
  output   = "./generated/sorceryClient"
}

datasource db {
  provider = "postgresql"
  url      = env("SORCERY_DATABASE_URL")
}

model Principal {
  name  String
  email String
  roles String[]
  id    String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid

  @@map("principal")
}

model AuditLog {
  resource     String
  action       String
  author       Json?
  meta         Json?
  data         Json?
  previousData Json?    @map("previous_data")
  createdAt    DateTime @default(now()) @map("created_at")
  id           String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid

  @@map("audit_log")
}

model RolePermissions {
  id        String   @id(map: "resource_permissions_pkey") @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  role      String
  access    Json
  createdAt DateTime @default(now()) @map("created_at")

  @@map("role_permissions")
}
