generator client {
  provider = "prisma-client-js"
  output   = "./generated/complianceClient"
}

datasource db {
  provider = "postgresql"
  url      = env("COMPLIANCE_DB_URL")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model config {
  id             String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  partner_id     String    @db.Uuid
  name           String
  description    String?
  action         String
  query          String
  metadata       Json?
  created_at     DateTime  @default(now()) @db.Timestamptz(6)
  updated_at     DateTime  @default(now()) @db.Timestamptz(6)
  deactivated_at DateTime? @db.Timestamptz(6)

  @@unique([partner_id, name], map: "unique_partner_name")
  @@index([partner_id, deactivated_at], map: "idx_config_partner_deactivated_at")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model logs {
  log_id      String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  request_id  String   @db.Uuid
  user_id     String   @db.Uuid
  partner_id  String   @db.Uuid
  action_type String
  result      String
  message     String?
  details     Json?
  created_at  DateTime @default(now()) @db.Timestamptz(6)
  requests    requests @relation(fields: [request_id], references: [request_id], onDelete: NoAction, onUpdate: NoAction, map: "fk_logs_request_id")

  @@index([partner_id, created_at], map: "idx_logs_partner_created_at")
  @@index([request_id, result], map: "idx_logs_request_id")
  @@index([user_id], map: "idx_logs_user_id")
}

model reports {
  report_id  String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  partner_id String
  statistics Json?
  metadata   Json?
  created_at DateTime @default(now()) @db.Timestamptz(6)

  @@index([partner_id, created_at], map: "idx_reports_partner_created")
}

/// This table contains check constraints and requires additional setup for migrations. https://pris.ly/d/check-constraints for more info.
model requests {
  request_id   String   @id @unique(map: "unique_request_id") @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  request_type String
  entity_type  String
  entity_id    String   @db.Uuid
  partner_id   String   @db.Uuid
  action       String
  metadata     Json?
  created_at   DateTime @default(now()) @db.Timestamptz(6)
  logs         logs[]

  @@index([partner_id, entity_type, entity_id], map: "idx_requests_entity_type_id")
  @@index([partner_id, created_at], map: "idx_requests_partner_created")
}
