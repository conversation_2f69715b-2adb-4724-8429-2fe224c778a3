generator client {
  provider = "prisma-client-js"
  output   = "./generated/schedulerClient"
}

datasource db {
  provider = "postgresql"
  url      = env("SCHEDULER_DATABASE_URL")
}

model Jobs {
  id            String      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  status        Jobstatus?
  priority      Jobpriority @default(regular)
  scheduledFor  DateTime    @map("scheduled_for") @db.Timestamptz(6)
  executedAt    DateTime?   @map("executed_at") @db.Timestamptz(6)
  deactivatedAt DateTime?   @map("deactivated_at") @db.Timestamptz(6)
  createdAt     DateTime?   @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt     DateTime?   @default(now()) @map("updated_at") @db.Timestamptz(6)
  scheduleId    String      @map("schedule_id") @db.Uuid
  schedule      Schedules   @relation(fields: [scheduleId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("jobs")
}

model Schedules {
  id            String           @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  referenceId   String           @map("reference_id") @db.Uuid
  job           Jobname
  pattern       Frequencypattern
  start         DateTime         @db.Timestamptz(6)
  count         Int?
  payload       Json             @db.Json
  createdAt     DateTime?        @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt     DateTime?        @default(now()) @map("updated_at") @db.Timestamptz(6)
  deactivatedAt DateTime?        @map("deactivated_at") @db.Timestamptz(6)
  duration      Scheduleduration @default(finite)
  jobs          Jobs[]

  @@map("schedules")
}

model settings {
  id          String  @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  key         String  @unique(map: "settings_key_unique_idx")
  value       String?
  description String?
}

enum Frequencypattern {
  daily
  weekly
  biWeekly
  semiMonthly
  monthly
  oneTime

  @@map("frequencypattern")
}

enum Jobname {
  RecurringPayment
  USIODailyTransfer

  @@map("jobname")
}

enum Jobpriority {
  regular
  timeSensitive
  immediate

  @@map("jobpriority")
}

enum Jobstatus {
  Scheduled
  Pending
  Failed
  Completed

  @@map("jobstatus")
}

enum Scheduleduration {
  infinite
  finite

  @@map("scheduleduration")
}
