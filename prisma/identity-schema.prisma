generator client {
  provider = "prisma-client-js"
  output   = "./generated/identityClient"
}

datasource db {
  provider = "postgresql"
  url      = env("IDENTITY_DATABASE_URL")
}

model Advocates {
  id         String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId     String @map("user_id") @db.Uuid
  partnerId  String @map("partner_id") @db.Uuid
  coreUserId String @map("core_user_id") @db.Uuid
  user       Users  @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("advocates")
}

model Applicants {
  id        String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId    String @map("user_id") @db.Uuid
  partnerId String @map("partner_id") @db.Uuid
  user      Users  @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("applicants")
}

model ExternalApiUsers {
  id        String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId    String @map("user_id") @db.Uuid
  appId     String @map("app_id")
  partnerId String @map("partner_id") @db.Uuid
  user      Users  @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("external_api_users")
}

model GcipUsers {
  id             String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId         String    @map("user_id") @db.Uuid
  uid            String    @unique(map: "uid")
  tenantId       String    @map("tenant_id")
  created_at     DateTime  @default(now()) @db.Timestamp(6)
  updated_at     DateTime  @default(now()) @db.Timestamp(6)
  deactivated_at DateTime? @db.Timestamp(6)
  user           Users     @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("gcip_users")
}

model Users {
  id                                                 String             @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                                               String?
  email                                              String?            @unique
  phone                                              String?            @unique
  verifiedEmail                                      Boolean?           @default(false) @map("verified_email")
  createdAt                                          DateTime?          @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt                                          DateTime?          @map("updated_at") @db.Timestamp(6)
  deactivatedAt                                      DateTime?          @map("deactivated_at") @db.Timestamp(6)
  zed_token                                          String?
  access_requests_access_requests_reviewer_idTousers access_requests[]  @relation("access_requests_reviewer_idTousers")
  access_requests_access_requests_subject_idTousers  access_requests[]  @relation("access_requests_subject_idTousers")
  advocates                                          Advocates[]
  applicants                                         Applicants[]
  externalApiUsers                                   ExternalApiUsers[]
  gcipUsers                                          GcipUsers[]
  sessions                                           sessions[]

  @@map("users")
}

model audit_log {
  id          String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  entity_id   String?   @db.Uuid
  entity_type String?
  session_id  String?   @db.Uuid
  created_at  DateTime? @default(now()) @db.Timestamp(6)
  details     Json?     @db.Json
  sessions    sessions? @relation(fields: [session_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model sessions {
  id                       String      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  user_id                  String?     @db.Uuid
  started_at               DateTime?   @default(now()) @db.Timestamp(6)
  ended_at                 DateTime?   @db.Timestamp(6)
  headers                  Json?       @db.Json
  claims                   Json?       @db.Json
  authentication_mechanism String?
  audit_log                audit_log[]
  users                    Users?      @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model access_requests {
  id                                       String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  partner_id                               String    @db.Uuid
  resource_id                              String    @db.Uuid
  resource_type                            String
  subject_id                               String    @db.Uuid
  subject_type                             String
  relation                                 String
  approved                                 Boolean?
  request_detail                           String?
  review_detail                            String?
  reviewer_id                              String?   @db.Uuid
  created_at                               DateTime? @default(now()) @db.Timestamp(6)
  updated_at                               DateTime? @db.Timestamp(6)
  deactivated_at                           DateTime? @db.Timestamp(6)
  users_access_requests_reviewer_idTousers Users?    @relation("access_requests_reviewer_idTousers", fields: [reviewer_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  users_access_requests_subject_idTousers  Users     @relation("access_requests_subject_idTousers", fields: [subject_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}
