import { PrismaClient as ComplianceClient } from './compliance';
import { PrismaClient as DocumentClient } from './document';
import { PrismaClient as IdentityClient } from './identity';
import { PrismaClient as PaymentClient } from './payment';
import { PrismaClient as PlatformClient } from './platform';
import { PrismaClient as SchedulerClient } from './scheduler';
import { PrismaClient as SorceryClient } from './sorcery';
export type PrismaClients = {
  document: DocumentClient;
  platform: PlatformClient;
  scheduler: SchedulerClient;
  sorcery: SorceryClient;
  payment: PaymentClient;
  identity: IdentityClient;
  compliance: ComplianceClient;
};

const prismaClients: PrismaClients = {
  document: new DocumentClient(),
  platform: new PlatformClient(),
  scheduler: new SchedulerClient(),
  sorcery: new SorceryClient(),
  payment: new PaymentClient(),
  identity: new IdentityClient(),
  compliance: new ComplianceClient(),
};

export default prismaClients;
