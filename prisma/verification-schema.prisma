generator client {
  provider = "prisma-client-js"
  output   = "./generated/verificationClient"
}

datasource db {
  provider = "postgresql"
  url      = env("VERIFICATION_DATABASE_URL")
}

model Configs {
  id              String          @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  programId       String          @map("program_id") @db.Uuid
  service         ServiceType
  createdAt       DateTime?       @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt       DateTime?       @default(now()) @map("updated_at") @db.Timestamp(6)
  deactivatedAt   DateTime?       @map("deactivated_at") @db.Timestamp(6)
  applicantTypeId String          @map("applicant_type_id") @db.Uuid
  lookupConfigs   LookupConfigs[]

  @@unique([programId, service])
  @@map("configs")
}

model LookupConfigs {
  id                 String               @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  configId           String               @map("config_id") @db.Uuid
  filepath           String?
  fields             <PERSON><PERSON>
  createdAt          DateTime?            @default(now()) @map("created_at") @db.Timestamp(6)
  deactivatedAt      DateTime?            @map("deactivated_at") @db.Timestamp(6)
  config             Configs              @relation(fields: [configId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  lookupFileUploads  LookupFileUploads[]
  verifiedApplicants VerifiedApplicants[]

  @@map("lookup_configs")
}

model LookupFileUploads {
  id                 String               @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  lookupConfigId     String               @map("lookup_config_id") @db.Uuid
  status             UploadStatus         @default(InProgress)
  filepath           String
  uploadedAt         DateTime?            @default(now()) @map("uploaded_at") @db.Timestamp(6)
  lookupConfig       LookupConfigs        @relation(fields: [lookupConfigId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  verifiedApplicants VerifiedApplicants[]

  @@map("lookup_file_uploads")
}

model VerifiedApplicants {
  id                 String            @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  lookupConfigId     String?           @map("lookup_config_id") @db.Uuid
  fields             Json
  createdAt          DateTime?         @default(now()) @map("created_at") @db.Timestamp(6)
  deactivatedAt      DateTime?         @map("deactivated_at") @db.Timestamp(6)
  lookupFileUploadId String            @map("lookup_file_upload_id") @db.Uuid
  lookupConfig       LookupConfigs?    @relation(fields: [lookupConfigId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  lookupFileUpload   LookupFileUploads @relation(fields: [lookupFileUploadId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("verified_applicants")
}

enum ServiceType {
  DataLookup

  @@map("service_type")
}

enum UploadStatus {
  InProgress
  Success
  Failed

  @@map("upload_status")
}
