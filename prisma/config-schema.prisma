generator client {
  provider = "prisma-client-js"
  output   = "./generated/configClient"
}

datasource db {
  provider = "postgresql"
  url      = env("CONFIG_DATABASE_URL")
}

model ApplicantProfileConfigurations {
  id              String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  partnerId       String @map("partner_id") @db.Uuid
  applicantTypeId String @map("applicant_type_id") @db.Uuid
  config          Json

  @@unique([partnerId, applicantTypeId], map: "applicant_profile_config_partner_id_applicant_type_id_key")
  @@map("applicant_profile_configurations")
}

model ApplicationConfigurations {
  id                               String                             @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                             String                             @db.VarChar(255)
  description                      String
  config                           Json
  partnerId                        String?                            @map("partner_id") @db.Uuid
  replica                          Boolean                            @default(false)
  programApplicationConfigurations ProgramApplicationConfigurations[]

  @@map("application_configurations")
}

model ProgramApplicationConfigurations {
  id                       String                    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  programId                String                    @map("program_id") @db.Uuid
  configurationId          String                    @map("configuration_id") @db.Uuid
  applicantTypeId          String                    @map("applicant_type_id") @db.Uuid
  applicationConfiguration ApplicationConfigurations @relation(fields: [configurationId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([programId, applicantTypeId], map: "program_application_configurations_unique")
  @@map("program_application_configurations")
}

model partner_communication_channels_configuration {
  id         String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  partner_id String @unique @db.Uuid
  config     Json
}
