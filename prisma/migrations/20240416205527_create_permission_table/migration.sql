-- CreateTable
CREATE TABLE "role_permissions"
(
    "id"         UUID         NOT NULL DEFAULT gen_random_uuid(),
    "role"       TEXT         NOT NULL,
    "access"     JSONB        NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "resource_permissions_pkey" PRIMARY KEY ("id")
);

INSERT INTO public.role_permissions (role, access) VALUES 
 ('admin', '{"cases": ["list", "show"], "funds": ["list", "show", "edit", "create"], "users": ["list", "show", "edit", "create", "deactivate", "delete"], "admins": ["list", "show", "edit", "create"], "features": ["list", "show", "edit", "create"], "partners": ["list", "show", "edit", "create", "reports"], "payments": ["list", "show"], "programs": ["list", "show", "edit", "create", "updateApplicationAnswers"], "settings": ["list"], "iam-admin": ["list"], "schedules": ["list", "show", "edit"], "audit_logs": ["list", "show"], "principals": ["list", "show", "edit", "create", "delete"], "fulfillments": ["list", "show"], "vendor_types": ["list", "show", "create"], "program_funds": ["list", "show", "create"], "configurations": ["list", "show"], "partner-admins": ["list", "show", "edit", "create"], "applicant_types": ["list", "show", "edit", "create", "delete"], "email_templates": ["list", "show", "edit", "create", "delete"], "workflow_events": ["list", "show"], "partner_features": ["list", "show", "edit", "create"], "payments-section": ["list", "show"], "program_features": ["list", "show", "edit", "create"], "role_permissions": ["list", "create", "edit", "show"], "partner_incidents": ["list", "show", "edit", "create"], "payments-settings": ["list", "show", "edit"], "eligibility_config": ["show"], "payments-schedules": ["list", "show"], "analytics_resources": ["list", "show", "edit", "create"], "program_applicant_types": ["list", "edit", "create", "delete"], "application_configurations": ["list", "show", "edit", "create"], "verification_configurations": ["create", "edit"], "applicant_profile_configurations": ["show", "edit", "create", "delete"], "application_configurations_courses": ["list", "create", "edit", "delete"]}'),
 ('pst', '{"cases": ["list", "show"], "funds": ["list", "show", "edit", "create"], "users": ["list", "show", "edit", "deactivate"], "admins": ["list", "show", "edit", "create"], "partners": ["list", "show", "edit", "create"], "payments": ["list", "show"], "programs": ["list", "show", "edit", "create"], "vendor_types": ["list", "show", "create"], "program_funds": ["list", "show", "create"], "configurations": ["list", "show"], "partner-admins": ["list", "show", "edit", "create"], "applicant_types": ["list", "show", "edit", "create", "delete"], "email_templates": ["list", "show", "edit", "create", "delete"], "workflow_events": ["list", "show"], "payments-section": ["list", "show"], "program_features": ["list", "show", "edit", "create"], "partner_incidents": ["list", "show", "edit", "create"], "payments-settings": ["list", "show"], "payments-schedules": ["list", "show"], "analytics_resources": ["list", "show", "edit", "create"], "program_applicant_types": ["list", "edit", "create", "delete"], "application_configurations": ["list", "show", "edit", "create"], "applicant_profile_configurations": ["show", "edit", "create", "delete"], "application_configurations_courses": ["list", "create", "edit", "delete"]}');
