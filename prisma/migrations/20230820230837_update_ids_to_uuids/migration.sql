/*
  Warnings:

  - The primary key for the `audit_log` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The `id` column on the `audit_log` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The primary key for the `principal` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The `id` column on the `principal` table would be dropped and recreated. This will lead to data loss if there is data in the column.

*/
-- AlterTable
ALTER TABLE "audit_log" DROP CONSTRAINT "audit_log_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" UUID NOT NULL DEFAULT gen_random_uuid(),
ADD CONSTRAINT "audit_log_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "principal" DROP CONSTRAINT "principal_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" UUID NOT NULL DEFAULT gen_random_uuid(),
ADD CONSTRAINT "principal_pkey" PRIMARY KEY ("id");
