generator client {
  provider = "prisma-client-js"
  output   = "./generated/platformClient"
}

datasource db {
  provider = "postgresql"
  url      = env("PLATFORM_DATABASE_URL")
}

model Addresses {
  id                   String                 @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  addressLine1         String?                @map("address_line_1") @db.VarChar(255)
  addressLine2         String?                @map("address_line_2") @db.VarChar(255)
  city                 String?                @db.VarChar(255)
  state                String?                @db.VarChar(255)
  zip                  String?                @db.VarChar(255)
  latitude             Float?
  longitude            Float?
  incomeLimitAreaId    String?                @map("income_limit_area_id") @db.Uuid
  createdAt            DateTime?              @default(now()) @map("created_at") @db.Timestamptz(6)
  incomeLimitArea      IncomeLimitAreas?      @relation(fields: [incomeLimitAreaId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "addresses_county_id_fkey")
  applicantProfiles    ApplicantProfiles[]
  applicationAddresses ApplicationAddresses[]
  partners             Partners[]
  profileAddresses     ProfileAddresses[]
  vendors              Vendors[]

  @@index([incomeLimitAreaId])
  @@map("addresses")
}

model Admins {
  id                       String                     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId                   String                     @map("user_id") @db.Uuid
  archivedAt               DateTime?                  @map("archived_at") @db.Timestamptz(6)
  user                     Users                      @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  application_answer_notes application_answer_notes[] @ignore
  assignments              Assignments[]
  bulkOperations           BulkOperations[]
  notes                    Notes[]
  programReferrals         ProgramReferrals[]
  workflowEvents           WorkflowEvents[]

  @@index([userId])
  @@map("admins")
}

model AnalyticsResources {
  id           String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  partnerId    String   @map("partner_id") @db.Uuid
  name         String   @db.VarChar(255)
  url          String   @db.VarChar(255)
  workspace_id String?  @db.VarChar
  dashboard_id String?  @db.VarChar
  partner      Partners @relation(fields: [partnerId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("analytics_resources")
}

model ApplicantProfiles {
  id               String             @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId           String             @unique @map("user_id") @db.Uuid
  secondaryEmail   String?            @map("secondary_email") @db.VarChar
  secondaryPhone   String?            @map("secondary_phone") @db.VarChar
  mailingAddressId String?            @map("mailing_address_id") @db.Uuid
  createdAt        DateTime?          @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt        DateTime?          @default(now()) @map("updated_at") @db.Timestamptz(6)
  deactivatedAt    DateTime?          @map("deactivated_at") @db.Timestamptz(6)
  applicantTypeId  String             @map("applicant_type_id") @db.Uuid
  address          Addresses?         @relation(fields: [mailingAddressId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user             Users              @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  applicantType    ApplicantTypes     @relation(fields: [applicantTypeId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "applicant_type_id")
  profileAddresses ProfileAddresses[]
  profileAnswers   ProfileAnswers[]
  profileDocuments ProfileDocuments[]
  profileNotes     ProfileNotes[]

  @@index([mailingAddressId])
  @@index([userId])
  @@map("applicant_profiles")
}

model ApplicantTypes {
  id                    String                  @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                  String                  @db.VarChar(100)
  partnerId             String?                 @map("partner_id") @db.Uuid
  applicantProfiles     ApplicantProfiles[]
  partner               Partners?               @relation(fields: [partnerId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  caseParticipants      CaseParticipants[]
  programApplicantTypes ProgramApplicantTypes[]

  @@unique([partnerId, name])
  @@map("applicant_types")
}

model ApplicationAddresses {
  id            String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  applicationId String       @map("application_id") @db.Uuid
  addressId     String       @map("address_id") @db.Uuid
  deactivatedAt DateTime?    @map("deactivated_at") @db.Timestamptz(6)
  address       Addresses    @relation(fields: [addressId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  application   Applications @relation(fields: [applicationId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([addressId])
  @@index([applicationId])
  @@map("application_addresses")
}

model ApplicationAnswers {
  id                         String                       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  key                        String                       @db.VarChar
  value                      String                       @db.VarChar
  createdAt                  DateTime?                    @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt                  DateTime?                    @default(now()) @map("updated_at") @db.Timestamptz(6)
  deactivatedAt              DateTime?                    @map("deactivated_at") @db.Timestamptz(6)
  versionId                  String                       @map("version_id") @db.Uuid
  sensitive_value            String?                      @db.VarChar
  application_answer_notes   application_answer_notes[]   @ignore
  application_answer_reviews application_answer_reviews[]
  applicationVersion         ApplicationVersions          @relation(fields: [versionId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([key])
  @@index([versionId], map: "application_answers_version_id_index")
  @@map("application_answers")
}

model ApplicationConfigurations {
  id                               String                             @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                             String                             @db.VarChar(255)
  description                      String
  config                           Json
  partnerId                        String?                            @map("partner_id") @db.Uuid
  partner                          Partners?                          @relation(fields: [partnerId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  programApplicationConfigurations ProgramApplicationConfigurations[]

  @@map("application_configurations")
}

model ApplicationDocuments {
  id            String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  applicationId String       @map("application_id") @db.Uuid
  documentId    String       @map("document_id") @db.Uuid
  deactivatedAt DateTime?    @map("deactivated_at") @db.Timestamptz(6)
  application   Applications @relation(fields: [applicationId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  document      Documents    @relation(fields: [documentId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([applicationId, documentId])
  @@index([applicationId])
  @@index([documentId])
  @@map("application_documents")
}

model ApplicationScores {
  id                   String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  applicationVersionId String              @map("application_version_id") @db.Uuid
  score                Int
  algorithmVersion     Int                 @map("algorithm_version")
  createdAt            DateTime            @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt            DateTime            @default(now()) @map("updated_at") @db.Timestamptz(6)
  deactivatedAt        DateTime?           @map("deactivated_at") @db.Timestamptz(6)
  applicationVersion   ApplicationVersions @relation(fields: [applicationVersionId], references: [id], onUpdate: Restrict, map: "fk_application_versions_application_scores")

  @@map("application_scores")
}

model ApplicationVerifications {
  id                   String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  service              Verificationservice
  confidence           Decimal             @db.Decimal
  applicationVersionId String              @map("application_version_id") @db.Uuid
  details              Json
  createdAt            DateTime            @default(now()) @map("created_at") @db.Timestamptz(6)
  deactivatedAt        DateTime?           @map("deactivated_at") @db.Timestamptz(6)
  metadata             Json?
  applicationVersion   ApplicationVersions @relation(fields: [applicationVersionId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([applicationVersionId, deactivatedAt], map: "idx_application_verifications_version_deactivated")
  @@index([applicationVersionId], map: "idx_application_verifications_version_id")
  @@map("application_verifications")
}

model ApplicationVersions {
  id                       String                     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  applicationId            String                     @map("application_id") @db.Uuid
  creatorId                String                     @map("creator_id") @db.Uuid
  createdAt                DateTime?                  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt                DateTime?                  @default(now()) @map("updated_at") @db.Timestamptz(6)
  description              String?
  deactivatedAt            DateTime?                  @map("deactivated_at") @db.Timestamptz(6)
  applicationAnswers       ApplicationAnswers[]
  applicationScores        ApplicationScores[]
  applicationVerifications ApplicationVerifications[]
  application              Applications               @relation(fields: [applicationId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user                     Users                      @relation(fields: [creatorId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([applicationId])
  @@index([creatorId])
  @@map("application_versions")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model Applications {
  id                   String                 @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  submitterId          String                 @map("submitter_id") @db.Uuid
  requestedAmount      Int?                   @map("requested_amount")
  answers              Json?
  createdAt            DateTime?              @default(now()) @map("created_at") @db.Timestamptz(6)
  submittedAt          DateTime?              @map("submitted_at") @db.Timestamptz(6)
  deactivatedAt        DateTime?              @map("deactivated_at") @db.Timestamptz(6)
  updatedAt            DateTime               @default(now()) @map("updated_at") @db.Timestamptz(6)
  caseId               String                 @map("case_id") @db.Uuid
  displayId            BigInt                 @unique @default(autoincrement()) @map("display_id")
  referralId           String?                @map("referral_id") @db.Uuid
  applicationAddresses ApplicationAddresses[]
  applicationDocuments ApplicationDocuments[]
  applicationVersions  ApplicationVersions[]
  case                 Cases                  @relation(fields: [caseId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  programReferral      ProgramReferrals?      @relation(fields: [referralId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user                 Users                  @relation(fields: [submitterId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  linkAttempts         LinkAttempts[]

  @@index([submitterId])
  @@map("applications")
}

model Assignments {
  id            String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  caseId        String    @map("case_id") @db.Uuid
  assigneeId    String    @map("assignee_id") @db.Uuid
  createdAt     DateTime? @default(now()) @map("created_at") @db.Timestamptz(6)
  deactivatedAt DateTime? @map("deactivated_at") @db.Timestamptz(6)
  admin         Admins    @relation(fields: [assigneeId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  case          Cases     @relation(fields: [caseId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([assigneeId])
  @@index([caseId])
  @@map("assignments")
}

model BulkOperations {
  id             String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  operation      String              @db.VarChar(25)
  status         Bulkoperationstatus
  adminId        String              @map("admin_id") @db.Uuid
  payload        Json                @db.Json
  createdAt      DateTime            @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime            @default(now()) @map("updated_at") @db.Timestamptz(6)
  completedAt    DateTime?           @map("completed_at") @db.Timestamptz(6)
  admin          Admins              @relation(fields: [adminId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  workflowEvents WorkflowEvents[]

  @@map("bulk_operations")
}

model CaseDocuments {
  id            String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  caseId        String    @map("case_id") @db.Uuid
  documentId    String    @map("document_id") @db.Uuid
  deactivatedAt DateTime? @map("deactivated_at") @db.Timestamptz(6)
  case          Cases     @relation(fields: [caseId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  document      Documents @relation(fields: [documentId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([caseId, documentId])
  @@index([documentId])
  @@index([caseId], map: "case_documents_profile_id_idx")
  @@map("case_documents")
}

model CaseNotes {
  id            String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  caseId        String    @map("case_id") @db.Uuid
  noteId        String    @map("note_id") @db.Uuid
  deactivatedAt DateTime? @map("deactivated_at") @db.Timestamptz(6)
  case          Cases     @relation(fields: [caseId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  note          Notes     @relation(fields: [noteId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([noteId])
  @@index([caseId], map: "case_notes_profile_id_idx")
  @@map("case_notes")
}

model CaseParticipants {
  id              String            @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  caseId          String            @map("case_id") @db.Uuid
  applicantTypeId String            @map("applicant_type_id") @db.Uuid
  name            String
  createdAt       DateTime          @default(now()) @map("created_at") @db.Timestamptz(6)
  deactivatedAt   DateTime?         @map("deactivated_at") @db.Timestamptz(6)
  email           String
  applicantType   ApplicantTypes    @relation(fields: [applicantTypeId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  case            Cases             @relation(fields: [caseId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  invitationCodes InvitationCodes[]
  linkAttempts    LinkAttempts[]

  @@index([applicantTypeId])
  @@index([caseId])
  @@map("case_participants")
}

model CaseTags {
  id        String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  caseId    String    @map("case_id") @db.Uuid
  tagId     String    @map("tag_id") @db.Uuid
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt DateTime? @map("updated_at") @db.Timestamp(6)
  case      Cases     @relation(fields: [caseId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  tag       Tags      @relation(fields: [tagId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([caseId], map: "case_tags_case_id")
  @@index([tagId], map: "case_tags_tag_id")
  @@map("case_tags")
}

model Cases {
  id                String             @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  programId         String             @map("program_id") @db.Uuid
  name              String             @db.VarChar(255)
  createdAt         DateTime?          @default(now()) @map("created_at") @db.Timestamptz(6)
  deactivatedAt     DateTime?          @map("deactivated_at") @db.Timestamptz(6)
  status            Casestatus         @default(InProgress)
  decisionReachedAt DateTime?          @map("decision_reached_at") @db.Timestamptz(6)
  displayId         BigInt             @unique @default(autoincrement()) @map("display_id")
  priority          Int                @default(10)
  legacyId          String?            @map("legacy_id") @db.VarChar
  statusUpdatedAt   DateTime?          @map("status_updated_at") @db.Timestamptz(6)
  applications      Applications[]
  assignments       Assignments[]
  caseDocuments     CaseDocuments[]
  caseNotes         CaseNotes[]
  caseParticipants  CaseParticipants[]
  caseTags          CaseTags[]
  program           Programs           @relation(fields: [programId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  comments          Comments[]
  fulfillments      Fulfillments[]
  linkAttempts      LinkAttempts[]
  partnerCases      PartnerCases[]

  @@index([programId])
  @@map("cases")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model Documents {
  id                   String                 @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  documentKey          String                 @map("document_key") @db.VarChar(255)
  uploaderId           String                 @map("uploader_id") @db.Uuid
  filename             String                 @db.VarChar(255)
  mimetype             String                 @db.VarChar(100)
  createdAt            DateTime?              @default(now()) @map("created_at") @db.Timestamptz(6)
  pinned               Boolean?
  deactivatedAt        DateTime?              @map("deactivated_at") @db.Timestamptz(6)
  sha256               String?                @db.VarChar(64)
  applicationDocuments ApplicationDocuments[]
  caseDocuments        CaseDocuments[]
  user                 Users                  @relation(fields: [uploaderId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  duplicate_documents  duplicate_documents[]
  profileDocuments     ProfileDocuments[]
  programDocuments     ProgramDocuments[]
  taxForms             TaxForms[]
  vendorDocuments      VendorDocuments[]

  @@index([sha256], map: "idx_documents_sha256")
  @@map("documents")
}

model EligibilityConfig {
  id                         String                       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  partnerId                  String?                      @map("partner_id") @db.Uuid
  description                String
  createdAt                  DateTime?                    @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt                  DateTime?                    @default(now()) @map("updated_at") @db.Timestamptz(6)
  deactivatedAt              DateTime?                    @map("deactivated_at") @db.Timestamptz(6)
  title                      String?
  copy                       String?
  message                    Json?
  partner                    Partners?                    @relation(fields: [partnerId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  eligibilityConfigQuestions EligibilityConfigQuestions[]

  @@map("eligibility_config")
}

model EligibilityConfigQuestions {
  id                  String               @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  configId            String               @map("config_id") @db.Uuid
  questionId          String               @map("question_id") @db.Uuid
  eligibilityConfig   EligibilityConfig    @relation(fields: [configId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  eligibilityQuestion EligibilityQuestions @relation(fields: [questionId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([configId])
  @@index([questionId])
  @@map("eligibility_config_questions")
}

model EligibilityQuestions {
  id                         String                       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  description                String
  type                       EligibilityQuestionType
  key                        String
  copy                       String?
  props                      Json?
  createdAt                  DateTime?                    @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt                  DateTime?                    @default(now()) @map("updated_at") @db.Timestamptz(6)
  deactivatedAt              DateTime?                    @map("deactivated_at") @db.Timestamptz(6)
  eligibilityConfigQuestions EligibilityConfigQuestions[]

  @@map("eligibility_questions")
}

model EnrollmentOutcomeOutcomes {
  id                  String             @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  enrollmentOutcomeId String             @map("enrollment_outcome_id") @db.Uuid
  outcomeId           String             @map("outcome_id") @db.Uuid
  deactivatedAt       DateTime?          @map("deactivated_at") @db.Timestamptz(6)
  enrollmentOutcome   EnrollmentOutcomes @relation(fields: [enrollmentOutcomeId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  outcome             Outcomes           @relation(fields: [outcomeId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([enrollmentOutcomeId, outcomeId])
  @@index([enrollmentOutcomeId], map: "enrollment_outcome_outcomes_enrollment_service_id_idx")
  @@index([outcomeId], map: "enrollment_outcome_outcomes_service_id_idx")
  @@map("enrollment_outcome_outcomes")
}

model EnrollmentOutcomes {
  id                        String                      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  enrollmentId              String                      @map("enrollment_id") @db.Uuid
  createdAt                 DateTime?                   @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt                 DateTime?                   @default(now()) @map("updated_at") @db.Timestamptz(6)
  deactivatedAt             DateTime?                   @map("deactivated_at") @db.Timestamptz(6)
  outcomeDate               DateTime                    @map("outcome_date") @db.Date
  enrollmentOutcomeOutcomes EnrollmentOutcomeOutcomes[]
  enrollment                Enrollments                 @relation(fields: [enrollmentId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([enrollmentId])
  @@map("enrollment_outcomes")
}

model EnrollmentServiceServices {
  id                  String             @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  enrollmentServiceId String             @map("enrollment_service_id") @db.Uuid
  serviceId           String             @map("service_id") @db.Uuid
  deactivatedAt       DateTime?          @map("deactivated_at") @db.Timestamptz(6)
  enrollmentService   EnrollmentServices @relation(fields: [enrollmentServiceId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  service             Services           @relation(fields: [serviceId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([enrollmentServiceId, serviceId])
  @@index([enrollmentServiceId])
  @@index([serviceId])
  @@map("enrollment_service_services")
}

model EnrollmentServices {
  id                        String                      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  enrollmentId              String                      @map("enrollment_id") @db.Uuid
  createdAt                 DateTime?                   @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt                 DateTime?                   @default(now()) @map("updated_at") @db.Timestamptz(6)
  deactivatedAt             DateTime?                   @map("deactivated_at") @db.Timestamptz(6)
  serviceDate               DateTime                    @map("service_date") @db.Date
  enrollmentServiceServices EnrollmentServiceServices[]
  enrollment                Enrollments                 @relation(fields: [enrollmentId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([enrollmentId])
  @@map("enrollment_services")
}

model Enrollments {
  id                 String               @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  applicantId        String               @map("applicant_id") @db.Uuid
  programId          String               @map("program_id") @db.Uuid
  createdAt          DateTime?            @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt          DateTime?            @default(now()) @map("updated_at") @db.Timestamptz(6)
  startDate          DateTime?            @map("start_date") @db.Date
  endDate            DateTime?            @map("end_date") @db.Date
  deactivatedAt      DateTime?            @map("deactivated_at") @db.Timestamptz(6)
  enrollmentOutcomes EnrollmentOutcomes[]
  enrollmentServices EnrollmentServices[]
  user               Users                @relation(fields: [applicantId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  program            Programs             @relation(fields: [programId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([applicantId])
  @@index([programId])
  @@map("enrollments")
}

model Features {
  id              String            @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name            String            @db.VarChar(255)
  description     String?
  createdAt       DateTime          @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt       DateTime          @default(now()) @map("updated_at") @db.Timestamptz(6)
  partnerFeatures PartnerFeatures[]
  programFeatures ProgramFeatures[]

  @@map("features")
}

model FulfillmentMeta {
  id            String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  fulfillmentId String       @map("fulfillment_id") @db.Uuid
  months        Json?        @default("[]")
  type          String?      @db.VarChar
  serviceDate   DateTime?    @map("service_date") @db.Timestamptz(6)
  startDate     DateTime?    @map("start_date") @db.Timestamptz(6)
  endDate       DateTime?    @map("end_date") @db.Timestamptz(6)
  checkNumber   String?      @map("check_number") @db.VarChar(255)
  billingCode   String?      @map("billing_code") @db.VarChar
  accountNumber String?      @map("account_number") @db.VarChar
  utilityType   String?      @map("utility_type") @db.VarChar
  expenseType   String?      @map("expense_type") @db.VarChar
  deactivatedAt DateTime?    @map("deactivated_at") @db.Timestamptz(6)
  benefit       String?      @db.VarChar(255)
  fulfillment   Fulfillments @relation(fields: [fulfillmentId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([fulfillmentId])
  @@map("fulfillment_meta")
}

model Fulfillments {
  id              String            @id(map: "fullfillments_pkey") @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  caseId          String            @map("case_id") @db.Uuid
  fundId          String            @map("fund_id") @db.Uuid
  deactivatedAt   DateTime?         @map("deactivated_at") @db.Timestamptz(6)
  legacyId        String?           @map("legacy_id") @db.VarChar
  displayId       BigInt            @default(autoincrement()) @map("display_id")
  approvedAmount  Int               @default(-1) @map("approved_amount")
  scheduleType    Scheduletype?     @default(onetime) @map("schedule_type")
  fulfillmentMeta FulfillmentMeta[]
  case            Cases             @relation(fields: [caseId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fullfillments_case_id_fkey")
  fund            Funds             @relation(fields: [fundId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "fullfillments_fund_id_fkey")
  paymentPattern  PaymentPatterns[]
  payments        Payments[]

  @@index([caseId])
  @@index([fundId])
  @@map("fulfillments")
}

model Funds {
  id                        String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  partnerId                 String         @map("partner_id") @db.Uuid
  name                      String         @db.VarChar(255)
  startingBalance           BigInt         @map("starting_balance")
  deactivated_at            DateTime?      @db.Timestamptz(6)
  created_at                DateTime       @default(now()) @db.Timestamptz(6)
  updated_at                DateTime       @default(now()) @db.Timestamptz(6)
  default_payment_field_key String?
  fulfillments              Fulfillments[]
  partner                   Partners       @relation(fields: [partnerId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  programFunds              ProgramFunds[]

  @@map("funds")
}

model IncidentMessages {
  id               String             @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  message          String
  severity         Int?               @default(10)
  createdAt        DateTime?          @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt        DateTime?          @default(now()) @map("updated_at") @db.Timestamptz(6)
  deactivatedAt    DateTime?          @map("deactivated_at") @db.Timestamptz(6)
  partnerIncidents PartnerIncidents[]

  @@map("incident_messages")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model IncomeLimitAreas {
  id          String       @id(map: "counties_pkey") @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  county      String       @db.VarChar(255)
  state       String       @db.VarChar(255)
  fipsCode    String       @unique(map: "income_limit_areas_fips_code_unique") @map("fips_code") @db.VarChar(255)
  town        String?      @db.VarChar(255)
  addresses   Addresses[]
  incomeLimit IncomeLimits @relation(fields: [fipsCode], references: [fipsCode], onDelete: NoAction, onUpdate: NoAction)

  @@index([fipsCode])
  @@map("income_limit_areas")
}

model IncomeLimits {
  id              String            @id(map: "income_limit_areas_pkey") @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  year            Int?
  fipsCode        String            @unique(map: "income_limits_fips_code_unique") @map("fips_code") @db.VarChar(255)
  refreshedAt     DateTime?         @map("refreshed_at") @db.Timestamptz(6)
  limits          Json?
  incomeLimitArea IncomeLimitAreas?

  @@index([fipsCode])
  @@map("income_limits")
}

model InvitationCodes {
  id                String           @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  caseParticipantId String           @map("case_participant_id") @db.Uuid
  code              String
  createdAt         DateTime         @default(now()) @map("created_at") @db.Timestamptz(6)
  usedAt            DateTime?        @map("used_at") @db.Timestamptz(6)
  deactivatedAt     DateTime?        @map("deactivated_at") @db.Timestamptz(6)
  caseParticipant   CaseParticipants @relation(fields: [caseParticipantId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([caseParticipantId])
  @@map("invitation_codes")
}

model LinkAttempts {
  id                String            @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  applicationId     String            @map("application_id") @db.Uuid
  originalCaseId    String            @map("original_case_id") @db.Uuid
  caseParticipantId String?           @map("case_participant_id") @db.Uuid
  result            Linkattemptresult
  details           Json?             @db.Json
  createdAt         DateTime          @default(now()) @map("created_at") @db.Timestamptz(6)
  deactivatedAt     DateTime?         @map("deactivated_at") @db.Timestamptz(6)
  application       Applications      @relation(fields: [applicationId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  caseParticipant   CaseParticipants? @relation(fields: [caseParticipantId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  case              Cases             @relation(fields: [originalCaseId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([applicationId])
  @@index([caseParticipantId])
  @@index([originalCaseId])
  @@map("link_attempts")
}

model Locks {
  id            String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  entityId      String    @map("entity_id") @db.Uuid
  createdAt     DateTime? @default(now()) @map("created_at") @db.Timestamptz(6)
  deactivatedAt DateTime? @map("deactivated_at") @db.Timestamptz(6)

  @@map("locks")
}

model Notes {
  id                         String                       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  authorId                   String                       @map("author_id") @db.Uuid
  content                    String?
  createdAt                  DateTime?                    @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt                  DateTime?                    @default(now()) @map("updated_at") @db.Timestamptz(6)
  application_answer_notes   application_answer_notes[]   @ignore
  application_answer_reviews application_answer_reviews[]
  caseNotes                  CaseNotes[]
  admin                      Admins                       @relation(fields: [authorId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  profileNotes               ProfileNotes[]
  vendorNotes                VendorNotes[]

  @@map("notes")
}

model Outcomes {
  id                        String                      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                      String                      @db.VarChar(255)
  programId                 String                      @map("program_id") @db.Uuid
  enrollmentOutcomeOutcomes EnrollmentOutcomeOutcomes[]
  program                   Programs                    @relation(fields: [programId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("outcomes")
}

model PartnerCases {
  id            String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  partnerId     String    @map("partner_id") @db.Uuid
  caseId        String    @map("case_id") @db.Uuid
  createdAt     DateTime? @default(now()) @map("created_at") @db.Timestamptz(6)
  deactivatedAt DateTime? @map("deactivated_at") @db.Timestamptz(6)
  case          Cases     @relation(fields: [caseId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  partner       Partners  @relation(fields: [partnerId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("partner_cases")
}

model PartnerFeatures {
  id        String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  partnerId String    @map("partner_id") @db.Uuid
  featureId String    @map("feature_id") @db.Uuid
  enabled   Boolean?  @default(false)
  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt DateTime? @map("updated_at") @db.Timestamp(6)
  feature   Features  @relation(fields: [featureId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  partner   Partners  @relation(fields: [partnerId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([featureId], map: "partner_features_feature_id")
  @@index([partnerId], map: "partner_features_partner_id")
  @@map("partner_features")
}

model PartnerIncidents {
  id              String           @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  incidentId      String           @map("incident_id") @db.Uuid
  partnerId       String           @map("partner_id") @db.Uuid
  incidentMessage IncidentMessages @relation(fields: [incidentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  partner         Partners         @relation(fields: [partnerId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([incidentId])
  @@index([partnerId])
  @@map("partner_incidents")
}

model PartnerReports {
  id             String          @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  partnerId      String          @map("partner_id") @db.Uuid
  title          String
  baseRepository String          @map("base_repository")
  batchSize      Int?            @map("batch_size")
  createdAt      DateTime?       @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime?       @default(now()) @map("updated_at") @db.Timestamptz(6)
  filter         Json?
  subfolder      String?
  partner        Partners        @relation(fields: [partnerId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  reportColumns  ReportColumns[]

  @@index([partnerId])
  @@map("partner_reports")
}

model PartnerWhitelabeling {
  id           String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  partnerId    String   @map("partner_id") @db.Uuid
  logo         String   @db.VarChar(255)
  platformName String   @map("platform_name") @db.VarChar(255)
  brandColor   String   @map("brand_color") @db.VarChar(255)
  favicon      String   @db.VarChar(255)
  senderEmail  String?  @map("sender_email") @db.VarChar(255)
  partner      Partners @relation(fields: [partnerId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([partnerId])
  @@map("partner_whitelabeling")
}

model Partners {
  id                        String                      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                      String?                     @db.VarChar(255)
  externalId                String                      @unique(map: "partners_external_id_unique") @map("external_id") @db.VarChar(25)
  email                     String                      @db.VarChar(255)
  phone                     String?                     @db.VarChar(255)
  parentId                  String?                     @map("parent_id") @db.Uuid
  mailingAddressId          String?                     @map("mailing_address_id") @db.Uuid
  gleanInviteToken          String?                     @map("glean_invite_token") @db.VarChar(255)
  config                    Json                        @default("{}")
  analyticsResources        AnalyticsResources[]
  applicantTypes            ApplicantTypes[]
  applicationConfigurations ApplicationConfigurations[]
  eligibilityConfigs        EligibilityConfig[]
  funds                     Funds[]
  notification_templates    notification_templates[]
  partnerCases              PartnerCases[]
  partnerFeatures           PartnerFeatures[]
  partnerIncidents          PartnerIncidents[]
  partnerReports            PartnerReports[]
  partnerWhitelabelings     PartnerWhitelabeling[]
  address                   Addresses?                  @relation(fields: [mailingAddressId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  partner                   Partners?                   @relation("partnersTopartners", fields: [parentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  otherPartners             Partners[]                  @relation("partnersTopartners")
  programEligibilities      ProgramEligibility[]
  programs                  Programs[]
  saved_views               saved_views[]
  tags                      Tags[]
  users                     Users[]
  vendorTypes               VendorTypes[]
  vendors                   Vendors[]

  @@index([mailingAddressId], map: "partner_mailing_address_id_idx")
  @@map("partners")
}

model PaymentPatterns {
  id            String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  fulfillmentId String         @map("fulfillment_id") @db.Uuid
  amount        Int
  pattern       Paymentpattern
  start         DateTime?      @db.Timestamptz(6)
  count         Int
  createdAt     DateTime       @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt     DateTime       @default(now()) @map("updated_at") @db.Timestamptz(6)
  deactivatedAt DateTime?      @map("deactivated_at") @db.Timestamptz(6)
  fulfillment   Fulfillments   @relation(fields: [fulfillmentId], references: [id], onUpdate: Restrict, map: "fk_payment_patterns_fulfillment")

  @@index([fulfillmentId])
  @@map("payment_patterns")
}

model Payments {
  id                   String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  fulfillmentId        String         @map("fulfillment_id") @db.Uuid
  amount               Int
  status               Paymentstatus
  method               Paymentmethod?
  createdAt            DateTime?      @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt            DateTime?      @default(now()) @map("updated_at") @db.Timestamptz(6)
  completedAt          DateTime?      @map("completed_at") @db.Timestamptz(6)
  deactivatedAt        DateTime?      @map("deactivated_at") @db.Timestamptz(6)
  displayId            BigInt         @unique @default(autoincrement()) @map("display_id")
  payeeType            Payee          @map("payee_type")
  payeeId              String         @map("payee_id") @db.Uuid
  initiatedAt          DateTime?      @map("initiated_at") @db.Timestamptz(6)
  note                 String?        @db.VarChar(255)
  scheduledFor         DateTime?      @map("scheduled_for") @db.Timestamptz(6)
  details              Json?
  mailing_address_type String?
  mailing_address      Json?
  fulfillment          Fulfillments   @relation(fields: [fulfillmentId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([fulfillmentId])
  @@index([payeeId])
  @@map("payments")
}

model ProfileAddresses {
  id               String            @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  profileId        String            @map("profile_id") @db.Uuid
  addressId        String            @map("address_id") @db.Uuid
  deactivatedAt    DateTime?         @map("deactivated_at") @db.Timestamptz(6)
  address          Addresses         @relation(fields: [addressId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  applicantProfile ApplicantProfiles @relation(fields: [profileId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([addressId])
  @@index([profileId])
  @@map("profile_addresses")
}

model ProfileAnswers {
  id               String            @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  profileId        String            @map("profile_id") @db.Uuid
  key              String
  value            String
  createdAt        DateTime?         @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt        DateTime?         @default(now()) @map("updated_at") @db.Timestamptz(6)
  deactivatedAt    DateTime?         @map("deactivated_at") @db.Timestamptz(6)
  applicantProfile ApplicantProfiles @relation(fields: [profileId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([profileId])
  @@map("profile_answers")
}

model ProfileDocuments {
  id               String            @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  profileId        String            @map("profile_id") @db.Uuid
  documentId       String            @map("document_id") @db.Uuid
  deactivatedAt    DateTime?         @map("deactivated_at") @db.Timestamptz(6)
  document         Documents         @relation(fields: [documentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  applicantProfile ApplicantProfiles @relation(fields: [profileId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([documentId])
  @@index([profileId])
  @@map("profile_documents")
}

model ProfileNotes {
  id               String            @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  profileId        String            @map("profile_id") @db.Uuid
  noteId           String            @map("note_id") @db.Uuid
  deactivatedAt    DateTime?         @map("deactivated_at") @db.Timestamptz(6)
  note             Notes             @relation(fields: [noteId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  applicantProfile ApplicantProfiles @relation(fields: [profileId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([noteId])
  @@index([profileId])
  @@map("profile_notes")
}

model ProgramApplicantTypes {
  id              String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  applicantTypeId String         @map("applicant_type_id") @db.Uuid
  programId       String         @map("program_id") @db.Uuid
  nameOverride    String?        @map("name_override") @db.VarChar(100)
  applicantType   ApplicantTypes @relation(fields: [applicantTypeId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  program         Programs       @relation(fields: [programId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([programId, applicantTypeId])
  @@map("program_applicant_types")
}

model ProgramApplicationConfigurations {
  id                       String                    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  programId                String                    @map("program_id") @db.Uuid
  configurationId          String                    @map("configuration_id") @db.Uuid
  applicationConfiguration ApplicationConfigurations @relation(fields: [configurationId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  program                  Programs                  @relation(fields: [programId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("program_application_configurations")
}

model ProgramDocuments {
  id         String                 @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  programId  String                 @map("program_id") @db.Uuid
  documentId String                 @map("document_id") @db.Uuid
  status     Programdocumentstatus?
  document   Documents              @relation(fields: [documentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  program    Programs               @relation(fields: [programId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("program_documents")
}

model ProgramEligibility {
  id        String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  programId String    @map("program_id") @db.Uuid
  partnerId String?   @map("partner_id") @db.Uuid
  filter    Json
  partner   Partners? @relation(fields: [partnerId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  program   Programs  @relation(fields: [programId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([programId, partnerId])
  @@map("program_eligibility")
}

model ProgramFeatures {
  id        String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  programId String   @map("program_id") @db.Uuid
  featureId String   @map("feature_id") @db.Uuid
  enabled   Boolean? @default(true)
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt DateTime @default(now()) @map("updated_at") @db.Timestamptz(6)
  feature   Features @relation(fields: [featureId], references: [id], map: "fk_features_program_features")
  program   Programs @relation(fields: [programId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "fk_program_program_features")

  @@unique([featureId, programId], map: "unique_feature_program")
  @@index([featureId], map: "program_features_features_id_idx")
  @@index([programId])
  @@map("program_features")
}

model ProgramFunds {
  id        String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  programId String   @map("program_id") @db.Uuid
  fundId    String   @map("fund_id") @db.Uuid
  fund      Funds    @relation(fields: [fundId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  program   Programs @relation(fields: [programId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("program_funds")
}

model ProgramReferrals {
  id            String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId        String         @map("user_id") @db.Uuid
  programId     String         @map("program_id") @db.Uuid
  adminId       String         @map("admin_id") @db.Uuid
  createdAt     DateTime       @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt     DateTime       @default(now()) @map("updated_at") @db.Timestamptz(6)
  deactivatedAt DateTime?      @map("deactivated_at") @db.Timestamptz(6)
  applications  Applications[]
  admin         Admins         @relation(fields: [adminId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  program       Programs       @relation(fields: [programId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user          Users          @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([adminId])
  @@index([programId])
  @@index([userId])
  @@map("program_referrals")
}

model Programs {
  id                               String                             @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                             String                             @db.VarChar(255)
  heroImage                        String?                            @map("hero_image") @db.VarChar(255)
  partnerId                        String                             @map("partner_id") @db.Uuid
  config                           Json
  status                           Programstatus                      @default(Open)
  cases                            Cases[]
  enrollments                      Enrollments[]
  notification_templates           notification_templates[]
  outcomes                         Outcomes[]
  programApplicantTypes            ProgramApplicantTypes[]
  programApplicationConfigurations ProgramApplicationConfigurations[]
  programDocuments                 ProgramDocuments[]
  programEligibilities             ProgramEligibility[]
  programFeatures                  ProgramFeatures[]
  programFunds                     ProgramFunds[]
  programReferrals                 ProgramReferrals[]
  partner                          Partners                           @relation(fields: [partnerId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  services                         Services[]

  @@index([partnerId], map: "program_partner_id_idx")
  @@map("programs")
}

model ReportColumns {
  id            String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  reportId      String         @map("report_id") @db.Uuid
  header        String
  map           String
  dataType      String?        @map("data_type")
  relations     String[]
  createdAt     DateTime?      @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt     DateTime?      @default(now()) @map("updated_at") @db.Timestamptz(6)
  columnOrder   Int?           @map("column_order") @db.SmallInt
  partnerReport PartnerReports @relation(fields: [reportId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([reportId])
  @@map("report_columns")
}

model Services {
  id                        String                      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                      String                      @db.VarChar(255)
  programId                 String                      @map("program_id") @db.Uuid
  parentId                  String?                     @map("parent_id") @db.Uuid
  order                     Int                         @default(0)
  enrollmentServiceServices EnrollmentServiceServices[]
  service                   Services?                   @relation("servicesToservices", fields: [parentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  otherServices             Services[]                  @relation("servicesToservices")
  program                   Programs                    @relation(fields: [programId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@map("services")
}

model Tags {
  id            String     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name          String
  partnerId     String     @map("partner_id") @db.Uuid
  createdAt     DateTime   @default(now()) @map("created_at") @db.Timestamp(6)
  updatedAt     DateTime   @default(now()) @map("updated_at") @db.Timestamp(6)
  deactivatedAt DateTime?  @map("deactivated_at") @db.Timestamp(6)
  caseTags      CaseTags[]
  partner       Partners   @relation(fields: [partnerId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([partnerId], map: "tags_partner_id")
  @@map("tags")
}

model TaxForms {
  id            String      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId        String?     @map("user_id") @db.Uuid
  documentId    String?     @map("document_id") @db.Uuid
  type          Taxformtype
  data          Json?       @default("{}")
  deactivatedAt DateTime?   @map("deactivated_at") @db.Timestamptz(6)
  document      Documents?  @relation(fields: [documentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  user          Users?      @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([documentId])
  @@index([userId])
  @@map("tax_forms")
}

model TransactionAuditLog {
  id        String               @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  operation Transactionoperation
  status    Transactionstatus
  input     Json
  metadata  Json
  createdAt DateTime?            @default(now()) @map("created_at") @db.Timestamptz(6)

  @@map("transaction_audit_log")
}

model UserDocuments {
  id         String @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId     String @map("user_id") @db.Uuid
  documentId String @map("document_id") @db.Uuid

  @@unique([userId, documentId])
  @@map("user_documents")
}

model Users {
  id                        String                @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  email                     String?               @db.VarChar(255)
  name                      String                @db.VarChar(255)
  createdAt                 DateTime?             @default(now()) @map("created_at") @db.Timestamptz(6)
  validatedEmail            Boolean?              @default(false) @map("validated_email")
  phone                     String?               @db.VarChar(25)
  updatedAt                 DateTime              @default(now()) @map("updated_at") @db.Timestamptz(6)
  partnerId                 String                @map("partner_id") @db.Uuid
  displayId                 BigInt                @unique @default(autoincrement()) @map("display_id")
  legacyId                  String?               @map("legacy_id") @db.VarChar(255)
  deactivatedAt             DateTime?             @map("deactivated_at") @db.Timestamptz(6)
  taxId                     String?               @map("tax_id") @db.VarChar
  communication_preferences Json?
  admins                    Admins[]
  applicantProfile          ApplicantProfiles?
  applicationVersions       ApplicationVersions[]
  applications              Applications[]
  comments                  Comments[]
  documents                 Documents[]
  enrollments               Enrollments[]
  programReferrals          ProgramReferrals[]
  saved_views               saved_views[]
  taxForms                  TaxForms[]
  partner                   Partners              @relation(fields: [partnerId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  workflow_events           WorkflowEvents[]

  @@index([partnerId])
  @@map("users")
}

model VendorDocuments {
  id         String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  vendorId   String    @map("vendor_id") @db.Uuid
  documentId String    @map("document_id") @db.Uuid
  document   Documents @relation(fields: [documentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  vendor     Vendors   @relation(fields: [vendorId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([vendorId, documentId])
  @@index([documentId])
  @@index([vendorId])
  @@map("vendor_documents")
}

model VendorNotes {
  id       String  @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  vendorId String  @map("vendor_id") @db.Uuid
  noteId   String  @map("note_id") @db.Uuid
  note     Notes   @relation(fields: [noteId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  vendor   Vendors @relation(fields: [vendorId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([noteId])
  @@index([vendorId])
  @@map("vendor_notes")
}

model VendorTypes {
  id                String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  partnerId         String              @map("partner_id") @db.Uuid
  name              String              @db.VarChar(255)
  partner           Partners            @relation(fields: [partnerId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  vendorVendorTypes VendorVendorTypes[]

  @@map("vendor_types")
}

model VendorVendorTypes {
  id           String      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  vendorId     String      @map("vendor_id") @db.Uuid
  vendorTypeId String      @map("vendor_type_id") @db.Uuid
  vendor       Vendors     @relation(fields: [vendorId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  vendorType   VendorTypes @relation(fields: [vendorTypeId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([vendorId])
  @@index([vendorTypeId])
  @@map("vendor_vendor_types")
}

model Vendors {
  id                String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  partnerId         String              @map("partner_id") @db.Uuid
  name              String              @db.VarChar(255)
  taxId             String?             @map("tax_id") @db.VarChar(255)
  externalId        String?             @map("external_id") @db.VarChar(255)
  phone             String?             @db.VarChar(25)
  email             String?             @db.VarChar(255)
  mailingAddressId  String              @map("mailing_address_id") @db.Uuid
  createdAt         DateTime?           @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt         DateTime?           @default(now()) @map("updated_at") @db.Timestamptz(6)
  legacyId          String?             @map("legacy_id") @db.VarChar
  deactivatedAt     DateTime?           @map("deactivated_at") @db.Timestamptz(6)
  vendorDocuments   VendorDocuments[]
  vendorNotes       VendorNotes[]
  vendorVendorTypes VendorVendorTypes[]
  address           Addresses           @relation(fields: [mailingAddressId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  partner           Partners            @relation(fields: [partnerId], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([mailingAddressId])
  @@index([partnerId])
  @@map("vendors")
}

model WorkflowEvents {
  id              String          @id(map: "workflow_events_temp_pkey") @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  entityId        String          @map("entity_id") @db.Uuid
  entityType      String?         @default("case") @map("entity_type") @db.VarChar
  adminId         String?         @map("admin_id") @db.Uuid
  assigneeId      String?         @map("assignee_id") @db.Uuid
  action          String
  previousValue   String?         @map("previous_value")
  newValue        String?         @map("new_value")
  details         String?
  createdAt       DateTime?       @default(now()) @map("created_at") @db.Timestamptz(6)
  bulkOperationId String?         @map("bulk_operation_id") @db.Uuid
  deactivatedAt   DateTime?       @map("deactivated_at") @db.Timestamptz(6)
  user_id         String?         @db.Uuid
  bulkOperation   BulkOperations? @relation(fields: [bulkOperationId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  admin           Admins?         @relation(fields: [adminId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "workflow_events_temp_admin_id_fkey")
  users           Users?          @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([adminId])
  @@index([entityId])
  @@index([newValue])
  @@map("workflow_events")
}

model Changelogs {
  id            String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  content       String
  createdAt     DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt     DateTime  @default(now()) @map("updated_at") @db.Timestamptz(6)
  deactivatedAt DateTime? @map("deactivated_at") @db.Timestamptz(6)

  @@map("changelogs")
}

model Comments {
  id             String          @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  content        String
  authorId       String          @map("author_id") @db.Uuid
  caseId         String          @map("case_id") @db.Uuid
  parentId       String?         @map("parent_id") @db.Uuid
  createdAt      DateTime        @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt      DateTime        @default(now()) @map("updated_at") @db.Timestamptz(6)
  deactivatedAt  DateTime?       @map("deactivated_at") @db.Timestamptz(6)
  user           Users           @relation(fields: [authorId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  case           Cases           @relation(fields: [caseId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  comments       Comments?       @relation("commentsTocomments", fields: [parentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  other_comments Comments[]      @relation("commentsTocomments")
  notifications  notifications[]

  @@index([authorId], map: "comments_author_id")
  @@index([caseId], map: "comments_case_id")
  @@index([parentId], map: "comments_parent_id")
  @@map("comments")
}

model notifications {
  id          String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  comment_id  String   @db.Uuid
  external_id String
  created_at  DateTime @default(now()) @db.Timestamptz(6)
  updated_at  DateTime @default(now()) @db.Timestamptz(6)
  comments    Comments @relation(fields: [comment_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([comment_id], map: "notifications_comment_id")
}

model notification_templates {
  id         String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  type       String
  channel    String
  content    Json      @default("{}")
  partner_id String?   @db.Uuid
  program_id String?   @db.Uuid
  partners   Partners? @relation(fields: [partner_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  programs   Programs? @relation(fields: [program_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([channel, type, partner_id, program_id], map: "templates_unique")
}

model saved_views {
  id             String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  partner_id     String    @db.Uuid
  author_id      String    @db.Uuid
  name           String
  description    String?
  filters        Json      @default("{}")
  is_public      Boolean?  @default(false)
  created_at     DateTime  @default(now()) @db.Timestamptz(6)
  updated_at     DateTime  @default(now()) @db.Timestamptz(6)
  deactivated_at DateTime? @db.Timestamptz(6)
  users          Users     @relation(fields: [author_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  partners       Partners  @relation(fields: [partner_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([author_id], map: "saved_views_author_id")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model application_answer_notes {
  id                    String             @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  application_answer_id String             @db.Uuid
  case_manager_id       String             @db.Uuid
  note_id               String?            @db.Uuid
  created_at            DateTime           @default(now()) @db.Timestamptz(6)
  updated_at            DateTime           @default(now()) @db.Timestamptz(6)
  deactivated_at        DateTime?          @db.Timestamptz(6)
  application_answers   ApplicationAnswers @relation(fields: [application_answer_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  admins                Admins             @relation(fields: [case_manager_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  notes                 Notes?             @relation(fields: [note_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@ignore
}

model application_answer_reviews {
  id                    String             @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  application_answer_id String             @db.Uuid
  review_status         review_status_enum
  note_id               String?            @db.Uuid
  created_at            DateTime           @default(now()) @db.Timestamptz(6)
  updated_at            DateTime           @default(now()) @db.Timestamptz(6)
  deactivated_at        DateTime?          @db.Timestamptz(6)
  application_answers   ApplicationAnswers @relation(fields: [application_answer_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  notes                 Notes?             @relation(fields: [note_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model duplicate_documents {
  id           String      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  duplicate_id String?     @db.Uuid
  document_id  String?     @db.Uuid
  documents    Documents?  @relation(fields: [document_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  duplicates   duplicates? @relation(fields: [duplicate_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@index([duplicate_id, document_id], map: "idx_duplicate_documents_duplicate_id_document_id")
}

model duplicates {
  id                  String                @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  group_id            String                @db.Uuid
  entity_type         String
  metadata            Json?
  created_at          DateTime?             @default(now()) @db.Timestamp(6)
  updated_at          DateTime?             @db.Timestamp(6)
  deactivated_at      DateTime?             @db.Timestamp(6)
  duplicate_documents duplicate_documents[]

  @@index([group_id], map: "idx_duplicates_group_id")
  @@index([id], map: "idx_duplicates_id")
}

enum Bulkoperationstatus {
  Validating
  ValidationFailed
  InProgress
  Failed
  Completed

  @@map("bulkoperationstatus")
}

enum Casestatus {
  Incomplete
  ReadyForReview
  InReview
  PendingCertification
  FiscalReview
  PaymentSent
  Denied
  Withdrawn
  Approved
  Archived
  InProgress

  @@map("casestatus")
}

enum Disbursementstatus {
  Pending
  Success
  Failed

  @@map("disbursementstatus")
}

enum EligibilityQuestionType {
  Address
  AMIComponent
  Content
  Dropdown
  TextInput
  YesOrNo

  @@map("eligibility_question_type")
}

enum Linkattemptresult {
  success
  fail

  @@map("linkattemptresult")
}

enum Payee {
  User
  Vendor

  @@map("payee")
}

enum Paymentmethod {
  external
  ach
  zelle
  physicalCard
  check
  virtualCard

  @@map("paymentmethod")
}

enum Paymentpattern {
  monthly
  semiMonthly
  biWeekly
  weekly
  oneTime

  @@map("paymentpattern")
}

enum Paymentstatus {
  pending
  success
  failed
  authorized
  initiated
  canceled

  @@map("paymentstatus")
}

enum Programdocumentstatus {
  InProgress
  Failed
  Completed

  @@map("programdocumentstatus")
}

enum Programstatus {
  Open
  ReferralOnly
  Closed

  @@map("programstatus")
}

enum Rejectionreason {
  NotInFile
  FailedSelfCertification
  DocumentationDoesNotSupport

  @@map("rejectionreason")
}

enum Scheduletype {
  onetime
  recurring

  @@map("scheduletype")
}

enum Taxformtype {
  W9

  @@map("taxformtype")
}

enum Transactionoperation {
  LinkLegacyUser

  @@map("transactionoperation")
}

enum Transactionstatus {
  Success
  Failure

  @@map("transactionstatus")
}

enum Verificationservice {
  DataLookup

  @@map("verificationservice")
}

enum review_status_enum {
  MEETS_REQUIREMENTS
  DOES_NOT_MEET_REQUIREMENTS
  NEEDS_FURTHER_REVIEW
  NEEDS_RESUBMISSION
  NOTE_ONLY
}
