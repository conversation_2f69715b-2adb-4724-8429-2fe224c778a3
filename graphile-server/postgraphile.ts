import PgSimplifyInflectorPlugin from '@graphile-contrib/pg-simplify-inflector';
import PgAggregatesPlugin from '@graphile/pg-aggregates';
import { Pool } from 'pg';
import { postgraphile } from 'postgraphile';
import ConnectionFilterPlugin from 'postgraphile-plugin-connection-filter';
import PostGraphileNestedMutations from 'postgraphile-plugin-nested-mutations';
import { type ServiceType, config } from './config';

type ServiceMap = { [service in ServiceType]: ReturnType<typeof postgraphile> };

const services = Object.fromEntries(
  Object.entries(config).map(([service, conf]) => [
    service,
    postgraphile(
      new Pool({
        connectionString: conf.CONNECTION_STRING,
      }),
      conf.DB_SCHEMA,
      {
        appendPlugins: [
          PgSimplifyInflectorPlugin,
          ConnectionFilterPlugin,
          PostGraphileNestedMutations,
          PgAggregatesPlugin,
        ],
        graphileBuildOptions: {
          pgShortPk: true,
          connectionFilterRelations: true,
          nestedMutationsSimpleFieldNames: true,
        },
        simpleCollections: 'both',
        // watchPg: true, // Need extension for this to work properly
        graphiql: true,
        enhanceGraphiql: true,
        // externalUrlBase: "/api", // Don't use this since graphql route is incorrect w/ it
        graphqlRoute: `/api/graphql/${service}`,
        graphiqlRoute: `/api/graphiql/${service}`,
        eventStreamRoute: `/api/graphql/${service}/stream`,
        retryOnInitFail: true,
        disableQueryLog: true,
        dynamicJson: true,
        bodySizeLimit: '5mb',
        // retryOnInitFail is mainly so that going to /api/graphiql
        // doesn't crash entire app if config is incorrect. Fix config.
      },
    ),
  ]),
) as ServiceMap;

export default services;
