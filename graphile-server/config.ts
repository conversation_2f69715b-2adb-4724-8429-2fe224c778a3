export enum ServiceType {
  Config = 'config',
  Compliance = 'compliance',
  CorePlatform = 'core-platform',
  Document = 'document',
  Payment = 'payment',
  Scheduler = 'scheduler',
  Sorcery = 'sorcery',
  Verification = 'verification',
  Identity = 'identity',
}

interface ServiceConfiguration {
  CONNECTION_STRING: string;
  DB_SCHEMA: string;
}

export const config: { [service in ServiceType]: ServiceConfiguration } = {
  [ServiceType.CorePlatform]: {
    CONNECTION_STRING:
      process.env.PLATFORM_DATABASE_URL || 'postgresql://postgres@localhost:5432/rentalassistance',
    DB_SCHEMA: process.env.PLATFORM_DATABASE_SCHEMA || 'public',
  },
  [ServiceType.Config]: {
    CONNECTION_STRING:
      process.env.CONFIG_DATABASE_URL || 'postgresql://postgres@localhost:5432/config',
    DB_SCHEMA: process.env.CONFIG_DATABASE_SCHEMA || 'public',
  },
  [ServiceType.Document]: {
    CONNECTION_STRING:
      process.env.DOCUMENT_DATABASE_URL || 'postgresql://postgres@localhost:5432/document_ai',
    DB_SCHEMA: process.env.DOCUMENT_DATABASE_SCHEMA || 'public',
  },
  [ServiceType.Payment]: {
    CONNECTION_STRING:
      process.env.PAYMENT_DATABASE_URL || 'postgresql://postgres@localhost:5432/payments',
    DB_SCHEMA: process.env.PAYMENT_DATABASE_SCHEMA || 'public',
  },
  [ServiceType.Scheduler]: {
    CONNECTION_STRING:
      process.env.SCHEDULER_DATABASE_URL || 'postgresql://postgres@localhost:5432/scheduler',
    DB_SCHEMA: process.env.SCHEDULER_DATABASE_SCHEMA || 'public',
  },
  [ServiceType.Sorcery]: {
    CONNECTION_STRING:
      process.env.SORCERY_DATABASE_URL || 'postgresql://postgres@localhost:5432/sorcery',
    DB_SCHEMA: process.env.SORCERY_DATABASE_SCHEMA || 'public',
  },
  [ServiceType.Verification]: {
    CONNECTION_STRING:
      process.env.VERIFICATION_DATABASE_URL || 'postgresql://postgres@localhost:5432/verification',
    DB_SCHEMA: process.env.VERIFICATION_DATABASE_SCHEMA || 'public',
  },
  [ServiceType.Identity]: {
    CONNECTION_STRING:
      process.env.IDENTITY_DATABASE_URL || 'postgresql://postgres@localhost:9022/identity',
    DB_SCHEMA: process.env.IDENTITY_DATABASE_SCHEMA || 'public',
  },
  [ServiceType.Compliance]: {
    CONNECTION_STRING:
      process.env.COMPLIANCE_DB_URL || 'postgresql://postgres@localhost:9023/compliance',
    DB_SCHEMA: process.env.COMPLIANCE_DB_SCHEMA || 'public',
  },
};
