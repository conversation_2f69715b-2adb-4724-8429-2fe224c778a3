import type { NextApiRequest, NextApiResponse } from 'next';

export default function runMiddleware(
  req: NextApiRequest,
  res: NextApiResponse,
  // biome-ignore lint/suspicious/noExplicitAny: IGNORE THIS
  fn: any,
) {
  return new Promise((resolve, reject) => {
    // biome-ignore lint/suspicious/noExplicitAny: IGNORE THIS
    fn(req, res, (result: any) => {
      if (result instanceof Error) {
        return reject(result);
      }

      return resolve(result);
    });
  });
}
