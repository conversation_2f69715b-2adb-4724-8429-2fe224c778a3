{"name": "sorcery", "version": "0.1.0", "private": true, "scripts": {"dev": "refine dev", "dev:debug": "DEBUG=\"graphile-build:warn\" refine dev", "build": "NODE_OPTIONS=--max_old_space_size=8192 refine build", "start": "refine start", "prisma:dbpull": "pnpm prisma:dbpull:platform && pnpm prisma:dbpull:payment && pnpm prisma:dbpull:scheduler && pnpm prisma:dbpull:sorcery && pnpm prisma:dbpull:config && pnpm prisma:dbpull:verification && pnpm prisma:dbpull:document && pnpm prisma:dbpull:identity && pnpm prisma:dbpull:compliance", "prisma:dbpull:config": "prisma db pull --schema prisma/config-schema.prisma", "prisma:dbpull:document": "prisma db pull --schema prisma/document-schema.prisma", "prisma:dbpull:platform": "prisma db pull --schema prisma/platform-schema.prisma", "prisma:dbpull:payment": "prisma db pull --schema prisma/payment-schema.prisma", "prisma:dbpull:scheduler": "prisma db pull --schema prisma/scheduler-schema.prisma", "prisma:dbpull:sorcery": "prisma db pull --schema prisma/sorcery-schema.prisma", "prisma:dbpull:verification": "prisma db pull --schema prisma/verification-schema.prisma", "prisma:dbpull:identity": "prisma db pull --schema prisma/identity-schema.prisma", "prisma:dbpull:compliance": "prisma db pull --schema prisma/compliance-schema.prisma", "prisma:migrate": "prisma migrate dev --schema prisma/sorcery-schema.prisma", "prisma:migrate:deploy": "prisma migrate deploy --schema prisma/sorcery-schema.prisma", "prisma:generate": "pnpm prisma:generate:platform && pnpm prisma:generate:payment && pnpm prisma:generate:scheduler && pnpm prisma:generate:sorcery && pnpm prisma:generate:config && pnpm prisma:generate:verification && pnpm prisma:generate:document && pnpm prisma:generate:identity && pnpm prisma:generate:compliance", "prisma:generate:config": "prisma generate --schema prisma/config-schema.prisma", "prisma:generate:document": "prisma generate --schema prisma/document-schema.prisma", "prisma:generate:platform": "prisma generate --schema prisma/platform-schema.prisma", "prisma:generate:payment": "prisma generate --schema prisma/payment-schema.prisma", "prisma:generate:scheduler": "prisma generate --schema prisma/scheduler-schema.prisma", "prisma:generate:sorcery": "prisma generate --schema prisma/sorcery-schema.prisma", "prisma:generate:verification": "prisma generate --schema prisma/verification-schema.prisma", "prisma:generate:identity": "prisma generate --schema prisma/identity-schema.prisma", "prisma:generate:compliance": "prisma generate --schema prisma/compliance-schema.prisma", "prisma:format": "pnpm prisma:db:format:platform && pnpm prisma:db:format:payment && pnpm prisma:db:format:scheduler && pnpm prisma:db:format:document && && pnpm prisma:db:format:verification && pnpm prisma:db:format:config && pnpm prisma:db:format:sorcery && pnpm prisma:db:format:identity", "prisma:db:format:platform": "prisma-case-format --map-table-case snake --map-field-case snake --field-case camel,singular --pluralize --file prisma/platform-schema.prisma", "prisma:db:format:payment": "prisma-case-format --map-table-case snake --map-field-case snake --field-case camel,singular --pluralize --file prisma/payment-schema.prisma", "prisma:db:format:scheduler": "prisma-case-format --map-table-case snake --map-field-case snake --field-case camel,singular --pluralize --file prisma/scheduler-schema.prisma", "prisma:db:format:document": "prisma-case-format --map-table-case snake --map-field-case snake --field-case camel,singular --pluralize --file prisma/document-schema.prisma", "prisma:db:format:verification": "prisma-case-format --map-table-case snake --map-field-case snake --field-case camel,singular --pluralize --file prisma/verification-schema.prisma", "prisma:db:format:config": "prisma-case-format --map-table-case snake --map-field-case snake --field-case camel,singular --pluralize --file prisma/config-schema.prisma", "prisma:db:format:sorcery": "prisma-case-format --map-table-case snake --map-field-case snake --field-case camel,singular --pluralize --file prisma/sorcery-schema.prisma", "prisma:db:format:identity": "prisma-case-format --map-table-case snake --map-field-case snake --field-case camel,singular --pluralize --file prisma/identity-schema.prisma", "prisma:db:format:compliance": "prisma-case-format --map-table-case snake --map-field-case snake --field-case camel,singular --pluralize --file prisma/compliance-schema.prisma", "prisma:setup": "pnpm prisma:migrate && pnpm prisma:dbpull && pnpm prisma:generate && pnpm prisma:format", "biome:check": "biome check .", "biome:format": "biome format .", "lint": "biome lint .", "refine": "refine", "test": "jest --watch", "test:coverage": "jest --coverage --ci --maxWorkers=50%", "test:watch": "jest --maxWorkers=25% --watch"}, "engines": {"node": ">=18.17.0", "npm": ">=8.19.2"}, "packageManager": "pnpm@10.4.1", "dependencies": {"@emotion/react": "^11.8.2", "@emotion/styled": "^11.8.1", "@fontsource/roboto": "^5.0.13", "@google-cloud/storage": "^7.13.0", "@graphile-contrib/pg-simplify-inflector": "^6.1.0", "@graphile/pg-aggregates": "^0.1.1", "@grpc/grpc-js": "^1.11.2", "@grpc/proto-loader": "^0.7.13", "@jsonforms/core": "^3.2.1", "@jsonforms/material-renderers": "^3.2.1", "@jsonforms/react": "^3.2.1", "@measured/puck": "^0.18.2", "@mui/icons-material": "6.0.0", "@mui/material": "6.4.9", "@mui/x-data-grid": "^6.18.3", "@mui/x-date-pickers": "^6.18.2", "@prisma/client": "^5.13.0", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/themes": "^3.2.1", "@refinedev/cli": "2.16.27", "@refinedev/core": "^4.46.2", "@refinedev/devtools": "^1.1.35", "@refinedev/inferencer": "4.5.17", "@refinedev/kbar": "latest", "@refinedev/mui": "6.0.0", "@refinedev/nextjs-router": "5.5.5", "@refinedev/react-hook-form": "4.8.15", "@sentry/browser": "^9.10.1", "@sentry/nextjs": "latest", "@tanstack/react-query": "^4.10.1", "archiver": "^6.0.1", "archiver-zip-encrypted": "^1.0.11", "axios": "1.7.4", "camelcase": "^6.2.0", "csv": "^6.3.9", "csv-parse": "^5.6.0", "csv-stringify": "^6.5.2", "date-fns": "^2.30.0", "dayjs": "1.11.10", "flat": "^6.0.1", "form-data": "^4.0.0", "google-auth-library": "^9.4.1", "gql-query-builder": "^3.8.0", "graphql-request": "^6.1.0", "graphql-ws": "^5.16.2", "handlebars": "^4.7.8", "http-status-codes": "^2.3.0", "humanparser": "^2.7.0", "imagekit": "^4.1.4", "imagekitio-react": "^3.0.0", "jose": "^5.2.3", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "libphonenumber-js": "^1.10.41", "lodash": "^4.17.21", "mnemonist": "^0.39.8", "next": "15.2.3", "node-cache": "^5.1.2", "nookies": "^2.5.2", "object-sizeof": "^2.6.4", "pg": "^8.11.1", "pino": "^9.1.0", "pino-cloud-logging": "^1.0.6", "pino-http": "^10.1.0", "pino-pretty": "^11.0.0", "pluralize": "^8.0.0", "postgraphile": "^4.13.0", "postgraphile-plugin-connection-filter": "^2.3.0", "postgraphile-plugin-nested-mutations": "^1.1.0", "posthog-js": "^1.234.1", "query-string": "9.0.0", "react": "^18.2.0", "react-color": "^2.19.3", "react-diff-viewer-continued": "^3.4.0", "react-dom": "^18.2.0", "react-hook-form": "^7.30.0", "react-markdown": "^9.0.0", "temp": "^0.9.4", "uuid": "^9.0.0", "validator": "^13.11.0", "vanilla-jsoneditor": "^2.3.3", "zod": "^3.24.2"}, "devDependencies": {"@biomejs/biome": "1.6.4", "@jest/globals": "^29.7.0", "@pulumi/gcp": "^7.2.1", "@pulumi/pulumi": "^3.76.0", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^16.0.0", "@types/archiver": "^6", "@types/jsonwebtoken": "^9", "@types/lodash": "^4.17.15", "@types/node": "^20.11.28", "@types/react": "^18.2.21", "@types/react-color": "^3.0.11", "@types/react-dom": "^18.2.18", "@types/swagger-ui-react": "^4.18.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.5.0", "jest-mock-extended": "^3.0.7", "lefthook": "^1.4.8", "prisma": "^5.13.0", "prisma-case-format": "^2.2.1", "ts-jest": "^29.1.3", "typescript": "5.2.2"}, "resolutions": {"dayjs": "1.11.10"}, "refine": {"projectId": "nmpZzg-qsVzje-oNAmQg"}}