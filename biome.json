{"$schema": "https://biomejs.dev/schemas/1.6.4/schema.json", "files": {"ignore": ["**/build/*", "**/.build/*", "./node_modules/*", "./.yarn/*", "./.next/*", "./prisma/generated/*", "./coverage/*"]}, "linter": {"enabled": true, "rules": {"recommended": true, "complexity": {"useSimplifiedLogicExpression": "off"}, "style": {"useImportType": "error"}}, "ignore": ["**/*.test.*"]}, "formatter": {"enabled": true, "formatWithErrors": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100}, "javascript": {"formatter": {"quoteStyle": "single", "trailingComma": "all"}}}