{"type": "Group", "elements": [{"type": "HorizontalLayout", "elements": [{"type": "Control", "scope": "#/properties/name"}, {"type": "Control", "scope": "#/properties/programId"}]}, {"type": "HorizontalLayout", "elements": [{"type": "Control", "scope": "#/properties/usioKey"}]}, {"type": "HorizontalLayout", "elements": [{"type": "Control", "scope": "#/properties/virtualDistributorId"}, {"type": "Control", "scope": "#/properties/physicalDistributorId"}]}, {"type": "HorizontalLayout", "elements": [{"type": "Control", "scope": "#/properties/skipACHTransfer"}]}]}