{"type": "object", "properties": {"authentication": {"type": "object", "properties": {"tokenLifetime": {"type": "object", "properties": {"access": {"type": "number", "minimum": 5, "maximum": 1440}, "email": {"type": "number", "minimum": 5, "maximum": 1440}, "refresh": {"type": "number", "minimum": 5, "maximum": 1440}}}}, "additionalProperties": false}, "identity": {"type": "object", "properties": {"advocate": {"type": "object", "properties": {"saml": {"type": "object", "properties": {"providerId": {"type": "string"}, "copy": {"type": "string"}, "seats": {"type": "number", "minimum": 1}}}, "email": {"type": "number", "minimum": 5, "maximum": 1440}, "refresh": {"type": "number", "minimum": 5, "maximum": 1440}}}}, "additionalProperties": false}, "applicantTypeRoles": {"type": "object", "properties": {"FIRST_PARTY": {"type": "object", "properties": {"description": {"type": "string"}}}, "THIRD_PARTY": {"type": "object", "properties": {"description": {"type": "string"}}}}}}}