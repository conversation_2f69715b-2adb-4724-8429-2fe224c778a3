{"type": "object", "properties": {"anonymousSignInEnabled": {"$ref": "#/definitions/anonymousSignInEnabled"}, "emailPrivacyConfig": {"$ref": "#/definitions/emailPrivacyConfig"}, "emailSignInConfig": {"$ref": "#/definitions/emailSignInConfig"}, "multiFactorConfig": {"$ref": "#/definitions/multiFactorConfig"}, "passwordPolicyConfig": {"$ref": "#/definitions/passwordPolicyConfig"}, "recaptchaConfig": {"$ref": "#/definitions/recaptchaConfig"}}, "definitions": {"anonymousSignInEnabled": {"type": "boolean"}, "emailPrivacyConfig": {"type": "object", "properties": {"enableImprovedEmailPrivacy": {"type": "boolean"}}}, "emailSignInConfig": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "passwordRequired": {"type": "boolean"}}}, "multiFactorConfig": {"type": "object", "properties": {"state": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "factorIds": {"type": "array", "items": {"type": "string"}}, "providerConfigs": {"type": "array", "items": {"type": "object", "properties": {"state": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "totpProviderConfig": {"type": "object", "properties": {"adjacentIntervals": {"type": "number"}}}}}}}}, "passwordPolicyConfig": {"type": "object", "properties": {"forceUpgradeOnSignin": {"type": "boolean"}, "enforcementState": {"type": "string", "enum": ["ENFORCE", "OFF"]}, "constraints": {"type": "object", "properties": {"maxLength": {"type": "number"}, "minLength": {"type": "number"}, "requireLowercase": {"type": "boolean"}, "requireUppercase": {"type": "boolean"}, "requireNonAlphanumeric": {"type": "boolean"}, "requireNumeric": {"type": "boolean"}}}}}, "recaptchaConfig": {"type": "object", "properties": {"useAccountDefender": {"type": "boolean"}, "emailPasswordEnforcementState": {"type": "string", "enum": ["OFF", "AUDIT", "ENFORCE"]}}}}}