{"type": "VerticalLayout", "elements": [{"type": "Control", "scope": "#/properties/anonymousSignInEnabled"}, {"type": "Group", "label": "Email Privacy Config", "elements": [{"type": "HorizontalLayout", "elements": [{"type": "Control", "scope": "#/properties/emailPrivacyConfig/properties/enableImprovedEmailPrivacy"}]}]}, {"type": "Group", "label": "Email Sign In Config", "elements": [{"type": "HorizontalLayout", "elements": [{"type": "Control", "scope": "#/properties/emailSignInConfig/properties/enabled"}, {"type": "Control", "scope": "#/properties/emailSignInConfig/properties/passwordRequired"}]}]}, {"type": "Group", "label": "Multi Factor Config", "elements": [{"type": "Control", "scope": "#/properties/multiFactorConfig/properties/state"}, {"type": "Control", "scope": "#/properties/multiFactorConfig/properties/factorIds"}, {"type": "Control", "scope": "#/properties/multiFactorConfig/properties/providerConfigs", "options": {"detail": {"type": "VerticalLayout", "showElementLabel": false, "elements": [{"type": "Control", "scope": "#/properties/state"}, {"type": "Control", "scope": "#/properties/totpProviderConfig"}]}}}]}, {"type": "Group", "label": "Password Policy Config", "elements": [{"type": "HorizontalLayout", "elements": [{"type": "Control", "scope": "#/properties/passwordPolicyConfig/properties/forceUpgradeOnSignin"}, {"type": "Control", "scope": "#/properties/passwordPolicyConfig/properties/enforcementState"}]}, {"type": "HorizontalLayout", "elements": [{"type": "Control", "scope": "#/properties/passwordPolicyConfig/properties/constraints/properties/minLength"}, {"type": "Control", "scope": "#/properties/passwordPolicyConfig/properties/constraints/properties/maxLength"}]}, {"type": "HorizontalLayout", "elements": [{"type": "Control", "scope": "#/properties/passwordPolicyConfig/properties/constraints/properties/requireLowercase"}, {"type": "Control", "scope": "#/properties/passwordPolicyConfig/properties/constraints/properties/requireUppercase"}]}, {"type": "HorizontalLayout", "elements": [{"type": "Control", "scope": "#/properties/passwordPolicyConfig/properties/constraints/properties/requireNonAlphanumeric"}, {"type": "Control", "scope": "#/properties/passwordPolicyConfig/properties/constraints/properties/requireNumeric"}]}]}, {"type": "Group", "label": "<PERSON><PERSON><PERSON><PERSON>", "elements": [{"type": "HorizontalLayout", "elements": [{"type": "Control", "scope": "#/properties/recaptchaConfig/properties/useAccountDefender"}, {"type": "Control", "scope": "#/properties/recaptchaConfig/properties/emailPasswordEnforcementState"}]}]}]}