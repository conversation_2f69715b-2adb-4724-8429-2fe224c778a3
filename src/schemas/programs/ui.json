{"type": "Group", "elements": [{"type": "HorizontalLayout", "elements": [{"type": "Control", "label": "Max Funding Amount", "scope": "#/properties/maxFundingAmount"}, {"type": "Control", "label": "Sort Order", "scope": "#/properties/sortOrder"}]}, {"type": "Group", "label": "Mailing Address Override", "elements": [{"type": "VerticalLayout", "elements": [{"type": "Control", "scope": "#/properties/mailingAddressOverride/properties/addressLine1"}, {"type": "Control", "scope": "#/properties/mailingAddressOverride/properties/addressLine2"}, {"type": "Control", "scope": "#/properties/mailingAddressOverride/properties/city"}, {"type": "Control", "scope": "#/properties/mailingAddressOverride/properties/state"}, {"type": "Control", "scope": "#/properties/mailingAddressOverride/properties/zip"}]}]}, {"type": "Group", "label": "Recurring Payments", "elements": [{"type": "HorizontalLayout", "elements": [{"type": "Control", "scope": "#/properties/payments/properties/recurring/properties/amount"}, {"type": "Control", "scope": "#/properties/payments/properties/recurring/properties/pattern"}, {"type": "Control", "scope": "#/properties/payments/properties/recurring/properties/count"}, {"type": "Control", "scope": "#/properties/payments/properties/recurring/properties/totalAmount"}, {"type": "Control", "scope": "#/properties/payments/properties/recurring/properties/start"}]}]}, {"type": "Control", "scope": "#/properties/reapplicationRules"}]}