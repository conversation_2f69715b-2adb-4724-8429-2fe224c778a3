{"type": "object", "properties": {"payments": {"type": "object", "properties": {"recurring": {"type": "object", "properties": {"amount": {"type": "number"}, "pattern": {"type": "string", "enum": ["oneTime", "weekly", "biWeekly", "semiMonthly", "monthly"]}, "count": {"type": "string"}, "totalAmount": {"type": "number"}, "start": {"type": "string", "format": "date-time"}}}}}, "mailingAddressOverride": {"type": "object", "properties": {"addressLine1": {"type": "string"}, "addressLine2": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "zip": {"type": "string"}}}, "maxFundingAmount": {"type": "number", "description": "Please enter the amount *in cents*, e.g., $500 should be 50000"}, "reapplicationRules": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["TimeSinceDecision", "MaxNumberOfApplications"]}, "value": {"type": "number"}, "unit": {"type": "string", "enum": ["day", "week", "month", "year"]}}}}, "sortOrder": {"type": "number"}}}