{"type": "object", "properties": {"profileKeys": {"type": "array", "items": {"type": "object", "required": ["type", "key", "label"], "properties": {"type": {"type": "string", "enum": ["address", "calculated", "checkbox", "checkboxGroup", "complex", "date", "document", "dropdown", "dropdownMultiselect", "radioGroup", "radioList", "text"]}, "key": {"type": "string", "pattern": "^[a-zA-Z0-9]+(\\.[a-zA-Z0-9]+)*$"}, "label": {"type": "string"}}}}}}