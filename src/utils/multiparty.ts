export function isMultiPartyApplication(case_): boolean {
  return case_?.program?.programApplicantTypesList?.length > 1;
}

export function getApplicantType(application, program): string {
  const applicantType = application.submitter.applicantProfile.applicantType;
  const programType = program.programApplicantTypesList.find(
    ({ applicantTypeId }) => applicantTypeId === applicantType.id,
  );
  return programType?.nameOverride ?? applicantType.name;
}
