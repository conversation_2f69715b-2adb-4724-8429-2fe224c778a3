import { isDayjs } from 'dayjs';

export default function sort<T, Value>(
  arr: T[],
  accessor: (item: T) => Value,
  ascending = true,
  nullishLast = true,
): T[] {
  return [...arr].sort((item1, item2) => {
    const prop1 = accessor(item1);
    const prop2 = accessor(item2);

    let comparison: number;
    if (!prop1 || !prop2) {
      // biome-ignore lint/suspicious/noExplicitAny: used to allow toString
      comparison = ((prop1 as any) ?? '')
        .toString()
        // biome-ignore lint/suspicious/noExplicitAny: used to allow toString
        .localeCompare(((prop2 as any) ?? '').toString());
      if (nullishLast) comparison = -comparison;
    } else if (typeof prop1 === 'string' && typeof prop2 === 'string') {
      comparison = prop1.localeCompare(prop2);
    } else if (isDayjs(prop1) && isDayjs(prop2)) {
      comparison = prop1.unix() - prop2.unix();
    } else if (typeof prop1 === 'number' && typeof prop2 === 'number') {
      comparison = prop1 - prop2;
    } else {
      // biome-ignore lint/suspicious/noExplicitAny: used to allow toString
      comparison = (item1 as any).toString().localeCompare((item2 as any).toString());
    }
    return comparison * (ascending ? 1 : -1);
  });
}
