export function pick<T>(
  // biome-ignore lint/suspicious/noExplicitAny: it can really be anything
  input: Record<string, any>,
  fields: string[],
): Partial<T> | undefined {
  const newObject = Object.entries(input).reduce((a, [key, val]) => {
    if (fields.includes(key)) a[key as keyof T] = val;
    return a;
  }, {} as T);

  if (newObject && Object.keys(newObject).length > 0) return newObject;
  return undefined;
}

export function omit<T>(
  // biome-ignore lint/suspicious/noExplicitAny: it can really be anything
  input: Record<string, any>,
  fields: string[],
): Partial<T> | undefined {
  const newObject = Object.entries(input).reduce((a, [key, val]) => {
    if (!fields.includes(key)) a[key as keyof T] = val;
    return a;
  }, {} as T);

  if (newObject && Object.keys(newObject).length > 0) return newObject;
  return undefined;
}
