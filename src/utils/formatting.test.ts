import { describe, expect, it } from '@jest/globals';
import { getFirstName, getLastName } from './formatting';

describe('getFirstName', () => {
  it('splits the name and returns the first name', () => {
    expect(getFirstName('Polly Pocket')).toBe('Polly');
  });

  it('returns an empty string if there is no first/last', () => {
    expect(getFirstName('Ken')).toBe('');
  });

  it('can handle non american names', () => {
    expect(getFirstName('Der Yang')).toBe('Der');
    expect(getFirstName('<PERSON>')).toBe('Robin');
    expect(getFirstName('<PERSON>')).toBe('Alfredo');
    expect(getFirstName('Wenzhang Ma')).toBe('<PERSON>zhang');
  });
});

describe('getLastName', () => {
  it('splits the name and returns the last name', () => {
    expect(getLastName('<PERSON> Pocket')).toBe('Pocket');
  });

  it('returns the full name if there is no last name', () => {
    expect(getLastName('<PERSON>')).toBe('<PERSON>');
  });

  it('can handle non american names', () => {
    expect(getLastName('Der Yang')).toBe('Yang');
    expect(getLastName('Robin Van Persie')).toBe('Van Persie');
    expect(getLastName('<PERSON> Di St<PERSON>fano')).toBe('Di Stéfano');
    expect(getLastName('Wenzhang Ma')).toBe('Ma');
  });
});
