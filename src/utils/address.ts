import type { Addresses as Address } from '@prisma/clients/platform';

const states = {
  Alabama: 'AL',
  Alaska: 'AK',
  Arizona: 'AZ',
  Arkansas: 'AR',
  California: 'CA',
  Colorado: 'CO',
  Connecticut: 'CT',
  Delaware: 'DE',
  Florida: 'FL',
  Georgia: 'GA',
  Hawaii: 'HI',
  Idaho: 'ID',
  Illinois: 'IL',
  Indiana: 'IN',
  Iowa: 'IA',
  Kansas: 'KS',
  Kentucky: 'KY',
  Louisiana: 'LA',
  Maine: 'ME',
  Maryland: 'MD',
  Massachusetts: 'MA',
  Michigan: 'MI',
  Minnesota: 'MN',
  Mississippi: 'MS',
  Missouri: 'MO',
  Montana: 'MT',
  Nebraska: 'NE',
  Nevada: 'NV',
  'New Hampshire': 'NH',
  'New Jersey': 'NJ',
  'New Mexico': 'NM',
  'New York': 'NY',
  'North Carolina': 'NC',
  'North Dakota': 'ND',
  Ohio: 'OH',
  Oklahoma: 'OK',
  Oregon: 'OR',
  Pennsylvania: 'PA',
  'Rhode Island': 'RI',
  'South Carolina': 'SC',
  'South Dakota': 'SD',
  Tennessee: 'TN',
  Texas: 'TX',
  Utah: 'UT',
  Vermont: 'VT',
  Virginia: 'VA',
  Washington: 'WA',
  'West Virginia': 'WV',
  Wisconsin: 'WI',
  Wyoming: 'WY',
  'District of Columbia': 'DC',
  'American Samoa': 'AS',
  Guam: 'GU',
  'Northern Mariana Islands': 'MP',
  'Puerto Rico': 'PR',
  'Virgin Islands': 'VI',
  Alberta: 'AB',
  'British Columbia': 'BC',
  Manitoba: 'MB',
  'New Brunswick': 'NB',
  Newfoundland: 'NL',
  'Nova Scotia': 'NS',
  'Northwest Territories': 'NT',
  Nunavut: 'NU',
  Ontario: 'ON',
  'Prince Edward Island': 'PE',
  Quebec: 'QC',
  Saskatchewan: 'SK',
  Yukon: 'YT',
};
function isState(str: string) {
  return Object.entries(states).some(
    ([key, value]) => key.toLowerCase() === str.toLowerCase() || value === str.toUpperCase(),
  );
}

export function parseAddress(addressInput: string): Omit<Address, 'id'> {
  const addressObj = {
    addressLine1: undefined,
    city: undefined,
    state: undefined,
    zip: undefined,
  } as unknown as Omit<Address, 'id'>;
  let singleLineAddress = addressInput;

  if (typeof singleLineAddress !== 'string') {
    new Error('Input must be a String');
  }

  singleLineAddress = singleLineAddress.trim();

  const zipMatch: RegExpMatchArray | null = singleLineAddress.match(
    /([0-9]{5})|([a-z][0-9][a-z] ?[0-9][a-z][0-9])/gi,
  );
  if (zipMatch) {
    let zip: string | undefined = zipMatch.pop(); // pick match closest to end
    const indexOfZip = singleLineAddress.lastIndexOf(zip as string);
    if (indexOfZip === 0 && singleLineAddress.length > 10) {
      zip = undefined;
    }
    if (zip) {
      addressObj.zip = zip as string;
      const everythingAfterZip = singleLineAddress.substring(indexOfZip + zip.length);
      singleLineAddress = singleLineAddress.substring(0, indexOfZip) + everythingAfterZip;
    }
  }

  const addressParts = singleLineAddress.split(',');
  // Handle special cases...
  // Neighborhood, City, State
  if (addressParts.length === 3 && isState(addressParts[2].trim())) {
    addressObj.addressLine1 = addressParts[0].trim();
    addressObj.city = addressParts[1].trim();
    addressObj.state = addressParts[2].trim();
    return addressObj;
  }

  // Handle generic case...
  for (const addressPart of addressParts) {
    const part = addressPart.trim();
    // if has numbers, assume street address
    if (/[0-9]/.test(part)) {
      if (!addressObj.addressLine1) {
        addressObj.addressLine1 = part;
      } else if (!addressObj.addressLine2) {
        addressObj.addressLine2 = part;
      }
    }
    // if it is state
    else if (isState(part) && !addressObj.state) {
      addressObj.state = part;
    }
    // else assume city
    else if (!addressObj.city) addressObj.city = part;
  }

  return addressObj;
}

export function formatAddress(address: Partial<Address>): string {
  return address
    ? `${address.addressLine1 || ''}${address.addressLine2 ? ` ${address.addressLine2}` : ''}${
        address.addressLine1 ? ',' : ''
      } ${address.city}, ${address.state} ${address.zip}`
    : '';
}
