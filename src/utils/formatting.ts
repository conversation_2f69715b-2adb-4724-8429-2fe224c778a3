import human from 'humanparser';

const humanParserConfig = { ignoreSuffix: ['ma'] };

export function getFirstName(name: string): string {
  const parsedName = human.parseName(name, humanParserConfig);
  return parsedName?.lastName ? (parsedName?.firstName as string) : '';
}

export function getLastName(name: string): string {
  const parsedName = human.parseName(name, humanParserConfig);
  return (parsedName?.lastName as string) || parsedName.fullName;
}
