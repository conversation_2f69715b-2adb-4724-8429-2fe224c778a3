import { describe, expect, it } from '@jest/globals';
import { buildUrl } from './url';

describe('url', () => {
  describe('buildUrl', () => {
    it('should return a valid URL if only base is provided', () => {
      expect(buildUrl({ base: 'http://mock' }).toString()).toStrictEqual('http://mock/');
    });
    it('should return a valid URL if base and port are provided', () => {
      expect(buildUrl({ base: 'http://mock', port: '6789' }).toString()).toStrictEqual(
        'http://mock:6789/',
      );
    });
    it('should return a valid URL if base and path are provided', () => {
      expect(buildUrl({ base: 'http://mock', path: 'pathOne/pathTwo' }).toString()).toStrictEqual(
        'http://mock/pathOne/pathTwo',
      );
    });
    it('should return a valid URL if base, port, and path are provided', () => {
      expect(
        buildUrl({
          base: 'http://mock',
          port: '6789',
          path: 'pathOne/pathTwo/pathThree',
        }).toString(),
      ).toStrictEqual('http://mock:6789/pathOne/pathTwo/pathThree');
    });
    it('should override a port attached to the base URL if override is set', () => {
      expect(
        buildUrl({ base: 'http://mock.com:9091', port: '1000', overridePort: true }).toString(),
      ).toStrictEqual('http://mock.com:1000/');
    });
    it('should not override a port attached to the base URL by default', () => {
      expect(buildUrl({ base: 'http://mock.com:9091', port: '1000' }).toString()).toStrictEqual(
        'http://mock.com:9091/',
      );
    });
  });
  it('should throw if the provided base string is not a valid URL', () => {
    expect(() => buildUrl({ base: 'mock' })).toThrow();
  });
});
