import { getConfig } from '@utils/config';

const getLocation = () => {
  if (typeof window === 'undefined') return '';
  const host = window.location.host;
  if (host.includes('localhost')) return 'local';
  if (host.includes('admin.dev')) return 'development';
  if (host.includes('admin.bybeam')) return 'production';
};

const getEnvironment = () => {
  // This is a workaround until we improve the build and deploy process
  // to account for build-time client side environment variables.
  // First, check the node env if running server side. If that fails,
  // check the window location on the client, and as a last resort, send a
  // request to the server for the public config.
  const env = process?.env?.PUBLIC_ENVIRONMENT ?? getLocation() ?? getConfig('PUBLIC_ENVIRONMENT');
  switch ((env ?? '').toLowerCase()) {
    case 'production':
      return 'PROD';
    case 'development':
      return 'DEV';
    case 'local':
      return 'LOCAL';
    default:
      return 'UNKNOWN';
  }
};

const isProduction = () => getEnvironment() === 'PROD';

const isLocal = () => getEnvironment() === 'LOCAL';

export { getEnvironment, isProduction, isLocal };
