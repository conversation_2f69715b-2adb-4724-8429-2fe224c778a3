import { FieldLabel } from '@measured/puck';
import FormatAlignLeftIcon from '@mui/icons-material/FormatAlignLeft';
import HttpsIcon from '@mui/icons-material/Https';
import SettingsSuggestIcon from '@mui/icons-material/SettingsSuggest';
import TextFieldsIcon from '@mui/icons-material/TextFields';
import { Stack, TextField, Typography } from '@mui/material';
import { FaceIcon } from '@radix-ui/react-icons';
import { useDuplicateKeys } from 'providers/DuplicateKeyProvider';
import { useEffect, useState } from 'react';
import { PartnerReportingKeys } from 'types/appConfig';

export const RequiredTextField = ({ name, onChange, value, field }) => {
  const isEmpty = !value;
  return (
    <FieldLabel icon={<TextFieldsIcon />} label={`${field?.label}*`}>
      <TextField
        defaultValue={value}
        name={name}
        onChange={(e) => onChange(e.currentTarget.value)}
        sx={{ width: '100%' }}
        error={isEmpty}
        helperText={isEmpty && 'Required'}
      />
    </FieldLabel>
  );
};

export const DisplayNameTextField = ({ name, onChange, value, field }) => (
  <FieldLabel icon={<FaceIcon />} label={field?.label}>
    <TextField
      defaultValue={value}
      name={name}
      onChange={(e) => onChange(e.currentTarget.value)}
      sx={{ width: '100%' }}
      helperText={'For partner-portal only'}
    />
  </FieldLabel>
);

const PartnerKeysList = Object.values(PartnerReportingKeys);

export const KeyField = ({ name, onChange, value, field }) => {
  const keyRegex = /[^a-zA-Z\d]|\.|\s/;
  const camelCaseRegex = /^[a-z][a-z0-9]*([A-Z][a-zA-Z0-9]*)?$/;
  const [hasError, setHasError] = useState(false);
  const [isCamelCase, setIsCamelCase] = useState(true);
  const [isCustom, setCustom] = useState(value && !PartnerKeysList.includes(value));
  const [isKeyDuplicate, setIsKeyDuplicate] = useState(false);
  const { duplicates } = useDuplicateKeys();

  useEffect(() => {
    setHasError(keyRegex.test(value) || !value);
    setIsCamelCase(camelCaseRegex.test(value));
    setIsKeyDuplicate(!!duplicates[value]);
  }, [value, duplicates]);
  const getHelperText = () => {
    if (hasError) return 'Invalid Key';
    if (isKeyDuplicate) return 'This key is already in use';
    if (!isCamelCase) return 'Key must be in camelCase format';
    if (isCustom) return 'Custom keys will not be available in standard partner reporting';
  };

  return (
    <FieldLabel icon={<TextFieldsIcon />} label={`${field?.label}*`}>
      <TextField
        defaultValue={value}
        name={name}
        onChange={(e) => {
          if (e.currentTarget.value) {
            onChange(e.currentTarget.value);
            setCustom(!PartnerKeysList.includes(e.currentTarget.value as PartnerReportingKeys));
          }
        }}
        sx={{ width: '100%' }}
        error={hasError || !isCamelCase || isKeyDuplicate}
        helperText={getHelperText()}
      />
      {hasError && (
        <p>
          Rules for keys:
          <ul style={{ marginTop: 0 }}>
            <li>
              no underscores <code>_</code>
            </li>
            <li>no periods "."</li>
            <li>no spaces</li>
          </ul>
        </p>
      )}
    </FieldLabel>
  );
};

export const RequiredTypeField = ({ name, onChange, value, field }) => {
  const isEmpty = !value;
  return (
    <FieldLabel icon={<HttpsIcon />} label={`${field?.label}*`}>
      <TextField
        defaultValue={value}
        name={name}
        onChange={(e) => onChange(e.currentTarget.value)}
        sx={{ width: '100%' }}
        error={isEmpty}
        helperText={isEmpty && 'Required'}
        disabled
      />
    </FieldLabel>
  );
};

export const NonEmptyTextField = ({ name, onChange, value, field }) => {
  return (
    <FieldLabel icon={<TextFieldsIcon />} label={field?.label}>
      <TextField
        defaultValue={value}
        name={name}
        onChange={(e) => onChange(e.currentTarget.value ? e.currentTarget.value : undefined)}
        sx={{ width: '100%' }}
      />
    </FieldLabel>
  );
};

export const RequiredTextAreaField = ({ name, onChange, value, field }) => {
  const isEmpty = !value;
  return (
    <FieldLabel icon={<FormatAlignLeftIcon />} label={`${field?.label}*`}>
      <TextField
        defaultValue={value}
        name={name}
        onChange={(e) => onChange(e.currentTarget.value)}
        sx={{ width: '100%' }}
        error={isEmpty}
        multiline
        helperText={isEmpty && 'Required'}
      />
    </FieldLabel>
  );
};

export const NonEmptyTextAreaField = ({ name, onChange, value, field }) => {
  return (
    <FieldLabel icon={<FormatAlignLeftIcon />} label={field?.label}>
      <TextField
        defaultValue={value}
        name={name}
        onChange={(e) => onChange(e.currentTarget.value ? e.currentTarget.value : undefined)}
        multiline
        sx={{ width: '100%' }}
      />
    </FieldLabel>
  );
};

export const DynamicLogicIndicator = ({ visible }: { visible: boolean | undefined }) => {
  return visible ? (
    <Stack
      sx={{
        position: 'absolute',
        right: '3px',
        top: '3px',
        backgroundColor: 'white',
        zIndex: 10000,
      }}
      direction="row"
      alignItems="center"
    >
      <SettingsSuggestIcon color="primary" fontSize="medium" />
      <Typography marginLeft="4px" color="primary" fontSize="small">
        Dynamic Logic
      </Typography>
    </Stack>
  ) : null;
};
