import { flatten } from 'flat';
import {
  type AnswerExpression,
  type CompareExpression,
  CompareOperator,
  type ConditionExpression,
} from 'types/appConfig';
import parseAnswer from './parseAnswer';

// biome-ignore lint/suspicious/noExplicitAny: It can be any type
function answer(expression: AnswerExpression, answers: Record<string, any>) {
  if (expression.key === '.') return answers;
  // biome-ignore lint/suspicious/noExplicitAny: It can be any type
  const extracted = (flatten(answers, { safe: true }) as Record<string, any>)[expression.key];
  if (typeof extracted !== 'string') return extracted;
  return parseAnswer(expression.key, extracted);
}

// biome-ignore lint/suspicious/noExplicitAny: It can be any type
function compare(expression: CompareExpression, answers: Record<string, any>): boolean {
  const left = answer(expression.comparators[0], answers);
  const right = expression.comparators[1].value;
  switch (expression.op) {
    case CompareOperator.Eq:
      return left === right;
    case CompareOperator.Gt:
      return left > right;
    case CompareOperator.Gte:
      return left >= right;
    case CompareOperator.Lt:
      return left < right;
    case CompareOperator.Lte:
      return left <= right;
    case CompareOperator.In:
      return ((left as (string | number | boolean)[]) ?? []).includes(
        right as string | number | boolean,
      );
    default:
      return expression.op;
  }
}

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
export function condition(expression: ConditionExpression, answers: Record<string, any>) {
  const then = expression.conditions.find((c) => compare(c.if, answers))?.then ?? expression.else;
  if (then) return then.value;
}
