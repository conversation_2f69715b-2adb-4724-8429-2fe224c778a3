import isNum from '@utils/isNum';

const DO_NOT_PARSE = ['zip', 'zipCode', 'phone', 'phoneNumber', 'dob', 'dateOfBirth', 'studentId'];
const allowsParsing = (key: string) => !DO_NOT_PARSE.includes(key.split('.').slice(-1)[0]);

export default function parseAnswer(key: string, value: string): string | boolean | number {
  if (allowsParsing(key)) {
    if (['true', 'false'].includes(value)) return value === 'true';
    if (isNum(value)) return Number.parseFloat(value);
  }

  return value;
}
