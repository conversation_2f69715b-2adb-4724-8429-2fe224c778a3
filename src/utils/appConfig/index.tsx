import { type Config, type DefaultRootProps, DropZone } from '@measured/puck';
import type { ReactNode } from 'react';
import { EmployerAddressField } from './blocks/AddressField/EmployeerAddress';
import { GenericAddressField, type GenericAddressFieldProps } from './blocks/AddressField/Generic';
import { MailingAddressField } from './blocks/AddressField/MailingAddress';
import { ApplicationQuestion, type ApplicationQuestionProps } from './blocks/ApplicationQuestion';
import { CalculatedField, type CalculatedFieldProps } from './blocks/CalculatedField';
import { CheckboxField, type CheckboxFieldProps } from './blocks/CheckboxField';
import { CheckboxGroupField, type CheckboxGroupFieldProps } from './blocks/CheckboxGroupField';
import { ComplexField, type ComplexFieldProps } from './blocks/ComplexField';
import { DateOfBirthField } from './blocks/DateField/DateOfBirth';
import { GenericDateField, type GenericDateFieldProps } from './blocks/DateField/Generic';
import { GraduationDateField } from './blocks/DateField/GraduationDate';
import {
  GenericDocumentField,
  type GenericDocumentFieldProps,
} from './blocks/DocumentField/Generic';
import { IdentificationDocumentField } from './blocks/DocumentField/Identification';
import { EthnicityDropdownField } from './blocks/DropdownField/Ethnicty';
import { GenderDropdownField } from './blocks/DropdownField/Gender';
import {
  GenericDropdownField,
  type GenericDropdownFieldProps,
} from './blocks/DropdownField/Generic';
import { NumberOfDependentsField } from './blocks/Household/Dependents';
import { IncomeSourcesField } from './blocks/Household/IncomeSources';
import { HouseholdSizeField } from './blocks/Household/Size';
import { IntroPage, type IntroPageProps } from './blocks/IntroPage';
import { type ConfigOverridesProps, Overrides } from './blocks/Override';
import { QuestionGroup, type QuestionGroupProps } from './blocks/QuestionGroup';
import { RadioListField, type RadioListFieldProps } from './blocks/RadioListField';
import { Section, type SectionProps } from './blocks/Section';
import { ContactEmailField } from './blocks/Text/ContactEmail';
import { ContactPhoneNumberField } from './blocks/Text/ContactPhone';
import { GenericTextField, type GenericTextFieldProps } from './blocks/Text/Generic';
import { PhoneNumberField } from './blocks/Text/PhoneNumber';
import { SignatureField } from './blocks/Text/Signature';
import { SocialSecurityNumberField } from './blocks/Text/SocialSecurityNumber';
import { StudentIdField } from './blocks/Text/StudentId';
import { TypographyComponent, type TypographyComponentProps } from './blocks/TypographyComponent';

interface ApplicationConfig {
  introPagesContainer: { pages: IntroPageProps[] };
  overridesContainer: { overrides: ConfigOverridesProps[] };
  sectionsContainer: { sections: SectionProps[] };
}

export type ContentConfig = {
  GenericAddressField: GenericAddressFieldProps;
  MailingAddressField: GenericAddressFieldProps;
  EmployerAddressField: GenericAddressFieldProps;
  ApplicationQuestion: ApplicationQuestionProps;
  CalculatedField: CalculatedFieldProps;
  CheckboxField: CheckboxFieldProps;
  CheckboxGroupField: CheckboxGroupFieldProps;
  ComplexField: ComplexFieldProps;
  GenericDateField: GenericDateFieldProps;
  DateOfBirthField: GenericDateFieldProps;
  GraduationDateField: GenericDateFieldProps;
  GenericDocumentField: GenericDocumentFieldProps;
  IdentificationDocumentField: GenericDocumentFieldProps;
  GenericDropdownField: GenericDropdownFieldProps;
  EthnicityDropdownField: GenericDropdownFieldProps;
  GenderDropdownField: GenericDropdownFieldProps;
  IntroPage: IntroPageProps;
  Overrides: ConfigOverridesProps;
  QuestionGroup: QuestionGroupProps;
  RadioListField: RadioListFieldProps;
  Section: SectionProps;
  GenericTextField: GenericTextFieldProps;
  PhoneNumberField: GenericTextFieldProps;
  SocialSecurityNumberField: GenericTextFieldProps;
  ContactEmailField: GenericTextFieldProps;
  ContactPhoneNumberField: GenericTextFieldProps;
  SignatureField: GenericTextFieldProps;
  StudentIdField: GenericTextFieldProps;
  TypographyComponent: TypographyComponentProps;
  IncomeSourcesField: GenericDropdownFieldProps;
  NumberOfDependentsField: GenericTextFieldProps;
  HouseholdSizeField: GenericTextFieldProps;
} & ApplicationConfig;

type RootProps = {
  children: ReactNode;
} & DefaultRootProps;

function Root({ children }: RootProps) {
  return (
    <div id="app-config-editor" style={{ padding: '3rem' }}>
      {children}
    </div>
  );
}

export const defaultAppConfig = {
  introPages: [],
  sections: [],
  overrides: {},
  participants: [],
};

export const appConfig: Config<ContentConfig, RootProps> = {
  root: {
    render: Root,
  },
  categories: {
    textFields: {
      components: [
        'PhoneNumberField',
        'SocialSecurityNumberField',
        'ContactEmailField',
        'ContactPhoneNumberField',
        'SignatureField',
        'StudentIdField',
        'TypographyComponent',
        'GenericTextField',
      ],
      title: 'Text Fields',
    },
    dateFields: {
      components: ['DateOfBirthField', 'GraduationDateField', 'GenericDateField'],
      title: 'Date Fields',
    },
    addressFields: {
      components: ['MailingAddressField', 'EmployerAddressField', 'GenericAddressField'],
      title: 'Address Fields',
    },
    dropdownFields: {
      components: ['EthnicityDropdownField', 'GenderDropdownField', 'GenericDropdownField'],
      title: 'Dropdown Fields',
    },
    documentFields: {
      components: ['IdentificationDocumentField', 'GenericDocumentField'],
      title: 'Document Fields',
    },
    householdFields: {
      components: ['IncomeSourcesField', 'NumberOfDependentsField', 'HouseholdSizeField'],
      title: 'Household Fields',
    },
    otherFields: {
      components: [
        'CalculatedField',
        'CheckboxField',
        'CheckboxGroupField',
        'ComplexField',
        'RadioListField',
        'TypographyComponent',
      ],
      title: 'Other Fields',
    },
    containers: {
      components: [
        'Section',
        'QuestionGroup',
        'ApplicationQuestion',
        'sectionsContainer',
        'introPagesContainer',
        'overridesContainer',
      ],
      title: 'Structure',
      defaultExpanded: false,
    },
    pages: {
      components: ['IntroPage', 'Overrides'],
      title: 'Extras',
      defaultExpanded: false,
    },
  },
  components: {
    introPagesContainer: {
      render: () => (
        <div>
          <DropZone zone="Intro-Pages" />
        </div>
      ),
    },
    sectionsContainer: {
      render: () => (
        <div style={{ border: 'dotted 2px black', paddingBottom: '2rem' }}>
          <DropZone zone="Sections" />
        </div>
      ),
    },
    overridesContainer: {
      render: () => (
        <div>
          <DropZone zone="Overrides" />
        </div>
      ),
    },
    GenericAddressField,
    EmployerAddressField,
    MailingAddressField,
    ApplicationQuestion,
    CalculatedField,
    CheckboxField,
    CheckboxGroupField,
    ComplexField,
    GenericDateField,
    DateOfBirthField,
    GraduationDateField,
    GenericDocumentField,
    IdentificationDocumentField,
    GenericDropdownField,
    EthnicityDropdownField,
    GenderDropdownField,
    IntroPage,
    Overrides,
    QuestionGroup,
    RadioListField,
    Section,
    GenericTextField,
    PhoneNumberField,
    StudentIdField,
    ContactPhoneNumberField,
    ContactEmailField,
    SignatureField,
    SocialSecurityNumberField,
    IncomeSourcesField,
    NumberOfDependentsField,
    HouseholdSizeField,
    TypographyComponent,
  },
};

export const ComponentNameLookup: Record<keyof ContentConfig, string> = {
  ApplicationQuestion: 'Question',
  CalculatedField: 'Calculated',
  CheckboxField: 'Checkbox',
  CheckboxGroupField: 'Checkbox Group',
  ComplexField: 'Complex',
  GenericDocumentField: 'Document',
  IdentificationDocumentField: 'Identification',
  IntroPage: 'Intro Page',
  Overrides: 'Override',
  QuestionGroup: 'Question Group',
  RadioListField: 'Radio List',
  Section: 'Section',
  TypographyComponent: 'Typography',
  sectionsContainer: 'Section Container',
  introPagesContainer: 'Intro Page Container',
  overridesContainer: 'Override Container',
  // Text
  GenericTextField: 'Custom Text',
  PhoneNumberField: 'Phone',
  SocialSecurityNumberField: 'Social Security Number',
  ContactPhoneNumberField: 'Contact Phone',
  ContactEmailField: 'Contact Email',
  StudentIdField: 'Student ID',
  SignatureField: 'Signature',
  // Dates
  GenericDateField: 'Custom Date',
  DateOfBirthField: 'Date Of Birth',
  GraduationDateField: 'Graduation Date',
  // Address
  GenericAddressField: 'Address',
  MailingAddressField: 'Mailing Address',
  EmployerAddressField: 'Employer Address',
  // Dropdown
  GenericDropdownField: 'Dropdown',
  EthnicityDropdownField: 'Race/Ethnicity',
  GenderDropdownField: 'Gender',
  // Household
  IncomeSourcesField: 'Income Sources',
  NumberOfDependentsField: 'Number of Dependents',
  HouseholdSizeField: 'Household Size',
};
