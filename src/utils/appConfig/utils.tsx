import DatePicker from '@components/forms/DatePicker';
import type { Field } from '@measured/puck';
import { FormControl, InputLabel, MenuItem, Select } from '@mui/material';
import dayjs from 'dayjs';
import { flatten } from 'flat';
import { cloneDeep, get, get as getByStringPath, isNumber, set, unset } from 'lodash';
import {
  BoolOperator,
  type CompareExpression,
  CompareOperator,
  type CourseQuestion,
  DeepDynamicLogicValues,
  DynamicLogicBaseValues,
  type Expression,
  ExpressionType,
  type FieldValidation,
  FieldValidationValues,
  LOGIC_TYPES,
  ListOperator,
  type LiteralExpression,
} from 'types/appConfig';

export function getResolvedDynamicLogicValues(props) {
  const copyOfProps = structuredClone(props);
  const flattenedProps = flatten(copyOfProps, { safe: true }) as Record<string, unknown>;
  const dynamicLogicKeys = Object.keys(flattenedProps).filter((key) =>
    key.includes('dynamicLogic'),
  );
  if (dynamicLogicKeys.some((key) => flattenedProps[key] === LOGIC_TYPES.NONE)) {
    return { ...copyOfProps, dynamicLogic: undefined };
  }
  return copyOfProps;
}

const CustomDatePicker = ({ onChange, value, label }) => {
  return (
    <DatePicker
      sx={{ height: '50px' }}
      value={value}
      label={label}
      onChange={(e) => onChange(dayjs(e)?.isValid() ? dayjs(e)?.format('MM/DD/YYYY') : undefined)}
      disablePast={false}
      slotProps={{
        field: { clearable: true, onClear: () => onChange(undefined) },
      }}
    />
  );
};

// biome-ignore lint/suspicious/noExplicitAny: need any here for puck
export const DateFieldValidation: { [x: string]: Field<any> } = {
  allowedRange: {
    type: 'custom',
    render: ({ onChange, value }) => (
      <FormControl fullWidth>
        <InputLabel>Allowed Range</InputLabel>
        <Select
          labelId="demo-simple-select-label"
          id="demo-simple-select"
          value={value}
          label="Age"
          onChange={(e) => onChange(e.target.value)}
        >
          <MenuItem>any</MenuItem>
          <MenuItem value={'today'}>today</MenuItem>
          <MenuItem value={'past'}>past</MenuItem>
          <MenuItem value={'future'}>future</MenuItem>
        </Select>
      </FormControl>
    ),
  },
  minimumDate: {
    type: 'custom',
    render: ({ onChange, value }) => (
      <CustomDatePicker {...{ onChange, value, label: 'minimum date' }} />
    ),
  },
  maximumDate: {
    type: 'custom',
    render: ({ onChange, value }) => (
      <CustomDatePicker {...{ onChange, value, label: 'maximum date' }} />
    ),
  },
};

export function getReadonlyExpressionProps(props, validationKey = 'dynamicLogic') {
  const accessor = getByStringPath(props, validationKey, {});
  const { type = '', op, format } = accessor || {};
  const defaultReadonly = {
    [`${validationKey}.comparators`]: true,
    [`${validationKey}.conditions`]: true,
    [`${validationKey}.else`]: true,
    [`${validationKey}.expressions`]: true,
    [`${validationKey}.format`]: true,
    [`${validationKey}.key`]: true,
    [`${validationKey}.list`]: true,
    [`${validationKey}.mapper`]: true,
    [`${validationKey}.op`]: true,
    [`${validationKey}.pattern`]: true,
    [`${validationKey}.type`]: false,
    [`${validationKey}.valueAsExpression`]: true,
    [`${validationKey}.valueAsText`]: true,
  };

  switch (type) {
    case 'answer':
      return { ...defaultReadonly, [`${validationKey}.key`]: false };
    case 'boolean':
      return {
        ...defaultReadonly,
        [`${validationKey}.op`]: false,
        ...(op === 'not'
          ? { [`${validationKey}.expression`]: false }
          : { [`${validationKey}.expressions`]: false }),
      };
    case 'compare':
      return {
        ...defaultReadonly,
        [`${validationKey}.op`]: false,
        [`${validationKey}.comparators`]: false,
      };
    case 'condition':
      return {
        ...defaultReadonly,
        [`${validationKey}.conditions`]: false,
        [`${validationKey}.else`]: false,
      };
    case 'format':
      return {
        ...defaultReadonly,
        [`${validationKey}.format`]: false,
        [`${validationKey}.value`]: false,
        ...(format === 'pattern' ? { [`${validationKey}.pattern`]: false } : {}),
      };
    case 'list':
      return {
        ...defaultReadonly,
        [`${validationKey}.op`]: false,
        ...(['length', 'sum'].includes(op) ? { [`${validationKey}.list`]: false } : {}),
        ...(op === 'map'
          ? {
              [`${validationKey}.list`]: false,
              [`${validationKey}.mapper`]: false,
            }
          : {}),
      };
    case 'literal':
      return {
        ...defaultReadonly,
        [`${validationKey}.valueAsText`]: false,
      };
    case 'math':
      return {
        ...defaultReadonly,
        [`${validationKey}.op`]: false,
        [`${validationKey}.expressions`]: false,
      };
    default:
      return defaultReadonly;
  }
}

export function getDeepValueKeys(obj) {
  let keys: string[] = [];
  for (const key in obj) {
    if (typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
      const subKeys = getDeepValueKeys(obj[key]);
      keys = keys.concat(subKeys.map((subKey) => `${key}.${subKey}`));
    } else if (Array.isArray(obj[key])) {
      for (let i = 0; i < obj[key].length; i++) {
        const subKeys = getDeepValueKeys(obj[key][i]);
        keys = keys.concat(subKeys.map((subKey) => `${key}[${i}].${subKey}`));
      }
    } else {
      keys.push(key);
    }
  }
  return keys;
}

export function getQuestion(config, key: string) {
  return config.sections
    .flatMap(({ questionGroups }) => questionGroups.flatMap(({ questions }) => questions))
    .find(({ key: qKey }) => qKey === key);
}

export function getField(config, key: string) {
  return config.sections
    .flatMap(({ questionGroups }) =>
      questionGroups.flatMap(({ questions }) => questions.flatMap(({ fields }) => fields)),
    )
    .find(({ key: fieldKey }) => fieldKey === key);
}

export function getCoursesByKey(config, key: string) {
  const question = getQuestion(config, key);
  if (!question) return undefined;

  const courseQuestion = question.fields.find(
    ({ key: fieldKey }) => fieldKey === `${question.key}.courses`,
  );
  const courses = courseQuestion.subFields.find(({ key }) => key === 'name')?.options;
  const formula = courseQuestion.subFields.find(({ key }) => key === 'creditHours')?.formula;
  return { key, courses, formula } as CourseQuestion;
}

const durationToDecimal = (duration: string): number => {
  const [hours, minutes] = duration.split(':').map(Number);
  const decimal = hours + minutes / 60;
  return Number.parseFloat(decimal.toFixed(2)); // Rounding to two decimal places
};

export const decimalToDuration = (decimal: number): string => {
  const minutes = Math.round(Number.parseFloat(((decimal % 1) * 60).toFixed(2)));
  const hours = Math.floor(decimal);

  return `${hours}:${minutes < 10 ? `0${minutes}` : minutes}`;
};

export function getCourseCondition(
  name: string,
  duration: string,
): {
  if: CompareExpression;
  then: LiteralExpression;
} {
  return {
    if: {
      comparators: [
        { key: 'name', type: ExpressionType.Answer },
        { type: ExpressionType.Literal, value: name },
      ],
      op: CompareOperator.Eq,
      type: ExpressionType.Compare,
    },
    // biome-ignore lint/suspicious/noThenProperty: <explanation>
    then: { type: ExpressionType.Literal, value: durationToDecimal(duration) },
  };
}

export const makeCourseConfig = (existingConfig, questionKey, courses, formula) => {
  const CONFIG = { ...existingConfig };
  // find incentive levels
  const courseSection = CONFIG.sections.find(({ questionGroups }) =>
    questionGroups.some(({ questions }) => questions.some(({ key: qKey }) => qKey === questionKey)),
  );
  const questionGroup = courseSection.questionGroups.find(({ questions }) =>
    questions.some(({ key: qKey }) => qKey === questionKey),
  );
  const question = questionGroup.questions.find(({ key: qKey }) => qKey === questionKey);
  const courseIdx = question.fields.findIndex(({ key: qKey }) => qKey === `${questionKey}.courses`);

  const courseField = question.fields[courseIdx];
  const courseSubFieldIdx = courseField.subFields.findIndex(({ key }) => key === 'name');
  const hoursSubFieldIdx = courseField.subFields.findIndex(({ key }) => key === 'creditHours');

  question.fields[courseIdx].subFields[courseSubFieldIdx] = {
    ...courseField.subFields[courseSubFieldIdx],
    options: courses,
  };
  question.fields[courseIdx].subFields[hoursSubFieldIdx] = {
    ...courseField.subFields[hoursSubFieldIdx],
    formula: formula,
  };

  return CONFIG;
};

export function intersect<T>(a: T[], b: T[]): T[] {
  const setA = new Set(a);
  const setB = new Set(b);
  const intersection = new Set([...setA].filter((x) => setB.has(x)));
  return Array.from(intersection);
}

export interface OrderedSection {
  props: {
    [key: string]: unknown;
    order: number;
  };
  [key: string]: unknown;
}
export function mergeOrderedArrays<T extends OrderedSection>(
  left: T[],
  right: T[] | undefined,
): T[] {
  if (!right) return left;
  const orderMap = new Map<number, T>();

  for (const item of left) {
    isNumber(item?.props?.order) && orderMap.set(item?.props?.order, item);
  }

  for (const item of right) {
    isNumber(item?.props?.order) && orderMap.set(item?.props?.order, item);
  }
  return Array.from(orderMap.values()).sort((a, b) => a.props.order - b.props.order);
}

export const resetDynamicLogicValueTypes = (props: Record<string, unknown>) => {
  const result = cloneDeep(props);
  const resultKeys = getDeepValueKeys(result);
  for (const key of resultKeys) {
    const newKey = key?.split('.')?.slice(0, -1)?.join('.');
    if (key.includes('valueAsText') || key.includes('valueAsExpression')) {
      set(result, `${newKey}.value`, get(result, key));
      // remove original value from object
      unset(result, key);
    }
  }
  return result;
};

export const removeKeys = (
  target: Record<string, unknown>,
  keys: string[],
): Record<string, unknown> => {
  const result = cloneDeep(target);
  for (const key of keys) {
    unset(result, key);
  }
  return result;
};
const booleanOps = {
  type: 'select',
  label: 'Operator',
  options: [
    { label: '', value: '' },
    { label: 'And', value: 'and' },
    { label: 'Or', value: 'or' },
    { label: 'Not', value: 'not' },
  ],
};
const compareOps = {
  type: 'select',
  label: 'Operator',
  options: [
    { label: '', value: '' },
    { label: 'Eq', value: 'eq' },
    { label: 'Gt', value: 'gt' },
    { label: 'Gte', value: 'gte' },
    { label: 'In', value: 'in' },
    { label: 'Lt', value: 'lt' },
    { label: 'Lte', value: 'lte' },
  ],
};
const listOps = {
  type: 'select',
  label: 'Operator',
  options: [
    { label: '', value: '' },
    { label: 'Length', value: 'length' },
    { label: 'Map', value: 'map' },
    { label: 'Sum', value: 'sum' },
  ],
};
const mathOps = {
  type: 'select',
  label: 'Operator',
  options: [
    { label: '', value: '' },
    { label: 'Add', value: 'add' },
    { label: 'Divide', value: 'divide' },
    { label: 'Multiply', value: 'multiply' },
    { label: 'Subtract', value: 'subtract' },
  ],
};
export const resolveDynamicLogicObjectFields = (
  currentDynamicLogic: Expression,
): Record<string, unknown> => {
  const { type, op } = currentDynamicLogic;

  const baseReturn = { type: DynamicLogicBaseValues.type };
  switch (type) {
    case LOGIC_TYPES.ANSWER:
      return { ...baseReturn, key: DeepDynamicLogicValues.key };
    case LOGIC_TYPES.BOOLEAN: {
      switch (op) {
        case BoolOperator.And:
        case BoolOperator.Or:
          return {
            ...baseReturn,
            op: booleanOps,
            expressions: DeepDynamicLogicValues.expressions,
          };
        case BoolOperator.Not: {
          return {
            ...baseReturn,
            op: booleanOps,
            expression: DeepDynamicLogicValues.expression,
          };
        }
        default:
          return { ...baseReturn, op: booleanOps };
      }
    }
    case LOGIC_TYPES.COMPARE:
      return {
        ...baseReturn,
        op: compareOps,
        comparators: DeepDynamicLogicValues.comparators,
      };
    case LOGIC_TYPES.CONDITION:
      return {
        ...baseReturn,
        conditions: DeepDynamicLogicValues.conditions,
        else: DeepDynamicLogicValues.else,
      };
    case LOGIC_TYPES.FORMAT: {
      const { format } = currentDynamicLogic;
      switch (format) {
        case 'phone':
          return {
            ...baseReturn,
            format: DynamicLogicBaseValues.format,
            value: DeepDynamicLogicValues.expression,
          };
        case 'pattern':
          return {
            ...baseReturn,
            format: DynamicLogicBaseValues.format,
            pattern: DynamicLogicBaseValues.pattern,
            value: DeepDynamicLogicValues.expression,
          };
        default:
          return { ...baseReturn, format: DynamicLogicBaseValues.format };
      }
    }
    case LOGIC_TYPES.LIST: {
      const { op } = currentDynamicLogic;
      switch (op) {
        case ListOperator.LengthOperator:
        case ListOperator.Sum:
          return { ...baseReturn, op: listOps, list: DeepDynamicLogicValues.expression };
        case ListOperator.Map:
          return {
            ...baseReturn,
            op: listOps,
            list: DeepDynamicLogicValues.expression,
            mapper: DeepDynamicLogicValues.expression,
          };
        default:
          return { ...baseReturn, op: listOps };
      }
    }
    case LOGIC_TYPES.LITERAL:
      return { ...baseReturn, valueAsText: DeepDynamicLogicValues.valueAsText };
    case LOGIC_TYPES.MATH:
      return { ...baseReturn, op: mathOps, expressions: DeepDynamicLogicValues.expressions };
    default:
      return { ...baseReturn };
  }
};

export const resolveValidationObjectFields = (
  currentValidation: FieldValidation,
): Record<string, unknown> => {
  const { type, op } = currentValidation.condition || {};

  return {
    required: FieldValidationValues.required,
    rules: FieldValidationValues.rules,
    condition: { type: 'object', objectFields: resolveDynamicLogicObjectFields({ type, op }) },
  };
};

export const resolveDynamicLogicFields = (data, params) => {
  if (data.props.dynamicLogic) {
    return {
      ...params.fields,
      dynamicLogic: {
        label: 'Dynamic Logic',
        type: 'object',
        objectFields: {
          type: DynamicLogicBaseValues.type,
          ...resolveDynamicLogicObjectFields(data.props.dynamicLogic),
        },
      },
    };
  }
  return params.fields;
};

export const resolveDynamicLogicAndValidationFields = (data, params) => {
  let result = { ...params.fields };
  if (data.props.dynamicLogic) {
    result = {
      ...result,
      dynamicLogic: {
        label: 'Dynamic Logic',
        type: 'object',
        objectFields: {
          type: DynamicLogicBaseValues.type,
          ...resolveDynamicLogicObjectFields(data.props.dynamicLogic),
        },
      },
    };
  }
  if (data.props.validation) {
    const isDateField = data.props.type === 'date';
    result = {
      ...result,
      validation: {
        type: 'object',
        objectFields: {
          ...(isDateField ? DateFieldValidation : {}),
          ...resolveValidationObjectFields(data.props.validation),
        },
      },
    };
  }
  if (data.props?.display?.validation) {
    console.log({ data: data.props?.display?.validation, fields: result });
    result = {
      ...result,
      display: {
        type: 'object',
        objectFields: {
          ...result.display.objectFields,
          validation: {
            type: 'object',
            objectFields: resolveValidationObjectFields(data.props.display.validation),
          },
        },
      },
    };
  }
  if (data.props.formula) {
    result = {
      ...result,
      formula: {
        type: 'object',
        objectFields: {
          type: DynamicLogicBaseValues.type,
          ...resolveDynamicLogicObjectFields(data.props.formula),
        },
      },
    };
  }
  return result;
};
