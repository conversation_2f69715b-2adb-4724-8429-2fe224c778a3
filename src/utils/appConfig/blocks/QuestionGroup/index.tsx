import Markdown from '@components/markdown/Markdown';
import { type ComponentConfig, DropZone } from '@measured/puck';
import { Box, Stack, Typography } from '@mui/material';
import {
  DynamicLogicIndicator,
  KeyField,
  RequiredTextField,
} from '@utils/appConfig/customComponents';
import {
  getReadonlyExpressionProps,
  getResolvedDynamicLogicValues,
  resolveDynamicLogicAndValidationFields,
} from '@utils/appConfig/utils';
import { DynamicLogicBaseValues, type Expression } from 'types/appConfig';

export type QuestionGroupProps = {
  key: string;
  name: string;
  overview?: {
    title: string;
    description: string;
  };
  dynamicLogic?: Expression;
};

export const QuestionGroup: ComponentConfig<QuestionGroupProps> = {
  fields: {
    key: { type: 'custom', render: KeyField, label: 'key' },
    name: { type: 'custom', render: RequiredTextField, label: 'name' },
    overview: {
      type: 'object',
      objectFields: {
        title: { type: 'text' },
        description: { type: 'textarea' },
      },
    },
    dynamicLogic: {
      label: 'Dynamic Logic',
      type: 'object',
      objectFields: { type: DynamicLogicBaseValues.type },
    },
  },
  defaultProps: {
    key: 'requiredKey',
    name: 'required name',
    overview: undefined,
    dynamicLogic: undefined,
  },
  resolveData: async ({ props }) => {
    return {
      props: getResolvedDynamicLogicValues(props),
      readOnly: getReadonlyExpressionProps(props),
    };
  },
  resolveFields: resolveDynamicLogicAndValidationFields,
  render: QuestionGroupLayout,
};

function QuestionGroupLayout(group) {
  return (
    <Box sx={{ border: 'solid black 2px' }}>
      <DynamicLogicIndicator visible={group.dynamicLogic} />
      <Stack spacing={2} direction="column" alignItems="center">
        <Typography
          variant="h5"
          component="h5"
          sx={{ textAlign: 'center', mb: 4, fontWeight: 500 }}
        >
          {group?.overview?.title}
        </Typography>
        {group?.overview?.description && (
          <Markdown
            sx={{
              fontSize: 20,
              textAlign: 'center',
              paddingX: 4,
              width: '50%',
            }}
          >
            {group?.overview?.description}
          </Markdown>
        )}
        <DropZone zone={'Question'} />
      </Stack>
    </Box>
  );
}
