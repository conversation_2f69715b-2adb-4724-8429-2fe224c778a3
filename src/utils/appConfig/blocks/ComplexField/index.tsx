import { type ComponentConfig, DropZone } from '@measured/puck';
import { Stack, Typography } from '@mui/material';
import {
  DisplayNameTextField,
  DynamicLogicIndicator,
  KeyField,
  RequiredTypeField,
} from '@utils/appConfig/customComponents';
import {
  getReadonlyExpressionProps,
  getResolvedDynamicLogicValues,
  resolveDynamicLogicAndValidationFields,
} from '@utils/appConfig/utils';
import {
  type BaseFieldProps,
  DefaultValidationValues,
  DynamicLogicBaseValues,
  type FieldValidation,
  FieldValidationValues,
} from 'types/appConfig';

export interface ComplexFieldProps extends BaseFieldProps {
  copy?: string;
  subFields?: { key?: string }[];
  validation?: FieldValidation;
  useLabelPrefix?: boolean;
}

export const ComplexField: ComponentConfig<ComplexFieldProps> = {
  fields: {
    type: { type: 'custom', render: RequiredTypeField, label: 'type' },
    key: { type: 'custom', render: KeyField, label: 'key' },
    displayName: { type: 'custom', render: DisplayNameTextField, label: 'display name' },
    copy: {
      type: 'text',
    },
    useLabelPrefix: {
      type: 'radio',
      options: [
        { label: 'true', value: true },
        { label: 'false', value: false },
      ],
    },
    validation: {
      type: 'object',
      objectFields: {
        ...FieldValidationValues,
        condition: { type: 'object', objectFields: { type: DynamicLogicBaseValues.type } },
      },
    },
    dynamicLogic: {
      label: 'Dynamic Logic',
      type: 'object',
      objectFields: { type: DynamicLogicBaseValues.type },
    },
  },
  defaultProps: {
    type: 'complex',
    key: '',
    subFields: [],
    validation: DefaultValidationValues,
  },
  resolveData: async ({ props }) => {
    return {
      props: getResolvedDynamicLogicValues(props),
      readOnly: {
        ...getReadonlyExpressionProps(props),
        ...getReadonlyExpressionProps(props, 'validation.condition'),
        type: true,
      },
    };
  },
  resolveFields: resolveDynamicLogicAndValidationFields,
  render: ComplexFieldLayout,
};

function ComplexFieldLayout(field) {
  return (
    <Stack
      direction="row"
      alignItems="center"
      gap={3}
      my={2}
      p={1}
      sx={{ border: 'dashed purple 2px' }}
    >
      <DynamicLogicIndicator visible={field.dynamicLogic} />
      <Typography>{field?.copy}</Typography>
      <DropZone
        zone={'complex:subFields'}
        allow={[
          'CalculatedField',
          'CheckboxField',
          'GenericDateField',
          'GenericDocumentField',
          'GenericDropdownField',
          'GenericTextField',
          'TypographyComponent',
          'IncomeSourcesField',
          'NumberOfDependentsField',
          'HouseholdSizeField',
        ]}
      />
    </Stack>
  );
}
