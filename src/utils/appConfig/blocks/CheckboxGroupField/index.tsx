import type { ComponentConfig } from '@measured/puck';
import { Checkbox, FormControlLabel, FormGroup, Stack, Typography } from '@mui/material';
import {
  DisplayNameTextField,
  DynamicLogicIndicator,
  KeyField,
  RequiredTextField,
  RequiredTypeField,
} from '@utils/appConfig/customComponents';
import {
  getReadonlyExpressionProps,
  getResolvedDynamicLogicValues,
  resolveDynamicLogicAndValidationFields,
} from '@utils/appConfig/utils';
import {
  type BaseFieldProps,
  DefaultValidationValues,
  DynamicLogicBaseValues,
  type FieldValidation,
  FieldValidationValues,
} from 'types/appConfig';

export interface CheckboxGroupFieldProps extends BaseFieldProps {
  options?: {
    label: string;
    description?: string;
    value: string;
    disabled?: boolean;
    children?: {
      label: string;
      description?: string;
      value: string;
      disabled?: boolean;
    }[];
  }[];
  validation?: FieldValidation;
}

export const CheckboxGroupField: ComponentConfig<CheckboxGroupFieldProps> = {
  fields: {
    type: { type: 'custom', render: RequiredTypeField, label: 'type' },
    key: { type: 'custom', render: KeyField, label: 'key' },
    displayName: { type: 'custom', render: DisplayNameTextField, label: 'display name' },
    options: {
      type: 'array',
      arrayFields: {
        label: { type: 'custom', render: RequiredTextField, label: 'label' },
        description: { type: 'text' },
        value: { type: 'custom', render: RequiredTextField, label: 'value' },
        disabled: {
          type: 'radio',
          options: [
            { label: 'true', value: true },
            { label: 'false', value: false },
          ],
        },
        children: {
          type: 'array',
          arrayFields: {
            label: {
              type: 'custom',
              render: RequiredTextField,
              label: 'label',
            },
            description: { type: 'text' },
            value: {
              type: 'custom',
              render: RequiredTextField,
              label: 'value',
            },
            disabled: {
              type: 'radio',
              options: [
                { label: 'true', value: true },
                { label: 'false', value: false },
              ],
            },
          },
        },
      },
    },
    validation: {
      type: 'object',
      objectFields: {
        ...FieldValidationValues,
        condition: { type: 'object', objectFields: { type: DynamicLogicBaseValues.type } },
      },
    },
    dynamicLogic: {
      label: 'Dynamic Logic',
      type: 'object',
      objectFields: { type: DynamicLogicBaseValues.type },
    },
  },
  defaultProps: {
    type: 'checkboxGroup',
    key: '',
    validation: DefaultValidationValues,
  },
  resolveData: async ({ props }) => {
    return {
      props: getResolvedDynamicLogicValues(props),
      readOnly: {
        ...getReadonlyExpressionProps(props),
        ...getReadonlyExpressionProps(props, 'validation.condition'),
        type: true,
      },
    };
  },
  resolveFields: resolveDynamicLogicAndValidationFields,
  render: CheckboxGroupFieldLayout,
};

function CheckboxGroupFieldLayout(field) {
  return (
    <Stack alignItems="center">
      <DynamicLogicIndicator visible={field.dynamicLogic} />
      <Typography>{`Please select all that apply${
        field?.validation?.required !== false ? '*' : ''
      }:`}</Typography>
      <FormGroup>
        {field?.options?.map((option) => (
          <FormControlLabel
            disabled={option?.disabled === true}
            key={option?.value}
            control={<Checkbox />}
            label={
              <Stack direction="column" spacing={0.5}>
                <Typography
                  variant="body1"
                  component="span"
                  sx={{ fontStyle: option?.description ? 'bold' : 'initial' }}
                >
                  {option.label}
                </Typography>
                {option?.description && (
                  <Typography variant="body1" component="span">
                    {option.description}
                  </Typography>
                )}
              </Stack>
            }
          />
        ))}
      </FormGroup>
    </Stack>
  );
}
