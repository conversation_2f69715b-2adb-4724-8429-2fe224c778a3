import type { ComponentConfig } from '@measured/puck';
import {
  KeyField,
  NonEmptyTextAreaField,
  RequiredTextField,
} from '@utils/appConfig/customComponents';
import { DefaultLayout } from '@utils/appConfig/layouts/IntroPage/Default';
import { StepsLayout } from '@utils/appConfig/layouts/IntroPage/Steps';
import { WelcomeLayout } from '@utils/appConfig/layouts/IntroPage/Welcome';
import { IconSelectValues } from 'types/appConfig';

export type IntroPageProps = {
  key: string;
  title: string;
  layout: string;
  copy?: string;
  body: string;
  steps?: {
    title: string;
    description?: string;
    icon: string;
  }[];
};

const introPageDefaultValues = {
  key: '',
  title: 'page title',
  layout: 'default',
  body: 'Body Text',
  steps: [],
};

export const IntroPage: ComponentConfig<IntroPageProps> = {
  fields: {
    key: { type: 'custom', render: KeyField, label: 'key' },
    title: { type: 'custom', render: RequiredTextField, label: 'title' },
    layout: {
      type: 'select',
      options: [
        { label: 'welcome', value: 'welcome' },
        { label: 'steps', value: 'steps' },
        { label: 'default', value: 'default' },
      ],
    },
    body: { type: 'custom', render: NonEmptyTextAreaField, label: 'body' },
    copy: { type: 'custom', render: NonEmptyTextAreaField, label: 'copy' },
    steps: {
      type: 'array',
      arrayFields: {
        title: { type: 'custom', render: RequiredTextField, label: 'title' },
        description: {
          type: 'custom',
          render: NonEmptyTextAreaField,
          label: 'description',
        },
        icon: { type: 'select', options: IconSelectValues },
      },
      defaultItemProps: {
        title: 'What Happens next',
        description: 'This is how you claim',
        icon: 'verifiedOutlined',
      },
    },
  },
  defaultProps: introPageDefaultValues,
  render: (page) => {
    switch (page.layout) {
      case 'welcome':
        return WelcomeLayout(page);
      case 'default':
        return DefaultLayout(page);
      case 'steps':
        return StepsLayout(page);
      default:
        return <>Layout not recognized</>;
    }
  },
};
