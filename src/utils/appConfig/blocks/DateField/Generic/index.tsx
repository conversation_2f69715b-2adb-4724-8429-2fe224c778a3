import DatePicker from '@components/forms/DatePicker';
import type { ComponentConfig } from '@measured/puck';
import { Stack, Typography } from '@mui/material';
import {
  DisplayNameTextField,
  DynamicLogicIndicator,
  KeyField,
  RequiredTypeField,
} from '@utils/appConfig/customComponents';
import {
  DateFieldValidation,
  getReadonlyExpressionProps,
  getResolvedDynamicLogicValues,
  resolveDynamicLogicAndValidationFields,
} from '@utils/appConfig/utils';

import {
  type BaseFieldProps,
  DefaultValidationValues,
  DynamicLogicBaseValues,
  type EligibilityValidation,
  EligibilityValidationValues,
  type FieldValidation,
  FieldValidationValues,
} from 'types/appConfig';

export interface GenericDateFieldProps extends BaseFieldProps {
  copy?: string;
  props?: { helperText?: string };
  validation?: FieldValidation & {
    allowedRange?: 'today' | 'past' | 'future';
    minimumDate?: string;
    maximumDate?: string;
  };
  eligibilityValidation?: EligibilityValidation;
}

export const GenericDateField: ComponentConfig<GenericDateFieldProps> = {
  fields: {
    type: { type: 'custom', render: RequiredTypeField, label: 'type' },
    key: { type: 'custom', render: KeyField, label: 'key' },
    displayName: { type: 'custom', render: DisplayNameTextField, label: 'display name' },
    props: { type: 'object', objectFields: { helperText: { type: 'text' } } },
    copy: { type: 'text' },
    validation: {
      type: 'object',
      objectFields: {
        ...DateFieldValidation,
        ...FieldValidationValues,
      },
    },
    eligibilityValidation: { type: 'object', objectFields: EligibilityValidationValues },
    dynamicLogic: {
      label: 'Dynamic Logic',
      type: 'object',
      objectFields: { type: DynamicLogicBaseValues.type },
    },
  },
  defaultProps: {
    type: 'date',
    key: '',
    validation: DefaultValidationValues,
  },
  resolveFields: resolveDynamicLogicAndValidationFields,
  resolveData: async ({ props }) => {
    return {
      props: getResolvedDynamicLogicValues(props),
      readOnly: {
        ...getReadonlyExpressionProps(props),
        ...getReadonlyExpressionProps(props, 'validation.condition'),
        type: true,
      },
    };
  },
  render: DateFieldLayout,
};

export function DateFieldLayout(field) {
  return (
    <Stack>
      <DynamicLogicIndicator visible={field.dynamicLogic} />
      <DatePicker label={field.copy} />
      {field?.props?.helperText && (
        <Typography variant="body1" component="span" sx={{ mx: 2, mt: '3px' }}>
          {field.props.helperText}
        </Typography>
      )}
    </Stack>
  );
}
