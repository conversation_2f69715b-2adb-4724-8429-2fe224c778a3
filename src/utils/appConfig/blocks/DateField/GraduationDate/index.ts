import type { ComponentConfig } from '@measured/puck';
import dayjs from 'dayjs';
import { DefaultValidationValues, PartnerReportingKeys } from 'types/appConfig';
import { GenericDateField, type GenericDateFieldProps } from '../Generic';

export const GraduationDateField: ComponentConfig<GenericDateFieldProps> = {
  ...GenericDateField,
  defaultProps: {
    type: 'date',
    key: PartnerReportingKeys.DateOfBirth,
    copy: 'Expected Graduation Date',
    props: { helperText: `Example: ${dayjs().add(1, 'year').format('MM/DD/YYYY')}` },
    validation: { ...DefaultValidationValues, allowedRange: 'future' },
  },
};
