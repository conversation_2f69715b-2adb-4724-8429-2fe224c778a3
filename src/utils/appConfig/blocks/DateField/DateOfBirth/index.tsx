import type { ComponentConfig } from '@measured/puck';
import { DefaultValidationValues, PartnerReportingKeys } from 'types/appConfig';
import { GenericDateField, type GenericDateFieldProps } from '../Generic';

export const DateOfBirthField: ComponentConfig<GenericDateFieldProps> = {
  ...GenericDateField,
  defaultProps: {
    type: 'date',
    key: PartnerReportingKeys.DateOfBirth,
    copy: 'Date of Birth',
    props: { helperText: 'Example: 06/01/2004' },
    validation: { ...DefaultValidationValues, allowedRange: 'past' },
  },
};
