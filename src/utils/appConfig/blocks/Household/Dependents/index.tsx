import type { ComponentConfig } from '@measured/puck';
import { GenericTextField, type GenericTextFieldProps } from '@utils/appConfig/blocks/Text/Generic';
import { DefaultValidationValues, PartnerReportingKeys, TextInputType } from 'types/appConfig';

export const NumberOfDependentsField: ComponentConfig<GenericTextFieldProps> = {
  ...GenericTextField,
  defaultProps: {
    type: 'text',
    key: PartnerReportingKeys.Dependents,
    copy: 'Number of dependents',
    inputType: TextInputType.Number,
    validation: DefaultValidationValues,
  },
};
