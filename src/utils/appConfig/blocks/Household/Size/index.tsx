import type { ComponentConfig } from '@measured/puck';
import { GenericTextField, type GenericTextFieldProps } from '@utils/appConfig/blocks/Text/Generic';
import { DefaultValidationValues, PartnerReportingKeys, TextInputType } from 'types/appConfig';

export const HouseholdSizeField: ComponentConfig<GenericTextFieldProps> = {
  ...GenericTextField,
  defaultProps: {
    type: 'text',
    key: PartnerReportingKeys.HouseholdSize,
    copy: 'Household Size',
    inputType: TextInputType.Number,
    validation: DefaultValidationValues,
    props: { helperText: 'Example: 3' },
  },
};
