import type { ComponentConfig } from '@measured/puck';
import {
  GenericDropdownField,
  type GenericDropdownFieldProps,
} from '@utils/appConfig/blocks/DropdownField/Generic';
import { PartnerReportingKeys } from 'types/appConfig';

const IncomeTypes = [
  { label: 'Employment', value: 'employment' },
  { label: 'Self-Employment', value: 'selfEmployment' },
  { label: 'Unemployment', value: 'unemployment' },
  { label: 'Rental Income', value: 'rentalIncome' },
  { label: 'Pension/Retirement benefits', value: 'pensionOrRetirement' },
  { label: 'Money from family/friends', value: 'moneyFromFamily' },
  { label: 'Child Support', value: 'childSupport' },
  { label: 'Alimony', value: 'alimony' },
  { label: 'Worker’s Compensation', value: 'workerCompensation' },
  { label: 'Disability benefits', value: 'disabilityBenefits' },
  { label: 'Social Security benefits', value: 'ssb' },
  { label: 'Supplemental Security Income (SSI)', value: 'ssi' },
  { label: 'Temporary Assistance for Needy Families (TANF)', value: 'tanf' },
  { label: 'Tribal payments', value: 'tribalPayments' },
  { label: 'Veteran’s benefits', value: 'veteran' },
  { label: 'Other income sources', value: 'other' },
];

export const IncomeSourcesField: ComponentConfig<GenericDropdownFieldProps> = {
  ...GenericDropdownField,
  defaultProps: {
    type: 'dropdown',
    key: PartnerReportingKeys.IncomeSource,
    options: IncomeTypes,
    copy: 'Income Sources',
    props: { multiple: true },
  },
};
