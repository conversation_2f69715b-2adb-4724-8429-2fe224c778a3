import Markdown from '@components/markdown/Markdown';
import { type ComponentConfig, DropZone } from '@measured/puck';
import { Box, Stack, Typography } from '@mui/material';
import { KeyField, RequiredTextField } from '@utils/appConfig/customComponents';

export type SectionProps = {
  key: string;
  name: string;
  overview: {
    title: string;
    description: string;
  };
};

export const Section: ComponentConfig<SectionProps> = {
  fields: {
    key: { type: 'custom', render: KeyField, label: 'key' },
    name: { type: 'custom', render: RequiredTextField, label: 'name' },
    overview: {
      type: 'object',
      objectFields: {
        title: { type: 'custom', render: RequiredTextField, label: 'title' },
        description: { type: 'textarea' },
      },
    },
  },
  defaultProps: {
    key: 'section_key',
    name: 'section name',
    overview: {
      title: 'section overview title',
      description: '',
    },
  },
  render: SectionLayout,
};

function SectionLayout(section) {
  return (
    <Box sx={{ px: 7, pt: 13, pb: 10, m: 2, minHeight: 600 }}>
      <Stack spacing={8} direction="column" alignItems="center">
        <Box sx={{ width: '50%', height: 'auto' }}>
          <Typography variant="h5" sx={{ textAlign: 'center' }}>
            [ Partner logo ]
          </Typography>
        </Box>
        <Stack spacing={8} alignItems="center">
          <Typography
            variant="h1"
            component="h2"
            sx={{
              fontSize: '2.5rem',
              fontWeight: 500,
              textAlign: 'center',
              mb: 4,
            }}
          >
            {section?.overview?.title}
          </Typography>
          {section?.overview?.description && (
            <Markdown
              sx={{
                fontSize: 20,
                textAlign: 'center',
                paddingX: 4,
                width: '50%',
              }}
            >
              {section?.overview?.description}
            </Markdown>
          )}
        </Stack>
      </Stack>
      <Stack direction="column" spacing={8} alignItems="center">
        <hr />
        {section?.questionGroups?.map((group, index) => (
          <DropZone zone={`Question-Group::${index}`} key={group.key} />
        ))}
      </Stack>
    </Box>
  );
}
