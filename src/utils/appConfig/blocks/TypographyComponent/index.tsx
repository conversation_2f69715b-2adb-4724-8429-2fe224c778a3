import type { ComponentConfig } from '@measured/puck';
import { Box, Typography } from '@mui/material';
import {
  DynamicLogicIndicator,
  KeyField,
  RequiredTypeField,
} from '@utils/appConfig/customComponents';
import {
  getReadonlyExpressionProps,
  getResolvedDynamicLogicValues,
  resolveDynamicLogicAndValidationFields,
} from '@utils/appConfig/utils';
import { type BaseFieldProps, DynamicLogicBaseValues } from 'types/appConfig';

export interface TypographyComponentProps extends BaseFieldProps {
  copy?: string;
  props?: {
    alignment?: 'left' | 'center' | 'right';
    variant:
      | 'h1'
      | 'h2'
      | 'h3'
      | 'h4'
      | 'body'
      | 'fieldLabel'
      | 'label'
      | 'largeBody'
      | 'stat'
      | 'statusBlockHeader';
  };
}

export const TypographyComponent: ComponentConfig<TypographyComponentProps> = {
  fields: {
    type: { type: 'custom', render: Required<PERSON><PERSON><PERSON><PERSON>, label: 'type' },
    key: { type: 'custom', render: Key<PERSON>ield, label: 'key' },
    copy: {
      type: 'textarea',
    },
    props: {
      type: 'object',
      objectFields: {
        alignment: {
          type: 'select',
          options: [
            { label: '', value: '' },
            { label: 'left', value: 'left' },
            { label: 'center', value: 'center' },
            { label: 'right', value: 'right' },
          ],
        },
        variant: {
          type: 'select',
          options: [
            { label: 'H1', value: 'h1' },
            { label: 'H2', value: 'h2' },
            { label: 'H3', value: 'h3' },
            { label: 'H4', value: 'h4' },
            { label: 'Body', value: 'body' },
            { label: 'FieldLabel', value: 'fieldLabel' },
            { label: 'Label', value: 'label' },
            { label: 'LargeBody', value: 'largeBody' },
            { label: 'Stat', value: 'stat' },
            { label: 'StatusBlockHeader', value: 'statusBlockHeader' },
          ],
        },
      },
    },
    dynamicLogic: {
      label: 'Dynamic Logic',
      type: 'object',
      objectFields: { type: DynamicLogicBaseValues.type },
    },
  },
  defaultProps: {
    type: 'typography',
    key: '',
    props: { variant: 'body', alignment: 'center' },
  },
  resolveFields: resolveDynamicLogicAndValidationFields,
  resolveData: async ({ props }) => {
    return {
      props: getResolvedDynamicLogicValues(props),
      readOnly: {
        ...getReadonlyExpressionProps(props),
        ...getReadonlyExpressionProps(props, 'validation.condition'),
        type: true,
      },
    };
  },
  render: TypographyComponentLayout,
};

function TypographyComponentLayout(field) {
  return (
    <Box alignItems={field?.props?.alignment || 'center'}>
      <DynamicLogicIndicator visible={field.dynamicLogic} />
      <Typography variant={field?.props?.variant || 'body1'}>{field?.copy}</Typography>
    </Box>
  );
}
