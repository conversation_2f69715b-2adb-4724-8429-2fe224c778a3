import Markdown from '@components/markdown/Markdown';
import type { ComponentConfig } from '@measured/puck';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { Button, Stack, Typography } from '@mui/material';
import {
  DisplayNameTextField,
  DynamicLogicIndicator,
  KeyField,
  RequiredTypeField,
} from '@utils/appConfig/customComponents';
import {
  getReadonlyExpressionProps,
  getResolvedDynamicLogicValues,
  resolveDynamicLogicAndValidationFields,
} from '@utils/appConfig/utils';
import {
  type BaseFieldProps,
  DefaultValidationValues,
  type DocumentEligibilityValidation,
  DocumentEligibilityValidationValues,
  DynamicLogicBaseValues,
  type FieldValidation,
  FieldValidationValues,
} from 'types/appConfig';

export interface GenericDocumentFieldProps extends BaseFieldProps {
  validation?: FieldValidation;
  copy?: {
    title?: string;
    description?: string;
  };
  documentEligibilityValidation?: DocumentEligibilityValidation;
}

export const GenericDocumentField: ComponentConfig<GenericDocumentFieldProps> = {
  fields: {
    type: { type: 'custom', render: RequiredTypeField, label: 'type' },
    key: { type: 'custom', render: KeyField, label: 'key' },
    displayName: { type: 'custom', render: DisplayNameTextField, label: 'display name' },
    copy: {
      type: 'object',
      objectFields: {
        title: { type: 'text' },
        description: { type: 'textarea' },
      },
    },
    documentEligibilityValidation: {
      type: 'object',
      objectFields: DocumentEligibilityValidationValues,
    },
    validation: {
      type: 'object',
      objectFields: {
        ...FieldValidationValues,
        condition: { type: 'object', objectFields: { type: DynamicLogicBaseValues.type } },
      },
    },
    dynamicLogic: {
      label: 'Dynamic Logic',
      type: 'object',
      objectFields: { type: DynamicLogicBaseValues.type },
    },
  },
  defaultProps: {
    type: 'document',
    key: '',
    validation: DefaultValidationValues,
  },
  resolveFields: resolveDynamicLogicAndValidationFields,
  resolveData: async ({ props }) => {
    return {
      props: getResolvedDynamicLogicValues(props),
      readOnly: {
        ...getReadonlyExpressionProps(props),
        ...getReadonlyExpressionProps(props, 'validation.condition'),
        type: true,
      },
    };
  },
  render: DocumentFieldLayout,
};

function DocumentFieldLayout(field) {
  return (
    <Stack alignItems="center">
      <DynamicLogicIndicator visible={field.dynamicLogic} />
      {field?.copy?.title && (
        <Typography variant="h6" component="h6">
          {field?.copy.title}
        </Typography>
      )}
      {field?.copy?.description && <Markdown>{field?.copy.description}</Markdown>}
      <Button component="label" variant="outlined" tabIndex={-1} startIcon={<CloudUploadIcon />}>
        {`UPLOAD ${field?.validation?.required !== false ? '*' : ''}`}
      </Button>
    </Stack>
  );
}
