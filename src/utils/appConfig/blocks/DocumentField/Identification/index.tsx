import type { ComponentConfig } from '@measured/puck';
import { DefaultValidationValues, PartnerReportingKeys } from 'types/appConfig';
import { GenericDocumentField, type GenericDocumentFieldProps } from '../Generic';

export const IdentificationDocumentField: ComponentConfig<GenericDocumentFieldProps> = {
  ...GenericDocumentField,
  defaultProps: {
    type: 'document',
    key: PartnerReportingKeys.Identification,
    copy: {
      title: 'Proof of Identification',
      description: "Examples include your driver's license or a government issued ID.",
    },
    validation: DefaultValidationValues,
  },
};
