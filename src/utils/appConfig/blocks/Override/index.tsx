import Markdown from '@components/markdown/Markdown';
import type { ComponentConfig } from '@measured/puck';
import { Stack, Typography } from '@mui/material';
import {
  KeyField,
  NonEmptyTextAreaField,
  NonEmptyTextField,
  RequiredTextField,
} from '@utils/appConfig/customComponents';
import { IconSelectValues } from 'types/appConfig';

export type ConfigOverridesProps = {
  statuses?: ApplicationStatus[];
  submission?: {
    title: string;
    bullets: Bullet[];
  };
};

type ApplicationStatus = {
  status: string;
  label: string;
  message: string;
  action?: string;
  referralMessage?: string;
};

type Bullet = {
  key: string;
  icon: string;
  title: string;
  description?: string;
};

export const Overrides: ComponentConfig<ConfigOverridesProps> = {
  fields: {
    statuses: {
      type: 'array',
      arrayFields: {
        status: {
          type: 'select',
          options: [
            { label: 'Application', value: 'Application' },
            { label: 'Approved', value: 'Approved' },
            { label: 'Denied', value: 'Denied' },
            { label: 'Awaiting Review', value: 'AwaitingReview' },
            { label: 'In Review', value: 'InReview' },
            { label: 'In Progress', value: 'InProgress' },
            { label: 'Incomplete', value: 'Incomplete' },
            { label: 'Withdrawn - Open', value: 'WithdrawnOpen' },
            { label: 'Withdrawn- Closed', value: 'WithdrawnClosed' },
            { label: 'Unknown', value: 'Unknown' },
          ],
        },
        label: { type: 'text' },
        message: {
          type: 'custom',
          render: NonEmptyTextAreaField,
          label: 'message',
        },
        referralMessage: {
          type: 'custom',
          render: NonEmptyTextAreaField,
          label: 'referralMessage',
        },
        action: { type: 'custom', render: NonEmptyTextField, label: 'action' },
      },
      defaultItemProps: {
        status: 'Application',
        message: 'Great message',
        label: 'Great label',
      },
      getItemSummary: (item, index) => {
        const statusOverride = item as ApplicationStatus;
        return statusOverride?.status || `Status ${index}`;
      },
    },
    submission: {
      type: 'object',
      objectFields: {
        title: { type: 'custom', render: RequiredTextField, label: 'title' },
        bullets: {
          type: 'array',
          arrayFields: {
            key: { type: 'custom', render: KeyField, label: 'key' },
            icon: { type: 'select', options: IconSelectValues },
            title: {
              type: 'custom',
              render: RequiredTextField,
              label: 'title',
            },
            description: {
              type: 'custom',
              render: NonEmptyTextAreaField,
              label: 'description',
            },
          },
          defaultItemProps: {
            key: 'unique key',
            icon: 'verifiedOutlined',
            title: 'Title for bullet',
          },
          getItemSummary: (bullet, index) => {
            const submissionBullet = bullet as Bullet;
            return submissionBullet?.key || `Bullet ${index}`;
          },
        },
      },
    },
  },
  render: OverridesLayout,
};

export function OverridesLayout(section) {
  return (
    <Stack minHeight={300}>
      {!section?.statuses && !section.submission && (
        <Typography m="auto">To add overrides click me and use the panel on the right!</Typography>
      )}
      <Stack alignItems="center">
        {section?.statuses && (
          <>
            <Typography component="h2" variant="h5" mb={2}>
              Status Overrides
            </Typography>
            <Stack borderTop="solid 2px black" width="100%">
              {section.statuses?.map(({ message, status, label }, index) => (
                <Stack
                  gap={2}
                  key={`${status}-${
                    // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
                    index
                  }`}
                  my={4}
                  alignItems="center"
                >
                  <Typography>Status: {status}</Typography>
                  {label && <Typography>{label}</Typography>}
                  <Markdown sx={{ textAlign: 'center', maxWidth: '60%' }}>{message}</Markdown>
                </Stack>
              ))}
            </Stack>
          </>
        )}
      </Stack>
      <Stack alignItems="center">
        {section?.submission && (
          <>
            <Typography component="h2" variant="h5" mb={2}>
              Submissions
            </Typography>
            <Stack borderTop="solid 2px black" width="100%">
              <Typography component="h3" variant="h5" my={2} textAlign="center">
                {section.submission?.title}
              </Typography>
              {section.submission.bullets?.map(({ key, icon, title, description }, index) => (
                <Stack
                  gap={2}
                  key={`${key}-${
                    // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
                    index
                  }`}
                  my={4}
                  alignItems="center"
                >
                  <Stack direction="row" gap={2}>
                    <Typography>{icon}</Typography>
                    <Stack gap={1}>
                      <Typography>{title}</Typography>
                      <Markdown>{description}</Markdown>
                    </Stack>
                  </Stack>
                </Stack>
              ))}
            </Stack>
          </>
        )}
      </Stack>
    </Stack>
  );
}
