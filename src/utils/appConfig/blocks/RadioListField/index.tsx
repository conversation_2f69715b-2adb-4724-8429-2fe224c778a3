import type { ComponentConfig } from '@measured/puck';
import { FormControl, FormControlLabel, FormLabel, Radio, RadioGroup } from '@mui/material';
import {
  DisplayNameTextField,
  DynamicLogicIndicator,
  KeyField,
  RequiredTextField,
  RequiredTypeField,
} from '@utils/appConfig/customComponents';
import {
  getReadonlyExpressionProps,
  getResolvedDynamicLogicValues,
  resolveDynamicLogicAndValidationFields,
  resolveDynamicLogicFields,
} from '@utils/appConfig/utils';
import {
  type BaseFieldProps,
  DefaultValidationValues,
  DynamicLogicBaseValues,
  type EligibilityValidation,
  EligibilityValidationValues,
  type FieldValidation,
  FieldValidationValues,
  type LabelValue,
} from 'types/appConfig';

export interface RadioListFieldProps extends BaseFieldProps {
  copy?: string;
  options?: LabelValue[];
  validation?: FieldValidation;
  eligibilityValidation?: EligibilityValidation;
}

export const RadioListField: ComponentConfig<RadioListFieldProps> = {
  fields: {
    type: { type: 'custom', render: RequiredTypeField, label: 'type' },
    key: { type: 'custom', render: KeyField, label: 'key' },
    displayName: { type: 'custom', render: DisplayNameTextField, label: 'display name' },
    copy: {
      type: 'text',
    },
    options: {
      type: 'array',
      arrayFields: {
        label: { type: 'custom', render: RequiredTextField, label: 'label' },
        value: { type: 'custom', render: RequiredTextField, label: 'value' },
        disabled: {
          type: 'radio',
          options: [
            { label: 'true', value: true },
            { label: 'false', value: false },
          ],
        },
      },
    },
    validation: {
      type: 'object',
      objectFields: {
        ...FieldValidationValues,
        condition: { type: 'object', objectFields: { type: DynamicLogicBaseValues.type } },
      },
    },
    eligibilityValidation: { type: 'object', objectFields: EligibilityValidationValues },
    dynamicLogic: {
      label: 'Dynamic Logic',
      type: 'object',
      objectFields: { type: DynamicLogicBaseValues.type },
    },
  },
  defaultProps: {
    type: 'radioList',
    key: '',
    validation: DefaultValidationValues,
  },
  resolveFields: resolveDynamicLogicAndValidationFields,
  resolveData: async ({ props }) => {
    return {
      props: getResolvedDynamicLogicValues(props),
      readOnly: {
        ...getReadonlyExpressionProps(props),
        ...getReadonlyExpressionProps(props, 'validation.condition'),
        type: true,
      },
    };
  },
  render: RadioListFieldLayout,
};

function RadioListFieldLayout(field) {
  return (
    <FormControl>
      <DynamicLogicIndicator visible={field.dynamicLogic} />
      <FormLabel>{field?.copy}</FormLabel>
      <RadioGroup>
        {field?.options?.map((option) => (
          <FormControlLabel
            value={option.value}
            key={option.value}
            control={<Radio disabled={option.disabled === true} />}
            label={option.label}
          />
        ))}
      </RadioGroup>
    </FormControl>
  );
}
