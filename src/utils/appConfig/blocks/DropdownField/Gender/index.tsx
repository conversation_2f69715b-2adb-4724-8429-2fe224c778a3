import type { ComponentConfig } from '@measured/puck';
import { PartnerReportingKeys } from 'types/appConfig';
import { GenericDropdownField, type GenericDropdownFieldProps } from '../Generic';

const GenderDropdownOptions = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'transgender-male', label: 'Transgender Male' },
  { value: 'transgender-female', label: 'Transgender Female' },
  {
    value: 'genderqueer/gender-non-conforming/gender-non-binary',
    label: 'Genderqueer/Gender non-conforming/Gender non-binary',
  },
  { value: 'questioning-or-unsure', label: 'Questioning or unsure' },
  { value: 'other', label: 'Other' },
];

export const GenderDropdownField: ComponentConfig<GenericDropdownFieldProps> = {
  ...GenericDropdownField,
  defaultProps: {
    type: 'dropdown',
    key: PartnerReportingKeys.Gender,
    options: GenderDropdownOptions,
    copy: 'Gender',
  },
};
