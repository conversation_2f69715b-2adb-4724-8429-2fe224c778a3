import type { ComponentConfig } from '@measured/puck';
import { FormControl, InputLabel, Select } from '@mui/material';
import {
  DisplayNameTextField,
  DynamicLogicIndicator,
  KeyField,
  RequiredTextField,
  RequiredTypeField,
} from '@utils/appConfig/customComponents';
import {
  getReadonlyExpressionProps,
  getResolvedDynamicLogicValues,
  resolveDynamicLogicAndValidationFields,
} from '@utils/appConfig/utils';
import {
  type BaseFieldProps,
  DefaultValidationValues,
  DynamicLogicBaseValues,
  type FieldValidation,
  FieldValidationValues,
} from 'types/appConfig';

export interface GenericDropdownFieldProps extends BaseFieldProps {
  copy?: string;
  options?: { label: string; value: string | number; disabled?: boolean }[];
  validation?: FieldValidation;
  props?:
    | { searchable?: boolean }
    | {
        childMode?: 'OnParentSelect' | 'AlwaysAvailable';
        multiple: true;
        selectAll?: true;
      };
}

export const GenericDropdownField: ComponentConfig<GenericDropdownFieldProps> = {
  fields: {
    type: { type: 'custom', render: RequiredTypeField, label: 'type' },
    key: { type: 'custom', render: KeyField, label: 'key' },
    displayName: { type: 'custom', render: DisplayNameTextField, label: 'display name' },
    copy: {
      type: 'text',
    },
    options: {
      type: 'array',
      arrayFields: {
        label: { type: 'custom', render: RequiredTextField, label: 'label' },
        value: { type: 'custom', render: RequiredTextField, label: 'value' },
        disabled: {
          type: 'radio',
          options: [
            { label: 'true', value: true },
            { label: 'false', value: false },
          ],
        },
      },
    },
    props: {
      type: 'object',
      objectFields: {
        searchable: {
          type: 'radio',
          options: [
            { label: 'true', value: true },
            { label: 'false', value: false },
          ],
        },
        childMode: {
          type: 'radio',
          options: [
            { label: 'OnParentSelect', value: 'OnParentSelect' },
            { label: 'AlwaysAvailable', value: 'AlwaysAvailable' },
          ],
        },
        multiple: {
          type: 'radio',
          options: [
            { label: 'true', value: true },
            { label: 'false', value: false },
          ],
        },
        selectAll: {
          type: 'radio',
          options: [
            { label: 'true', value: true },
            { label: 'false', value: false },
          ],
        },
      },
    },
    validation: {
      type: 'object',
      objectFields: {
        ...FieldValidationValues,
        condition: { type: 'object', objectFields: { type: DynamicLogicBaseValues.type } },
      },
    },
    dynamicLogic: {
      label: 'Dynamic Logic',
      type: 'object',
      objectFields: { type: DynamicLogicBaseValues.type },
    },
  },
  defaultProps: {
    type: 'dropdown',
    key: '',
    validation: DefaultValidationValues,
  },
  resolveData: async ({ props }) => {
    return {
      props: getResolvedDynamicLogicValues(props),
      readOnly: {
        ...getReadonlyExpressionProps(props, 'validation.condition'),
        type: true,
      },
    };
  },
  resolveFields: resolveDynamicLogicAndValidationFields,
  render: DropdownFieldLayout,
};

function DropdownFieldLayout(field) {
  return (
    <FormControl fullWidth sx={{ my: 2 }}>
      <DynamicLogicIndicator visible={field.dynamicLogic} />
      <InputLabel>{field?.copy}</InputLabel>
      <Select
        value=""
        label={`${field?.copy} ${field?.validation?.required !== false ? '*' : ''}`}
      />
    </FormControl>
  );
}
