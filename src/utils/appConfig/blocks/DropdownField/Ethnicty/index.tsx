import type { ComponentConfig } from '@measured/puck';
import { PartnerReportingKeys } from 'types/appConfig';
import { GenericDropdownField, type GenericDropdownFieldProps } from '../Generic';

const EthnicityDropdownOptions = [
  {
    label: 'African / Black American',
    value: 'africanAndOrBlackAmerican',
  },
  {
    label: 'North American Native',
    value: 'northAmericanNative',
  },
  {
    label: 'Asian American',
    value: 'asianAmerican',
  },
  {
    label: 'Chicano / Hispanic / Latino American',
    value: 'chicanoAndOrHispanicAndOrLatinoAmerican',
  },
  {
    label: 'Native Hawaiian or Other Pacific Islander',
    value: 'nativeHawaiianOrOtherPacificIslander',
  },
  { label: 'White/Caucasian', value: 'whiteOrCaucasian' },
  { label: 'Not specified in list', value: 'other' },
  { label: 'Prefer not to say', value: 'decline-to-answer' },
];

export const EthnicityDropdownField: ComponentConfig<GenericDropdownFieldProps> = {
  ...GenericDropdownField,
  defaultProps: {
    type: 'dropdown',
    key: PartnerReportingKeys.Ethnicity,
    options: EthnicityDropdownOptions,
    copy: 'Ethnicity',
    props: { multiple: true },
  },
};
