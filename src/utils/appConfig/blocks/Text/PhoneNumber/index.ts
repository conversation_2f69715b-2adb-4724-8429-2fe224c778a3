import type { ComponentConfig } from '@measured/puck';
import { DefaultValidationValues, PartnerReportingKeys, TextInputType } from 'types/appConfig';
import { GenericTextField, type GenericTextFieldProps } from '../Generic';

export const PhoneNumberField: ComponentConfig<GenericTextFieldProps> = {
  ...GenericTextField,
  defaultProps: {
    type: 'text',
    key: PartnerReportingKeys.PhoneNumber,
    copy: 'Phone Number',
    inputType: TextInputType.Text,
    validation: DefaultValidationValues,
  },
};
