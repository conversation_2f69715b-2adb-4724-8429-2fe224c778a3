import type { ComponentConfig } from '@measured/puck';
import { DefaultValidationValues, PartnerReportingKeys, TextInputType } from 'types/appConfig';
import { GenericTextField, type GenericTextFieldProps } from '../Generic';

export const ContactEmailField: ComponentConfig<GenericTextFieldProps> = {
  ...GenericTextField,
  defaultProps: {
    type: 'text',
    key: PartnerReportingKeys.ContactEmail,
    copy: 'Primary Contact Email',
    inputType: TextInputType.Text,
    validation: DefaultValidationValues,
  },
};
