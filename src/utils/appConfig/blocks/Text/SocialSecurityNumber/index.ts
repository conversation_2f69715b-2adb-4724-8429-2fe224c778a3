import type { ComponentConfig } from '@measured/puck';
import { DefaultValidationValues, PartnerReportingKeys, TextInputType } from 'types/appConfig';
import { GenericTextField, type GenericTextFieldProps } from '../Generic';

export const SocialSecurityNumberField: ComponentConfig<GenericTextFieldProps> = {
  ...GenericTextField,
  defaultProps: {
    type: 'text',
    key: PartnerReportingKeys.SSN,
    copy: 'Social Security Number',
    inputType: TextInputType.Text,
    validation: DefaultValidationValues,
    props: { characterCounter: { exact: 9 } },
    isSensitive: true,
  },
};
