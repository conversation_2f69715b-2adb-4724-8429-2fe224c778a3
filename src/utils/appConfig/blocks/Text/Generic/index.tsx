import type { ComponentConfig } from '@measured/puck';
import { TextField as TextInput } from '@mui/material';
import {
  DisplayNameTextField,
  DynamicLogicIndicator,
  KeyField,
  RequiredTypeField,
} from '@utils/appConfig/customComponents';
import {
  getReadonlyExpressionProps,
  resolveDynamicLogicAndValidationFields,
} from '@utils/appConfig/utils';
import {
  type BaseFieldProps,
  DefaultValidationValues,
  DynamicLogicBaseValues,
  type FieldValidation,
  FieldValidationValues,
  TextInputType,
} from 'types/appConfig';

export interface GenericTextFieldProps extends BaseFieldProps {
  type: string;
  key: string;
  copy?: string;
  inputType?: TextInputType;
  isSensitive?: boolean;
  validation?: FieldValidation;
  props?: {
    characterCounter?: { max?: number; min?: number; exact?: number };
    helperText?: string;
    inputProps?: { maxlength?: number };
    multiline?: boolean;
    rows?: number;
    tooltip?: string;
  };
}

export const GenericTextField: ComponentConfig<GenericTextFieldProps> = {
  fields: {
    type: { type: 'custom', render: RequiredTypeField, label: 'type' },
    key: { type: 'custom', render: KeyField, label: 'key' },
    displayName: { type: 'custom', render: DisplayNameTextField, label: 'display name' },
    copy: {
      type: 'text',
    },
    isSensitive: {
      type: 'radio',
      options: [
        { label: 'true', value: true },
        { label: 'false', value: false },
      ],
      label: 'Is Sensitive',
    },
    inputType: {
      type: 'select',
      options: [
        { label: 'Currency', value: 'currency' },
        { label: 'Date', value: 'date' },
        { label: 'Email', value: 'email' },
        { label: 'Number', value: 'number' },
        { label: 'Password', value: 'password' },
        { label: 'Phone', value: 'tel' },
        { label: 'Signature', value: 'signature' },
        { label: 'Text', value: 'text' },
      ],
    },
    props: {
      type: 'object',
      objectFields: {
        characterCounter: {
          type: 'object',
          objectFields: {
            max: { type: 'number' },
            min: { type: 'number' },
            exact: { type: 'number' },
          },
        },
        helperText: { type: 'text' },
        inputProps: {
          type: 'object',
          objectFields: {
            maxLength: { type: 'number' },
          },
        },
        multiline: {
          type: 'radio',
          options: [
            { label: 'true', value: true },
            { label: 'false', value: false },
          ],
        },
        rows: {
          type: 'number',
        },
        tooltip: {
          type: 'text',
        },
      },
    },
    validation: {
      type: 'object',
      objectFields: {
        ...FieldValidationValues,
        condition: { type: 'object', objectFields: { type: DynamicLogicBaseValues.type } },
      },
    },
    dynamicLogic: {
      label: 'Dynamic Logic',
      type: 'object',
      objectFields: { type: DynamicLogicBaseValues.type },
    },
  },
  defaultProps: {
    type: 'text',
    key: '',
    inputType: TextInputType.Text,
    validation: DefaultValidationValues,
  },
  resolveFields: resolveDynamicLogicAndValidationFields,
  resolveData: async ({ props }) => {
    return {
      props,
      readOnly: {
        ...getReadonlyExpressionProps(props),
        ...getReadonlyExpressionProps(props, 'validation.condition'),
        type: true,
      },
    };
  },
  render: TextFieldLayout,
};

export function TextFieldLayout(field) {
  return (
    <>
      <DynamicLogicIndicator visible={field.dynamicLogic} />
      <TextInput
        label={`${field.copy} ${field?.validation?.required === false ? '' : '*'}`}
        variant="outlined"
        type={field.type}
        sx={{ my: 2 }}
        fullWidth
      />
    </>
  );
}
