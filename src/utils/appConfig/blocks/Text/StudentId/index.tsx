import type { ComponentConfig } from '@measured/puck';
import { DefaultValidationValues, PartnerReportingKeys, TextInputType } from 'types/appConfig';
import { GenericTextField, type GenericTextFieldProps } from '../Generic';

export const StudentIdField: ComponentConfig<GenericTextFieldProps> = {
  ...GenericTextField,
  defaultProps: {
    type: 'text',
    key: PartnerReportingKeys.StudentId,
    inputType: TextInputType.Text,
    copy: 'Student ID',
    validation: DefaultValidationValues,
  },
};
