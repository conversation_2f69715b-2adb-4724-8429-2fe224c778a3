import type { ComponentConfig } from '@measured/puck';
import { DefaultValidationValues, PartnerReportingKeys, TextInputType } from 'types/appConfig';
import { GenericTextField, type GenericTextFieldProps } from '../Generic';

export const SignatureField: ComponentConfig<GenericTextFieldProps> = {
  ...GenericTextField,
  defaultProps: {
    type: 'text',
    key: PartnerReportingKeys.Signature,
    copy: 'Electronic Signature',
    inputType: TextInputType.Signature,
    validation: DefaultValidationValues,
  },
};
