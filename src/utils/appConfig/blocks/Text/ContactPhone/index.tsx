import type { ComponentConfig } from '@measured/puck';
import { DefaultValidationValues, PartnerReportingKeys, TextInputType } from 'types/appConfig';
import { GenericTextField, type GenericTextFieldProps } from '../Generic';

export const ContactPhoneNumberField: ComponentConfig<GenericTextFieldProps> = {
  ...GenericTextField,
  defaultProps: {
    type: 'text',
    key: PartnerReportingKeys.ContactPhone,
    copy: 'Primary Contact Phone',
    inputType: TextInputType.Text,
    validation: DefaultValidationValues,
  },
};
