import Markdown from '@components/markdown/Markdown';
import { type ComponentConfig, DropZone } from '@measured/puck';
import { Box, Button, Stack, Typography } from '@mui/material';
import {
  DisplayNameTextField,
  DynamicLogicIndicator,
  KeyField,
  NonEmptyTextAreaField,
  RequiredTextField,
} from '@utils/appConfig/customComponents';
import {
  getReadonlyExpressionProps,
  getResolvedDynamicLogicValues,
  resolveDynamicLogicAndValidationFields,
} from '@utils/appConfig/utils';
import { DynamicLogicBaseValues, type Expression } from 'types/appConfig';

export interface ApplicationQuestionProps {
  copy: {
    title: string;
    intro?: string;
  };
  key: string;
  displayName?: string;
  dynamicLogic?: Expression;
  layout?: string;
  skippable?: boolean;
}

export const ApplicationQuestion: ComponentConfig<ApplicationQuestionProps> = {
  fields: {
    key: { type: 'custom', render: KeyField, label: 'key' },
    displayName: { type: 'custom', render: DisplayNameTextField, label: 'display name' },
    copy: {
      type: 'object',
      objectFields: {
        title: { type: 'custom', render: RequiredTextField, label: 'title' },
        intro: {
          type: 'custom',
          render: NonEmptyTextAreaField,
          label: 'intro',
        },
      },
    },
    layout: {
      type: 'select',
      options: [
        { label: 'default', value: 'default' },
        { label: 'panel', value: 'panel' },
      ],
    },
    skippable: {
      type: 'radio',
      options: [
        { label: 'true', value: true },
        { label: 'false', value: false },
      ],
    },
    dynamicLogic: {
      label: 'Dynamic Logic',
      type: 'object',
      objectFields: { type: DynamicLogicBaseValues.type },
    },
  },
  resolveData: async ({ props }) => {
    return {
      props: getResolvedDynamicLogicValues(props),
      readOnly: getReadonlyExpressionProps(props),
    };
  },
  resolveFields: resolveDynamicLogicAndValidationFields,
  defaultProps: {
    key: '',
    copy: {
      title: 'required title',
      intro: undefined,
    },
    layout: 'default',
    skippable: undefined,
    dynamicLogic: undefined,
  },
  render: ApplicationQuestionLayout,
};

function ApplicationQuestionLayout(question) {
  return (
    <Box
      sx={{
        backgroundColor: question.layout === 'panel' ? '#F7F7FB' : 'initial',
        p: 4,
        my: 2,
      }}
    >
      <DynamicLogicIndicator visible={question.dynamicLogic} />
      <Stack spacing={2} direction="column" alignItems="center">
        <Typography
          variant="h5"
          component="h5"
          sx={{ textAlign: 'center', mb: 4, fontWeight: 500 }}
        >
          {question?.copy?.title}
        </Typography>
        {question?.copy?.intro && (
          <Markdown
            sx={{
              fontSize: 20,
              textAlign: 'center',
              paddingX: 4,
              width: '50%',
            }}
          >
            {question?.copy?.intro}
          </Markdown>
        )}
        <Typography variant="body1">(*) An asterisk indicates a required response</Typography>
        <Stack sx={{ width: '75%' }} alignItems="center">
          <DropZone zone={'Field'} />
        </Stack>
        <Button variant="contained">Save and Continue</Button>
      </Stack>
    </Box>
  );
}
