import type { ComponentConfig } from '@measured/puck';
import { Checkbox, FormControlLabel, FormGroup } from '@mui/material';
import {
  DisplayNameTextField,
  DynamicLogicIndicator,
  KeyField,
  RequiredTypeField,
} from '@utils/appConfig/customComponents';
import {
  getReadonlyExpressionProps,
  getResolvedDynamicLogicValues,
  resolveDynamicLogicAndValidationFields,
} from '@utils/appConfig/utils';
import {
  type BaseFieldProps,
  DefaultValidationValues,
  DynamicLogicBaseValues,
  type FieldValidation,
  FieldValidationValues,
} from 'types/appConfig';

export interface CheckboxFieldProps extends BaseFieldProps {
  copy?: string;
  validation?: FieldValidation;
}

export const CheckboxField: ComponentConfig<CheckboxFieldProps> = {
  fields: {
    type: { type: 'custom', render: RequiredTypeField, label: 'type' },
    key: { type: 'custom', render: KeyField, label: 'key' },
    displayName: { type: 'custom', render: DisplayNameTextField, label: 'display name' },
    copy: { type: 'text' },
    validation: {
      type: 'object',
      objectFields: {
        ...FieldValidationValues,
        condition: { type: 'object', objectFields: { type: DynamicLogicBaseValues.type } },
      },
    },
    dynamicLogic: {
      label: 'Dynamic Logic',
      type: 'object',
      objectFields: { type: DynamicLogicBaseValues.type },
    },
  },
  defaultProps: {
    type: 'checkbox',
    key: '',
    validation: DefaultValidationValues,
  },
  resolveData: async ({ props }) => {
    return {
      props: getResolvedDynamicLogicValues(props),
      readOnly: {
        ...getReadonlyExpressionProps(props),
        ...getReadonlyExpressionProps(props, 'display.validation.condition'),
        type: true,
      },
    };
  },
  resolveFields: resolveDynamicLogicAndValidationFields,
  render: CheckboxFieldLayout,
};

function CheckboxFieldLayout(field) {
  return (
    <FormGroup>
      <DynamicLogicIndicator visible={field.dynamicLogic} />
      <FormControlLabel
        control={<Checkbox />}
        required={field?.validation?.required !== false}
        label={field?.copy}
      />
    </FormGroup>
  );
}
