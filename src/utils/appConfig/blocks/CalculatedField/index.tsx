import type { ComponentConfig } from '@measured/puck';
import { Stack, TextField } from '@mui/material';
import {
  DisplayNameTextField,
  DynamicLogicIndicator,
  KeyField,
  RequiredTypeField,
} from '@utils/appConfig/customComponents';
import {
  getReadonlyExpressionProps,
  getResolvedDynamicLogicValues,
  resolveDynamicLogicAndValidationFields,
} from '@utils/appConfig/utils';
import {
  type BaseFieldProps,
  DynamicLogicBaseValues,
  DynamicLogicValues,
  type Expression,
  type FieldValidation,
  FieldValidationValues,
  type TextInputType,
} from 'types/appConfig';

export interface CalculatedFieldProps extends BaseFieldProps {
  formula?: Expression;
  // If this field is set, the calculated field will display to the user.
  // Otherwise, it's only computed and stored on the backend
  display?: {
    copy: string;
    inputType?: TextInputType;
    validation?: FieldValidation;
  };
}

export const CalculatedField: ComponentConfig<CalculatedFieldProps> = {
  fields: {
    type: { type: 'custom', render: RequiredTypeField, label: 'type' },
    key: { type: 'custom', render: KeyField, label: 'key' },
    displayName: { type: 'custom', render: DisplayNameTextField, label: 'display name' },
    display: {
      type: 'object',
      objectFields: {
        copy: { type: 'text' },
        inputType: {
          label: 'Input Type',
          type: 'select',
          options: [
            { label: 'Currency', value: 'currency' },
            { label: 'Date', value: 'date' },
            { label: 'Email', value: 'email' },
            { label: 'Number', value: 'number' },
            { label: 'Password', value: 'password' },
            { label: 'Phone', value: 'tel' },
            { label: 'Signature', value: 'signature' },
            { label: 'Text', value: 'text' },
          ],
        },
        validation: {
          type: 'object',
          objectFields: {
            ...FieldValidationValues,
            condition: { type: 'object', objectFields: { type: DynamicLogicBaseValues.type } },
          },
        },
      },
    },
    formula: { type: 'object', objectFields: { type: DynamicLogicBaseValues.type } },
    dynamicLogic: {
      label: 'Dynamic Logic',
      type: 'object',
      objectFields: { type: DynamicLogicBaseValues.type },
    },
  },
  defaultProps: { type: 'calculated', key: '' },
  resolveData: async ({ props }) => {
    return {
      props: getResolvedDynamicLogicValues(props),
      readOnly: {
        ...getReadonlyExpressionProps(props),
        ...getReadonlyExpressionProps(props, 'display.validation.condition'),
        type: true,
      },
    };
  },
  resolveFields: resolveDynamicLogicAndValidationFields,
  render: CalculatedFieldLayout,
};

function CalculatedFieldLayout(field) {
  return (
    <Stack alignItems="center" spacing={1}>
      <DynamicLogicIndicator visible={field.dynamicLogic} />
      <TextField
        label={field?.display?.copy}
        disabled={true}
        variant="outlined"
        type={field.type}
      />
    </Stack>
  );
}
