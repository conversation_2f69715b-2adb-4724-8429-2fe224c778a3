import type { ComponentConfig } from '@measured/puck';
import { Stack, TextField, Typography } from '@mui/material';
import {
  DisplayNameTextField,
  DynamicLogicIndicator,
  NonEmptyTextField,
  RequiredTextField,
  RequiredTypeField,
} from '@utils/appConfig/customComponents';
import {
  getReadonlyExpressionProps,
  getResolvedDynamicLogicValues,
  resolveDynamicLogicAndValidationFields,
} from '@utils/appConfig/utils';
import {
  type BaseFieldProps,
  DefaultValidationValues,
  DynamicLogicBaseValues,
  type FieldValidation,
  FieldValidationValues,
} from 'types/appConfig';

export interface GenericAddressFieldProps extends BaseFieldProps {
  type: string;
  copy?: string;
  validation?: FieldValidation;
  copyAddressKey?: string;
  filter?: { state: string; city?: string };
  allowUnhoused?: boolean;
}

export const GenericAddressField: ComponentConfig<GenericAddressFieldProps> = {
  fields: {
    type: { type: 'custom', render: RequiredType<PERSON>ield, label: 'type' },
    key: { type: 'custom', render: RequiredTextField, label: 'key' },
    displayName: { type: 'custom', render: DisplayNameTextField, label: 'display name' },
    copy: { type: 'text' },
    copyAddressKey: { type: 'custom', render: NonEmptyTextField, label: 'copyAddressKey' },
    filter: {
      type: 'object',
      objectFields: { state: { type: 'text' }, city: { type: 'text' } },
    },
    allowUnhoused: {
      type: 'radio',
      options: [
        { label: 'true', value: true },
        { label: 'false', value: false },
      ],
    },
    validation: {
      type: 'object',
      objectFields: {
        ...FieldValidationValues,
        condition: { type: 'object', objectFields: { type: DynamicLogicBaseValues.type } },
      },
    },
    dynamicLogic: {
      label: 'Dynamic Logic',
      type: 'object',
      objectFields: { type: DynamicLogicBaseValues.type },
    },
  },
  defaultProps: {
    type: 'address',
    key: 'address.address',
    validation: DefaultValidationValues,
  },
  resolveData: async ({ props }) => {
    return {
      props: getResolvedDynamicLogicValues(props),
      readOnly: {
        ...getReadonlyExpressionProps(props),
        ...getReadonlyExpressionProps(props, 'validation.condition'),
        type: true,
      },
    };
  },
  resolveFields: resolveDynamicLogicAndValidationFields,
  render: AddressFieldLayout,
};

function AddressFieldLayout(field) {
  return (
    <Stack alignItems="center" spacing={1}>
      <DynamicLogicIndicator visible={field.dynamicLogic} />
      <Typography variant="body1">{field.copy}</Typography>
      <Stack spacing={1}>
        <TextField label="Street Address *" variant="outlined" />
        <TextField label="Apartment/Suite #" variant="outlined" />
      </Stack>
    </Stack>
  );
}
