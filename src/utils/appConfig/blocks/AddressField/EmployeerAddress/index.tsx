import type { ComponentConfig } from '@measured/puck';
import { DefaultValidationValues, PartnerReportingKeys } from 'types/appConfig';
import { GenericAddressField, type GenericAddressFieldProps } from '../Generic';

export const EmployerAddressField: ComponentConfig<GenericAddressFieldProps> = {
  ...GenericAddressField,
  defaultProps: {
    type: 'address',
    key: PartnerReportingKeys.EmployerAddress,
    validation: DefaultValidationValues,
  },
};
