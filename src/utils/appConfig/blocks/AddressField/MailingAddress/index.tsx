import type { ComponentConfig } from '@measured/puck';
import { DefaultValidationValues, PartnerReportingKeys } from 'types/appConfig';
import { GenericAddressField, type GenericAddressFieldProps } from '../Generic';

export const MailingAddressField: ComponentConfig<GenericAddressFieldProps> = {
  ...GenericAddressField,
  defaultProps: {
    type: 'address',
    key: PartnerReportingKeys.MailingAddress,
    validation: DefaultValidationValues,
  },
};
