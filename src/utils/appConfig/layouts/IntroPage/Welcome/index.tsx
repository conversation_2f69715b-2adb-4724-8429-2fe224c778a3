import Markdown from '@components/markdown/Markdown';
import { Box, Stack, Typography } from '@mui/material';

export function WelcomeLayout(page) {
  return (
    <Box sx={{ px: 7, pt: 13, pb: 10, m: 2, minHeight: 600 }}>
      <Typography
        variant="h1"
        component="h2"
        sx={{ fontSize: '2.5rem', fontWeight: 500, textAlign: 'center', mb: 4 }}
      >
        {page?.title}
      </Typography>
      <Stack spacing={8} direction="row" alignItems="center">
        <Box sx={{ width: '50%', height: 'auto' }}>
          <Typography variant="h5" sx={{ marginLeft: 'auto' }}>
            [ Partner logo ]
          </Typography>
        </Box>
        <Stack spacing={8} sx={{ width: '50%' }}>
          {page?.body && <Markdown sx={{ fontSize: 20 }}>{page.body}</Markdown>}
        </Stack>
      </Stack>
    </Box>
  );
}
