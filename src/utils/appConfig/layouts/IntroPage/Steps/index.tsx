import Markdown from '@components/markdown/Markdown';
import { Box, Stack, Typography } from '@mui/material';

export function StepsLayout(page) {
  return (
    <Box sx={{ px: 7, pt: 13, pb: 10, m: 2, minHeight: 600 }}>
      <Typography
        variant="h1"
        component="h2"
        sx={{ fontSize: '2.5rem', fontWeight: 500, textAlign: 'center', mb: 4 }}
      >
        {page?.title}
      </Typography>
      <Stack spacing={8}>
        {page?.copy && <Markdown sx={{ fontSize: 20 }}>{page.copy}</Markdown>}
      </Stack>
      {page?.steps.length && (
        <ul className="flex flex-col list-none gap-y-4">
          {page.steps.map(({ iconColor, icon, title, description }, index) => (
            <li
              className="flex flex-col"
              key={`step-${
                // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
                index
              }`}
            >
              <div className="flex items-center gap-x-3">
                <Typography>
                  Icon: {iconColor} {icon}
                </Typography>
                {title}
              </div>
              {description && (
                <div className="ml-9">
                  <Markdown>{description}</Markdown>
                </div>
              )}
            </li>
          ))}
        </ul>
      )}
    </Box>
  );
}
