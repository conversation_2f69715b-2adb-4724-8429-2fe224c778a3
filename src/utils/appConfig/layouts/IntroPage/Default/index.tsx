import Markdown from '@components/markdown/Markdown';
import { Box, Stack, Typography } from '@mui/material';

export function DefaultLayout(page) {
  return (
    <Box sx={{ px: 7, pt: 13, pb: 10, m: 2, minHeight: 600 }}>
      <Typography
        variant="h1"
        component="h2"
        sx={{ fontSize: '2.5rem', fontWeight: 500, textAlign: 'center', mb: 4 }}
      >
        {page?.title}
      </Typography>
      <Stack spacing={8} direction="row" alignItems="center">
        <Stack spacing={8}>
          {page?.body && <Markdown sx={{ fontSize: 20 }}>{page.body}</Markdown>}
        </Stack>
      </Stack>
    </Box>
  );
}
