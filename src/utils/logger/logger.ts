import type { IncomingMessage, ServerResponse } from 'node:http';
import pino, {
  type BaseLogger,
  type Level,
  type Logger,
  type LoggerOptions,
  type SerializerFn,
} from 'pino';
import { gcpLogOptions } from 'pino-cloud-logging';
import httpDefault, { type HttpLogger, type Options as HttpLoggerOptions } from 'pino-http';
// @ts-ignore
const http = httpDefault.default;
import { defaultSerializers } from './serializers';

interface CreateLogger {
  environment?: string;
  level?: Level;
  serializers?: { [key: string]: SerializerFn };
}

function getHttpOptions(): HttpLoggerOptions {
  return {
    customLogLevel: (_, res: ServerResponse<IncomingMessage>, err: Error | undefined) => {
      if (res.statusCode >= 400 && res.statusCode < 500) return 'warn';
      if (res.statusCode >= 500 || err) return 'error';
      if (res.statusCode >= 300 && res.statusCode < 400) return 'silent';
      return 'info';
    },
    serializers: { ...defaultSerializers },
  };
}

function getOptions({
  environment = 'local',
  level = 'info',
}: Pick<CreateLogger, 'environment' | 'level'>) {
  return environment === 'local'
    ? { level, transport: { target: 'pino-pretty' } }
    : (gcpLogOptions() as LoggerOptions);
}

function createLogger({
  environment = 'local',
  level = 'info',
  serializers,
}: CreateLogger): Logger {
  const options = getOptions({ environment });
  // @ts-ignore
  return pino.default({
    ...options,
    level,
    serializers: {
      ...options.serializers,
      ...defaultSerializers,
      ...serializers,
    },
  });
}

export { http, createLogger, getOptions, getHttpOptions };
export type { BaseLogger, Logger, LoggerOptions, HttpLogger };
