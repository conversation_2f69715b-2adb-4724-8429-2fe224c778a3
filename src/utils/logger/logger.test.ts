import type { IncomingMessage, ServerResponse } from 'node:http';
import { describe, expect, it } from '@jest/globals';
import { createLogger, getHttpOptions, getOptions } from './logger';

describe('getOptions', () => {
  it('should set target to pretty if the env is local', () => {
    expect(getOptions({ environment: 'local' })).toEqual({
      level: 'info',
      transport: { target: 'pino-pretty' },
    });
  });
  it('should set the level based on the provided argument', () => {
    expect(getOptions({ level: 'trace', environment: 'local' })).toEqual({
      level: 'trace',
      transport: { target: 'pino-pretty' },
    });
  });
  it('should use gcp log options is the env is not local', () => {
    expect(getOptions({ environment: 'production' })).toEqual({
      base: {},
      formatters: {
        level: expect.any(Function),
        log: expect.any(Function),
      },
      messageKey: 'message',
      mixin: undefined,
    });
  });
});

describe('getHttpOptions', () => {
  it('should return a customLogLevel function and the default serializers', () => {
    expect(getHttpOptions()).toEqual({
      customLogLevel: expect.any(Function),
      serializers: {
        error: expect.any(Function),
        req: expect.any(Function),
        res: expect.any(Function),
      },
    });
  });
  describe('customLogLevel', () => {
    it('the log level should be info by default', () => {
      expect(
        getHttpOptions().customLogLevel(
          {} as IncomingMessage,
          { statusCode: 200 } as ServerResponse<IncomingMessage>,
          undefined as Error,
        ),
      ).toEqual('info');
    });
    it('the log level should be warn for 4xx', () => {
      expect(
        getHttpOptions().customLogLevel(
          {} as IncomingMessage,
          { statusCode: 401 } as ServerResponse<IncomingMessage>,
          undefined as Error,
        ),
      ).toEqual('warn');
    });
    it('the log level should be error for 5xx', () => {
      expect(
        getHttpOptions().customLogLevel(
          {} as IncomingMessage,
          { statusCode: 500 } as ServerResponse<IncomingMessage>,
          undefined as Error,
        ),
      ).toEqual('error');
    });
    it('the log level should be error if an error is thrown', () => {
      expect(
        getHttpOptions().customLogLevel(
          {} as IncomingMessage,
          {} as ServerResponse<IncomingMessage>,
          { message: 'BAAAAD' } as Error,
        ),
      ).toEqual('error');
    });
    it('the log level should be silent for 3xx', () => {
      expect(
        getHttpOptions().customLogLevel(
          {} as IncomingMessage,
          { statusCode: 300 } as ServerResponse<IncomingMessage>,
          undefined as Error,
        ),
      ).toEqual('silent');
    });
  });
});

describe('createLogger', () => {
  it('should construct a usable logger', () => {
    const logger = createLogger({
      environment: 'production',
      serializers: { whatIsThisFor: () => null, somethingElse: () => null },
    });
    expect(logger.info).toEqual(expect.any(Function));
  });
});
