import { describe, expect, it } from '@jest/globals';
import type { SerializedError } from 'pino';
import { redactProperties } from './serializers';

describe('redactProperties', () => {
  it('should remove any properties from the error object that are in the REDACT_LIST', () => {
    expect(
      redactProperties({
        type: 'request',
        message: 'something went wrong',
        stack: 'long call stack',
        config: 'this is a senstive field',
        raw: new Error('bad'),
      }),
    ).toEqual({
      type: 'request',
      message: 'something went wrong',
      stack: 'long call stack',
      config: 'REDACTED',
      raw: new Error('bad'),
    });
  });
  it('should handle nested objects', () => {
    expect(
      redactProperties({
        level1: { level2: { config: 'this is a senstive field' } },
      } as unknown as SerializedError),
    ).toEqual({
      level1: { level2: { config: 'REDACTED' } },
    });
  });
  it('should handle nested arrays', () => {
    expect(
      redactProperties({
        level1: [{ level2: { config: 'this is a senstive field' } }],
      } as unknown as SerializedError),
    ).toEqual({ level1: [{ level2: { config: 'REDACTED' } }] });
  });
  it('should leave in extended properties that are not in the REDACT_LIST', () => {
    expect(
      redactProperties({
        type: 'request',
        message: 'something went wrong',
        stack: 'long call stack',
        normal: 'this is not a senstive field',
        raw: new Error('bad'),
      }),
    ).toEqual({
      type: 'request',
      message: 'something went wrong',
      stack: 'long call stack',
      normal: 'this is not a senstive field',
      raw: new Error('bad'),
    });
  });
});
