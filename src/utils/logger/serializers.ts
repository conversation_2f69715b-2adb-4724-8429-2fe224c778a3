import {
  type SerializedError,
  type SerializedRequest,
  type SerializedResponse,
  stdSerializers,
} from 'pino';

// Add keys here that should be removed from logs
const BASE_REDACT_LIST = [
  'authorization',
  'config',
  'cookie',
  'key',
  'secret',
  'token',
  'x-gcip-token',
];
// biome-ignore lint/suspicious/noExplicitAny: can be anything tossed into the logger
type ExpectedAny = any;

function isArray(value: ExpectedAny): value is ExpectedAny[] {
  return Array.isArray(value);
}

function isObject(value: ExpectedAny): value is { [key: string]: ExpectedAny } {
  return (
    typeof value === 'object' && !isArray(value) && value !== null && !(value instanceof Error)
  );
}

// TODO filter callstack and raw error as well?
function redact(entry: [string, ExpectedAny]): [string, string | number | object | string[]] {
  const [key, value] = entry;
  if (BASE_REDACT_LIST.includes(key)) return [key, 'REDACTED'];
  if (isArray(value))
    return [
      key,
      value.map((entry, idx: number) => {
        const [_, value] = redact([idx.toString(), entry]);
        return value;
      }),
    ];

  if (isObject(value)) return [key, Object.fromEntries(Object.entries(value).map(redact))];
  return [key, value];
}

export function redactProperties<
  T extends SerializedError | SerializedRequest | SerializedResponse,
>(payload: T): T {
  return Object.fromEntries(Object.entries(payload).map(redact)) as T;
}

export const defaultSerializers = {
  error: stdSerializers.wrapErrorSerializer(redactProperties<SerializedError>),
  req: stdSerializers.wrapRequestSerializer(redactProperties<SerializedRequest>),
  res: stdSerializers.wrapResponseSerializer(redactProperties<SerializedResponse>),
};
