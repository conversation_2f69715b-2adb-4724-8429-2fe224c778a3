import type { UseMutateFunction } from '@tanstack/react-query';

export default function promisify<
  TData = unknown,
  TError = unknown,
  TVariables = void,
  TContext = unknown,
>(
  fn: UseMutateFunction<TData, TError, TVariables, TContext>,
  variables: TVariables,
): Promise<TData> {
  return new Promise((resolve, reject) =>
    fn(variables, {
      onSuccess: resolve,
      onError: reject,
    }),
  );
}
