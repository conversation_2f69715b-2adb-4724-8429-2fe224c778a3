import { describe, expect, it } from '@jest/globals';
import { convertToCents } from './currency';

describe('convertToCents', () => {
  it('should convert currency string including $ sign to cent', () => {
    expect(convertToCents('$22')).toBe('2200');
    expect(convertToCents('$22.00')).toBe('2200');
    expect(convertToCents('$22.05')).toBe('2205');
    expect(convertToCents('$22.55')).toBe('2255');
    expect(convertToCents('$22.5')).toBe('2250');
  });
  it('should convert currency string without $ sign to cent', () => {
    expect(convertToCents('1022')).toBe('102200');
    expect(convertToCents('1020.0')).toBe('102000');
    expect(convertToCents('1022.00')).toBe('102200');
    expect(convertToCents('1022.05')).toBe('102205');
    expect(convertToCents('1022.55')).toBe('102255');
    expect(convertToCents('1022.5')).toBe('102250');
  });
  it('should return 0', () => {
    expect(convertToCents('$0.00')).toBe('0');
    expect(convertToCents('0.00')).toBe('0');
    expect(convertToCents('0')).toBe('0');
    expect(convertToCents()).toBe('0');
  });
  it('should throw an error for invalid dollar amounts', () => {
    expect(() => convertToCents('$1670.999')).toThrow('Cannot convert $1670.999 to cents');
    expect(() => convertToCents('$1670.')).toThrow('Cannot convert $1670. to cents');
    expect(() => convertToCents('invalid input')).toThrow('Cannot convert invalid input to cents');
    expect(() => convertToCents('$')).toThrow('Cannot convert $ to cents');
  });
});
