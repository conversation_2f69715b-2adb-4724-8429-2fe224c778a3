export const COMMON_VARIABLES = ['PARTNER_NAME', 'PARTNER_CONTACT', 'USIO_CONTACT'];

// TODO: store this in the db?
export const TEMPLATE_VARIABLES = {
  AuthenticationChangePrimaryEmail: ['TOKEN', 'TOKEN_LIFETIME'],
  AuthenticationResetPassword: ['EMAIL', 'TOKEN'],
  AuthenticationVerifyEmail: ['TOKEN', 'TOKEN_LIFETIME'],

  ApplicationApproved: ['PROGRAM_NAME', 'APPROVED_AMOUNT', 'APPROVED_AMOUNT_MSG'],
  ApplicationDenied: ['PROGRAM_NAME', 'DENIAL_REASON'],
  ApplicationIncomplete: ['PROGRAM_NAME', 'INCOMPLETE_REASON'],
  ApplicationSubmitted: ['PROGRAM_NAME'],
  ApplicationWithdrawn: ['PROGRAM_NAME', 'WITHDRAWAL_REASON'],

  PaymentsClaimFunds: ['PROGRAM_NAME', 'APPROVED_AMOUNT', 'FUND_NAME'],
  PaymentsFundsClaimed: ['PROGRAM_NAME', 'PAYMENT_METHOD', 'FUND_NAME'],
  PaymentsFundsIssued: ['PROGRAM_NAME', 'PAYMENT_METHOD', 'APPROVED_AMOUNT', 'FUND_NAME'],
  PaymentSentToOtherParty: [
    'PROGRAM_NAME',
    'IS_CLAIM_FUNDS',
    'APPROVED_AMOUNT',
    'PAYEE_NAME',
    'FUND_NAME',
  ],
  PaymentsApplicantPaymentReset: ['PROGRAM_NAME', 'FAILURE_REASON', 'FUND_NAME'],
  PaymentsAdvocatePaymentReset: [
    'PROGRAM_NAME',
    'PAYEE_NAME',
    'APPLICANT_NAME',
    'FAILURE_REASON',
    'PROGRAM_ID',
    'CASE_ID',
    'FUND_NAME',
  ],
  PaymentsRecurringPaymentInitiated: [
    'PROGRAM_NAME',
    'PAYMENT_INITIATED',
    'PAYMENT_FREQUENCY',
    'PAYMENT_METHOD',
    'PAYEE_NAME',
    'FUND_NAME',
  ],
  PaymentsRecurringPaymentsScheduled: [
    'PROGRAM_NAME',
    'PAYMENT_FREQUENCY',
    'PAYMENT_METHOD',
    'FIRST_PAYMENT_DATE',
    'FUND_NAME',
  ],
  PaymentsRecurringPaymentsScheduledOtherParty: [
    'PROGRAM_NAME',
    'PAYMENT_FREQUENCY',
    'PAYMENT_METHOD',
    'PAYEE_NAME',
    'FIRST_PAYMENT_DATE',
    'FUND_NAME',
  ],

  MultipartyInvitation: ['PROGRAM_NAME', 'INVITER_TYPE', 'CODE'],

  ProgramReferral: ['PROGRAM_NAME', 'PENDING_APPLICATIONS_MESSAGE'],

  CaseComment: ['PROGRAM_NAME', 'CONTENT'],

  ParticipantCaseCommentAssignee: [
    'PROGRAM_NAME',
    'PROGRAM_ID',
    'CONTENT',
    'AUTHOR',
    'AUTHOR_DISPLAY_ID',
    'CASE_ID',
    'CASE_DISPLAY_ID',
  ],
  ParticipantCaseCommentSupport: [
    'PROGRAM_NAME',
    'PROGRAM_ID',
    'CONTENT',
    'AUTHOR',
    'AUTHOR_DISPLAY_ID',
    'CASE_ID',
    'CASE_DISPLAY_ID',
  ],
};

export interface NotificationAction {
  url: string;
  domain?: 'Applicant' | 'Partner';
  text: string;
}

export interface BaseContent {
  preText: string;
  postText?: string;
}

export interface SmsTemplateContent extends BaseContent {
  link?: NotificationAction;
}

export interface EmailTemplateContent extends BaseContent {
  subject: string;
  button?: NotificationAction;
}

export type TemplateContent = SmsTemplateContent | EmailTemplateContent;

export interface NotificationTemplate {
  type: string;
  channel: string;
  content: TemplateContent;
}

export enum NotificationChannel {
  EMAIL = 'email',
  SMS = 'sms',
}
