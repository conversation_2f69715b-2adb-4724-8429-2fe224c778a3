import type { Config } from '@measured/puck';
import {
  Box,
  FormControl,
  FormControlLabel,
  FormLabel,
  InputLabel,
  MenuItem,
  Paper,
  Radio,
  RadioGroup,
  Select,
  TextField,
} from '@mui/material';
import Markdown from 'react-markdown';
import {
  type Props,
  type RootProps,
  amiDefaultValues,
  dropdownDefaultValues,
  radioDefaultValues,
  textDefaultValues,
} from 'types/eligibilityConfig';

const componentStyles = { py: 2, px: 4, backgroundColor: '#F7F7FB' };

export const eligibilityConfig: Config<Props, RootProps> = {
  root: {
    render: ({ children }: RootProps) => (
      <Box
        sx={{
          maxWidth: '44rem',
          m: 'auto',
          my: 4,
          borderRadius: 1,
        }}
      >
        {children}
      </Box>
    ),
  },
  categories: {
    page: {
      components: ['title', 'copy'],
    },
    form: {
      components: ['amicomponent', 'content', 'dropdown', 'text_input', 'yes_or_no'],
    },
  },
  components: {
    title: {
      fields: {
        title: { type: 'text' },
      },
      render: ({ title }) => {
        return <h1 style={{ textAlign: 'center' }}>{title}</h1>;
      },
    },
    copy: {
      fields: {
        copy: { type: 'textarea' },
      },
      defaultProps: {
        copy: 'Text',
      },
      render: ({ copy }) => {
        return (
          <Box sx={{ textAlign: 'center' }}>
            <Markdown>{copy}</Markdown>
          </Box>
        );
      },
    },
    message: {
      fields: {
        ineligible: { type: 'textarea' },
        eligible: { type: 'textarea' },
      },
      render: ({ ineligible, eligible }) => {
        return (
          <Box sx={{ textAlign: 'center' }}>
            <Markdown>{ineligible}</Markdown>
            <hr />
            <Markdown>{eligible}</Markdown>
          </Box>
        );
      },
    },
    content: {
      fields: {
        content: { type: 'text' },
      },
      defaultProps: {
        content: 'Text',
      },
      render: ({ content }) => {
        return (
          <Box sx={componentStyles}>
            <span>{content}</span>
          </Box>
        );
      },
    },
    dropdown: {
      fields: {
        copy: { type: 'text' },
        items: {
          type: 'array',
          arrayFields: {
            label: { type: 'text' },
            value: { type: 'text' },
          },
        },
        label: { type: 'text' },
        answer: { type: 'text' },
      },
      defaultProps: dropdownDefaultValues,
      render: ({ copy, items, label }) => {
        return (
          <Box sx={componentStyles}>
            <span>{copy}</span>
            <FormControl fullWidth>
              <InputLabel>{label}</InputLabel>
              <Select>
                <MenuItem disabled value="">
                  <em>{label}</em>
                </MenuItem>
                {items?.map(({ label, value }) => {
                  return (
                    <MenuItem key={value} value={value}>
                      {label}
                    </MenuItem>
                  );
                })}
              </Select>
            </FormControl>
          </Box>
        );
      },
    },
    amicomponent: {
      fields: {
        label: { type: 'text' },
        state: { type: 'text' },
        county: {
          type: 'array',
          getItemSummary: (item) => {
            const itemObject = item as { name: string };
            const county = {
              name: typeof item === 'object' ? itemObject?.name : (item as string) || 'county',
            };
            return county?.name || 'county';
          },
          arrayFields: {
            name: { type: 'text' },
          },
        },
      },
      defaultProps: amiDefaultValues,
      render: ({ label }) => {
        return (
          <Box sx={componentStyles}>
            <span>{label}</span>
            <Paper elevation={3} sx={{ p: 4 }}>
              AMI Component
            </Paper>
          </Box>
        );
      },
    },
    yes_or_no: {
      fields: {
        copy: { type: 'text' },
        answer: {
          type: 'radio',
          options: [
            { label: 'true', value: true },
            { label: 'false', value: false },
          ],
        },
      },
      defaultProps: radioDefaultValues,
      render: ({ copy, answer }) => {
        return (
          <Box sx={componentStyles}>
            <FormControl>
              <FormLabel>{copy}</FormLabel>
              <RadioGroup
                value={answer}
                aria-labelledby="radio-buttons-group"
                defaultValue={answer}
                name="radio-buttons-group"
              >
                <FormControlLabel value={true} control={<Radio />} label="Yes" />
                <FormControlLabel value={false} control={<Radio />} label="No" />
              </RadioGroup>
            </FormControl>
          </Box>
        );
      },
    },
    text_input: {
      fields: {
        label: { type: 'text' },
        copy: { type: 'text' },
        answer: { type: 'text' },
      },
      defaultProps: textDefaultValues,
      render: ({ label, copy }) => (
        <Box sx={componentStyles}>
          <TextField
            id="outlined-basic"
            label={copy}
            defaultValue={label}
            fullWidth
            variant="outlined"
          />
        </Box>
      ),
    },
  },
};

export function flattenObjectProps(props) {
  if (typeof props !== 'object' || props === null) {
    return {};
  }

  const result: Record<string, unknown> = {};

  for (const key in props) {
    if (props[key]) {
      if (typeof props[key] === 'object' && !Array.isArray(props[key])) {
        const nestedProps = flattenObjectProps(props[key]);
        for (const nestedKey in nestedProps) {
          if (nestedProps[nestedKey]) {
            result[nestedKey] = nestedProps[nestedKey];
          }
        }
      } else {
        result[key] = props[key];
      }
    }
  }

  return result;
}

export function parsePuckId(id): string {
  return id?.split('_')?.[0] || '';
}

export function normalizeProps(props: Record<string, unknown>, type: string) {
  const answerProp = props?.answer?.toString() || undefined;
  const countyProp = (props?.county as unknown[]) || [];
  const normalizedCounties = countyProp
    .map((county) => {
      if (typeof county === 'object') {
        return (county as { name: string }).name;
      }
      return county;
    })
    .filter((county) => county);
  const hasLimitFilter = type.includes('ami');
  const limitFilter = {
    state: props?.state,
    county: normalizedCounties.length > 0 ? normalizedCounties : undefined,
  };
  return {
    ...props,
    id: undefined,
    copy: undefined,
    state: undefined,
    county: undefined,
    answer: answerProp?.includes(',') ? answerProp.split(',') : answerProp,
    limitFilter: hasLimitFilter ? limitFilter : undefined,
  };
}
