declare global {
  interface Window {
    publicConfig: object;
  }
}

if (typeof window !== 'undefined') window.publicConfig;

/** Simple utility function set and get the public runtime config */
export const getConfig = (variable: string): string | undefined => {
  if (typeof window === 'undefined') {
    return '';
  }

  if (!window.publicConfig) {
    fetch('/api/config')
      .then((response) => response.json())
      .then((config) => {
        window.publicConfig = config;

        // TODO: This isn't doing anything
        return window.publicConfig[variable];
      });
  } else {
    return window.publicConfig[variable];
  }
};
