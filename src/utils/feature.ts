import type { Features, ProgramFeatures } from '@prisma/generated/platformClient';
import type { FeatureName } from 'hooks/features/types';

type Feature = ProgramFeatures & { feature: Features };

function checkFeature(features: Feature[] | undefined, featureName: FeatureName): boolean {
  return !!features?.find((f) => f.feature.name === featureName && f.enabled === true);
}

function checkFeatures(features: Feature[] | undefined, featureNames: FeatureName[]): boolean {
  return featureNames.every((featureName) => checkFeature(features, featureName));
}

export { checkFeature, checkFeatures };
