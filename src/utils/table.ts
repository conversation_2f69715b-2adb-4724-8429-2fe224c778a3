export const sortErrorsToTop = ({
  rows,
  errors,
  accessor,
}: {
  rows: Record<string, unknown>[];
  errors: Record<string, string>;
  accessor: string;
}): Record<string, unknown>[] => {
  const errorRows: Record<string, unknown>[] = [];
  const cleanRows: Record<string, unknown>[] = [];
  for (const row of rows) {
    if (errors[String(row[accessor])]) {
      errorRows.push(row);
    } else {
      cleanRows.push(row);
    }
  }
  return errorRows.concat(cleanRows);
};
