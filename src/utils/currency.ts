const nf = Intl.NumberFormat('en-US', {
  style: 'currency',
  currency: 'USD',
  minimumFractionDigits: 2,
  maximumFractionDigits: 2,
});

export const displayCurrency = (cents: number): string => nf.format(Number(cents / 100));

export const centsToDollars = (cents: number): string => (cents / 100).toFixed(2);

export const dollarsToCents = (dollars: number): number => Math.round(dollars * 100);

export const convertToCents = (dollarAmount?: string): string => {
  if (!dollarAmount) return '0';
  const regex = /^\$?(\d+)(?:\.(\d{1,2}))?$/;
  const match = dollarAmount.match(regex);
  if (match) {
    const dollars = Number.parseInt(match[1], 10);
    const cents = match[2] ? Number.parseInt(match[2].padEnd(2, '0'), 10) : 0; // Default to 0 if no cents
    return (dollars * 100 + cents).toString();
  }
  throw new Error(`Cannot convert ${dollarAmount} to cents`);
};
