import { describe, expect, it } from '@jest/globals';
import { parseAddress } from './address';

describe('parseAddress', () => {
  it('should parse address', () => {
    expect(parseAddress('4296 W 7th St, Long Beach, CA 90802')).toEqual({
      addressLine1: '4296 W 7th St',
      city: 'Long Beach',
      state: 'CA',
      zip: '90802',
    });

    expect(parseAddress('1842 W Washington Blvd, Los Angeles, CA 90007, US')).toEqual({
      addressLine1: '1842 W Washington Blvd',
      city: 'Los Angeles',
      state: 'CA',
      zip: '90007',
    });
  });
});
