import dayjs from 'dayjs';

export function getPaymentNumber(fulfillment, payment): number {
  if (!fulfillment.paymentsList?.length) return 0;
  const sortedPayments = [...fulfillment.paymentsList]
    .filter((each) => !each.deactivatedAt)
    .sort((p1, p2) => dayjs(p1.scheduledFor).unix() - dayjs(p2.scheduledFor).unix());
  return sortedPayments.findIndex(({ id }) => id === payment.id) + 1;
}

export function getRemainingPayments(fulfillment) {
  return fulfillment.payments.filter((payment) => !payment.deactivatedAt && !payment.initiatedAt);
}

export function getAccountValue(value) {
  const keys = value?.keys;
  if (keys?.cardId) return `Prepaid ID: ${keys.cardId}`;
  if (keys?.externalAccountAliasId) return `External ID: ${keys.externalAccountAliasId}`;
  return '';
}

export function isPrepaidCard(method: string) {
  return ['PHYSICAL_CARD', 'VIRTUAL_CARD'].includes(method);
}
