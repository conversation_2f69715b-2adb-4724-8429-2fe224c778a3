import { describe, expect, it } from '@jest/globals';
import { omit, pick } from './set';

describe('pick', () => {
  it('should return the chosen keys from the object', () => {
    expect(pick({ test: true, other: 10 }, ['test'])).toEqual({ test: true });
  });
  it('should return undefined if the result is empty', () => {
    expect(pick({ test: true }, ['notHere'])).toBe(undefined);
  });
});

describe('omit', () => {
  it('should omit the chosen keys from the object when omit is true', () => {
    expect(omit({ test: true, other: 10 }, ['test'])).toEqual({ other: 10 });
  });
  it('should return undefined if the result is empty', () => {
    expect(omit({ test: true }, ['test'])).toBe(undefined);
  });
});
