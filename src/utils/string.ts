export const isEqual = (str1: string, str2: string) => {
  return (
    str1
      .toLowerCase()
      .replace(/&/g, 'and')
      .replace(/[^a-zA-Z0-9\s]/g, '')
      .replace(/[^\w]/g, '') ===
    str2
      .toLowerCase()
      .replace(/&/g, 'and')
      .replace(/[^a-zA-Z0-9\s]/g, '')
      .replace(/[^\w]/g, '')
  );
};

export const mask = (str: string, start?: number, end?: number): string =>
  str ? `• • • • • ${str.slice(start ?? -4, end)}` : '';
