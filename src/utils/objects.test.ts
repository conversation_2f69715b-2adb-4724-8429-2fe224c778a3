import { clean } from './object';

describe('objects', () => {
  describe('clean', () => {
    test('should remove empty array from an object', () => {
      expect(clean({ id: 'mockId', arr: [] })).toEqual({ id: 'mockId' });
      expect(clean({ id: 'mockId', nested: { id: 'mockNested', arr: [] } })).toEqual({
        id: 'mockId',
        nested: { id: 'mockNested' },
      });
      expect(
        clean({
          id: 'mockId',
          nested: { id: 'mockNested', nestedL2: { id: 'mockNestedL2', arr1: [], arr2: ['1'] } },
        }),
      ).toEqual({
        id: 'mockId',
        nested: { id: 'mockNested', nestedL2: { id: 'mockNestedL2', arr2: ['1'] } },
      });
    });
    test('should remove empty nested objects from an object', () => {
      expect(clean({ id: 'mockId', nested: undefined })).toEqual({ id: 'mockId' });
      expect(clean({ id: 'mockId', nested: null })).toEqual({ id: 'mockId' });
      expect(clean({ id: 'mockId', nested: false })).toEqual({ id: 'mockId', nested: false });
      expect(clean({ id: 'mockId', nested: {} })).toEqual({ id: 'mockId' });
    });
  });
});
