import { ArrowRight } from '@mui/icons-material';
import {
  Box,
  Button,
  Card,
  CardActions,
  CardContent,
  CardMedia,
  Divider,
  Grid,
  Typography,
} from '@mui/material';
import { type LoginFormTypes, useLogin } from '@refinedev/core';
import { isLocal } from '@utils/env';
import { mockUsers } from 'lib/authProvider';
import type { GetServerSideProps } from 'next';

const mockRoleSelector = (login) => {
  return (
    <>
      <CardContent>
        DevMode Login: Select a role to impersonate
        <Divider />
        <Box sx={{ display: 'grid', gap: 1, gridTemplateColumns: 'repeat(2, 1fr)', mt: 1 }}>
          {Object.keys(mockUsers).map((key) => {
            return (
              <Button
                key={mockUsers[key].email}
                variant="outlined"
                color="primary"
                onClick={() => login({ mockUserRole: key, redirectPath: '/' })}
              >
                {mockUsers[key].name}
              </Button>
            );
          })}
        </Box>
      </CardContent>
    </>
  );
};

export default function Login({ loggedInEmail }) {
  const { mutate: login } = useLogin<LoginFormTypes>();

  const handleLogin = (event) => {
    event.preventDefault();
    login({ providerName: 'iap', redirectPath: '/' });
  };
  const showMockRoles = isLocal();
  return (
    <Grid
      container
      spacing={0}
      direction="column"
      alignItems="center"
      justifyContent="center"
      style={{ minHeight: '100vh' }}
    >
      <Card variant="elevation">
        {!showMockRoles && (
          <>
            <CardMedia
              component="img"
              width="300"
              height="200"
              src="https://source.unsplash.com/collection/11649432/600x400"
            />
            <CardContent>
              <Typography gutterBottom variant="body1" component="div">
                Welcome, <strong>{loggedInEmail}</strong>!
              </Typography>
            </CardContent>
            <CardActions>
              <form onSubmit={handleLogin}>
                <Button variant="contained" color="primary" type="submit">
                  Continue <ArrowRight />
                </Button>
              </form>
            </CardActions>
          </>
        )}
        {showMockRoles && mockRoleSelector(login)}
      </Card>
    </Grid>
  );
}

Login.noLayout = true;

// biome-ignore lint/complexity/noBannedTypes: IGNORE_THIS
export const getServerSideProps: GetServerSideProps<{}> = async (context) => {
  const emailHeader = context.req.headers['x-goog-authenticated-user-email'] as string;
  const loggedInEmail = emailHeader ? emailHeader.replace('accounts.google.com:', '') : '';

  return {
    props: {
      loggedInEmail,
    },
  };
};
