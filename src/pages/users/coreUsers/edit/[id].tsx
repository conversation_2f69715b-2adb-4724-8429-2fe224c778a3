import CheckboxInput from '@components/forms/CheckboxInput';
import HiddenInput from '@components/forms/HiddenInput';
import SelectInput from '@components/forms/SelectInput';
import TextInput from '@components/forms/TextInput';
import { Alert, Stack } from '@mui/material';
import { CanAccess, useParsed } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import useAvailableApplicantTypes from 'hooks/applicantTypes/useAvailableApplicantTypes';
import { FormProvider } from 'react-hook-form';

export default function EditUser() {
  const { id: userId } = useParsed();
  const formProps = useForm({
    refineCoreProps: {
      dataProviderName: 'default',
      resource: 'users',
      action: 'edit',
      id: userId,
      meta: {
        fields: [
          'id',
          'name',
          'email',
          'validatedEmail',
          'legacyId',
          'phone',
          { partner: ['id', 'parentId'] },
          { applicantProfile: ['id', 'applicantTypeId'] },
          {
            applicationsBySubmitterIdList: [
              'id',
              {
                case: [
                  'id',
                  {
                    program: [
                      'id',
                      'name',
                      { programApplicantTypesList: ['id', 'applicantTypeId'] },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
      redirect: 'list',
    },
  });

  const {
    refineCore: { queryResult, redirect },
    handleSubmit,
    getValues,
    setError,
  } = formProps;
  const user = queryResult?.data?.data;
  const types = useAvailableApplicantTypes(user?.partner, true);

  const onSubmit = async (e): Promise<void> => {
    const { applicantProfile } = getValues();
    if (user?.applicationsBySubmitterIdList?.length && applicantProfile?.applicantTypeId) {
      const programs = user?.applicationsBySubmitterIdList.map((app) => app.case.program);
      const noTypePrograms = programs?.filter(
        (program) =>
          !program?.programApplicantTypesList?.find(
            (type) => type?.applicantTypeId === applicantProfile.applicantTypeId,
          ),
      );
      if (noTypePrograms?.length) {
        setError('applicantProfile.applicantTypeId', {
          type: 'manual',
          message: `The user has some applications and the related programs don't support the selected type: ${noTypePrograms
            .map((program) => program.name)
            .join(', ')}`,
        });
        return;
      }
    }
    return formProps.saveButtonProps.onClick(e);
  };

  return (
    <CanAccess resource="core_users" action="edit">
      <Edit
        resource="core_users"
        saveButtonProps={{
          ...formProps.saveButtonProps,
          onClick: handleSubmit(onSubmit),
        }}
      >
        <FormProvider {...formProps}>
          <Stack component="form" autoComplete="off" sx={{ gap: 2 }}>
            <TextInput name="name" label="Name" required />
            <TextInput name="email" label="Email" type="email" required />
            <TextInput name="legacyId" label="Legacy ID" />
            <CheckboxInput name="validatedEmail" label="Validated Email" />
            <TextInput name="phone" label="Phone" type="tel" />
            {types?.length > 1 && user?.applicantProfile?.id && (
              <>
                <HiddenInput name="applicantProfile.id" value={user?.applicantProfile?.id} />
                <SelectInput
                  name="applicantProfile.applicantTypeId"
                  label="Applicant Type"
                  options={types}
                  value={user?.applicantProfile?.applicantTypeId}
                />
                <Alert severity="warning">
                  Please note that changing the applicant type could have consequences for any
                  existing applications that have been submitted by the user, if the new applicant
                  type are not compatible.
                </Alert>
              </>
            )}
          </Stack>
        </FormProvider>
      </Edit>
    </CanAccess>
  );
}
