import PaymentsList from '@components/payments/PaymentsList';
import UpdatePrepaidCardDrawer from '@components/payments/UpdatePrepaidCardDrawer';
import DeleteUserButton from '@components/users/DeleteUserButton';
import DeleteUserDrawer from '@components/users/DeleteUserDrawer';
import SendSmsDrawer from '@components/users/SendSmsDrawer';
import WorkflowEventsList from '@components/workflows/WorkflowEventsList';
import { ChatBubble, CheckCircleOutline, WarningAmber } from '@mui/icons-material';
import EditIcon from '@mui/icons-material/Edit';
import {
  Button,
  Divider,
  IconButton,
  Link,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Stack,
  Typography,
} from '@mui/material';
import {
  type BaseKey,
  type BaseRecord,
  CanAccess,
  useBack,
  useModal,
  useShow,
} from '@refinedev/core';
import { EmailField, Show } from '@refinedev/mui';
import useCommunicationConfiguration from 'hooks/communicationConfigurations/useCommunicationConfiguration';
import usePaymentAccounts from 'hooks/payments/usePaymentAccounts';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { CommunicationChannels } from 'types/notification';

export default function ShowUser() {
  const [selectedAccount, setSelectedAccount] = useState<BaseRecord>();
  const router = useRouter();
  const { id: userId } = router.query;

  const {
    queryResult: { data, isLoading },
  } = useShow({
    dataProviderName: 'default',
    resource: 'users',
    id: userId as BaseKey,
    meta: {
      fields: [
        'id',
        'name',
        'legacyId',
        'email',
        'validatedEmail',
        'phone',
        { applicantProfile: ['id', { applicantType: ['id', 'name'] }] },
        { partner: ['id', 'name'] },
        { adminsList: ['id'] },
        { applicationsBySubmitterId: ['totalCount'] },
      ],
    },
  });

  const user = data?.data;
  const { configuration } = useCommunicationConfiguration(user?.partner?.id);
  const channels = configuration?.config?.channels || {};
  const isSmsAvailable = channels?.[CommunicationChannels.SMS] === true && user?.phone;

  const updatePrepaidCardDrawer = useModal();
  const sendSmsDrawer = useModal();
  const deleteUserDrawer = useModal();
  const back = useBack();

  const { paymentAccounts, refetch } = usePaymentAccounts({
    filters: [
      {
        field: 'recipient.referenceId',
        operator: 'eq',
        value: userId,
      },
      {
        field: 'type',
        operator: 'in',
        value: ['PHYSICAL_CARD', 'VIRTUAL_CARD'],
      },
    ],
    enabled: !!userId,
  });

  return (
    <CanAccess resource="core_users" action="show">
      <>
        <Show
          resource="core_users"
          isLoading={isLoading}
          title={<Typography variant="h5">{user?.name}</Typography>}
          headerButtons={({ defaultButtons }) => {
            return (
              <>
                {isSmsAvailable ? (
                  <Button onClick={sendSmsDrawer.show} startIcon={<ChatBubble />}>
                    Send SMS
                  </Button>
                ) : null}
                <DeleteUserButton
                  user={user}
                  openDrawer={() => {
                    deleteUserDrawer.show();
                  }}
                />
                {defaultButtons}
              </>
            );
          }}
        >
          <Stack gap={1}>
            <List
              sx={{
                width: '100%',
                bgcolor: 'background.paper',
                display: 'grid',
                gridTemplateColumns: '1fr 1fr 1fr',
              }}
            >
              <ListItem>
                <ListItemText primary={user?.name} secondary="Name" />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary={
                    <Link href={`/partners/show/${user?.partner.id}`}>{user?.partner.name}</Link>
                  }
                  secondary="Partner"
                />
              </ListItem>
              <ListItem>
                <ListItemText primary={user?.id} secondary="UUID" />
              </ListItem>
              <ListItem>
                <ListItemText primary={<EmailField value={user?.email} />} secondary="Email" />
              </ListItem>
              <ListItem>
                <ListItemText primary={user?.phone} secondary="Phone" />
              </ListItem>
              <ListItem>
                <ListItemText primary={user?.legacyId} secondary="Legacy ID" />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary={user?.applicantProfile?.applicantType?.name}
                  secondary="Applicant Type"
                />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary={
                    user?.validatedEmail ? (
                      <CheckCircleOutline color="success" />
                    ) : (
                      <WarningAmber color="warning" />
                    )
                  }
                  secondary="Validated Email"
                />
              </ListItem>
            </List>
            {!!paymentAccounts?.length && (
              <List
                sx={{
                  width: '100%',
                  bgcolor: 'background.paper',
                  display: 'grid',
                  gridTemplateColumns: '1fr 1fr 1fr',
                }}
              >
                {paymentAccounts?.map((account) => (
                  <ListItem key={account.id}>
                    <ListItemText
                      primary={account?.keys?.cardId}
                      secondary={`Prepaid Card ID (${account.type})`}
                    />
                    <ListItemIcon>
                      <IconButton
                        onClick={() => {
                          setSelectedAccount(account);
                          updatePrepaidCardDrawer.show();
                        }}
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                    </ListItemIcon>
                  </ListItem>
                ))}
              </List>
            )}
            {user?.id && (
              <>
                <Divider />
                <PaymentsList filter={{ applicantId: user.id as string }} />
                <PaymentsList filter={{ applicantId: user.id as string, deactivatedAt: true }} />
                <Divider />
                <WorkflowEventsList filter={{ entityId: user.id, entityType: 'user' }} />
                <SendSmsDrawer userId={user?.id} {...sendSmsDrawer} />
              </>
            )}
          </Stack>
        </Show>
        {!!selectedAccount && (
          <UpdatePrepaidCardDrawer
            {...updatePrepaidCardDrawer}
            account={selectedAccount}
            close={() => {
              updatePrepaidCardDrawer.close();
              setSelectedAccount(undefined);
              refetch();
            }}
          />
        )}
        {deleteUserDrawer.visible && user && (
          <DeleteUserDrawer
            {...deleteUserDrawer}
            userId={user.id}
            close={(success = false) => {
              deleteUserDrawer.close();
              if (success) back();
            }}
          />
        )}
      </>
    </CanAccess>
  );
}
