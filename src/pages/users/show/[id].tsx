import PermissionsList from '@components/permissions/PermissionsList';
import AdvocatesList from '@components/users/AdvocatesList';
import ApplicantsList from '@components/users/ApplicantsList';
import { CheckCircleOutline, WarningAmber } from '@mui/icons-material';
import { List, ListItem, ListItemText, Stack, Typography } from '@mui/material';
import { CanAccess, useParsed, useShow } from '@refinedev/core';
import { EmailField, Show } from '@refinedev/mui';
import { Relation } from 'pages/api/types/identity';

export default function ShowUser() {
  const { id: userId } = useParsed();
  const {
    queryResult: { data, isLoading },
  } = useShow({
    dataProviderName: 'identity',
    resource: 'users',
    id: userId,
    meta: {
      fields: [
        'id',
        'name',
        'email',
        'phone',
        'createdAt',
        'deactivatedAt',
        'verifiedEmail',
        { advocatesList: ['id'] },
        { applicantsList: ['id'] },
      ],
    },
  });

  const user = data?.data;

  return (
    <CanAccess resource="users" action="show">
      <Show isLoading={isLoading} title={<Typography variant="h5">{user?.name}</Typography>}>
        <Stack gap={1}>
          <List
            sx={{
              width: '100%',
              bgcolor: 'background.paper',
              display: 'grid',
              gridTemplateColumns: '1fr 1fr 1fr',
            }}
          >
            <ListItem>
              <ListItemText primary={user?.name} secondary="Name" />
            </ListItem>
            <ListItem>
              <ListItemText primary={user?.id} secondary="UUID" />
            </ListItem>
            <ListItem>
              <ListItemText primary={<EmailField value={user?.email} />} secondary="Email" />
            </ListItem>
            <ListItem>
              <ListItemText primary={user?.phone} secondary="Phone" />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={
                  user?.verifiedEmail ? (
                    <CheckCircleOutline color="success" />
                  ) : (
                    <WarningAmber color="warning" />
                  )
                }
                secondary="Verified Email"
              />
            </ListItem>
          </List>
          {user?.id && (
            <>
              <AdvocatesList userId={user.id as string} />
              <ApplicantsList userId={user.id as string} />
              <PermissionsList
                subject={{ objectId: user.id as string, objectType: Relation.USER }}
              />
            </>
          )}
        </Stack>
      </Show>
    </CanAccess>
  );
}
