import AutocompleteInput from '@components/forms/AutocompleteInput';
import HiddenInput from '@components/forms/HiddenInput';
import TextInput from '@components/forms/TextInput';
import { Stack, Typography } from '@mui/material';
import { CanAccess, useOne } from '@refinedev/core';
import { Create } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import { defaultAppConfig } from '@utils/appConfig';
import { FormProvider } from 'react-hook-form';

export default function CreateAppConfig(): JSX.Element {
  const formProps = useForm({
    refineCoreProps: {
      resource: 'application_configurations',
      dataProviderName: 'config',
      redirect: 'show',
    },
  });

  const {
    handleSubmit,
    saveButtonProps: { onClick },
    getValues,
    setValue,
    unregister,
  } = formProps;

  const onSubmit = (_, e) => {
    const { existing_config } = getValues();

    setValue('config', existing_config?.config ?? { ...defaultAppConfig });
    unregister('existing_config');
    return onClick(e);
  };

  return (
    <CanAccess>
      <Create
        isLoading={formProps.refineCore.formLoading}
        saveButtonProps={{ onClick: handleSubmit(onSubmit) }}
        title={<Typography variant="h5">Create Application Config</Typography>}
      >
        <Stack direction="column" gap={2}>
          <FormProvider {...formProps}>
            <TextInput name="name" label="Name" required />
            <TextInput name="description" label="Description" required />
            <AutocompleteInput name="partnerId" label="Partner" resource="partners" />
            <AutocompleteInput
              name="existing_config"
              label="Use existing config"
              resource="application_configurations"
              dataProviderName="config"
              dataField="config"
              transformers={{
                from: (option) => option,
                id: (value: { id: string }) => value.id,
              }}
            />
            <HiddenInput name="config" />
          </FormProvider>
        </Stack>
      </Create>
    </CanAccess>
  );
}
