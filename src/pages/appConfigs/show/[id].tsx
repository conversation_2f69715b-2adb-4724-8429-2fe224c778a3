import ParticipantList from '@components/appConfigs/participants/ParticipantList';
import Link from '@components/navigation/Link';
import AutoFixHighIcon from '@mui/icons-material/AutoFixHigh';
import EditIcon from '@mui/icons-material/Edit';
import { Button, Divider, List, ListItem, ListItemText, Stack, Typography } from '@mui/material';
import { ArrowLeftIcon } from '@mui/x-date-pickers';
import { CanAccess, useParsed } from '@refinedev/core';
import { Show, ShowButton } from '@refinedev/mui';
import { getQuestion } from '@utils/appConfig/utils';
import useAppConfig from 'hooks/applicationConfigurations/useAppConfig';
import { CourseKeys } from 'types/ApplicationAnswer';

export default function ViewAppConfig() {
  const { id } = useParsed();
  const { isLoading, appConfig, programs } = useAppConfig(id as string);

  return (
    <CanAccess>
      <Show
        isLoading={isLoading}
        goBack={
          <Link to={'/appConfigs'}>
            <ArrowLeftIcon />
          </Link>
        }
        canEdit={false}
        title={<Typography variant="h5">{appConfig?.name}</Typography>}
        headerButtons={({ defaultButtons }) => (
          <>
            {defaultButtons}{' '}
            {!!appConfig &&
              Object.values(CourseKeys).some((key) => !!getQuestion(appConfig.config, key)) && (
                <CanAccess resource="application_configurations_courses" action="list">
                  <Link to={`/appConfigs/courses/${appConfig?.id}`}>
                    <Button startIcon={<EditIcon />}>Edit Courses</Button>
                  </Link>
                </CanAccess>
              )}
            <Link to={`/appConfigs/visual-editor/${appConfig?.id}#sections`}>
              <Button variant="contained" startIcon={<AutoFixHighIcon />} disabled={!appConfig?.id}>
                Visual Editor
              </Button>
            </Link>
          </>
        )}
      >
        <Stack gap={1}>
          <Typography variant="h6">Current Program(s)</Typography>
          {appConfig?.programApplicationConfigurationsByConfigurationId.length ? (
            <>
              <List dense={true}>
                {programs?.map((program) => (
                  <ListItem
                    secondaryAction={<ShowButton resource="programs" recordItemId={program.id} />}
                    key={program.id}
                  >
                    <ListItemText primary={program.name} secondary={program.partner?.name} />
                  </ListItem>
                ))}
              </List>
            </>
          ) : (
            <Typography>-</Typography>
          )}
          <Divider />
          {!!appConfig && <ParticipantList appConfig={appConfig} />}
        </Stack>
      </Show>
    </CanAccess>
  );
}
