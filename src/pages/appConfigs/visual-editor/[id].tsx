'use client';
import { NoAccessFallback } from '@components/NoAccessFallback';
import { ComponentsDrawer } from '@components/appConfigs/ComponentsDrawer';
import JSONEditor from '@components/appConfigs/JSONeditor';
import { KeyWarningModal } from '@components/appConfigs/KeyWarningModal';
import { PublishConfirmationButton } from '@components/appConfigs/PublishConfirmationButton';
import { UploadConfigModal } from '@components/appConfigs/UploadJSONModal';
import Link from '@components/navigation/Link';
import { type Data, Puck } from '@measured/puck';
import '@measured/puck/dist/index.css';
import AddIcon from '@mui/icons-material/Add';
import FindInPageIcon from '@mui/icons-material/FindInPage';
import {
  Box,
  Breadcrumbs,
  CircularProgress,
  Button as MUIButton,
  IconButton as MUIIconButton,
  Menu,
  MenuItem,
  Stack,
  Typography,
} from '@mui/material';
import {
  CodeIcon,
  Component2Icon,
  DotsHorizontalIcon,
  DownloadIcon,
  ExitIcon,
  MagnifyingGlassIcon,
  UploadIcon,
} from '@radix-ui/react-icons';
import { Dialog, DropdownMenu, Flex, IconButton, Theme as RadixTheme } from '@radix-ui/themes';
import '@radix-ui/themes/styles.css';
import {
  CanAccess,
  useModal,
  useNavigation,
  useNotification,
  useOne,
  useParsed,
  useUpdate,
} from '@refinedev/core';
import { appConfig } from '@utils/appConfig';
import type { QuestionGroupProps } from '@utils/appConfig/blocks/QuestionGroup';
import {
  getDeepValueKeys,
  mergeOrderedArrays,
  removeKeys,
  resetDynamicLogicValueTypes,
} from '@utils/appConfig/utils';
import { get, isArray, isNumber, mergeWith, set, unset } from 'lodash';
import NextLink from 'next/link';
import {
  DuplicateKeysProvider,
  type DuplicateMap,
  useDuplicateKeys,
} from 'providers/DuplicateKeyProvider';
import React, { Suspense } from 'react';
import { useContext, useEffect, useState } from 'react';
import { ColorModeContext } from 'theme/ColorModeContextProvider';
import {
  AppConfigSchema,
  CONFIG_SECTIONS,
  FIELD_TYPES,
  FieldToBlockType,
  QUESTION_GROUP_REGEX,
  QUESTION_REGEX,
  appConfigSections,
} from 'types/appConfig';
import { v4 as uuidv4 } from 'uuid';
import type { Content, JSONContent, TextContent } from 'vanilla-jsoneditor';
type Zone = Pick<Data, 'zones'> & { props: Record<string, string | number> };

const EDITOR_MODES = {
  VISUAL: 'VISUAL',
  JSON: 'JSON',
};

const VisualEditAppConfig = () => {
  const { id } = useParsed();
  const [content, setContent] = useState<Data>();
  const [zoneData, setZoneData] = useState<Pick<Data, 'zones'>>();
  const [sections, setSections] = useState<Zone[]>([]);
  const [sectionNames, setSectionNames] = useState<string[]>([]);
  const [isNavigating, setIsNavigating] = useState<boolean>(false);
  const [uploadedConfig, setUploadedConfig] = useState<unknown>();
  const [target, setTarget] = useState<string>();
  const [keyDialogOpen, setKeyDialogOpen] = useState(false);

  const [editorMode, setEditorMode] = useState<(typeof EDITOR_MODES)[keyof typeof EDITOR_MODES]>(
    EDITOR_MODES.VISUAL,
  );

  const [jsonContent, setJSONContent] = useState<Content | undefined>(undefined);
  const { push } = useNavigation();

  const { data, refetch } = useOne({
    resource: 'application_configurations',
    dataProviderName: 'config',
    id,
    meta: {
      fields: ['id', 'name', 'description', 'config'],
    },
  });
  const { setMode } = useContext(ColorModeContext);
  const { mutate: updateMutation } = useUpdate();
  const hash = window.location.hash;
  const configTitle = data?.data.name;
  const introPageZoneId = `intro-pages-${id}:Intro-Pages`;
  const sectionZoneId = `sections-${id}:Sections`;
  const overridesId = `overrides-${id}:Overrides`;
  const { show: showCreateModal, close, visible } = useModal();
  const { open } = useNotification();
  const { setDuplicates } = useDuplicateKeys();

  const handleUploadSubmit = (data) => {
    const file = data.file.item(0);
    const fileReader = new FileReader();

    fileReader.addEventListener(
      'load',
      () => {
        if (fileReader.result) {
          setUploadedConfig(JSON.parse(fileReader.result as string));
          setZoneData(undefined);
          open?.({
            type: 'success',
            message: 'Successfully imported config from file',
            description: 'Import complete',
            key: 'import-success',
          });
        }
      },
      false,
    );

    if (file) {
      fileReader.readAsText(file);
    }
    close();
  };

  useEffect(() => {
    if (zoneData) {
      return;
    }
    setMode('light');
    const config = uploadedConfig || data?.data.config;
    const initialQuestionGroups = {};
    const initialZoneData = {};

    function splitExpressionValues({
      config,
      keysToReplace,
      // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    }: { config: any; keysToReplace: string[] }) {
      const result = structuredClone(config);
      for (const key of keysToReplace) {
        const currentValue = get(result, key);
        const newKey = key?.split('.')?.slice(0, -1)?.join('.');
        if (typeof currentValue === 'object') {
          set(result, `${newKey}.valueAsExpression`, currentValue);
        } else {
          set(result, `${newKey}.valueAsText`, currentValue);
        }
        // remove original value from object
        unset(result, key);
      }
      return result;
    }

    if (config && id) {
      const expressionValueMatcher = new RegExp(/^(?!.*options.*).*value/);
      const configKeys = getDeepValueKeys(config);
      const expressionValueKeys = configKeys.filter((key) => expressionValueMatcher.test(key));
      const parsedConfig = splitExpressionValues({
        config,
        keysToReplace: expressionValueKeys,
      });

      const introPages = parsedConfig?.introPages?.map((page) => ({
        type: 'IntroPage',
        props: { id: page.key, ...page },
      }));
      const sections = parsedConfig?.sections?.map((section, index) => {
        initialQuestionGroups[section.key] = section.questionGroups;
        return {
          type: 'Section',
          props: { id: section.key, order: index, ...section },
        };
      });
      const overrides = parsedConfig?.overrides;

      initialZoneData[sectionZoneId] = sections;
      setSections(sections);
      initialZoneData[introPageZoneId] = introPages;
      initialZoneData[overridesId] = [{ props: { ...overrides, id: uuidv4() }, type: 'Overrides' }];

      for (const key in initialQuestionGroups) {
        initialQuestionGroups[key].map((group, index) => {
          const uniqueKey = `${key}:Question-Group::${index}::`;

          initialZoneData[`${uniqueKey}:Question`] = group.questions.map((question, index) => {
            const questionUniqueKey = `${uniqueKey}:Question::::${index}`;
            initialZoneData[`${questionUniqueKey}:Field`] = question?.fields.map((field) => {
              const isComplexField = field.type === 'complex';
              const generatedFieldKey = uuidv4();
              if (isComplexField) {
                initialZoneData[`${generatedFieldKey}:complex:subFields`] = field?.subFields?.map(
                  (sub) => ({
                    type: FieldToBlockType[sub.type],
                    props: { id: uuidv4(), ...sub },
                  }),
                );
              }
              return {
                type: FieldToBlockType[field.type],
                props: {
                  id: generatedFieldKey,
                  validation: { required: true },
                  ...field,
                },
              };
            });
            return {
              type: 'ApplicationQuestion',
              props: { id: questionUniqueKey, ...question },
            };
          });
          initialZoneData[`${key}:Question-Group::${index}`] = [
            {
              type: 'QuestionGroup',
              props: { id: uniqueKey, ...group },
            },
          ];
        });
      }

      setZoneData(initialZoneData);
      setIsNavigating(true);
      setTarget(window.location.hash.split('#')[1] || CONFIG_SECTIONS.INTRO_PAGES);
      // sets the duplicate keys on load
      constructPayload({ savedZones: initialZoneData, checkKeys: false });
    }
  }, [setMode, data, id, introPageZoneId, sectionZoneId, overridesId, zoneData, uploadedConfig]);

  useEffect(() => {
    const getDataShape = (route: string) => {
      const introPageZones = zoneData?.[introPageZoneId];
      const sectionZones = { [sectionZoneId]: sections };
      const overrides = zoneData?.[overridesId];
      for (const key in zoneData) {
        if (![sectionZoneId, introPageZoneId].includes(key)) {
          sectionZones[key] = zoneData[key];
        }
      }
      const sectionNames = sections.map((section) => section?.props?.key).filter(Boolean);
      setSectionNames(sectionNames as string[]);

      switch (route) {
        case appConfigSections[CONFIG_SECTIONS.INTRO_PAGES].value:
          return {
            root: {
              props: {
                title: `${configTitle} / ${appConfigSections[CONFIG_SECTIONS.INTRO_PAGES].label}`,
              },
            },
            content: [
              {
                type: 'introPagesContainer',
                props: { id: `intro-pages-${id}` },
              },
            ],
            zones: introPageZones
              ? {
                  [introPageZoneId]: introPageZones,
                }
              : undefined,
          };
        case appConfigSections[CONFIG_SECTIONS.SECTIONS].value: {
          return {
            root: {
              props: {
                title: `${configTitle} / ${appConfigSections[CONFIG_SECTIONS.SECTIONS].label}`,
              },
            },
            content: [
              {
                type: 'sectionsContainer',
                props: {
                  id: `sections-${id}`,
                },
              },
            ],
            zones: sectionZones,
          };
        }
        case appConfigSections[CONFIG_SECTIONS.OVERRIDES].value:
          return {
            root: {
              props: {
                title: `${configTitle} / ${appConfigSections[CONFIG_SECTIONS.OVERRIDES].label}`,
              },
            },
            content: [
              {
                type: 'overridesContainer',
                props: {
                  id: `overrides-${id}`,
                },
              },
            ],
            zones: overrides
              ? {
                  [overridesId]: overrides,
                }
              : undefined,
          };
        default: {
          if (sectionNames.includes(route)) {
            const filteredZones = {
              [sectionZoneId]: sections?.filter((zone) => {
                return (zone?.props?.key as string)?.includes(route);
              }),
            };
            for (const key in sectionZones) {
              if (key.includes(route) || key.includes('complex')) {
                filteredZones[key] = sectionZones[key];
              }
            }
            return {
              root: {
                props: {
                  title: `${configTitle} / Sections / ${route}`,
                },
              },
              content: [
                {
                  type: 'sectionsContainer',
                  props: {
                    id: `sections-${id}`,
                  },
                },
              ],
              zones: filteredZones,
            };
          }
          return {
            root: {
              props: {
                title: 'Coming Soon!',
              },
            },
            content: [],
            zones: undefined,
          };
        }
      }
    };
    if (isNavigating && target) {
      setContent(getDataShape(target));
      setIsNavigating(false);
      setTarget('');
    }
  }, [
    isNavigating,
    configTitle,
    id,
    target,
    zoneData,
    introPageZoneId,
    sectionZoneId,
    overridesId,
    sections,
  ]);

  const mergeDuplicateMaps = (
    /* Key will only be in here if it is duplicated in its sibling row or in its subtree, so will not double count keys that 
    appear in multiple places across the tree but not as siblings or ancestors */
    a: DuplicateMap,
    b: DuplicateMap,
  ): DuplicateMap => {
    const merged: DuplicateMap = { ...a };
    for (const key in b) {
      merged[key] = {
        count: (merged[key]?.count || 0) + b[key].count,
        trace: (merged[key]?.trace || []).concat(b[key]?.trace || []),
      };
    }

    return merged;
  };

  const constructPayload = ({
    savedZones,
    validate = true,
    checkKeys = true,
  }: { savedZones: Pick<Data, 'zones'>; validate?: boolean; checkKeys?: boolean }) => {
    let keyDuplicates: DuplicateMap = {};
    const previousConfig = data?.data.config;
    const changes = {
      introPages: previousConfig?.introPages || [],
      sections: previousConfig?.sections || [],
      overrides: previousConfig?.overrides,
      participants: previousConfig?.participants,
    };

    const introPages = savedZones?.[introPageZoneId] || [];
    const overrides = savedZones?.[overridesId]?.[0];
    const savedZoneKeys = Object.keys(savedZones);
    const ancestorKeys = new Map<string, string[]>();

    const sections = (savedZones?.[`sections-${id}:Sections`] || []).map((section) => {
      const ancestorDuplicates: DuplicateMap = {};
      const sectionKey = section.props.key;
      ancestorKeys.set(sectionKey, []);

      const questionGroupKeys = savedZoneKeys.filter(
        (key) => QUESTION_GROUP_REGEX.test(key) && key.includes(section?.props?.id),
      );

      let questionKeys = savedZoneKeys.filter(
        (key) => QUESTION_REGEX.test(key) && key.includes(section?.props?.id),
      );

      if (questionKeys.length === 0) {
        const idIndexes = questionGroupKeys.map((key) => key.split('::')?.[1]);
        questionKeys = idIndexes.flatMap((idIndex) => {
          const questionGroupKeyId = savedZones[questionGroupKeys?.[idIndex]]?.[0]?.props?.id;
          return savedZoneKeys.filter(
            (key) => QUESTION_REGEX.test(key) && key.includes(questionGroupKeyId),
          );
        });
      }

      const questionGroups: unknown[] = [];
      questionGroupKeys.forEach((questionGroupKey, index) => {
        const questionGroupKeyIndex = (
          Number.parseInt(questionGroupKey.split('::').at(-1) || '0') + 1
        ).toString();
        const questionGroup = savedZones?.[questionGroupKey]?.[0]?.props;
        if (!questionGroup) return;
        ancestorKeys.set(questionGroupKeyIndex, [sectionKey]);

        const questions = (savedZones?.[questionKeys[index]] || []).map((question) => {
          const questionKey = question.props.key;
          ancestorKeys.set(questionKey, [sectionKey, questionGroupKeyIndex, questionKey]);

          const siblingFieldKeys = new Map<string, string[]>();
          const siblingFieldDuplicates: DuplicateMap = {}; //Re-init this per question
          const fields = savedZones?.[`${question?.props?.id}:Field`]?.map((field) => {
            const lookupKey = field.props.key;
            if (field.props.type !== FIELD_TYPES.TYPOGRAPHY) {
              if (ancestorKeys.has(lookupKey)) {
                ancestorDuplicates[lookupKey] = {
                  count: (ancestorDuplicates[lookupKey]?.count || 1) + 1,
                  trace: [ancestorKeys.get(lookupKey) || []].concat([
                    [sectionKey, questionGroupKeyIndex, questionKey, lookupKey],
                  ]),
                };
              }
              if (siblingFieldKeys.has(lookupKey)) {
                siblingFieldDuplicates[lookupKey] = {
                  count: (siblingFieldDuplicates[lookupKey]?.count || 1) + 1,
                  trace: [siblingFieldKeys.get(lookupKey) || []].concat([
                    [sectionKey, questionGroupKeyIndex, questionKey, lookupKey],
                  ]),
                };
              }
            }
            let subFields = [];
            if (field.props.type !== FIELD_TYPES.TYPOGRAPHY) {
              siblingFieldKeys.set(lookupKey, [
                sectionKey,
                questionGroupKeyIndex,
                questionKey,
                lookupKey,
              ]);
              ancestorKeys.set(lookupKey, [
                sectionKey,
                questionGroupKeyIndex,
                questionKey,
                lookupKey,
              ]);
            }

            if (field?.props?.type === 'complex') {
              const siblingSubfieldKeys = new Map<string, string[]>();
              const siblingSubfieldDuplicates: DuplicateMap = {};
              const subFieldZoneKey =
                Object.keys(savedZones).find((key) =>
                  key.includes(`${field?.props?.id}:complex:subFields`),
                ) || '';
              const subFieldZone = savedZones?.[subFieldZoneKey];
              subFields = subFieldZone?.map((subField) => {
                const subfieldKey = subField.props.key;
                if (subField.props.type !== FIELD_TYPES.TYPOGRAPHY) {
                  if (ancestorKeys.has(subfieldKey)) {
                    ancestorDuplicates[subfieldKey] = {
                      count: (ancestorDuplicates[lookupKey]?.count || 1) + 1,
                      trace: [ancestorKeys.get(subfieldKey) || []].concat([
                        [sectionKey, questionGroupKeyIndex, questionKey, subfieldKey],
                      ]),
                    };
                  }
                  if (siblingSubfieldKeys.has(subfieldKey)) {
                    siblingSubfieldDuplicates[subfieldKey] = {
                      count: (siblingFieldDuplicates[lookupKey]?.count || 1) + 1,
                      trace: [siblingSubfieldKeys.get(subfieldKey) || []].concat([
                        [sectionKey, questionGroupKeyIndex, questionKey, subfieldKey],
                      ]),
                    };
                  }
                  siblingSubfieldKeys.set(subfieldKey, [
                    sectionKey,
                    questionGroupKeyIndex,
                    questionKey,
                    subfieldKey,
                  ]);
                }
                return removeKeys({ ...subField?.props }, ['id']);
              });
              keyDuplicates = mergeDuplicateMaps(keyDuplicates, siblingSubfieldDuplicates);
            }

            ancestorKeys.delete(lookupKey);
            return removeKeys(
              {
                ...field?.props,
                subFields,
              },
              ['id'],
            );
          });
          keyDuplicates = mergeDuplicateMaps(keyDuplicates, siblingFieldDuplicates);
          ancestorKeys.delete(questionKey);
          return removeKeys({ ...question?.props, fields }, ['id']);
        });

        ancestorKeys.delete(questionGroupKeyIndex);
        const questionGroupKeysToRemove = ['id'];
        if (questionGroup.overview) {
          const overviewValue = questionGroup.overview as QuestionGroupProps['overview'];
          if (!overviewValue?.title || !overviewValue?.description) {
            questionGroupKeysToRemove.push('overview');
          }
        }
        questionGroups.push(removeKeys({ ...questionGroup, questions }, questionGroupKeysToRemove));
      });
      keyDuplicates = mergeDuplicateMaps(keyDuplicates, ancestorDuplicates);
      ancestorKeys.delete(sectionKey);
      return removeKeys({ ...section.props, questionGroups }, ['id', 'order']);
    });

    changes.introPages = introPages.map((pages) =>
      removeKeys(
        {
          ...pages.props,
        },
        ['id'],
      ),
    );
    changes.sections = sections;
    changes.overrides = overrides?.props ? removeKeys({ ...overrides.props }, ['id']) : undefined;

    const payload = resetDynamicLogicValueTypes(changes);

    if (checkKeys) {
      setDuplicates(keyDuplicates);
      setKeyDialogOpen(!!Object.keys(keyDuplicates).length);
    }

    if (!validate) {
      return payload;
    }

    const result = AppConfigSchema.safeParse(payload);
    if (!result.success) {
      open?.({
        type: 'error',
        description: 'Parse error',
        message: result.error.message,
        key: 'parse-error',
      });
      console.error(result.error.message);
    } else {
      return payload;
    }
  };

  async function parseEdits(json?: Record<string, unknown>) {
    let payload: Record<string, unknown> | undefined;
    if (json) {
      const sanitizedJSON = resetDynamicLogicValueTypes(json);
      const result = AppConfigSchema.safeParse(sanitizedJSON);
      if (!result.success) {
        open?.({
          type: 'error',
          description: 'Parse error',
          message: result.error.message,
          key: 'parse-error',
        });
        console.error(result.error.message);
      } else {
        payload = sanitizedJSON;
      }
    } else {
      payload = constructPayload({ savedZones: zoneData || {} });
    }

    if (payload) {
      updateMutation(
        {
          resource: 'application_configurations',
          dataProviderName: 'config',
          id: id || '',
          values: {
            config: payload,
          },
        },
        {
          onSuccess: () => {
            refetch();
          },
        },
      );
    }
  }

  const handleFileDownload = (json?: Record<string, unknown>) => {
    let payload: Record<string, unknown> | undefined;
    if (json) {
      const sanitizedJSON = resetDynamicLogicValueTypes(json);
      payload = sanitizedJSON;
    } else {
      payload = constructPayload({ savedZones: zoneData || {}, validate: false });
    }
    const rawData = JSON.stringify(payload);
    const blob = new Blob([rawData], { type: 'application/json' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `${configTitle}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
  };

  function customizer(objValue, srcValue) {
    if (isArray(objValue)) {
      if (
        objValue.every((val) => isNumber(val?.props?.order)) &&
        srcValue.every((val) => isNumber(val?.props?.order))
      ) {
        if (srcValue.length < objValue.length) {
          const mergedResult = mergeOrderedArrays(objValue, srcValue);
          setSections(mergedResult);
          return mergedResult;
        }
        const reorderedResult = srcValue.map((value, index) => ({
          ...value,
          props: { ...value.props, order: index },
        }));
        setSections((sections) => (reorderedResult.length ? reorderedResult : sections));
        setSectionNames((names) =>
          reorderedResult.length
            ? sections.map((section) => section?.props?.key as string).filter(Boolean)
            : names,
        );
        return reorderedResult;
      }
      return srcValue;
    }
  }

  const handlePublish = () => {
    if (!jsonContent) return parseEdits();
    if (Object.hasOwn(jsonContent, 'json') && (jsonContent as JSONContent).json) {
      return parseEdits((jsonContent as JSONContent).json as Record<string, unknown>);
    }
    if (Object.hasOwn(jsonContent, 'text') && (jsonContent as TextContent).text) {
      return parseEdits(JSON.parse((jsonContent as TextContent).text) as Record<string, unknown>);
    }
  };

  const handleAddSection = () => {
    setSections(
      (sections) =>
        [
          ...sections,
          {
            type: 'Section',
            props: {
              id: `NewSection${(sections.length ?? 0) + 1}`,
              key: `NewSection${(sections.length ?? 0) + 1}`,
              name: 'section name',
              order: (sections.length ?? 0) + 1,
              overview: {
                title: 'section overview title',
                description: '',
              },
              questionGroups: [
                {
                  key: 'question_group_key',
                },
              ],
            },
          },
        ] as unknown as Zone[],
    );
    setIsNavigating(true);
    setTarget(`NewSection${(sections.length ?? 0) + 1}`);
    push(`#sections-${`NewSection${(sections.length ?? 0) + 1}`}`);
  };

  return (
    <CanAccess resource="application_configurations" action="show" fallback={<NoAccessFallback />}>
      <Dialog.Root open={keyDialogOpen} onOpenChange={(state) => setKeyDialogOpen(state)}>
        <Suspense fallback={<CircularProgress />}>
          {content && !isNavigating && editorMode === EDITOR_MODES.VISUAL ? (
            <Box>
              <Puck
                config={appConfig}
                data={content}
                iframe={{ enabled: false }}
                onChange={(change) =>
                  setZoneData((zones) => mergeWith(zones, change.zones, customizer))
                }
                overrides={{
                  components: () => <ComponentsDrawer categories={appConfig.categories} />,
                  headerActions: () => {
                    return (
                      <Flex gap="4" align="center">
                        <DropdownMenu.Root>
                          <DropdownMenu.Trigger>
                            <IconButton
                              variant="ghost"
                              size="2"
                              aria-label="Config Actions"
                              color="gray"
                            >
                              <DotsHorizontalIcon height="20px" width="20px" />
                            </IconButton>
                          </DropdownMenu.Trigger>
                          <DropdownMenu.Content>
                            {editorMode === EDITOR_MODES.VISUAL ? (
                              <DropdownMenu.Item
                                onSelect={() => {
                                  setEditorMode(EDITOR_MODES.JSON);
                                }}
                              >
                                <CodeIcon /> Switch to JSON Editor
                              </DropdownMenu.Item>
                            ) : (
                              <DropdownMenu.Item
                                onSelect={() => {
                                  setEditorMode(EDITOR_MODES.VISUAL);
                                }}
                              >
                                <Component2Icon /> Switch to visual editor
                              </DropdownMenu.Item>
                            )}
                            <DropdownMenu.Separator />
                            <DropdownMenu.Item onSelect={() => showCreateModal()}>
                              <UploadIcon /> Import Config
                            </DropdownMenu.Item>

                            <DropdownMenu.Item onSelect={() => handleFileDownload()}>
                              <DownloadIcon /> Download Config
                            </DropdownMenu.Item>
                            <DropdownMenu.Separator />
                            <DropdownMenu.Item
                              onSelect={() =>
                                constructPayload({ savedZones: zoneData || {}, validate: false })
                              }
                            >
                              <MagnifyingGlassIcon /> Check Duplicate Keys
                            </DropdownMenu.Item>
                            <DropdownMenu.Separator />
                            <DropdownMenu.Item asChild>
                              <NextLink href={`/appConfigs/show/${id}`}>
                                <ExitIcon /> Go to config details
                              </NextLink>
                            </DropdownMenu.Item>
                          </DropdownMenu.Content>
                        </DropdownMenu.Root>
                        <PublishConfirmationButton onConfirmation={handlePublish} />
                      </Flex>
                    );
                  },
                  header: ({ children }) => {
                    const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(null);
                    const isOpen = Boolean(anchorEl);

                    const handleClick = (event: React.MouseEvent<HTMLButtonElement> | null) => {
                      if (event) {
                        setAnchorEl(event.currentTarget);
                      }
                    };

                    const handleClose = () => {
                      setAnchorEl(null);
                    };
                    return (
                      <>
                        <Menu anchorEl={anchorEl} open={isOpen} onClose={handleClose}>
                          {sectionNames.map((name) => (
                            <MenuItem
                              onClick={() => {
                                push(`#sections-${name}`);
                                setIsNavigating(true);
                                setTarget(name);
                                handleClose();
                              }}
                              selected={hash.includes(name)}
                            >
                              {name}
                            </MenuItem>
                          ))}
                        </Menu>
                        <Breadcrumbs
                          sx={{
                            display: 'flex',
                            justifyContent: 'center',
                            gridColumn: '1 / span 3',
                            pt: 2,
                            pb: 4,
                            border: 'solid grey 2px',
                          }}
                        >
                          {Object.values(appConfigSections).map((section) => (
                            <Link
                              underline="hover"
                              sx={{ display: 'flex', alignItems: 'center' }}
                              to={`#${section.value}`}
                              key={section.value}
                              onClick={() => {
                                setIsNavigating(true);
                                setTarget(section.value);
                              }}
                            >
                              <Typography
                                variant="h5"
                                component="p"
                                color={!hash.includes(section.value) ? 'text.primary' : ''}
                              >
                                {section.label}
                              </Typography>
                            </Link>
                          ))}
                          <MUIIconButton color="primary" onClick={handleClick} size="large">
                            <FindInPageIcon />
                          </MUIIconButton>
                        </Breadcrumbs>
                        {children}
                      </>
                    );
                  },
                  outline: ({ children }) => (
                    <Stack>
                      <MUIButton endIcon={<AddIcon />} onClick={handleAddSection}>
                        Add section
                      </MUIButton>
                      {children}
                    </Stack>
                  ),
                }}
              />
            </Box>
          ) : (
            <Stack>
              <header>
                <Stack
                  direction="row"
                  spacing={3}
                  alignItems="center"
                  justifyContent="space-between"
                  p={1}
                  width="100%"
                >
                  <Box justifySelf="flex-start">
                    <Typography fontWeight={600}>{configTitle} / JSON Editor</Typography>
                  </Box>
                  <Flex gap="4" align="center">
                    <DropdownMenu.Root>
                      <DropdownMenu.Trigger>
                        <IconButton
                          variant="ghost"
                          size="2"
                          aria-label="Config Actions"
                          color="gray"
                        >
                          <DotsHorizontalIcon height="20px" width="20px" />
                        </IconButton>
                      </DropdownMenu.Trigger>
                      <DropdownMenu.Content>
                        {editorMode === EDITOR_MODES.VISUAL ? (
                          <DropdownMenu.Item
                            onSelect={() => {
                              setEditorMode(EDITOR_MODES.JSON);
                            }}
                          >
                            <CodeIcon /> Switch to JSON Editor
                          </DropdownMenu.Item>
                        ) : (
                          <DropdownMenu.Item
                            onSelect={() => {
                              setEditorMode(EDITOR_MODES.VISUAL);
                              setJSONContent(undefined);
                              if (!jsonContent) return;
                              if (
                                Object.hasOwn(jsonContent, 'json') &&
                                (jsonContent as JSONContent).json
                              ) {
                                setUploadedConfig(
                                  (jsonContent as JSONContent).json as Record<string, unknown>,
                                );
                                return setZoneData(undefined);
                              }
                              if (
                                Object.hasOwn(jsonContent, 'text') &&
                                (jsonContent as TextContent).text
                              ) {
                                setUploadedConfig(
                                  JSON.parse((jsonContent as TextContent).text) as Record<
                                    string,
                                    unknown
                                  >,
                                );
                                return setZoneData(undefined);
                              }
                            }}
                          >
                            <Component2Icon /> Switch to visual editor
                          </DropdownMenu.Item>
                        )}
                        <DropdownMenu.Separator />
                        <DropdownMenu.Item onSelect={() => showCreateModal()}>
                          <UploadIcon /> Import Config
                        </DropdownMenu.Item>
                        <DropdownMenu.Item
                          onSelect={() => {
                            if (!jsonContent) return handleFileDownload();
                            if (
                              Object.hasOwn(jsonContent, 'json') &&
                              (jsonContent as JSONContent).json
                            ) {
                              return handleFileDownload(
                                (jsonContent as JSONContent).json as Record<string, unknown>,
                              );
                            }
                            if (
                              Object.hasOwn(jsonContent, 'text') &&
                              (jsonContent as TextContent).text
                            ) {
                              return handleFileDownload(
                                JSON.parse((jsonContent as TextContent).text) as Record<
                                  string,
                                  unknown
                                >,
                              );
                            }
                          }}
                        >
                          <DownloadIcon /> Download Config
                        </DropdownMenu.Item>
                        <DropdownMenu.Separator />
                        <DropdownMenu.Item asChild>
                          <NextLink href={`/appConfigs/show/${id}`}>
                            <ExitIcon /> Go to program details
                          </NextLink>
                        </DropdownMenu.Item>
                      </DropdownMenu.Content>
                    </DropdownMenu.Root>
                    <PublishConfirmationButton onConfirmation={handlePublish} />
                  </Flex>
                </Stack>
              </header>
              <Box width="100%" px={3} pt={2}>
                {editorMode === EDITOR_MODES.JSON ? (
                  <JSONEditor
                    content={
                      jsonContent || {
                        json: constructPayload({
                          savedZones: zoneData || {},
                          validate: false,
                          checkKeys: false,
                        }),
                      }
                    }
                    onChange={setJSONContent}
                  />
                ) : (
                  <Stack
                    direction="column"
                    alignItems="center"
                    justifyContent="center"
                    height="100vh"
                  >
                    <CircularProgress />
                  </Stack>
                )}
              </Box>
            </Stack>
          )}
        </Suspense>

        <UploadConfigModal {...{ close, visible, handleUploadSubmit }} />
        <KeyWarningModal />
      </Dialog.Root>
    </CanAccess>
  );
};

function EditorLayout() {
  return (
    <DuplicateKeysProvider>
      <RadixTheme>
        <VisualEditAppConfig />
      </RadixTheme>
    </DuplicateKeysProvider>
  );
}

EditorLayout.noLayout = true;
export default EditorLayout;
