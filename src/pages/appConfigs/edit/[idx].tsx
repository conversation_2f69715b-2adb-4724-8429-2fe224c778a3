import AutocompleteInput from '@components/forms/AutocompleteInput';
import TextInput from '@components/forms/TextInput';
import { Stack, Typography } from '@mui/material';
import { CanAccess } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import { FormProvider } from 'react-hook-form';

export default function EditAppConfig(): JSX.Element {
  const formProps = useForm({
    refineCoreProps: {
      resource: 'application_configurations',
      dataProviderName: 'config',
      redirect: 'list',
      meta: { fields: ['id', 'name', 'description', 'partnerId'] },
    },
  });

  const {
    handleSubmit,
    saveButtonProps: { onClick },
  } = formProps;

  const onSubmit = (_, e) => {
    return onClick(e);
  };

  return (
    <CanAccess>
      <Edit
        isLoading={formProps.refineCore.formLoading}
        saveButtonProps={{ onClick: handleSubmit(onSubmit) }}
        title={<Typography variant="h5">Edit Application Config</Typography>}
      >
        <Stack direction="column" gap={2}>
          <FormProvider {...formProps}>
            <TextInput name="name" label="Name" required />
            <TextInput name="description" label="Description" required />
            <AutocompleteInput name="partnerId" label="Partner" resource="partners" />
          </FormProvider>
        </Stack>
      </Edit>
    </CanAccess>
  );
}
