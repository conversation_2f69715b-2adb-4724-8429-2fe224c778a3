import CreateCoursesDrawer from '@components/appConfigs/courses/CreateCourseDrawer';
import DeleteCourseModal from '@components/appConfigs/courses/DeleteCourseModal';
import EditCourseDrawer from '@components/appConfigs/courses/EditCourseDrawer';
import { DataGrid } from '@components/data-grid/DataGrid';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CheckBoxIcon from '@mui/icons-material/CheckCircle';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import { IconButton, Typography } from '@mui/material';
import { CanAccess, useGo, useModal, useParsed } from '@refinedev/core';
import { List, Show } from '@refinedev/mui';
import { condition } from '@utils/appConfig/evaluate';
import { getCoursesByKey } from '@utils/appConfig/utils';
import useAppConfig from 'hooks/applicationConfigurations/useAppConfig';
import { useState } from 'react';
import { CourseKeys } from 'types/ApplicationAnswer';
import type { Course, CourseQuestion } from 'types/appConfig';

function CoursesConfig() {
  const { id } = useParsed();
  const { isLoading, appConfig, refetch } = useAppConfig(id as string);
  const [selectedQuestionKey, setSelectedQuestionKey] = useState<string>();
  const [selectedCourse, setSelectedCourse] = useState<Course & { duration: number }>();

  const createDrawer = useModal();
  const deleteDrawer = useModal();
  const editDrawer = useModal();
  const go = useGo();

  const courseQuestions = appConfig
    ? (Object.values(CourseKeys)
        .map((key) => getCoursesByKey(appConfig.config, key))
        .filter(Boolean) as CourseQuestion[])
    : [];

  const columns = (key: string) => [
    { field: 'value', headerName: 'Course', flex: 1, minWidth: 400 },
    {
      field: 'duration',
      headerName: 'Duration',
    },
    {
      field: 'disabled',
      headerName: 'Active',
      renderCell: ({ row }) => !row.disabled && <CheckBoxIcon fontSize="small" color="primary" />,
    },
    {
      field: 'actions',
      sortable: false,
      headerName: '',
      renderCell: ({ row }) => (
        <>
          <CanAccess resource="application_configurations_courses" action="edit">
            <IconButton
              onClick={() => {
                editDrawer.show();
                setSelectedQuestionKey(key);
                setSelectedCourse(row);
              }}
              color="primary"
            >
              <EditIcon fontSize="small" />
            </IconButton>
          </CanAccess>
          <CanAccess resource="application_configurations_courses" action="delete">
            <IconButton
              onClick={() => {
                setSelectedQuestionKey(key);
                setSelectedCourse(row);
                deleteDrawer.show();
              }}
              color="primary"
            >
              <DeleteIcon fontSize="small" color="error" />
            </IconButton>
          </CanAccess>
        </>
      ),
    },
  ];

  return (
    <CanAccess resource="application_configurations_courses" action="list">
      <Show
        resource="application_configurations_courses"
        isLoading={isLoading}
        title={<Typography variant="h5">{appConfig?.name}</Typography>}
        contentProps={{
          style: {
            display: 'flex',
            flexDirection: 'column',
            gap: '16px',
          },
        }}
        goBack={
          <IconButton
            onClick={() =>
              go({
                to: {
                  resource: 'application_configurations',
                  action: 'show',
                  id: id as string,
                },
              })
            }
          >
            <ArrowBackIcon />
          </IconButton>
        }
      >
        {!!courseQuestions?.length &&
          courseQuestions.map(({ key, courses, formula }) => {
            return (
              <div key={key}>
                <List
                  resource="application_configurations_courses"
                  title={`Question: ${key} (Number of Courses: ${courses.length})`}
                  breadcrumb={false}
                  createButtonProps={{
                    onClick: () => {
                      createDrawer.show();
                      setSelectedQuestionKey(key);
                    },
                  }}
                >
                  <DataGrid
                    columns={columns(key)}
                    rows={courses.map((course) => ({
                      id: course.value,
                      duration: condition(formula, { name: course.value }),
                      ...course,
                    }))}
                    pageSizeOptions={[10, 50, 100]}
                    autoHeight
                    initialState={{
                      pagination: {
                        paginationModel: { pageSize: 10 },
                      },
                    }}
                  />
                </List>
              </div>
            );
          })}
      </Show>
      {!!appConfig && !!selectedQuestionKey && (
        <>
          <CreateCoursesDrawer
            {...createDrawer}
            appConfig={appConfig}
            questionKey={selectedQuestionKey}
            close={() => {
              createDrawer.close();
              setSelectedQuestionKey(undefined);
            }}
            onSuccess={refetch}
          />
          <DeleteCourseModal
            {...deleteDrawer}
            appConfig={appConfig}
            questionKey={selectedQuestionKey}
            selectedCourse={selectedCourse as Course}
            close={() => {
              deleteDrawer.close();
              setSelectedQuestionKey(undefined);
              setSelectedCourse(undefined);
            }}
            onSuccess={refetch}
          />
          <EditCourseDrawer
            {...editDrawer}
            appConfig={appConfig}
            questionKey={selectedQuestionKey}
            course={selectedCourse}
            close={() => {
              editDrawer.close();
              setSelectedQuestionKey(undefined);
              setSelectedCourse(undefined);
            }}
            onSuccess={refetch}
          />
        </>
      )}
    </CanAccess>
  );
}
export default CoursesConfig;
