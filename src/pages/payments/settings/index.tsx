import HiddenInput from '@components/forms/HiddenInput';
import JsonForm from '@components/forms/JsonForm';
import SchedulerSettingsList from '@components/schedules/SchedulerSettingsList';
import { Box, CardHeader, Typography } from '@mui/material';
import { CanAccess } from '@refinedev/core';
import { Breadcrumb, EditButton, SaveButton } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import useTransfer from 'hooks/transfers/useTransfer';
import { useEffect, useState } from 'react';
import { FormProvider } from 'react-hook-form';
import config from 'schemas/transferSchedule/config.json';
import uiConfig from 'schemas/transferSchedule/ui.json';

// TODO make this just about transfers? Show list of past transfers for inspection?
const PaymentsSettingsPage = () => {
  const { schedules, isLoading, update } = useTransfer('USIODailyTransfer');

  const transfer = schedules?.data?.[0];
  const transferFormProps = useForm({
    refineCoreProps: {
      resource: 'schedules',
      dataProviderName: 'scheduler',
      meta: {
        fields: ['id', 'referenceId', 'payload'],
      },
    },
    warnWhenUnsavedChanges: false,
  });

  useEffect(
    () => transferFormProps.setValue('payload', transfer?.payload),
    [transfer, transferFormProps.setValue],
  );

  const [editing, setEditing] = useState(false);
  const onSave = () => {
    const { id, payload } = transferFormProps.getValues();
    update(id, { payload });
    setEditing(false);
  };

  return (
    <CanAccess>
      <Breadcrumb />
      <CardHeader title="Payments Settings" />
      <Box sx={{ padding: '1rem' }}>
        <Typography>
          <strong>Scheduler</strong>
        </Typography>
        <SchedulerSettingsList />
      </Box>
      {!isLoading && !!transfer && (
        <Box sx={{ padding: '1rem' }}>
          <Box sx={{ paddingBottom: '1rem', display: 'flex', justifyContent: 'space-between' }}>
            <Typography>
              <strong>Transfers</strong>
            </Typography>
            {editing ? (
              <SaveButton
                {...transferFormProps.saveButtonProps}
                resource="schedules"
                onClick={onSave}
              />
            ) : (
              <EditButton
                recordItemId={transfer?.id}
                resource="schedules"
                onClick={() => setEditing(true)}
              />
            )}
          </Box>
          {!!transfer && (
            <FormProvider {...transferFormProps}>
              <HiddenInput name="id" value={transfer?.id} />
              <JsonForm schema={config} uiSchema={uiConfig} name="payload" readonly={!editing} />
            </FormProvider>
          )}
        </Box>
      )}
    </CanAccess>
  );
};

export default PaymentsSettingsPage;
