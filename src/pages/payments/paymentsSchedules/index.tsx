import AutocompleteInput from '@components/forms/AutocompleteInput';
import ScheduledPaymentsList from '@components/payments/ScheduledPaymentList';
import { Box, Stack } from '@mui/material';
import { CanAccess } from '@refinedev/core';
import { useForm } from '@refinedev/react-hook-form';
import { FormProvider } from 'react-hook-form';

export default function PaymentSchedules(): JSX.Element {
  const formProps = useForm({
    refineCoreProps: {
      resource: 'partners',
      redirect: 'list',
      meta: { fields: ['id', 'name'] },
    },
  });

  const { partnerId } = formProps.watch();

  return (
    <CanAccess>
      <Stack>
        <Box mb={3}>
          <FormProvider {...formProps}>
            <AutocompleteInput name="partnerId" label="Filter by partner" resource="partners" />
          </FormProvider>
        </Box>
        <ScheduledPaymentsList partnerId={partnerId} />
      </Stack>
    </CanAccess>
  );
}
