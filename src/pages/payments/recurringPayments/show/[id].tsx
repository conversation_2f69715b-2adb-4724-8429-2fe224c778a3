import PaymentsList from '@components/payments/PaymentsList';
import CancelPaymentsDrawer from '@components/schedules/CancelPaymentsDrawer';
import EditPaymentMethodDrawer from '@components/schedules/EditPaymentMethodDrawer';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import {
  Box,
  Button,
  Divider,
  IconButton,
  Link,
  List,
  ListItem,
  ListItemText,
  Stack,
  Typography,
} from '@mui/material';
import { CanAccess, useModal, useOne, useParsed } from '@refinedev/core';
import { Show } from '@refinedev/mui';
import { displayCurrency } from '@utils/currency';
import dayjs from 'dayjs';
import { usePaymentSchedule } from 'hooks/payments/usePaymentSchedule';
import { useSchedule } from 'hooks/schedules/useSchedule';

export default function ViewPaymentSchedule() {
  const { id } = useParsed();

  const { data, isLoading } = useOne({
    resource: 'fulfillments',
    id,
    meta: {
      fields: [
        'id',
        'approvedAmount',
        'displayId',
        'scheduleType',
        'deactivatedAt',
        { fund: ['id', 'name', { partner: ['id', 'name'] }] },
        {
          case: [
            'id',
            { applicationsList: ['id', { submitter: ['id', 'name'] }] },
            { program: ['id', 'name'] },
          ],
        },
        {
          payments: [
            {
              nodes: [
                'id',
                'displayId',
                'fulfillmentId',
                'amount',
                'status',
                'method',
                'scheduledFor',
                'initiatedAt',
                'deactivatedAt',
                'payeeId',
              ],
            },
          ],
        },
      ],
    },
  });
  const { schedule } = useSchedule(id as string);
  const { paymentSchedule } = usePaymentSchedule(id as string);

  const paymentMethodDrawer = useModal();
  const cancelPaymentDrawer = useModal();

  const fulfillment = data?.data;
  const isActive = !fulfillment?.payments.some(({ status }) => status === 'CANCELED');

  const nextPayment = fulfillment?.payments
    ?.filter((payment) => !payment.deactivatedAt && !payment.initiatedAt)
    .sort((p1, p2) => dayjs(p1.scheduledFor).unix() - dayjs(p2.scheduledFor).unix())[0];
  const lastPayment = fulfillment?.payments
    ?.filter((payment) => !payment.deactivatedAt)
    .sort((p1, p2) => dayjs(p2.scheduledFor).unix() - dayjs(p1.scheduledFor).unix())[0];
  const primaryPayment = nextPayment ?? lastPayment;

  const EditPaymentMethodButton = () => (
    <IconButton
      onClick={(): void => paymentMethodDrawer.show()}
      sx={{ width: '16px', height: '16px' }}
    >
      <EditIcon fontSize="small" />
    </IconButton>
  );

  const CancelButton = () => (
    <Button
      component="label"
      variant="contained"
      startIcon={<DeleteIcon />}
      onClick={() => cancelPaymentDrawer.show()}
      color="error"
      disabled={isLoading || !isActive}
    >
      Cancel Remaining Payments
    </Button>
  );

  return (
    <CanAccess>
      <Show
        isLoading={isLoading}
        title={<Typography variant="h5">Payment Schedule {fulfillment?.displayId}</Typography>}
      >
        <Stack gap={1}>
          <List
            sx={{
              width: '100%',
              bgcolor: 'background.paper',
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
            }}
          >
            <ListItem>
              <ListItemText
                primary={
                  <Link href={`/partners/show/${fulfillment?.fund?.partner.id}`}>
                    {fulfillment?.fund?.partner.name}
                  </Link>
                }
                secondary="Partner"
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={
                  <Link href={`/funds/show/${fulfillment?.fund?.id}`}>
                    {fulfillment?.fund?.name}
                  </Link>
                }
                secondary="Fund"
              />
            </ListItem>
            <ListItem>
              <ListItemText primary={fulfillment?.id} secondary="Fulfillment ID" />
            </ListItem>

            <ListItem>
              <ListItemText
                primary={displayCurrency(fulfillment?.approvedAmount)}
                secondary="Approved Amount"
              />
            </ListItem>
            <ListItem>
              <ListItemText primary={fulfillment?.scheduleType} secondary="Schedule Type" />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={
                  <Stack direction="row" alignItems="center" gap={1}>
                    {primaryPayment?.method}
                    {nextPayment && <EditPaymentMethodButton />}
                  </Stack>
                }
                secondary="Payment Method"
              />
            </ListItem>
            {['PHYSICAL_CARD', 'VIRTUAL_CARD'].includes(primaryPayment?.method) && (
              <ListItem>
                <ListItemText
                  primary={
                    <Stack direction="row" alignItems="center" gap={1}>
                      {paymentSchedule?.account?.keys?.cardId}
                      {nextPayment && <EditPaymentMethodButton />}
                    </Stack>
                  }
                  secondary="Prepaid Card ID"
                />
              </ListItem>
            )}
          </List>
          <Box>
            <CancelButton />
          </Box>

          {fulfillment && (
            <>
              <Divider />
              <PaymentsList filter={{ fulfillmentId: fulfillment.id as string }} />
              {isActive && (
                <>
                  <CancelPaymentsDrawer {...cancelPaymentDrawer} fulfillment={fulfillment} />
                  <EditPaymentMethodDrawer {...paymentMethodDrawer} fulfillment={fulfillment} />
                </>
              )}
            </>
          )}
        </Stack>
      </Show>
    </CanAccess>
  );
}
