import Link from '@components/navigation/Link';
import PaymentsList from '@components/payments/PaymentsList';
import { Al<PERSON>, Divider, List, ListItem, ListItemText, Stack, Typography } from '@mui/material';
import { type BaseKey, CanAccess, useList, useParsed, useShow } from '@refinedev/core';
import { Show } from '@refinedev/mui';
import { displayCurrency } from '@utils/currency';
import { displayDate } from '@utils/date';

export default function ViewTransfer() {
  const { id } = useParsed();
  const {
    queryResult: { data, isLoading },
  } = useShow({
    id: id as BaseKey,
    dataProviderName: 'payment',
    resource: 'transfers',
    meta: {
      fields: [
        'id',
        'amount',
        'status',
        'keys',
        'transactionNumber',
        'createdAt',
        { fundingSource: ['id', 'referenceId', 'keys'] },
        {
          recipient: ['id', 'referenceId', 'keys'],
        },
      ],
    },
  });

  const transfer = data?.data;
  const { data: paymentsData } = useList({
    dataProviderName: 'payment',
    resource: 'payments',
    meta: {
      fields: [{ nodes: ['id', 'referenceId'] }],
    },
    filters: [
      {
        field: 'transferId',
        operator: 'eq',
        value: transfer?.id,
      },
    ],
    queryOptions: { enabled: !!transfer },
    pagination: { mode: 'off' },
  });
  const payments = paymentsData?.data;

  return (
    <CanAccess>
      <Show isLoading={isLoading} title={<Typography variant="h5">Transfer Details</Typography>}>
        <Stack gap={1}>
          <List
            sx={{
              width: '100%',
              bgcolor: 'background.paper',
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
            }}
          >
            <ListItem>
              <ListItemText
                primary={displayDate(transfer?.createdAt)}
                secondary="Transaction Date"
              />
            </ListItem>
            <ListItem>
              <ListItemText primary={displayCurrency(transfer?.amount)} secondary="Amount" />
            </ListItem>
            <ListItem>
              <ListItemText primary={transfer?.status} secondary="Status" />
            </ListItem>
            {!!transfer?.recipient && (
              <ListItem>
                <ListItemText
                  primary={[transfer.recipient.keys?.firstName, transfer.recipient.keys?.lastName]
                    .filter(Boolean)
                    .join(' ')}
                  secondary="Recipient"
                />
              </ListItem>
            )}
            {!!transfer?.fundingSource && (
              <ListItem>
                <ListItemText
                  primary={
                    <Link to={`/funds/show/${transfer.fundingSource.referenceId}`}>
                      {transfer.fundingSource.keys?.name}
                    </Link>
                  }
                  secondary="Fund"
                />
              </ListItem>
            )}
            <ListItem>
              <ListItemText
                primary={transfer?.keys?.confirmationNumber ?? '-'}
                secondary="Confirmation Number"
              />
            </ListItem>
          </List>

          <Divider />
          {payments?.length ? (
            <PaymentsList filter={{ ids: payments.map(({ referenceId }) => referenceId) }} />
          ) : (
            <Alert severity="error">
              Could not find relational data for transfer - please report to engineering
            </Alert>
          )}
        </Stack>
      </Show>
    </CanAccess>
  );
}
