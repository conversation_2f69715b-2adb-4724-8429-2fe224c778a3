import { DataGrid } from '@components/data-grid/DataGrid';
import Link from '@components/navigation/Link';
import { FormControl, Stack } from '@mui/material';
import { Autocomplete, TextField } from '@mui/material';
import { CanAccess, type CrudFilters } from '@refinedev/core';
import { useAutocomplete } from '@refinedev/mui';
import { List, useDataGrid } from '@refinedev/mui';
import { ShowButton } from '@refinedev/mui';
import { displayCurrency } from '@utils/currency';
import { displayDate } from '@utils/date';
import { useMemo, useState } from 'react';

export default function TransferPayments(): JSX.Element {
  const [fundId, setFundId] = useState();
  const { autocompleteProps: fundsAutoCompleteProps } = useAutocomplete({
    resource: 'funds',
    sorters: [{ field: 'name', order: 'asc' }],
    meta: { fields: [{ nodes: ['id', 'name'] }] },
    pagination: { mode: 'off' },
    onSearch: (value) => [{ field: 'name', operator: 'contains', value }],
  });

  const { dataGridProps } = useDataGrid({
    dataProviderName: 'payment',
    resource: 'transfers',
    meta: {
      fields: [
        {
          nodes: [
            'id',
            'amount',
            'status',
            'keys',
            'transactionNumber',
            'createdAt',
            { fundingSource: ['id', 'referenceId', 'keys'] },
            {
              recipient: ['id', 'referenceId', 'keys'],
            },
          ],
        },
      ],
    },
    sorters: {
      initial: [{ field: 'createdAt', order: 'desc' }],
    },
    filters: {
      defaultBehavior: 'replace',
      permanent: [
        { field: 'deactivatedAt', operator: 'null', value: true },
        fundId && {
          field: 'fundingSource.referenceId',
          operator: 'eq',
          value: fundId,
        },
      ].filter(Boolean) as CrudFilters,
    },
  });

  const columns = useMemo(() => {
    return [
      { field: 'transactionNumber', headerName: 'Transaction Number' },
      {
        field: 'fundingSource.keys.name',
        headerName: 'Funding Source',
        valueGetter: (params) => params.row.fundingSource,
        renderCell: ({ value: fundingSource }) => (
          <Link to={`/funds/show/${fundingSource?.referenceId}`}>{fundingSource?.keys?.name}</Link>
        ),
        sortable: false,
        filterable: false,
        flex: 1,
      },
      {
        field: 'status',
        headerName: 'Status',
        minWidth: 150,
      },
      {
        field: 'amount',
        headerName: 'Amount',
        valueGetter: (params) => displayCurrency(params.row.amount),
        minWidth: 150,
      },
      {
        field: 'recipient.keys.firstName',
        headerName: 'Recipient',
        valueGetter: (params) =>
          [params.row.recipient?.keys?.firstName, params.row.recipient?.keys?.lastName]
            .filter(Boolean)
            .join(' ') ?? params.row.recipient.referenceId,
        filterable: false,
        flex: 1,
      },
      {
        field: 'createdAt',
        headerName: 'Transaction Date',
        valueGetter: (params) => displayDate(params.row.createdAt),
        flex: 1,
      },
      {
        field: 'actions',
        headerName: 'Actions',
        sortable: false,
        renderCell: ({ row }) => <ShowButton hideText recordItemId={row.id} />,
        align: 'center',
        headerAlign: 'center',
      },
    ];
  }, []);

  return (
    <CanAccess>
      <List>
        <Stack direction="column" gap={2}>
          <FormControl>
            <Autocomplete
              {...fundsAutoCompleteProps}
              onChange={(_, value) => {
                setFundId(value?.id);
              }}
              getOptionLabel={(item) => {
                return (
                  fundsAutoCompleteProps?.options?.find(
                    (fund) => fund?.id?.toString() === (item?.id ?? item)?.toString(),
                  )?.name ?? ''
                );
              }}
              sx={{ width: '100%' }}
              renderInput={(params) => (
                <TextField {...params} label="Filter by fund" margin="normal" variant="outlined" />
              )}
            />
          </FormControl>
          <DataGrid {...dataGridProps} columns={columns} />
        </Stack>
      </List>
    </CanAccess>
  );
}
