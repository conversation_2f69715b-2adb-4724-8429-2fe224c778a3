import Link from '@components/navigation/Link';
import ChangeStatusDrawer from '@components/payments/ChangeStatusDrawer';
import PaymentsList from '@components/payments/PaymentsList';
import ResetPaymentButton from '@components/payments/ResetPaymentButton';
import ResetPaymentDrawer from '@components/payments/ResetPaymentDrawer';
import StopPaymentButton from '@components/payments/StopPaymentButton';
import StopPaymentDrawer from '@components/payments/StopPaymentDrawer';
import WorkflowEventsList from '@components/workflows/WorkflowEventsList';
import { Button, Divider, List, ListItem, ListItemText, Stack, Typography } from '@mui/material';
import { CanAccess, useModal, useShow } from '@refinedev/core';
import { Show } from '@refinedev/mui';
import { formatAddress } from '@utils/address';
import { displayCurrency } from '@utils/currency';
import { displayDate } from '@utils/date';
import { displayId } from '@utils/id';
import { getPaymentNumber } from '@utils/payments';
import usePayee from 'hooks/payments/usePayee';
import { useCallback } from 'react';

export default function ViewPayment() {
  const {
    queryResult: { data, isLoading, refetch },
  } = useShow({
    meta: {
      fields: [
        'id',
        'amount',
        'status',
        'method',
        'note',
        'mailingAddressType',
        'mailingAddress',
        'payeeType',
        'payeeId',
        'createdAt',
        'initiatedAt',
        'scheduledFor',
        'displayId',
        'deactivatedAt',
        {
          fulfillment: [
            'id',
            'approvedAmount',
            'scheduleType',
            { fund: ['id', 'name', { partner: ['id', 'name'] }] },
            {
              case: [
                'id',
                'displayId',
                {
                  applicationsList: [
                    'id',
                    {
                      submitter: [
                        'id',
                        'name',
                        { applicantProfile: ['id', { applicantType: ['id', 'name'] }] },
                      ],
                    },
                  ],
                },
                {
                  program: [
                    'id',
                    'name',
                    { programFeaturesList: ['id', 'enabled', { feature: ['id', 'name'] }] },
                  ],
                },
              ],
            },
            { paymentPatternsList: ['id', 'pattern'] },
            { paymentsList: ['id', 'scheduledFor', 'deactivatedAt'] },
          ],
        },
      ],
    },
  });

  const payment = data?.data;
  const { payee } = usePayee(payment as { payeeId: string; payeeType: 'USER' | 'VENDOR' });

  const resetPaymentDrawer = useModal();
  const stopPaymentDrawer = useModal();
  const statusDrawer = useModal();

  const getMailToRecipient = useCallback(() => {
    switch (payment?.mailingAddressType) {
      case 'applicant':
        return payment?.fulfillment?.case?.applicationsList?.find(
          (app) => app?.submitter?.applicantProfile?.applicantType?.name === 'Applicant',
        )?.submitter?.name;
      case 'payee':
        return payee?.name;
      default:
        return '';
    }
  }, [payment, payee]);

  return (
    <CanAccess>
      <Show
        isLoading={isLoading}
        title={
          <>
            <Typography variant="h5">Payment {displayId('P', payment?.displayId)}</Typography>
            {payment?.deactivatedAt && <Typography color="warning">(Deactivated)</Typography>}
          </>
        }
        headerButtons={({ defaultButtons }) => (
          <>
            {payment && (
              <>
                <ResetPaymentButton
                  payment={payment}
                  openDrawer={(): void => resetPaymentDrawer.show()}
                />
                <StopPaymentButton
                  payment={payment}
                  openDrawer={(): void => stopPaymentDrawer.show()}
                />
                <Button onClick={(): void => statusDrawer.show()}>Change Status</Button>
              </>
            )}
            {defaultButtons}
          </>
        )}
      >
        <Stack gap={1}>
          <List
            sx={{
              width: '100%',
              bgcolor: 'background.paper',
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
            }}
          >
            <ListItem>
              <ListItemText primary={displayId('P', payment?.displayId)} secondary="Display ID" />
            </ListItem>
            <ListItem>
              <ListItemText primary={displayCurrency(payment?.amount)} secondary="Amount" />
            </ListItem>
            <ListItem>
              <ListItemText primary={payment?.status} secondary="Status" />
            </ListItem>
            <ListItem>
              <ListItemText primary={payment?.method ?? '-'} secondary="Method" />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={displayDate(payment?.initiatedAt) || '-'}
                secondary="Payment Date"
              />
            </ListItem>
            <ListItem>
              <ListItemText primary={displayDate(payment?.createdAt)} secondary="Created Date" />
            </ListItem>
            {payment?.fulfillment.scheduleType === 'RECURRING' && (
              <>
                <ListItem>
                  <ListItemText
                    primary={displayDate(payment?.scheduledFor)}
                    secondary="Scheduled Date"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary={
                      payment
                        ? `${getPaymentNumber(payment?.fulfillment, payment)} of ${
                            payment?.fulfillment?.paymentsList?.filter(
                              (payment) => !payment.deactivatedAt,
                            )?.length
                          }`
                        : '-'
                    }
                    secondary="Payment Number"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary={payment?.fulfillment.paymentPatternsList[0].pattern}
                    secondary="Frequency"
                  />
                </ListItem>
              </>
            )}
            <ListItem>
              <ListItemText primary={`${payment?.payeeType} - ${payee?.name}`} secondary="Payee" />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={
                  <Link
                    to={`/users/coreUsers/show/${payment?.fulfillment.case.applicationsList[0].submitter.id}`}
                  >
                    {payment?.fulfillment.case.applicationsList[0].submitter.name}
                  </Link>
                }
                secondary="Applicant"
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={
                  <Link to={`/cases/show/${payment?.fulfillment.case.id}`}>
                    {displayId('C', payment?.fulfillment.case.displayId)}
                  </Link>
                }
                secondary="Case"
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={
                  <Link to={`/partners/show/${payment?.fulfillment.fund.partner.id}`}>
                    {payment?.fulfillment.fund.partner.name}
                  </Link>
                }
                secondary="Partner"
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={
                  <Link to={`/funds/show/${payment?.fulfillment.fund.id}`}>
                    {payment?.fulfillment.fund.name}
                  </Link>
                }
                secondary="Fund"
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={
                  <Link to={`/programs/show/${payment?.fulfillment.case.program.id}`}>
                    {payment?.fulfillment.case.program.name}
                  </Link>
                }
                secondary="Program"
              />
            </ListItem>
            {payment?.method === 'CHECK' && (
              <>
                <ListItem>
                  <ListItemText
                    primary={
                      [payment?.mailingAddressType, getMailToRecipient()]
                        .filter(Boolean)
                        .join(' - ') || '-'
                    }
                    secondary="Mail to"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary={payment?.mailingAddress ? formatAddress(payment.mailingAddress) : '-'}
                    secondary="Mailing Address"
                  />
                </ListItem>
              </>
            )}
          </List>
          <Divider />

          {payment?.fulfillment.scheduleType === 'RECURRING' && (
            <>
              <PaymentsList filter={{ fulfillmentId: payment?.fulfillment.id }} />
              <Divider />
            </>
          )}

          <WorkflowEventsList
            filter={{ entityId: payment?.fulfillment.id, entityType: 'fulfillment' }}
          />
        </Stack>
        {!!payment && (
          <>
            <ResetPaymentDrawer {...resetPaymentDrawer} payment={payment} />
            <StopPaymentDrawer {...stopPaymentDrawer} payment={payment} />
            <ChangeStatusDrawer {...statusDrawer} paymentId={payment.id} onSuccess={refetch} />
          </>
        )}
      </Show>
    </CanAccess>
  );
}
