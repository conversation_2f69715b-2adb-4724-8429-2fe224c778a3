import ApplicationsList from '@components/applications/ApplicationsList';
import PaymentsList from '@components/payments/PaymentsList';
import CoreUsersList from '@components/users/CoreUsersList';
import { Divider, List, ListItem, ListItemText, Stack, Typography } from '@mui/material';
import { CanAccess, useOne, useShow } from '@refinedev/core';
import { Show } from '@refinedev/mui';
import { displayDate } from '@utils/date';

export default function ShowWorkflowEvent() {
  const {
    queryResult: { data, isLoading },
  } = useShow({
    meta: {
      fields: [
        'id',
        'assigneeId',
        'entityId',
        'entityType',
        'action',
        'previousValue',
        'newValue',
        'details',
        'createdAt',
        {
          admin: ['id', { user: ['id', 'name'] }],
        },
        { bulkOperation: ['id', 'operation', 'status'] },
      ],
    },
  });

  const workflowEvent = data?.data;

  const { data: assigneeData } = useOne({
    resource: 'admins',
    id: workflowEvent?.assigneeId,
    meta: {
      fields: ['id', { user: ['id', 'name'] }],
    },
    queryOptions: { enabled: !!workflowEvent?.assigneeId },
  });

  const assignee = assigneeData?.data;

  return (
    <CanAccess>
      <Show
        isLoading={isLoading}
        title={<Typography variant="h5">{workflowEvent?.action}</Typography>}
      >
        <Stack gap={1}>
          <List
            sx={{
              width: '100%',
              bgcolor: 'background.paper',
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
            }}
          >
            <ListItem>
              <ListItemText primary={workflowEvent?.action} secondary="Action" />
            </ListItem>
            <ListItem>
              <ListItemText primary={workflowEvent?.admin?.user?.name} secondary="Admin" />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={displayDate(workflowEvent?.createdAt)}
                secondary="Created At"
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={
                  [workflowEvent?.bulkOperation?.operation, workflowEvent?.bulkOperation?.status]
                    .filter(Boolean)
                    .join(' - ') ?? '-'
                }
                secondary="Bulk Operation"
              />
            </ListItem>
            <ListItem>
              <ListItemText primary={assignee?.user?.name ?? '-'} secondary="Assignee" />
            </ListItem>
          </List>
          <List
            sx={{
              width: '100%',
              bgcolor: 'background.paper',
              display: 'grid',
              gridTemplateColumns: '1fr',
            }}
          >
            <ListItem>
              <ListItemText
                primary={workflowEvent?.previousValue ?? '-'}
                secondary="Previous Value"
              />
            </ListItem>
            <ListItem>
              <ListItemText primary={workflowEvent?.newValue ?? '-'} secondary="New Value" />
            </ListItem>
            <ListItem>
              <ListItemText primary={workflowEvent?.details ?? '-'} secondary="Details" />
            </ListItem>
          </List>

          {!!workflowEvent?.id && (
            <>
              {workflowEvent?.entityType === 'case' && (
                <>
                  <Divider />
                  <ApplicationsList filter={{ caseId: workflowEvent.entityId as string }} />
                </>
              )}

              {workflowEvent?.entityType === 'fulfillment' && (
                <>
                  <Divider />
                  <PaymentsList filter={{ fulfillmentId: workflowEvent.entityId as string }} />
                </>
              )}
              {workflowEvent?.entityType === 'user' && (
                <>
                  <Divider />
                  <CoreUsersList userId={workflowEvent.entityId as string} />
                </>
              )}
            </>
          )}
        </Stack>
      </Show>
    </CanAccess>
  );
}
