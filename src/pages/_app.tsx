import '@fontsource/roboto';
import {
  AdminPanelSettingsSharp,
  AutoAwesomeMosaicSharp,
  BusinessSharp,
  CardTravel,
  ContactPage,
  ContentCopy,
  DynamicForm,
  EngineeringSharp,
  Event,
  EventRepeatOutlined,
  LocalShipping,
  ManageAccountsSharp,
  Paid,
  Payments,
  Person2,
  SavingsSharp,
  ScienceTwoTone,
  SettingsAccessibility,
  SpeakerNotes,
  TodayOutlined,
  TrackChangesSharp,
  Upcoming,
  ViewListOutlined,
  WorkHistoryOutlined,
} from '@mui/icons-material';
import { CssBaseline } from '@mui/material';
import { Authenticated, Refine } from '@refinedev/core';
import { RefineKbar, RefineKbarProvider } from '@refinedev/kbar';
import {
  Breadcrumb,
  RefineSnackbarProvider,
  ThemedLayoutV2,
  notificationProvider,
} from '@refinedev/mui';
import routerProvider, { UnsavedChangesNotifier } from '@refinedev/nextjs-router';
import { getConfig } from '@utils/config';
import { Header } from 'components/header';
import { Title } from 'components/title';
import { accessControlProvider } from 'lib/accessControlProvider';
import { auditLogProvider } from 'lib/auditLogProvider';
import { authProvider } from 'lib/authProvider';
import dataProvider, { GraphQLClient } from 'lib/graphileDataProvider';
import { useRouter } from 'next/router';
import { Router } from 'next/router';
import posthog from 'posthog-js';
import { PostHogProvider } from 'posthog-js/react';
import { useEffect } from 'react';
import { ColorModeContextProvider } from 'theme/ColorModeContextProvider';
import { globalStyles } from 'theme/mui';
import type { AppPropsWithLayout } from 'types/next';

function MyApp({ Component, pageProps }: AppPropsWithLayout): JSX.Element {
  const POSTHOG_PUBLIC_KEY = getConfig('PUBLIC_POSTHOG_KEY');
  const POSTHOG_HOST = getConfig('PUBLIC_POSTHOG_HOST');
  useEffect(() => {
    posthog.init(POSTHOG_PUBLIC_KEY as string, {
      api_host: POSTHOG_HOST || 'https://us.i.posthog.com',
      person_profiles: 'identified_only', // or 'always' to create profiles for anonymous users as well
      // Enable debug mode in development
      loaded: (posthog) => {
        if (process.env.NODE_ENV === 'development') posthog.debug();
      },
    });

    const handleRouteChange = () => posthog?.capture('$pageview');

    Router.events.on('routeChangeComplete', handleRouteChange);

    return () => {
      Router.events.off('routeChangeComplete', handleRouteChange);
    };
  }, [POSTHOG_PUBLIC_KEY, POSTHOG_HOST]);

  const renderComponent = () => {
    const { pathname } = useRouter();
    if (pathname === '/login') {
      return <Component {...pageProps} />;
    }
    if (Component.noLayout) {
      return (
        <Authenticated key="no-layout">
          <Component {...pageProps} />
        </Authenticated>
      );
    }

    return (
      <Authenticated key="dashboard">
        <ThemedLayoutV2 Title={Title} Header={Header}>
          <Component {...pageProps} />
        </ThemedLayoutV2>
      </Authenticated>
    );
  };

  return (
    <>
      <PostHogProvider client={posthog}>
        <RefineKbarProvider>
          <ColorModeContextProvider>
            <CssBaseline enableColorScheme />
            {globalStyles}
            <RefineSnackbarProvider>
              <Refine
                routerProvider={routerProvider}
                dataProvider={{
                  default: dataProvider(new GraphQLClient('/api/graphql/core-platform')),
                  config: dataProvider(new GraphQLClient('/api/graphql/config')),
                  document: dataProvider(new GraphQLClient('/api/graphql/document')),
                  payment: dataProvider(new GraphQLClient('/api/graphql/payment')),
                  scheduler: dataProvider(new GraphQLClient('/api/graphql/scheduler')),
                  sorcery: dataProvider(new GraphQLClient('/api/graphql/sorcery')),
                  verification: dataProvider(new GraphQLClient('/api/graphql/verification')),
                  identity: dataProvider(new GraphQLClient('/api/graphql/identity')),
                  compliance: dataProvider(new GraphQLClient('/api/graphql/compliance')),
                }}
                notificationProvider={notificationProvider}
                authProvider={authProvider}
                accessControlProvider={accessControlProvider}
                auditLogProvider={auditLogProvider}
                resources={[
                  {
                    name: 'partners-section',
                    icon: <BusinessSharp />,
                    meta: {
                      label: 'Partners',
                    },
                  },
                  {
                    name: 'partners',
                    icon: <ViewListOutlined />,
                    list: '/partners',
                    edit: '/partners/edit/:id',
                    show: '/partners/show/:id',
                    create: '/partners/create',
                    meta: {
                      canDelete: false,
                      parent: 'partners-section',
                      label: 'All Partners',
                    },
                  },
                  {
                    name: 'jevs',
                    icon: <WorkHistoryOutlined />,
                    list: '/jevs',
                    meta: {
                      canDelete: false,
                      parent: 'partners-section',
                    },
                  },
                  {
                    name: 'programs',
                    list: '/programs',
                    show: '/programs/show/:id',
                    edit: '/programs/edit/:id',
                    create: '/programs/create',
                    meta: { canDelete: false },
                  },
                  {
                    name: 'configurations',
                    icon: <ScienceTwoTone />,
                    meta: {
                      label: 'Configurations',
                      hide: false,
                    },
                  },
                  {
                    name: 'application_configurations',
                    icon: <DynamicForm />,
                    list: '/appConfigs',
                    show: '/appConfigs/show/:id',
                    edit: '/appConfigs/edit/:id',
                    create: '/appConfigs/create',
                    meta: {
                      canDelete: false,
                      label: 'Applications',
                      parent: 'configurations',
                    },
                  },
                  {
                    name: 'incident_messages',
                    list: '/banners',
                    meta: { label: 'Banners', canDelete: false, icon: <Upcoming /> },
                  },
                  {
                    name: 'funds',
                    icon: <SavingsSharp />,
                    list: '/funds',
                    show: '/funds/show/:id',
                    edit: '/funds/edit/:id',
                    create: '/funds/create',
                    meta: { canDelete: false },
                  },
                  {
                    name: 'cases',
                    icon: <ContactPage />,
                    list: '/cases',
                    show: '/cases/show/:id',
                    meta: { canDelete: false },
                  },
                  {
                    name: 'workflow_events',
                    icon: <Event />,
                    list: '/workflowEvents',
                    show: '/workflowEvents/show/:id',
                    meta: { canDelete: false },
                  },
                  {
                    name: 'payments-section',
                    icon: <Payments />,
                    meta: {
                      label: 'Payments',
                    },
                  },
                  {
                    name: 'payments',
                    icon: <ViewListOutlined />,
                    list: '/payments',
                    show: '/payments/show/:id',
                    meta: {
                      canDelete: false,
                      parent: 'payments-section',
                      label: 'All Payments',
                    },
                  },
                  {
                    name: 'payments-settings',
                    icon: <CardTravel />,
                    list: '/payments/settings',
                    meta: {
                      canDelete: false,
                      parent: 'payments-section',
                      label: 'Payment Settings',
                    },
                  },
                  {
                    name: 'payments-schedules',
                    icon: <TodayOutlined />,
                    list: '/payments/paymentsSchedules',
                    show: '/payments/paymentsSchedules/show/:id',
                    meta: {
                      canDelete: false,
                      parent: 'payments-section',
                      label: 'Payment Schedules',
                    },
                  },
                  {
                    name: 'recurring-payments',
                    icon: <EventRepeatOutlined />,
                    list: '/payments/recurringPayments',
                    show: '/payments/recurringPayments/show/:id',
                    meta: {
                      canDelete: false,
                      parent: 'payments-section',
                      label: 'Recurring Payments ',
                    },
                  },
                  {
                    name: 'transfer-payments',
                    icon: <Paid />,
                    list: '/payments/transferPayments',
                    show: '/payments/transferPayments/show/:id',
                    meta: {
                      canDelete: false,
                      parent: 'payments-section',
                      label: 'Transfers ',
                    },
                  },
                  {
                    name: 'users',
                    list: '/users',
                    show: '/users/show/:id',
                    meta: {
                      canDelete: false,
                      canEdit: false,
                      icon: <Person2 />,
                    },
                  },
                  {
                    name: 'core_users',
                    show: '/users/coreUsers/show/:id',
                    edit: '/users/coreUsers/edit/:id',
                    meta: {
                      dataProviderName: 'default',
                      resource: 'users',
                      canDelete: false,
                      hide: true,
                    },
                  },
                  {
                    name: 'external_api_users',
                    list: '/externalApiUsers',
                    create: '/externalApiUsers/create',
                    meta: { canDelete: false, icon: <Person2 /> },
                  },
                  {
                    name: 'settings',
                    icon: <EngineeringSharp />,
                    meta: {
                      label: 'Platform Settings',
                    },
                  },
                  {
                    name: 'changelogs',
                    icon: <SpeakerNotes />,
                    list: '/changelogs',
                    show: '/changelogs/show/:id',
                    edit: '/changelogs/edit/:id',
                    create: '/changelogs/create',
                    meta: { parent: 'settings' },
                  },
                  {
                    name: 'features',
                    icon: <AutoAwesomeMosaicSharp />,
                    list: '/features',
                    edit: '/features/edit/:id',
                    show: '/features/show/:id',
                    create: '/features/create',
                    meta: {
                      parent: 'settings',
                      canDelete: false,
                    },
                  },
                  {
                    name: 'notification_templates',
                    icon: <ContentCopy />,
                    list: '/notificationTemplates',
                    show: '/notificationTemplates/show/:id',
                    edit: '/notificationTemplates/edit/:id',
                    create: '/notificationTemplates/create',
                    meta: { parent: 'settings', canDelete: false },
                  },
                  {
                    name: 'migrations',
                    icon: <LocalShipping />,
                    list: '/migrations',
                    meta: { parent: 'settings', canDelete: false },
                  },
                  {
                    name: 'applicant_profile_configurations',
                    meta: { dataProviderName: 'config' },
                  },
                  {
                    name: 'iam-admin',
                    icon: <AdminPanelSettingsSharp />,
                    meta: {
                      label: 'IAM & Admin',
                    },
                  },
                  {
                    name: 'principals',
                    icon: <ManageAccountsSharp />,
                    list: '/iam-admin/iam',
                    create: '/iam-admin/principals/create',
                    edit: '/iam-admin/principals/edit/:id',
                    meta: {
                      parent: 'iam-admin',
                      dataProviderName: 'sorcery',
                    },
                  },
                  {
                    name: 'role_permissions',
                    icon: <SettingsAccessibility />,
                    list: '/iam-admin/rolePermissions',
                    meta: {
                      parent: 'iam-admin',
                      dataProviderName: 'sorcery',
                    },
                  },
                  {
                    name: 'audit_logs',
                    icon: <TrackChangesSharp />,
                    list: '/iam-admin/audit-logs',
                    show: '/iam-admin/audit-logs/show/:id',
                    meta: {
                      parent: 'iam-admin',
                      dataProviderName: 'sorcery',
                    },
                  },
                  {
                    name: 'verification_configurations',
                  },
                ]}
                options={{
                  disableTelemetry: true,
                  syncWithLocation: true,
                  warnWhenUnsavedChanges: true,
                  breadcrumb: (
                    <div>
                      <Breadcrumb />
                    </div>
                  ),
                  projectId: 'nmpZzg-qsVzje-oNAmQg',
                }}
              >
                {renderComponent()}
                <RefineKbar />
                <UnsavedChangesNotifier />
              </Refine>
            </RefineSnackbarProvider>
          </ColorModeContextProvider>
        </RefineKbarProvider>
      </PostHogProvider>
    </>
  );
}

export default MyApp;
