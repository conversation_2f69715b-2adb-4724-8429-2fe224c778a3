import MarkdownEditor from '@components/markdown/Editor';
import { Box } from '@mui/material';
import { CanAccess } from '@refinedev/core';
import { Create } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import { FormProvider } from 'react-hook-form';

export default function EditChangelog() {
  const formProps = useForm({
    refineCoreProps: { meta: { fields: ['id', 'content'] }, redirect: 'show' },
  });

  return (
    <CanAccess>
      <Create saveButtonProps={formProps.saveButtonProps}>
        <FormProvider {...formProps}>
          <Box
            component="form"
            sx={{ display: 'flex', flexDirection: 'column' }}
            autoComplete="off"
          >
            <MarkdownEditor name="content" label="Content" preview required />
          </Box>
        </FormProvider>
      </Create>
    </CanAccess>
  );
}
