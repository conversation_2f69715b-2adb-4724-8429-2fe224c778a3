import MarkdownEditor from '@components/markdown/Editor';
import Preview from '@components/markdown/Preview';
import { Divider, ListItem, ListItemText, List as MuiList, Stack, Typography } from '@mui/material';
import type { Changelogs } from '@prisma/generated/platformClient';
import { CanAccess, useShow } from '@refinedev/core';
import { Show } from '@refinedev/mui';
import dayjs from '@utils/dayJsConfig';

export default function ViewChangelog() {
  const {
    queryResult: { data, isLoading },
  } = useShow<Changelogs>({
    meta: { fields: ['id', 'content', 'createdAt', 'updatedAt'] },
  });
  const changelog = data?.data;

  return (
    <CanAccess>
      <Show
        isLoading={isLoading}
        title={
          <Typography variant="h5">{dayjs(changelog?.createdAt).format('MMMM D, YYYY')}</Typography>
        }
      >
        <Stack gap={1}>
          <MuiList
            sx={{
              width: '100%',
              bgcolor: 'background.paper',
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
            }}
          >
            <ListItem>
              <ListItemText primary={changelog?.id} secondary="ID" />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={dayjs(changelog?.createdAt).format('YYYY-MM-DD HH:mm:ss')}
                secondary="Created"
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={dayjs(changelog?.updatedAt).format('YYYY-MM-DD HH:mm:ss')}
                secondary="Updated"
              />
            </ListItem>
          </MuiList>
        </Stack>

        <Divider />

        <Preview>{changelog?.content as string}</Preview>
      </Show>
    </CanAccess>
  );
}
