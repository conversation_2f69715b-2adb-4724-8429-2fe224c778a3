import type { GridColDef } from '@mui/x-data-grid';
import { CanAccess } from '@refinedev/core';
import { DeleteButton, EditButton, List, ShowButton, useDataGrid } from '@refinedev/mui';
import React from 'react';
import { DataGrid } from '../../components/data-grid/DataGrid';

const ChangelogList = () => {
  const { dataGridProps } = useDataGrid({
    meta: {
      fields: [{ nodes: ['id', 'content', 'createdAt', 'updatedAt'] }],
    },
  });

  const columns = React.useMemo<GridColDef[]>(
    () => [
      {
        field: 'id',
        headerName: 'Id',
        flex: 1,
      },
      {
        field: 'content',
        headerName: 'Content',
        flex: 1,
      },
      {
        field: 'actions',
        headerName: 'Actions',
        sortable: false,
        renderCell: function render({ row }) {
          return (
            <>
              <ShowButton hideText recordItemId={row.id} />
              <EditButton hideText recordItemId={row.id} />
              <DeleteButton hideText recordItemId={row.id} />
            </>
          );
        },
        align: 'center',
        headerAlign: 'center',
        minWidth: 80,
      },
    ],
    [],
  );

  return (
    <CanAccess>
      <List>
        <DataGrid {...dataGridProps} columns={columns} />
      </List>
    </CanAccess>
  );
};

export default ChangelogList;
