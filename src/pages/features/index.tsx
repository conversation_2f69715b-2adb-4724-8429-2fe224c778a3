import { NoAccessFallback } from '@components/NoAccessFallback';
import type { GridColDef } from '@mui/x-data-grid';
import { CanAccess } from '@refinedev/core';
import { DateField, List, ShowButton, useDataGrid } from '@refinedev/mui';
import { DataGrid } from 'components/data-grid/DataGrid';
import React from 'react';

const FeaturesList = () => {
  const { dataGridProps } = useDataGrid({
    meta: {
      fields: [{ nodes: ['id', 'name', 'description', 'updatedAt'] }],
    },
    sorters: { initial: [{ field: 'name', order: 'asc' }] },
  });

  const columns = React.useMemo<GridColDef[]>(
    () => [
      {
        field: 'id',
        headerName: 'Id',
        minWidth: 150,
      },
      {
        field: 'name',
        headerName: 'Name',
        flex: 1,
      },
      {
        field: 'description',
        headerName: 'Description',
        flex: 2,
      },
      {
        field: 'updatedAt',
        headerName: 'Updated At',
        flex: 0,
        renderCell: ({ row }) => <DateField value={row.updatedAt} />,
      },
      {
        field: 'actions',
        headerName: 'Actions',
        renderCell: function render({ row }) {
          return (
            <>
              <ShowButton recordItemId={row.id} />
            </>
          );
        },
        align: 'center',
        headerAlign: 'center',
        minWidth: 80,
      },
    ],
    [],
  );

  return (
    <CanAccess fallback={<NoAccessFallback />}>
      <List>
        <DataGrid {...dataGridProps} columns={columns} />
      </List>
    </CanAccess>
  );
};

export default FeaturesList;
