import { DependencyList } from '@components/features/DependencyList';
import {
  Box,
  Divider,
  ListItem,
  ListItemText,
  List as MUIList,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@mui/material';
import { CanAccess, useShow } from '@refinedev/core';
import { List, Show } from '@refinedev/mui';
import { displayDate } from '@utils/date';
import useFeatures from 'hooks/features/useFeatures';
import Link from 'next/link';

const FeaturesShow = () => {
  const { lookupDeps, getRelatedFeatures } = useFeatures();
  const { queryResult } = useShow({
    meta: {
      fields: [
        'id',
        'name',
        'description',
        'updatedAt',
        {
          programFeatures: [{ nodes: [{ program: ['id', 'name', { partner: ['id', 'name'] }] }] }],
          partnerFeatures: [{ nodes: [{ partner: ['id', 'name'] }] }],
        },
      ],
    },
  });
  const { data, isLoading } = queryResult;

  const feature = data?.data;
  const { requires, prohibits } = getRelatedFeatures(lookupDeps(feature?.name));

  return (
    <CanAccess>
      <Show isLoading={isLoading} title={<Typography variant="h5">{feature?.name}</Typography>}>
        <Stack gap={1}>
          <MUIList
            sx={{
              width: '100%',
              bgcolor: 'background.paper',
            }}
          >
            <ListItem>
              <ListItemText primary={feature?.name} secondary="Name" />
              <ListItemText primary={feature?.id} secondary="UUID" />
              <ListItemText primary={displayDate(feature?.updatedAt)} secondary="Updated At" />
            </ListItem>
            <ListItem>
              <ListItemText primary={feature?.description} secondary="Description" />
            </ListItem>
          </MUIList>
          <Box sx={{ padding: '1rem', display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            <Typography variant="h6">Dependencies</Typography>
            {!requires && !prohibits && 'None'}
            {requires && <DependencyList title="Requires" deps={requires} />}
            {prohibits && <DependencyList title="Prohibits" deps={prohibits} />}
          </Box>
          <Divider />

          <List
            canCreate={false}
            resource={'partners'}
            breadcrumb={false}
            title="Partners with feature enabled"
          >
            <TableContainer>
              <Table sx={{ minWidth: 650 }} aria-label="simple table">
                <TableHead>
                  <TableRow>
                    <TableCell>Program</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {feature?.partnerFeatures?.map(({ partner: { id, name } }) => {
                    return (
                      <TableRow key={id} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                        <TableCell>
                          <Link href={`/partners/show/${id}`}>
                            <ListItemText primary={name} secondary={id} />
                          </Link>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </List>

          <List
            canCreate={false}
            resource={'programs'}
            breadcrumb={false}
            title="Programs with feature enabled"
          >
            <TableContainer>
              <Table sx={{ minWidth: 650 }} aria-label="simple table">
                <TableHead>
                  <TableRow>
                    <TableCell>Program</TableCell>
                    <TableCell align="right">Partner</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {feature?.programFeatures?.map(({ program: { id, name, partner } }) => {
                    return (
                      <TableRow key={id} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                        <TableCell>
                          <ListItemText primary={name} secondary={id} />
                        </TableCell>
                        <TableCell align="right">
                          <Link href={`/partners/show/${partner.id}`}>
                            <ListItemText primary={partner.name} secondary={partner.id} />
                          </Link>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </List>
        </Stack>
      </Show>
    </CanAccess>
  );
};
export default FeaturesShow;
