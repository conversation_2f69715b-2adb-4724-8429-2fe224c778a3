import FeatureFields from '@components/features/FeatureFields';
import { Alert } from '@mui/material';
import { Edit } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import { FormProvider } from 'react-hook-form';

const FeaturesEdit = () => {
  const formProps = useForm({
    refineCoreProps: {
      metaData: {
        fields: ['id', 'name', 'description'],
      },
      redirect: 'show',
    },
  });
  const {
    saveButtonProps,
    refineCore: { queryResult },
    formState: { isLoading },
  } = formProps;

  return (
    <Edit resource="features" saveButtonProps={saveButtonProps} isLoading={isLoading}>
      <Alert severity="info" variant="outlined">
        Editing Feature ID: {queryResult?.data?.data?.id}
      </Alert>

      <FormProvider {...formProps}>
        <FeatureFields />
      </FormProvider>
    </Edit>
  );
};

export default FeaturesEdit;
