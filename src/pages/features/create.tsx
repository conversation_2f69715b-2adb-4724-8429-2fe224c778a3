import FeatureFields from '@components/features/FeatureFields';
import { Create } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import { FormProvider } from 'react-hook-form';

const FeaturesCreate = () => {
  const formProps = useForm({
    refineCoreProps: {
      metaData: {
        fields: ['id', 'name', 'description'],
      },
      redirect: 'show',
    },
  });
  const {
    saveButtonProps,
    formState: { isLoading },
  } = formProps;

  return (
    <Create resource="features" saveButtonProps={saveButtonProps} isLoading={isLoading}>
      <FormProvider {...formProps}>
        <FeatureFields />
      </FormProvider>
    </Create>
  );
};

export default FeaturesCreate;
