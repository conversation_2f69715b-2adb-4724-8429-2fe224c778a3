import AutocompleteInput from '@components/forms/AutocompleteInput';
import CheckboxInput from '@components/forms/CheckboxInput';
import CloseIcon from '@mui/icons-material/Close';
import {
  Al<PERSON>,
  Box,
  Button,
  Container,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  LinearProgress,
  type LinearProgressProps,
  Link,
  List,
  ListItem,
  Stack,
  Step,
  StepButton,
  Stepper,
  Typography,
  useTheme,
} from '@mui/material';
import type { Partners } from '@prisma/generated/platformClient';
import { useNotification, useSelect } from '@refinedev/core';
import { useForm } from '@refinedev/react-hook-form';
import axios, { AxiosError, type AxiosResponse } from 'axios';
import Image from 'next/image';
import { useState } from 'react';
import { FormProvider } from 'react-hook-form';

interface AuthorizationWizardProps {
  onClose: () => void;
}

function LinearProgressWithLabel(props: LinearProgressProps & { value: number }) {
  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <Box sx={{ width: '100%', mr: 1 }}>
        <LinearProgress variant="determinate" {...props} />
      </Box>
      <Box sx={{ minWidth: 35 }}>
        <Typography variant="body2" sx={{ color: 'text.secondary' }}>{`${Math.round(
          props.value,
        )}%`}</Typography>
      </Box>
    </Box>
  );
}

function AuthorizationWizard({ onClose: parentOnClose }: AuthorizationWizardProps) {
  const { palette } = useTheme();
  const [activeStep, setActiveStep] = useState(0);
  const [completed, setCompleted] = useState<{
    [k: number]: boolean;
  }>({});
  const [progress, setProgress] = useState(0);
  const [running, setRunning] = useState(false);
  const [logs, setLogs] = useState<AxiosResponse[]>([]);

  const { open: notify } = useNotification();
  const formProps = useForm<{
    allPartners: boolean;
    fixPayeeRelations: boolean;
    fixBulkOverridePaymentsRelations: boolean;
    migrateFiscalPermissions: boolean;
    selectedPartners: Partners[];
  }>({
    refineCoreProps: { redirect: 'show' },
    defaultValues: { allPartners: true, useStream: false, selectedPartners: [] },
  });

  const onClose = () => {
    setActiveStep(0);
    setCompleted({});
    parentOnClose();
  };
  const totalSteps = () => {
    return Object.keys(steps).length;
  };
  const isLastStep = () => {
    return activeStep === totalSteps() - 1;
  };
  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };
  const handleNext = () => {
    if (stepProps?.onComplete) stepProps.onComplete();
    if (!isLastStep()) setActiveStep(activeStep + 1);
    else {
      onClose();
    }
  };
  const handleStep = (step: number) => () => {
    setActiveStep(step);
  };
  const handleComplete = () => {
    const newCompleted = completed;
    newCompleted[activeStep] = true;
    setCompleted(newCompleted);
    handleNext();
  };

  const { allPartners, useStream, selectedPartners } = formProps.getValues();

  const {
    queryResult: { data: partners },
  } = useSelect<Partners>({
    resource: 'partners',
    meta: { fields: [{ nodes: ['id', 'name'] }] },
  });

  const migratePartners = async () => {
    if (!partners?.data || !partners?.total)
      return notify?.({ message: 'Migration failed - no partners found', type: 'error' });

    const selectedPartnerIds = selectedPartners.map(({ id }) => id);
    let filteredPartners = partners.data;
    if (selectedPartnerIds.length)
      filteredPartners = partners?.data.filter((partner) =>
        selectedPartnerIds.includes(partner.id),
      );
    const totalToMigrate = filteredPartners.length;
    let idx = 0;
    setRunning(true);

    for await (const partner of filteredPartners) {
      try {
        const response = await axios.post(
          '/api/platform/identity/migratePermissions',
          { partnerId: partner.id, objectTypes: [], useStream },
          { headers: { 'Content-Type': 'application/json' } },
        );
        idx++;
        setProgress((idx / totalToMigrate) * 100);
        setLogs((prev) => [...prev, response]);
      } catch (e) {
        const msg = e instanceof AxiosError ? e.response?.data?.error : (e as Error).message;
        notify?.({ message: `Migration encountered an error: ${msg}`, type: 'error' });
      }
    }
    setRunning(false);
  };

  const steps: {
    [key: string]: {
      label: string;
      content: JSX.Element;
      onComplete?: () => Promise<void>;
    };
  } = {
    intro: {
      label: 'Begin',
      content: (
        <Container>
          <p>
            This wizard will guide you through migration to our new authorization toolset, supported
            by SpiceDB. Please see the{' '}
            <Link
              href="https://linear.app/bybeam/project/authorization-foundations-db00ea4c4e34/issues"
              target="_blank"
              rel="noopener noreferrer"
            >
              linear project
            </Link>{' '}
            for more information.
          </p>
        </Container>
      ),
    },
    partnerSelection: {
      label: 'Choose Partner(s)',
      content: (
        <Container>
          <p>
            Select the partners that will be migrated to the new authorization system. By default,
            all partners are selected for your convenience:
          </p>
          <FormProvider {...formProps}>
            <Stack component="form" autoComplete="off" sx={{ p: 2 }}>
              <CheckboxInput name="allPartners" label="All Partners" defaultValue={true} />
              <CheckboxInput
                name="useStream"
                label="Use Stream for Migration?"
                defaultValue={false}
              />
              {allPartners ? null : (
                <AutocompleteInput
                  name="selectedPartners"
                  label="Partner(s)"
                  resource="partners"
                  transformers={{
                    from: (option) => option,
                    id: (value: { id: string }) => (value instanceof Object ? value.id : value),
                  }}
                  multiple
                />
              )}
            </Stack>
          </FormProvider>
        </Container>
      ),
    },
    startMigration: {
      label: 'Start Migration',
      content: (
        <Container>
          <Alert severity="info">
            You have chosen to migrate:{' '}
            <List dense>
              {allPartners ? (
                <ListItem>ALL PARTNERS</ListItem>
              ) : (
                selectedPartners?.map((partner) => <ListItem>- {partner.name}</ListItem>)
              )}
            </List>
          </Alert>
          <p>This process will iterate through all selected partners and do a few things:</p>
          <List>
            <ListItem>
              1. Migrate existing relationships and permissions into SpiceDB. This takes some time
              as we must query the core database and load the data into SpiceDB.
            </ListItem>
          </List>
          <Alert severity="warning">
            Please note that after the migration is complete, some manual work is required to go in
            and remove the feature checks and feature from the codebase.
          </Alert>
        </Container>
      ),
    },
    run: {
      label: 'Run',
      content: (
        <Container sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {!running && !logs.length ? (
            <Button
              variant="contained"
              onClick={migratePartners}
              sx={{ width: '25%', alignSelf: 'center' }}
            >
              🪱 Start 🪱
            </Button>
          ) : null}
          {running || logs.length ? (
            <>
              <p>{running ? 'Migration is running now...' : 'Migration complete'}</p>
              <LinearProgressWithLabel value={progress} />
              <Box
                component="code"
                sx={{
                  display: 'flex',
                  backgroundColor: '#f5f5f5',
                  flexDirection: 'column',
                  gap: 0,
                  fontSize: 10,
                  height: '30%',
                }}
              >
                {logs.map((log) => (
                  <span key={log?.data?.message.split(' ').slice(-1)}>{log?.data?.message}</span>
                ))}
              </Box>
            </>
          ) : null}
        </Container>
      ),
    },
    complete: {
      label: 'Migration Complete',
      content: (
        <Container sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
          Migration complete! Be sure to go run some smoke tests.
          <Container sx={{ height: '50%', width: '50%' }}>
            <Image src="/spice/lisan-al-gaib.gif" alt="Lisan Al Gaib" height={300} width={400} />
          </Container>
        </Container>
      ),
    },
  };

  const stepProps = Object.values(steps)[activeStep];

  return (
    <Dialog open={true} fullScreen onClose={onClose}>
      <Stack direction="row" justifyContent="space-between" mx="1.5rem" alignItems="center">
        <DialogTitle color={palette.primary.main}>Authorization Migration Wizard</DialogTitle>
        <IconButton edge="start" color="inherit" onClick={onClose} aria-label="close">
          <CloseIcon />
        </IconButton>
      </Stack>
      <DialogContent sx={{ pb: 0 }}>
        <Stack>
          <Stepper nonLinear activeStep={activeStep} sx={{ mb: 3 }}>
            {Object.values(steps).map(({ label }, index) => (
              <Step key={label} completed={completed[index]}>
                <StepButton color="inherit" onClick={handleStep(index)}>
                  {label}
                </StepButton>
              </Step>
            ))}
          </Stepper>
          <Box p={3}>{stepProps?.content}</Box>
          <Container
            sx={{
              display: 'flex',
              flexDirection: 'row',
              gap: 4,
              justifyContent: 'center',
              paddingY: 2,
            }}
          >
            <Button variant="outlined" onClick={handleBack} disabled={activeStep === 0 || running}>
              Back
            </Button>
            <Button variant="contained" onClick={handleComplete} disabled={running}>
              Next
            </Button>
          </Container>
        </Stack>
      </DialogContent>
    </Dialog>
  );
}

export default AuthorizationWizard;
