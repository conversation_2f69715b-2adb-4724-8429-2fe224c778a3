import { Box, Button, Container, Typography } from '@mui/material';
import { useState } from 'react';
import AuthorizationWizard from './AuthorizationWizard';
import BackfillPermissionsWizard from './BackfillPermissionsWizard';

const Migrations = () => {
  const [isAuthWizardOpen, setIsAuthWizardOpen] = useState(false);
  const [isBackfillWizardOpen, setIsBackfillWizardOpen] = useState(false);

  return (
    <Box component="main" sx={{ flexGrow: 1 }}>
      <Container maxWidth={false}>
        <Typography variant="h5" color="textPrimary" sx={{ paddingY: 3 }}>
          Release Migrations
        </Typography>
        <Typography variant="body1" color="textPrimary" sx={{ paddingY: 3 }}>
          This section holds helpers for large and/or complicated feature releases to aid in the
          developer experience.
        </Typography>
        <Box sx={{ display: 'flex', flexDirection: 'column', flexWrap: 'wrap', gap: 3 }}>
          <Button
            variant="contained"
            onClick={() => setIsAuthWizardOpen(true)}
            sx={{ minWidth: 'fit-content', width: '33%' }}
          >
            🪱 Authorization 🪱
          </Button>
          <Button
            variant="contained"
            onClick={() => setIsBackfillWizardOpen(true)}
            sx={{ minWidth: 'fit-content', width: '33%' }}
          >
            🪲 Backfill 🪲
          </Button>
        </Box>
        {isAuthWizardOpen && <AuthorizationWizard onClose={() => setIsAuthWizardOpen(false)} />}
        {isBackfillWizardOpen && (
          <BackfillPermissionsWizard onClose={() => setIsBackfillWizardOpen(false)} />
        )}
      </Container>
    </Box>
  );
};

export default Migrations;
