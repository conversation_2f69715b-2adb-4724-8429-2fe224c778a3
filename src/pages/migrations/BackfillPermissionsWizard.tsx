import AutocompleteInput from '@components/forms/AutocompleteInput';
import CheckboxInput from '@components/forms/CheckboxInput';
import CloseIcon from '@mui/icons-material/Close';
import {
  Alert,
  Box,
  Button,
  Container,
  Dialog,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  IconButton,
  LinearProgress,
  type LinearProgressProps,
  List,
  ListItem,
  Radio,
  RadioGroup,
  Stack,
  Step,
  StepButton,
  Stepper,
  Typography,
  useTheme,
} from '@mui/material';
import type { Partners } from '@prisma/generated/platformClient';
import { useNotification, useSelect } from '@refinedev/core';
import { useForm } from '@refinedev/react-hook-form';
import axios, { AxiosError, type AxiosResponse } from 'axios';
import Image from 'next/image';
import { useState } from 'react';
import { FormProvider } from 'react-hook-form';

interface BackfillPermissionsWizardProps {
  onClose: () => void;
}

function LinearProgressWithLabel(props: LinearProgressProps & { value: number }) {
  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <Box sx={{ width: '100%', mr: 1 }}>
        <LinearProgress variant="determinate" {...props} />
      </Box>
      <Box sx={{ minWidth: 35 }}>
        <Typography variant="body2" sx={{ color: 'text.secondary' }}>{`${Math.round(
          props.value,
        )}%`}</Typography>
      </Box>
    </Box>
  );
}

function BackfillPermissionsWizard({ onClose: parentOnClose }: BackfillPermissionsWizardProps) {
  const { palette } = useTheme();
  const [activeStep, setActiveStep] = useState(0);
  const [completed, setCompleted] = useState<{
    [k: number]: boolean;
  }>({});
  const [progress, setProgress] = useState(0);
  const [running, setRunning] = useState(false);
  const [logs, setLogs] = useState<AxiosResponse[]>([]);

  const { open: notify } = useNotification();
  const formProps = useForm<{
    allPartners: boolean;
    procedure: string;
    selectedPartners: Partners[];
  }>({
    refineCoreProps: { redirect: 'show' },
    defaultValues: { allPartners: true, procedure: '', selectedPartners: [] },
  });

  const onClose = () => {
    setActiveStep(0);
    setCompleted({});
    parentOnClose();
  };
  const totalSteps = () => {
    return Object.keys(steps).length;
  };
  const isLastStep = () => {
    return activeStep === totalSteps() - 1;
  };
  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };
  const handleNext = () => {
    if (stepProps?.onComplete) stepProps.onComplete();
    if (!isLastStep()) setActiveStep(activeStep + 1);
    else {
      onClose();
    }
  };
  const handleStep = (step: number) => () => {
    setActiveStep(step);
  };
  const handleComplete = () => {
    const newCompleted = completed;
    newCompleted[activeStep] = true;
    setCompleted(newCompleted);
    handleNext();
  };

  const { allPartners, procedure, selectedPartners } = formProps.getValues();
  const {
    queryResult: { data: partners },
  } = useSelect<Partners>({
    resource: 'partners',
    meta: { fields: [{ nodes: ['id', 'name'] }] },
  });

  const backfillPartners = async () => {
    if (!partners?.data || !partners?.total)
      return notify?.({ message: 'Backfill failed - no partners found', type: 'error' });

    const selectedPartnerIds = selectedPartners.map(({ id }) => id);
    let filteredPartners = partners.data;
    if (selectedPartnerIds.length)
      filteredPartners = partners?.data.filter((partner) =>
        selectedPartnerIds.includes(partner.id),
      );
    const total = filteredPartners.length;
    let idx = 0;
    setRunning(true);

    for await (const partner of filteredPartners) {
      const endpoint = (() => {
        if (procedure === 'fixPayeeRelations') return '/api/platform/identity/fixPayeeRelations';
        if (procedure === 'fixBulkOverridePaymentsRelations')
          return '/api/platform/identity/fixBulkOverridePaymentsRelations';
        if (procedure === 'migrateFiscalPermissions')
          return '/api/platform/identity/migrateFiscalPermissions';
        throw new Error('unsupported backfill');
      })();
      try {
        const response = await axios.post(
          endpoint,
          { partnerId: partner.id },
          { headers: { 'Content-Type': 'application/json' } },
        );
        idx++;
        setProgress((idx / total) * 100);
        setLogs((prev) => [...prev, response]);
      } catch (e) {
        const msg = e instanceof AxiosError ? e.response?.data?.error : (e as Error).message;
        notify?.({ message: `Backfill encountered an error: ${msg}`, type: 'error' });
      }
    }
    setRunning(false);
  };

  const steps: {
    [key: string]: {
      label: string;
      content: JSX.Element;
      onComplete?: () => Promise<void>;
    };
  } = {
    intro: {
      label: 'Begin',
      content: (
        <Container>
          <p>
            This wizard will guide you through backfilling data using procedures in the identity
            server.
          </p>
        </Container>
      ),
    },
    partnerSelection: {
      label: 'Choose Partner(s)',
      content: (
        <Container>
          <p>
            Select the partners for backfill. By default, all partners are selected for your
            convenience:
          </p>
          <Stack component="form" autoComplete="off" sx={{ p: 2 }}>
            <CheckboxInput name="allPartners" label="All Partners" defaultValue={true} />
            {allPartners ? null : (
              <AutocompleteInput
                name="selectedPartners"
                label="Partner(s)"
                resource="partners"
                transformers={{
                  from: (option) => option,
                  id: (value: { id: string }) => (value instanceof Object ? value.id : value),
                }}
                multiple
              />
            )}
          </Stack>
        </Container>
      ),
    },
    procedureSelection: {
      label: 'Choose Procedure',
      content: (
        <Container>
          <p>Select the procedure to run</p>
          <Stack component="form" autoComplete="off" sx={{ p: 2 }}>
            <RadioGroup name="procedure">
              <FormControlLabel
                value="fixPayeeRelations"
                control={<Radio {...formProps.register('procedure')} />}
                label="Fix Payee Relations"
              />
              <FormControlLabel
                value="fixBulkOverridePaymentsRelations"
                control={<Radio {...formProps.register('procedure')} />}
                label="Fix Bulk Override Payments Relations"
              />
              <FormControlLabel
                value="migrateFiscalPermissions"
                control={<Radio {...formProps.register('procedure')} />}
                label="Migrate all existing admins to have fiscal role"
              />
            </RadioGroup>
          </Stack>
        </Container>
      ),
    },
    startBackfill: {
      label: 'Start Backfill',
      content: (
        <Container>
          <Alert severity="info">
            You have chosen to backfill:{' '}
            <List dense>
              {allPartners ? (
                <ListItem>ALL PARTNERS</ListItem>
              ) : (
                selectedPartners?.map((partner) => <ListItem>- {partner.name}</ListItem>)
              )}
            </List>
          </Alert>
          <p>
            This process will iterate through all selected partners run the selected backfill
            procedure on the identity server. Options include:
          </p>
          <List>
            {procedure === 'fixPayeeRelations' && (
              <ListItem>
                <strong>Fix Payee Relations:</strong> this queries all applicants for a partner and
                retrieves their payee relations in spiceDB. Once gathers, the correct identity user
                ID is added as a relation for the same subject.
              </ListItem>
            )}
            {procedure === 'fixBulkOverridePaymentsRelations' && (
              <ListItem>
                <strong>Fix Bulk Override Payments Relations:</strong> This retrieves all workflow
                events for a partner created through a bulk operation and establishes all payment
                relationships in SpiceDB for the specified fulfillments.
              </ListItem>
            )}
          </List>
        </Container>
      ),
    },
    run: {
      label: 'Run',
      content: (
        <Container sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {!running && !logs.length ? (
            <Button
              variant="contained"
              onClick={backfillPartners}
              sx={{ width: '25%', alignSelf: 'center' }}
            >
              Start
            </Button>
          ) : null}
          {running || logs.length ? (
            <>
              <p>{running ? 'Backfill is running now...' : 'Backfill complete'}</p>
              <LinearProgressWithLabel value={progress} />
              <Box
                component="code"
                sx={{
                  display: 'flex',
                  backgroundColor: '#f5f5f5',
                  flexDirection: 'column',
                  gap: 0,
                  fontSize: 10,
                  height: '30%',
                }}
              >
                {logs.map((log) => (
                  <span key={log?.data?.message.split(' ').slice(-1)}>{log?.data?.message}</span>
                ))}
              </Box>
            </>
          ) : null}
        </Container>
      ),
    },
    complete: {
      label: 'Backfill Complete',
      content: (
        <Container sx={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
          Backfill complete! Be sure to go run some smoke tests.
          <Container sx={{ height: '50%', width: '50%' }}>
            <Image src="/spice/roach-cockroach.gif" alt="Roach Jig" height={300} width={400} />
          </Container>
        </Container>
      ),
    },
  };

  const stepProps = Object.values(steps)[activeStep];

  return (
    <Dialog open={true} fullScreen onClose={onClose}>
      <Stack direction="row" justifyContent="space-between" mx="1.5rem" alignItems="center">
        <DialogTitle color={palette.primary.main}>Backfill Wizard</DialogTitle>
        <IconButton edge="start" color="inherit" onClick={onClose} aria-label="close">
          <CloseIcon />
        </IconButton>
      </Stack>
      <DialogContent sx={{ pb: 0 }}>
        <Stack>
          <Stepper nonLinear activeStep={activeStep} sx={{ mb: 3 }}>
            {Object.values(steps).map(({ label }, index) => (
              <Step key={label} completed={completed[index]}>
                <StepButton color="inherit" onClick={handleStep(index)}>
                  {label}
                </StepButton>
              </Step>
            ))}
          </Stepper>
          <Box p={3}>
            <FormProvider {...formProps}>{stepProps?.content}</FormProvider>
          </Box>
          <Container
            sx={{
              display: 'flex',
              flexDirection: 'row',
              gap: 4,
              justifyContent: 'center',
              paddingY: 2,
            }}
          >
            <Button variant="outlined" onClick={handleBack} disabled={activeStep === 0 || running}>
              Back
            </Button>
            <Button variant="contained" onClick={handleComplete} disabled={running}>
              Next
            </Button>
          </Container>
        </Stack>
      </DialogContent>
    </Dialog>
  );
}

export default BackfillPermissionsWizard;
