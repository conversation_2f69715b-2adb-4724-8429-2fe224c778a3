import Link from '@components/navigation/Link';
import PaymentFileUploadModal from '@components/partners/jevs/PaymentFileUploadModal';
import USIOCardIdUploadModal from '@components/partners/jevs/USIO/USIOCardIdUploadModal';
import PaymentsList from '@components/payments/PaymentsList';
import ScheduledPaymentsList from '@components/payments/ScheduledPaymentList';
import CoreUsersList from '@components/users/CoreUsersList';
import AddCardIcon from '@mui/icons-material/AddCard';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import {
  Alert,
  Box,
  Button,
  CircularProgress,
  Container,
  Stack,
  Typography,
  useTheme,
} from '@mui/material';
import { Paymentstatus } from '@prisma/generated/platformClient';
import { useList } from '@refinedev/core';
import { List } from '@refinedev/mui';
import { useState } from 'react';

const JEVS = () => {
  const [isWizardOpen, setIsWizardOpen] = useState(false);
  const [isCardImporterOpen, setIsCardImporterOpen] = useState(false);
  const { palette } = useTheme();
  const { data, isLoading } = useList({
    resource: 'partners',
    meta: {
      fields: [
        {
          nodes: [
            'id',
            {
              programs: [{ nodes: ['id'] }],
            },
            {
              funds: [{ nodes: ['id'] }],
            },
          ],
        },
      ],
    },
    filters: [{ field: 'externalId', operator: 'eq', value: 'jevs' }],
  });
  const { id: jevsPartnerId, programs, funds } = data?.data?.[0] || {};
  const fundId = funds?.[0]?.id;

  const { data: fundingSources } = useList({
    dataProviderName: 'payment',
    resource: 'fundingSources',
    meta: {
      fields: [
        {
          nodes: ['id', 'keys'],
        },
      ],
    },
    filters: [{ field: 'referenceId', operator: 'eq', value: fundId }],
  });

  const { data: adminData } = useList({
    resource: 'admins',
    meta: {
      fields: [
        {
          nodes: [
            'id',
            {
              user: ['id', 'name', 'email', 'validatedEmail'],
            },
          ],
        },
      ],
    },
    filters: [
      { field: 'user.partnerId', operator: 'eq', value: jevsPartnerId },
      { field: 'user.name', operator: 'eq', value: 'Beam Admin' },
    ],
  });

  const programId = programs?.[0]?.id;
  const adminId = adminData?.data?.[0]?.id;
  const distributorId = fundingSources?.data?.[0]?.keys?.physicalDistributorId;

  return (
    <>
      {jevsPartnerId && programId && adminId ? (
        <List
          title={
            <Typography variant="h4" py={3} color={palette.text.primary}>
              <Link to={`/partners/show/${jevsPartnerId}`} underline="none">
                JEVS
              </Link>
            </Typography>
          }
          headerButtons={
            <Stack direction="row" gap={2}>
              <Button
                variant="outlined"
                startIcon={<AddCardIcon />}
                onClick={() => setIsCardImporterOpen(true)}
              >
                Import USIO card ids
              </Button>
              <Button
                variant="contained"
                startIcon={<UploadFileIcon />}
                onClick={() => setIsWizardOpen(true)}
              >
                Add new payment file
              </Button>
            </Stack>
          }
        >
          <Stack spacing={2}>
            <ScheduledPaymentsList partnerId={jevsPartnerId} />
            <PaymentsList
              filter={{ partnerId: jevsPartnerId, status: Paymentstatus.failed.toUpperCase() }}
              title="Payment Errors"
              userColumns={[
                {
                  field: 'fulfillment.case.applications.some.submitter.email',
                  flex: 1,
                  headerName: 'Email',
                  renderCell: ({ value }) => value?.email,
                  sortable: false,
                  userColumnName: 'email',
                  valueGetter: (params) =>
                    params.row.fulfillment?.case?.applicationsList?.[0]?.submitter,
                  valueFormatter: ({ value }) => value?.name,
                },
                {
                  field: 'fulfillment.case.applications.some.submitter.legacyId',
                  flex: 1,
                  headerName: 'SalesforceId',
                  renderCell: ({ value }) => value?.legacyId,
                  sortable: false,
                  userColumnName: 'legacyId',
                  valueGetter: (params) =>
                    params.row.fulfillment?.case?.applicationsList?.[0]?.submitter,
                  valueFormatter: ({ value }) => value?.legacyId,
                },
              ]}
            />
            <CoreUsersList
              partnerId={jevsPartnerId}
              columns={[{ field: 'legacyId', headerName: 'Salesforce ID' }]}
              withPaymentAccounts
            />
          </Stack>
          {isWizardOpen && adminId && (
            <PaymentFileUploadModal
              onClose={() => setIsWizardOpen(false)}
              partnerId={jevsPartnerId as string}
              programId={programId}
              adminId={adminId}
              fundingDistributorId={distributorId}
            />
          )}
          {isCardImporterOpen && jevsPartnerId && (
            <USIOCardIdUploadModal
              onClose={() => setIsCardImporterOpen(false)}
              partnerId={jevsPartnerId}
            />
          )}
        </List>
      ) : (
        <>
          {isLoading ? (
            <CircularProgress />
          ) : (
            <Alert severity="warning">
              Jevs partner not created yet, please create a new partner and program with{' '}
              <code>jevs</code> as the <code>externalId</code> Also add a new admin with the name of{' '}
              <code>Beam Admin</code>
            </Alert>
          )}
        </>
      )}
    </>
  );
};

export default JEVS;
