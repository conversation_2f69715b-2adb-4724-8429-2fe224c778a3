import { NoAccessFallback } from '@components/NoAccessFallback';
import { type Data, Puck } from '@measured/puck';
import '@measured/puck/dist/index.css';
import { Box, CircularProgress, Container } from '@mui/material';
import { CanAccess, useNotification, useOne, useParsed, useUpdate } from '@refinedev/core';
import {
  eligibilityConfig,
  flattenObjectProps,
  normalizeProps,
  parsePuckId,
} from '@utils/eligibilityConfig';
import axios, { AxiosError } from 'axios';
import { unset } from 'lodash';
import { useEffect, useState } from 'react';
import {
  DEFAULT_MESSAGE_CONTENT,
  eligibilityQuestionDBTypes,
  eligibilityQuestionDescriptions,
} from 'types/eligibilityConfig';
import { v4 as uuid } from 'uuid';

const PartnerEligibility = () => {
  const { id } = useParsed();
  const [content, setContent] = useState<Data>();
  const [originalQuestions, setOriginalQuestions] = useState<{
    [key: string]: { eligibilityConfigId: string };
  }>({});
  const { mutate: updateMutation } = useUpdate();
  const [configId, setConfigId] = useState();
  const { open: notify } = useNotification();
  const { data, isLoading, refetch } = useOne({
    resource: 'eligibilityConfig',
    id,
    meta: {
      fields: [
        'id',
        'partnerId',
        'copy',
        'description',
        'title',
        'message',
        { partner: ['name', 'externalId'] },
        {
          eligibilityConfigQuestionsByConfigIdList: [
            {
              question: [
                'description',
                'type',
                'key',
                'copy',
                'id',
                'props',
                {
                  eligibilityConfigQuestionsByQuestionId: [{ nodes: ['id'] }],
                },
              ],
            },
          ],
        },
      ],
    },
  });

  useEffect(() => {
    const config = data?.data as {
      id;
      copy;
      description;
      title;
      message?: { eligible: string; ineligible: string };
    };
    const questions =
      data?.data?.eligibilityConfigQuestionsByConfigIdList?.map((child) => child?.question) ?? [];
    const partnerName = data?.data?.partner?.name;

    setConfigId(config?.id);
    for (const question of questions) {
      setOriginalQuestions((content) => ({
        ...content,
        [question?.id]: {
          eligibilityConfigId:
            question?.eligibilityConfigQuestionsByQuestionId?.nodes?.[0]?.id || '',
        },
      }));
    }

    if (config && !isLoading) {
      setContent({
        root: {
          title: `${partnerName}'s Eligibility Config`,
        },
        content: [
          {
            type: 'title',
            props: {
              title: config?.title,
              id: `${config?.id}_config_title`,
            },
          },
          {
            type: 'copy',
            props: {
              copy: config?.copy,
              id: `${config?.id}_config_copy`,
            },
          },
          ...questions.map((question) => {
            const questionProps = flattenObjectProps(question?.props);
            const answerProp = questionProps?.answer;
            return {
              type: question?.type.toLowerCase(),
              props: {
                copy: question?.copy,
                id: `${question?.id}_existing`,
                ...flattenObjectProps(question?.props),
                answer: Array.isArray(answerProp) ? answerProp.join(',') : answerProp,
              },
            };
          }),
          {
            type: 'message',
            props: {
              ineligible: config?.message?.ineligible ?? DEFAULT_MESSAGE_CONTENT.ineligible,
              eligible: config?.message?.eligible ?? DEFAULT_MESSAGE_CONTENT.eligible,
              id: `${config?.id}_config_message`,
            },
          },
        ],
      });
    }
  }, [data, isLoading]);

  async function parseEdits(data: Data) {
    const titleToUpdate = data.content.find((child) => child.type === 'title')?.props;
    const copyToUpdate = data.content.find((child) => child.type === 'copy')?.props?.copy;
    const messageToUpdate = data.content.find((child) => child.type === 'message')?.props;
    const titleUpdateId = parsePuckId(titleToUpdate?.id);
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    const questionsToUpdate: { [key: string]: any }[] = [];
    const questionsToCreate: unknown[] = [];
    const questionsToDelete: string[] = [];
    const message = {
      ineligible: messageToUpdate?.ineligible,
      eligible: messageToUpdate?.eligible,
    };

    for (const key in message) {
      if (!message[key]) {
        unset(message, key);
      }
    }

    const questionData = data.content.filter(
      (child) => child.type !== 'title' && child.type !== 'copy',
    );
    updateMutation({
      resource: 'eligibilityConfig',
      values: {
        title: titleToUpdate?.title,
        copy: copyToUpdate,
        message,
      },
      id: titleUpdateId,
    });

    for (const question of questionData) {
      if (question.props.id.includes('config')) {
        continue;
      }
      if (question.props.id.includes('existing')) {
        questionsToUpdate.push({
          value: {
            props: normalizeProps(question?.props, question?.type.toString()),
            copy: question?.props?.copy,
          },
          id: parsePuckId(question?.props?.id),
        });
      } else {
        const questionKey = question?.type.toString();
        questionsToCreate.push({
          value: {
            props: normalizeProps(question?.props, questionKey),
            copy: question?.props?.copy,
            key: `${questionKey}-${uuid()}`,
            type: eligibilityQuestionDBTypes[questionKey],
            description: eligibilityQuestionDescriptions[questionKey],
          },
        });
      }
    }

    const updatedQuestionKeys = questionsToUpdate.map(({ id }) => parsePuckId(id));

    for (const id in originalQuestions) {
      if (!updatedQuestionKeys.includes(id)) {
        questionsToDelete.push(originalQuestions[id].eligibilityConfigId);
      }
    }

    try {
      await axios.post(
        '/api/platform/eligibilityQuestions/save',
        {
          creations: questionsToCreate,
          deletes: questionsToDelete,
          updates: questionsToUpdate,
          configId,
        },
        { headers: { 'Content-Type': 'application/json' } },
      );
      notify?.({
        message: 'successfully updated questions',
        type: 'success',
      });

      refetch();
    } catch (e) {
      const msg = e instanceof AxiosError ? e.response?.data?.error : (e as Error).message;
      notify?.({ message: `Issue: ${msg}`, type: 'error' });
    }
  }

  return content ? (
    <CanAccess resource="eligibility_config" action="show" fallback={<NoAccessFallback />}>
      <Box>
        <Puck config={eligibilityConfig} data={content} onPublish={parseEdits} />
      </Box>
    </CanAccess>
  ) : (
    <Container
      sx={{ display: 'flex', height: '100vh', justifyContent: 'center', alignItems: 'center' }}
    >
      <CircularProgress />
    </Container>
  );
};
PartnerEligibility.noLayout = true;
export default PartnerEligibility;
