import SelectInput from '@components/forms/SelectInput';
import TextInput from '@components/forms/TextInput';
import { Stack, Typography } from '@mui/material';
import { CanAccess, useParsed } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import { FormProvider } from 'react-hook-form';

const IAMUserEdit = () => {
  const { id } = useParsed();
  const formProps = useForm({
    refineCoreProps: {
      resource: 'principals',
      dataProviderName: 'sorcery',
      id,
      meta: {
        fields: ['id', 'name', 'email', 'roles'],
      },
    },
  });

  return (
    <CanAccess>
      <Edit
        resource="principals"
        dataProviderName="sorcery"
        title={<Typography variant="h5">Edit IAM User</Typography>}
        headerButtonProps={{ resource: 'internal-users' }}
        saveButtonProps={formProps.saveButtonProps}
      >
        <FormProvider {...formProps}>
          <Stack component="form" autoComplete="off">
            <TextInput name="name" label="Name" required />
            <TextInput name="email" label="Email" type="email" required />
            <SelectInput
              name="roles"
              label="Role"
              options={[
                { id: 'pst', name: 'pst' },
                { id: 'admin', name: 'admin' },
              ]}
            />
          </Stack>
        </FormProvider>
      </Edit>
    </CanAccess>
  );
};

export default IAMUserEdit;
