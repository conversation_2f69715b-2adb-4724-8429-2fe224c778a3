import SelectInput from '@components/forms/SelectInput';
import TextInput from '@components/forms/TextInput';
import { Stack } from '@mui/material';
import { CanAccess } from '@refinedev/core';
import { Create } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import { FormProvider } from 'react-hook-form';
import { Roles } from 'types/rolePermission';

const IAMUserCreate = () => {
  const formProps = useForm({
    refineCoreProps: {
      resource: 'principals',
      dataProviderName: 'sorcery',
      action: 'create',
    },
  });
  const {
    saveButtonProps,
    refineCore: { formLoading },
  } = formProps;

  return (
    <CanAccess>
      <Create isLoading={formLoading} saveButtonProps={saveButtonProps}>
        <FormProvider {...formProps}>
          <Stack component="form" autoComplete="off">
            <TextInput name="name" label="Name" required />
            <TextInput name="email" label="Email" type="email" required />
            <SelectInput
              name="roles"
              label="Role"
              options={Object.values(Roles).map((role) => ({ id: role, name: role }))}
            />
          </Stack>
        </FormProvider>
      </Create>
    </CanAccess>
  );
};

export default IAMUserCreate;
