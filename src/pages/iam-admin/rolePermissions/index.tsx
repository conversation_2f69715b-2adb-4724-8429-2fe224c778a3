import AddAccessDrawer from '@components/rolePermissions/AddAccessDrawer';
import EditAccessDrawer from '@components/rolePermissions/EditAccessDrawer';
import { ExpandMore } from '@mui/icons-material';
import {
  Accordion,
  AccordionActions,
  AccordionDetails,
  AccordionSummary,
  Button,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListSubheader,
  Switch,
} from '@mui/material';
import { CanAccess, useList, useRefineContext } from '@refinedev/core';
import { List } from '@refinedev/mui';
import { useModalForm } from '@refinedev/react-hook-form';
import React, { Fragment, useState } from 'react';
import { CustomOperations, Operations, Resources } from 'types/rolePermission';

export const RolePermissionsList = () => {
  const {
    options: { textTransformers },
  } = useRefineContext();
  const [selectedPermission, setSelectedPermission] = useState<{
    resource: string;
    operation: string;
  }>();

  const { data, refetch } = useList({
    resource: 'role_permissions',
    dataProviderName: 'sorcery',
    meta: {
      fields: [{ nodes: ['id', 'role', 'access'] }],
    },
  });

  const addAccessDrawerFormProps = useModalForm({
    syncWithLocation: false,
    refineCoreProps: {
      resource: Resources.RolePermissions,
      meta: { fields: ['id', 'access', 'role'] },
      action: 'edit',
      redirect: false,
      onMutationSuccess: () => {
        refetch();
      },
    },
  });

  const editAccessDrawerFormProps = useModalForm({
    syncWithLocation: false,
    refineCoreProps: {
      resource: Resources.RolePermissions,
      meta: { fields: ['id', 'access', 'role'] },
      action: 'edit',
      redirect: false,
      onMutationSuccess: () => {
        refetch();
        setSelectedPermission(undefined);
      },
    },
  });

  return (
    <CanAccess resource={Resources.RolePermissions} action="list">
      <List
        resource={Resources.RolePermissions}
        contentProps={{
          style: {
            display: 'flex',
            flexDirection: 'column',
            gap: '10px',
          },
        }}
      >
        {data?.data?.map(({ role, id, access }) => (
          <Accordion key={id}>
            <AccordionSummary expandIcon={<ExpandMore />} id={id as string}>
              {textTransformers.humanize(role)}: Access Permissions
            </AccordionSummary>
            <AccordionActions>
              <Button variant="contained" onClick={() => addAccessDrawerFormProps.modal.show(id)}>
                Add Permission
              </Button>
            </AccordionActions>
            <AccordionDetails>
              {Object.entries(access).map(([resource, grants]) => (
                <Fragment key={resource}>
                  <ListSubheader>{textTransformers.humanize(resource)}</ListSubheader>
                  {[...Object.values(Operations), ...(CustomOperations[resource] ?? [])].map(
                    (operation) => (
                      <ListItem key={operation}>
                        <ListItemAvatar>
                          <Switch
                            checked={(grants as Operations[]).includes(operation)}
                            onChange={() => {
                              setSelectedPermission({ resource, operation });
                              editAccessDrawerFormProps.modal.show(id);
                            }}
                          />
                        </ListItemAvatar>
                        <ListItemText
                          sx={{ whiteSpace: 'pre-line' }}
                          primary={textTransformers.humanize(operation)}
                        />
                      </ListItem>
                    ),
                  )}
                </Fragment>
              ))}
            </AccordionDetails>
          </Accordion>
        ))}
      </List>
      {!!selectedPermission && (
        <EditAccessDrawer {...editAccessDrawerFormProps} {...selectedPermission} />
      )}
      <AddAccessDrawer {...addAccessDrawerFormProps} />
    </CanAccess>
  );
};
export default RolePermissionsList;
