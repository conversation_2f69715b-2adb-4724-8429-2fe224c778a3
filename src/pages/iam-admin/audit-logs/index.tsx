import { DataGrid, type GridColDef } from '@mui/x-data-grid';
import { CanAccess } from '@refinedev/core';
import { List, ShowButton, useDataGrid } from '@refinedev/mui';
import React from 'react';

export const IAMUserList = () => {
  const { dataGridProps } = useDataGrid<GridColDef[]>({
    meta: {
      fields: [{ nodes: ['id', 'createdAt', 'resource', 'action', 'meta', 'author'] }],
    },
    sorters: {
      initial: [{ field: 'createdAt', order: 'desc' }],
    },
  });

  const columns = React.useMemo<GridColDef[]>(
    () => [
      {
        field: 'createdAt',
        headerName: 'Timestamp',
        minWidth: 200,
      },
      {
        field: 'resource',
        headerName: 'Resource',
        minWidth: 100,
      },
      {
        field: 'action',
        headerName: 'Action',
        minWidth: 100,
      },
      {
        field: 'meta',
        headerName: 'resource id',
        minWidth: 150,
        renderCell: ({ value }) => {
          return <div>{value.id}</div>;
        },
      },
      {
        field: 'author',
        headerName: 'Actor',
        minWidth: 250,
        renderCell: ({ value }) => {
          return (
            <div>
              {value.name} ({value.email})
            </div>
          );
        },
      },
      {
        field: 'actions',
        headerName: 'Actions',
        sortable: false,
        renderCell: function render({ row }) {
          return (
            <>
              <ShowButton hideText recordItemId={row.id} />
            </>
          );
        },
        align: 'center',
        headerAlign: 'center',
      },
    ],
    [],
  );

  return (
    <CanAccess>
      <List>
        <DataGrid {...dataGridProps} density="compact" columns={columns} autoHeight />
      </List>
    </CanAccess>
  );
};
export default IAMUserList;
