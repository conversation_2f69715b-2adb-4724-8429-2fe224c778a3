import { Paper, Stack, Typography } from '@mui/material';
import { CanAccess, useShow } from '@refinedev/core';
import ReactDiffViewer from 'react-diff-viewer-continued';

import { DateField, Show } from '@refinedev/mui';

import { useContext } from 'react';
import { ColorModeContext } from 'theme/ColorModeContextProvider';

export default function AuditLogShow() {
  const { queryResult } = useShow({
    meta: {
      fields: ['id', 'createdAt', 'resource', 'action', 'meta', 'author', 'previousData', 'data'],
    },
  });
  const { mode } = useContext(ColorModeContext);
  const { data, isLoading } = queryResult;

  const record = data?.data;

  return (
    <CanAccess>
      <Show isLoading={isLoading}>
        <Stack gap={1}>
          <DateField value={record?.createdAt} />
          <Typography variant="body1" fontWeight="bold">
            Resource
          </Typography>
          <Typography variant="body1">{record?.resource}</Typography>
          <Typography variant="body1" fontWeight="bold">
            Action
          </Typography>
          <Typography variant="body1">{record?.action}</Typography>
          <Typography variant="body1" fontWeight="bold">
            Author
          </Typography>
          <Paper elevation={0}>
            <Typography component="pre" variant="body2">
              {JSON.stringify(record?.author, null, 2)}
            </Typography>
          </Paper>
          <Typography variant="body1" fontWeight="bold">
            Meta
          </Typography>
          <Paper elevation={0}>
            <Typography component="pre" variant="body2">
              {JSON.stringify(record?.meta, null, 2)}
            </Typography>
          </Paper>
          <Paper elevation={1}>
            <Typography variant="body1" fontWeight="bold">
              Changes
            </Typography>
            <ReactDiffViewer
              oldValue={JSON.stringify(record?.previousData, null, 2)}
              newValue={JSON.stringify(record?.data, null, 2)}
              splitView
              hideLineNumbers
              leftTitle="Previous Data"
              rightTitle="New Data"
              useDarkTheme={mode === 'dark'}
              styles={{
                line: { wordBreak: 'break-all' },
              }}
            />
          </Paper>
        </Stack>
      </Show>
    </CanAccess>
  );
}
