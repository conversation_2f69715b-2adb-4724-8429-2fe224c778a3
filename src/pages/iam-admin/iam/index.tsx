import { DataGrid, type GridColDef } from '@mui/x-data-grid';
import { CanAccess } from '@refinedev/core';
import { DeleteButton, EditButton, EmailField, List, useDataGrid } from '@refinedev/mui';
import React from 'react';

export const IAMUserList = () => {
  const { dataGridProps } = useDataGrid({
    dataProviderName: 'sorcery',
    meta: {
      fields: [{ nodes: ['id', 'name', 'email', 'roles'] }],
    },
  });

  const columns = React.useMemo<GridColDef[]>(
    () => [
      {
        field: 'name',
        headerName: 'Name',
        minWidth: 200,
      },
      {
        field: 'email',
        flex: 1,
        headerName: 'Email',
        minWidth: 250,
        renderCell: function render({ value }) {
          return <EmailField value={value} />;
        },
      },
      {
        field: 'roles',
        flex: 1,
        headerName: 'Roles',
      },
      {
        field: 'actions',
        headerName: 'Actions',
        sortable: false,
        renderCell: function render({ row }) {
          return (
            <>
              <EditButton hideText recordItemId={row.id} />
              <DeleteButton
                hideText
                recordItemId={row.id}
                confirmTitle="This action cannot be undone. Are you sure you want to delete Principal?"
              />
            </>
          );
        },
        align: 'center',
        headerAlign: 'center',
        minWidth: 80,
      },
    ],
    [],
  );

  return (
    <CanAccess>
      <List>
        <DataGrid {...dataGridProps} columns={columns} autoHeight />
      </List>
    </CanAccess>
  );
};
export default IAMUserList;
