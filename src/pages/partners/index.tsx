import type { GridColDef } from '@mui/x-data-grid';
import { CanAccess } from '@refinedev/core';
import { List, type ListProps, ShowButton, UrlField, useDataGrid } from '@refinedev/mui';
import React from 'react';
import { DataGrid } from '../../components/data-grid/DataGrid';

const PartnerList = ({ filter }: { filter: { ids: string[] } }) => {
  const { dataGridProps } = useDataGrid({
    resource: 'partners',
    filters: {
      ...(!!filter?.ids && { permanent: [{ field: 'id', operator: 'in', value: filter.ids }] }),
    },
    meta: {
      fields: [
        { nodes: ['id', 'externalId', 'name', 'email', 'phone', { parent: ['id', 'name'] }] },
      ],
    },
    ...(!!filter?.ids && { syncWithLocation: false }),
  });

  const columns = React.useMemo<GridColDef[]>(
    () => [
      {
        field: 'id',
        headerName: 'Id',
        minWidth: 150,
      },
      {
        field: 'externalId',
        headerName: 'External ID',
        minWidth: 150,
        renderCell: ({ row }) => (
          <UrlField href={`/partners/show/${row.id}`} value={row.externalId} />
        ),
      },
      {
        field: 'name',
        headerName: 'Name',
        minWidth: 250,
      },
      {
        field: 'parent.name',
        headerName: 'Parent Partner',
        minWidth: 200,
        renderCell: ({ row }) => (
          <UrlField href={`/partners/show/${row.parent?.id}`} value={row.parent?.name} />
        ),
      },
      {
        field: 'actions',
        headerName: 'Actions',
        renderCell: function render({ row }) {
          return (
            <>
              <ShowButton resource="partners" recordItemId={row.id} />
            </>
          );
        },
        align: 'center',
        headerAlign: 'center',
        minWidth: 80,
        filterable: false,
      },
    ],
    [],
  );

  return (
    <CanAccess>
      <List
        title="Partners"
        resource="partners"
        {...(filter && { breadcrumb: false, canCreate: false })}
      >
        <DataGrid {...dataGridProps} columns={columns} />
      </List>
    </CanAccess>
  );
};

export default PartnerList;
