import HiddenInput from '@components/forms/HiddenInput';
import JsonForm from '@components/forms/JsonForm';
import EligibilitySetup from '@components/partners/setup/Eligibility';
import ThirdPartyApplicantTypes from '@components/partners/setup/ThirdPartyApplicantTypes';
import { Box, Button, Step, StepButton, Stepper } from '@mui/material';
import { CanAccess, useCreate, useParsed } from '@refinedev/core';
import { Create, SaveButton } from '@refinedev/mui';
import { useForm, useStepsForm } from '@refinedev/react-hook-form';
import { getConfig } from '@utils/config';
import promisify from '@utils/promisify';
import AdminAccounts from 'components/partners/setup/AdminAccounts';
import Branding from 'components/partners/setup/Branding';
import FundSetup from 'components/partners/setup/FundSetup';
import PlatformSetup from 'components/partners/setup/PlatformSetup';
import ProgramSetup from 'components/partners/setup/ProgramSetup';
import useAuthorization from 'hooks/identity/useAuthorization';
import { Relation } from 'pages/api/types/identity';
import { FormProvider } from 'react-hook-form';
import configSchema from 'schemas/partners/config.json';
import uiSchema from 'schemas/partners/ui.json';

const STEPS = [
  'Platform Setup',
  'Branding',
  'Fund Setup',
  'Third Party Types',
  'Program Setup',
  'Admin Accounts',
  'Eligibility',
  'Config',
];

const PartnerCreate = () => {
  const { params } = useParsed();
  const { parentId } = params ?? {};

  // TODO: Implement create many
  const { mutate: createMutation } = useCreate();

  // TODO: create many funding sources instead of doing roundabout client update
  const paymentsFormProps = useForm({
    refineCoreProps: {
      id: getConfig('PUBLIC_PAYMENT_CLIENT_ID'),
      action: 'edit',
      dataProviderName: 'payment',
      resource: 'clients',
      redirect: false,
      meta: { fields: ['id'] },
    },
  });

  const adminsFormProps = useForm({
    refineCoreProps: {
      action: 'create',
      resource: 'users',
      redirect: false,
    },
  });

  const authorization = useAuthorization();

  const createRelationships = async (data) => {
    const { id, parentId } = data;
    const funds = data.funds?.create;
    const programs = data.programs?.create;
    return authorization.createRelationships({
      partnerId: id,
      relationships: [
        {
          relation: 'platform',
          object: { objectId: id, objectType: Relation.ORGANIZATION },
          subject: { objectId: 'platform', objectType: Relation.PLATFORM },
        },
        !!parentId && {
          relation: 'parent',
          object: { objectId: id, objectType: Relation.ORGANIZATION },
          subject: { objectId: parentId, objectType: Relation.ORGANIZATION },
        },
        ...(programs?.map((program: { id: string }) => ({
          relation: 'org',
          object: { objectId: program.id, objectType: Relation.PROGRAM },
          subject: { objectId: id, objectType: Relation.ORGANIZATION },
        })) ?? []),
        ...(funds?.map((fund: { id: string }) => ({
          relation: 'org',
          object: { objectId: fund.id, objectType: Relation.FUND },
          subject: { objectId: id, objectType: Relation.ORGANIZATION },
        })) ?? []),
      ].filter(Boolean),
    });
  };

  const formProps = useStepsForm({
    refineCoreProps: {
      redirect: 'show',
      onMutationSuccess: async (_, variables) => {
        const users = adminsFormProps.getValues('users');
        console.log('users =>', users);
        if (users?.length) await authorization.createAdmins(users);
        await createRelationships(variables);
      },
    },
  });

  const {
    saveButtonProps: { disabled, onClick },
    refineCore: { formLoading },
    handleSubmit,
    setValue,
    steps: { currentStep, gotoStep },
  } = formProps;

  const onSubmit = async (data, e) => {
    const {
      programs: { create: programs },
    } = data;
    const appConfigs = programs
      .flatMap(({ id: programId, programApplicantTypes: { create: programApplicantTypes } }) =>
        programApplicantTypes.map(({ applicantTypeId, configurationId }) => ({
          applicantTypeId,
          configurationId,
          programId,
        })),
      )
      .filter(({ configurationId }) => !!configurationId);
    const verificationConfigs = programs
      .filter(({ verificationConfiguration }) => !!verificationConfiguration)
      .map(({ id: programId, verificationConfiguration }) => ({
        ...verificationConfiguration,
        programId,
      }));

    for (const [programIdx, program] of programs.entries()) {
      setValue(`programs.create.${programIdx}.verificationConfiguration`, undefined);
      for (const typeIdx of program.programApplicantTypes.create.keys())
        setValue(
          `programs.create.${programIdx}.programApplicantTypes.create.${typeIdx}.configurationId`,
          undefined,
        );
    }

    await Promise.all([
      ...appConfigs.map((cf) =>
        promisify(createMutation, {
          resource: 'program_application_configurations',
          dataProviderName: 'config',
          values: cf,
        }),
      ),
      ...verificationConfigs.map((values) =>
        promisify(createMutation, {
          resource: 'configs',
          dataProviderName: 'verification',
          values,
        }),
      ),
    ]);

    paymentsFormProps.saveButtonProps.onClick(e);
    return onClick(e);
  };

  return (
    <CanAccess>
      <Create
        isLoading={formLoading}
        footerButtons={
          <>
            {currentStep > 0 && <Button onClick={() => gotoStep(currentStep - 1)}>Previous</Button>}
            {currentStep < STEPS.length - 1 && (
              <Button onClick={() => gotoStep(currentStep + 1)}>Next</Button>
            )}
            {currentStep === STEPS.length - 1 && (
              <SaveButton disabled={disabled} onClick={handleSubmit(onSubmit)} />
            )}
          </>
        }
      >
        <Box component="form" sx={{ display: 'flex', flexDirection: 'column' }} autoComplete="off">
          <FormProvider {...formProps}>
            <Stepper activeStep={currentStep} sx={{ marginBottom: '1rem' }}>
              {STEPS.map((step, idx) => (
                <Step key={step}>
                  <StepButton onClick={() => gotoStep(idx)}>{step}</StepButton>
                </Step>
              ))}
            </Stepper>
            {parentId && <HiddenInput name="parentId" value={parentId} />}
            {(() => {
              switch (STEPS[currentStep]) {
                case 'Platform Setup':
                  return <PlatformSetup />;
                case 'Branding':
                  return <Branding />;
                case 'Third Party Types':
                  return <ThirdPartyApplicantTypes />;
                case 'Program Setup':
                  return <ProgramSetup />;
                case 'Fund Setup':
                  return <FundSetup paymentsFormProps={paymentsFormProps} />;
                case 'Admin Accounts':
                  return (
                    <AdminAccounts
                      formProps={adminsFormProps}
                      partnerId={formProps.getValues('id')}
                    />
                  );
                case 'Eligibility':
                  return <EligibilitySetup />;
                case 'Config':
                  return <JsonForm name="config" schema={configSchema} uiSchema={uiSchema} />;
                default:
                  return <></>;
              }
            })()}
          </FormProvider>
        </Box>
      </Create>
    </CanAccess>
  );
};
export default PartnerCreate;
