import { PartnerAdminList } from '@components/admins';
import AnalyticsResourceList from '@components/analytics/AnalyticsResourceList';
import ApplicantTypesList from '@components/applicantTypes/ApplicantTypesList';
import { AddFeatureDrawer } from '@components/features/AddFeatureDrawer';
import { EditFeatureDrawer } from '@components/features/EditFeatureDrawer';
import FeatureToggle from '@components/features/FeatureToggle';
import {
  type EditBrandImagesCustomModalProps,
  EditBrandImagesModal,
} from '@components/forms/images/EditBrandImagesModal';
import EditableBrandingImage from '@components/forms/images/EditableBrandingImage';
import { PartnerFundList } from '@components/funds';
import Markdown from '@components/markdown/Markdown';
import Link from '@components/navigation/Link';
import NotificationTemplateList from '@components/notificationTemplates/NotificationTemplateList';
import { AddBannerDrawer } from '@components/partners/AddBannerDrawer';
import AddChildPartnerDrawer from '@components/partners/AddChildPartnerDrawer';
import BeamSAMLButton from '@components/partners/BeamSAMLButton';
import CreatePartnerDialog from '@components/partners/CreatePartnerDialog';
import { EditBannerDrawer } from '@components/partners/EditBannerDrawer';
import { PartnerCommunicationChannels } from '@components/partners/PartnerCommunicationChannels';
import { PartnerNotifications } from '@components/partners/PartnerNotifications';
import ReportButton from '@components/partners/ReportButton';
import ReportDrawer from '@components/partners/ReportDrawer';
import PartnerIdentityConfig from '@components/partners/identity/PartnerIdentityConfig';
import { PartnerProgramList } from '@components/programs/PartnerProgramList';
import { PartnerVendorTypeList } from '@components/vendorTypes/PartnerVendorTypeList';
import { materialCells, materialRenderers } from '@jsonforms/material-renderers';
import { JsonForms } from '@jsonforms/react';
import { Delete, Edit } from '@mui/icons-material';
import {
  Button,
  Chip,
  Divider,
  IconButton,
  ListItem,
  ListItemText,
  ListSubheader,
  Link as MUILink,
  List as MUIList,
  Stack,
  Typography,
} from '@mui/material';
import { CanAccess, useDelete, useModal, useShow } from '@refinedev/core';
import { List, Show } from '@refinedev/mui';
import { useModalForm } from '@refinedev/react-hook-form';
import { isProduction } from '@utils/env';
import useFeatures from 'hooks/features/useFeatures';
import { Fragment, useEffect, useMemo, useState } from 'react';
import configSchema from 'schemas/partners/config.json';
import uiSchema from 'schemas/partners/ui.json';

const PartnerShow = () => {
  const { queryResult } = useShow({
    meta: {
      fields: [
        'id',
        'externalId',
        'name',
        'email',
        'phone',
        'config',
        'gleanInviteToken',
        { parent: ['id', 'name'] },
        { childPartners: [{ nodes: ['id', 'name'] }] },
        { partnerWhitelabelingsList: ['id', 'senderEmail', 'brandColor', 'logo', 'favicon'] },
        { eligibilityConfigs: [{ nodes: ['id'] }] },
        { partnerIncidents: [{ nodes: ['id', { incident: ['id', 'message', 'severity'] }] }] },
        {
          partnerFeatures: [
            { nodes: ['id', 'featureId', 'enabled', { feature: ['name', 'description'] }] },
          ],
        },
      ],
    },
  });

  const { data, isLoading, refetch } = queryResult;

  const partner = data?.data;
  const config = useMemo(() => partner?.config && partner.config, [partner]);
  const whitelabeling = partner?.partnerWhitelabelingsList[0] ?? {};
  const incidentMessages = partner?.partnerIncidents ?? {};

  const createBannerDrawerProps = useModalForm({
    syncWithLocation: false,
    warnWhenUnsavedChanges: false,
    refineCoreProps: {
      resource: 'partnerIncidents',
      action: 'create',
      redirect: false,
      onMutationSuccess: () => {
        queryResult.refetch();
      },
    },
  });

  const editBannerDrawerProps = useModalForm({
    syncWithLocation: false,
    warnWhenUnsavedChanges: false,
    refineCoreProps: {
      resource: 'incidentMessages',
      action: 'edit',
      redirect: false,
      meta: { fields: ['id', 'message', 'severity'] },
      onMutationSuccess: () => {
        queryResult.refetch();
      },
    },
  });

  const { mutate: deleteMessage } = useDelete();
  const deleteBannerMessage = async (partnerIncidentId: string, incidentMessageId: string) => {
    deleteMessage(
      {
        resource: 'partnerIncidents',
        id: partnerIncidentId,
        successNotification: false,
      },
      {
        onSuccess: () => {
          deleteMessage(
            {
              resource: 'incidentMessages',
              id: incidentMessageId,
              successNotification: () => ({
                description: 'Success',
                message: 'Banner message deleted',
                type: 'success',
              }),
            },
            { onSuccess: () => refetch() },
          );
        },
      },
    );
  };

  const [imageUploadModalProps, setImageUploadModalProps] =
    useState<EditBrandImagesCustomModalProps>({
      partnerExternalId: data?.data?.externalId,
      fieldToUpdate: '',
      fieldLabel: '',
      imageFilename: '',
    });
  const editBrandImagesModalFormProps = useModalForm({
    refineCoreProps: {
      action: 'edit',
      resource: 'partnerWhitelabeling',
      redirect: false,
      meta: {
        fields: [
          'id',
          'logo',
          'favicon',
          {
            partner: ['externalId'],
          },
        ],
      },
      invalidates: ['all'],
    },
  });

  const createFeatureDrawerFormProps = useModalForm({
    syncWithLocation: false,
    refineCoreProps: {
      resource: 'partnerFeatures',
      action: 'create',
      redirect: false,
      onMutationSuccess: () => {
        refetch();
      },
    },
  });

  const editFeatureDrawerFormProps = useModalForm({
    syncWithLocation: false,
    refineCoreProps: {
      resource: 'partnerFeatures',
      meta: { fields: ['id', 'featureId', 'enabled', { feature: ['name', 'description'] }] },
      action: 'edit',
      redirect: false,
      onMutationSuccess: () => {
        refetch();
      },
    },
  });

  const [partnerFeaturesBucket, setPartnerFeaturesBucket] = useState({});

  const { lookupDeps } = useFeatures();

  useEffect(() => {
    if (!partner?.partnerFeatures) return;

    const updatedBucket = {};
    for (const item of partner.partnerFeatures) {
      const feature = item?.feature;
      const featureName = feature?.name;
      const bucketName = featureName?.split(':')[0] ?? 'Uncategorized';

      const itemWithDeps = { ...item, feature: { ...item.feature, deps: lookupDeps(featureName) } };
      if (updatedBucket[bucketName]) {
        updatedBucket[bucketName].push(itemWithDeps);
      } else {
        updatedBucket[bucketName] = [itemWithDeps];
      }
    }

    setPartnerFeaturesBucket(updatedBucket);
  }, [partner, lookupDeps]);

  const reportDrawer = useModal();

  const {
    modal: { show: showEditBrandImagesModal },
  } = editBrandImagesModalFormProps;

  const createPartnerModalProps = useModal();
  const addChildPartnerDrawerProps = useModal();

  const getPlatformLink = (advocatePortal = false) => {
    const baseUrl = new URL(`https://app.${!isProduction() ? 'dev.' : ''}bybeam.co`);
    if (advocatePortal) baseUrl.pathname = 'partner/';
    if (partner?.externalId) baseUrl.pathname = `${baseUrl.pathname}${partner.externalId}`;
    return baseUrl.href;
  };

  return (
    <CanAccess>
      <Show
        isLoading={isLoading}
        title={<Typography variant="h5">{partner?.name}</Typography>}
        headerButtons={({ defaultButtons }) => (
          <>
            {partner && (
              <>
                <ReportButton partner={partner} openDrawer={(): void => reportDrawer.show()} />
                <BeamSAMLButton partner={partner} baseUrl={getPlatformLink(true)} />
              </>
            )}
            {defaultButtons}
          </>
        )}
      >
        <Stack gap={1}>
          <MUIList
            sx={{
              width: '100%',
              bgcolor: 'background.paper',
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
            }}
          >
            <ListItem>
              <ListItemText primary={partner?.name} secondary="Name" />
            </ListItem>
            <ListItem>
              <ListItemText primary={partner?.id} secondary="UUID" />
              {partner?.parent && (
                <ListItemText
                  primary={
                    <MUILink href={`/partners/show/${partner?.parent?.id}`}>
                      {partner?.parent?.name}
                    </MUILink>
                  }
                  secondary="Parent"
                />
              )}
            </ListItem>
            <ListItem>
              <ListItemText
                primary={
                  <MUILink href={getPlatformLink()} target="_blank" rel="noreferrer">
                    {partner?.externalId}
                  </MUILink>
                }
                secondary="External ID / URL"
              />
            </ListItem>
            <ListItem>
              <ListItemText primary={partner?.email} secondary="Email" />
            </ListItem>
            <ListItem>
              <ListItemText primary={partner?.phone} secondary="Phone" />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={whitelabeling?.senderEmail ?? '-'}
                secondary="Whitelabeled Email Sender"
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={
                  <Chip
                    sx={{ backgroundColor: whitelabeling?.brandColor ?? 'white' }}
                    label={whitelabeling?.brandColor ?? '-'}
                  />
                }
                secondary="Brand Color"
              />
            </ListItem>
          </MUIList>

          <Divider />

          <Typography variant="h5">Brand Images</Typography>

          <Stack direction="row" divider={<Divider orientation="vertical" flexItem />} spacing={2}>
            <EditableBrandingImage
              label="Logo"
              imageUrl={whitelabeling?.logo}
              openEditModal={() => {
                setImageUploadModalProps({
                  fieldToUpdate: 'logo',
                  fieldLabel: 'Logo',
                  imageFilename: 'logo',
                  partnerExternalId: partner?.externalId,
                });
                showEditBrandImagesModal(whitelabeling.id);
              }}
            />
            <EditableBrandingImage
              label="Favicon"
              imageUrl={whitelabeling?.favicon}
              openEditModal={() => {
                setImageUploadModalProps({
                  fieldToUpdate: 'favicon',
                  fieldLabel: 'Favicon',
                  imageFilename: 'favicon',
                  partnerExternalId: partner?.externalId,
                });
                showEditBrandImagesModal(whitelabeling.id);
              }}
            />
          </Stack>

          <CanAccess resource="partner_incidents" action="show">
            <>
              <Divider />
              <List
                title="Banner Messages"
                resource="partner_incidents"
                breadcrumb={false}
                createButtonProps={
                  !incidentMessages || incidentMessages.length === 0
                    ? {
                        onClick: () => createBannerDrawerProps.modal.show(),
                        children: 'Add Banner',
                      }
                    : undefined
                }
              >
                {incidentMessages?.length > 0 &&
                  incidentMessages
                    .sort((a, b) => a.incident.severity - b.incident.severity)
                    .map(({ id, incident }) => (
                      <ListItem
                        key={incident.id}
                        secondaryAction={
                          <>
                            <IconButton
                              edge="end"
                              aria-label="edit"
                              onClick={() => editBannerDrawerProps.modal.show(incident.id)}
                            >
                              <Edit />
                            </IconButton>
                            <IconButton
                              edge="end"
                              aria-label="delete"
                              onClick={() => deleteBannerMessage(id, incident.id)}
                            >
                              <Delete />
                            </IconButton>
                          </>
                        }
                      >
                        <ListItemText>
                          <Markdown>{incident.message}</Markdown>
                        </ListItemText>
                      </ListItem>
                    ))}
              </List>
              <AddBannerDrawer {...createBannerDrawerProps} partnerId={partner?.id} />
              <EditBannerDrawer {...editBannerDrawerProps} />
            </>
          </CanAccess>

          {partner?.id && (
            <>
              <Divider />
              <PartnerAdminList partnerId={partner.id} />
              <Divider />
              <PartnerProgramList partnerId={partner.id} />
              <Divider />
              <PartnerFundList partnerId={partner.id} />
              <Divider />
              <ApplicantTypesList partner={partner} />
              <Divider />
              <PartnerVendorTypeList partnerId={partner.id} />
              <Divider />
              <AnalyticsResourceList partner={partner} />
              <Divider />
              <NotificationTemplateList filter={{ partnerId: partner.id as string }} />
              <Divider />
              <List
                resource="partner_features"
                breadcrumb={false}
                createButtonProps={{
                  onClick: () => createFeatureDrawerFormProps.modal.show(),
                  children: 'Add Feature',
                }}
              >
                {Object.keys(partnerFeaturesBucket)
                  .sort()
                  .map((partnerKey) => (
                    <Fragment key={partnerKey}>
                      <ListSubheader>{partnerKey}</ListSubheader>
                      {partnerFeaturesBucket[partnerKey].map((partnerFeature) => (
                        <FeatureToggle
                          key={partnerFeature.id}
                          featureSetting={partnerFeature}
                          enabled={partnerFeature.enabled}
                          handleToggle={() =>
                            editFeatureDrawerFormProps.modal.show(partnerFeature.id)
                          }
                        />
                      ))}
                    </Fragment>
                  ))}
              </List>
              <AddFeatureDrawer
                {...createFeatureDrawerFormProps}
                entity={{ id: partner.id as string, resource: 'partner' }}
                currentFeatures={partner.partnerFeatures}
              />
              <EditFeatureDrawer
                {...editFeatureDrawerFormProps}
                resource="partnerFeatures"
                currentFeatures={partner.partnerFeatures}
              />
              <CanAccess resource="partner_communication_channels_configurations" action="list">
                <PartnerCommunicationChannels partnerId={partner.id} refetch={refetch} />
                <PartnerNotifications partnerId={partner.id} />
              </CanAccess>
              <Divider />
            </>
          )}

          <CanAccess resource="eligibility_config" action="show">
            {partner?.eligibilityConfigs?.[0]?.id && (
              <Stack>
                <Typography variant="h5" sx={{ marginTop: 2, marginBottom: 2 }}>
                  Eligibility Config
                </Typography>
                <Link to={`/eligibilityConfig/show/${partner?.eligibilityConfigs?.[0]?.id}`}>
                  <Button variant="contained" endIcon={<Edit />}>
                    Edit
                  </Button>
                </Link>
              </Stack>
            )}
          </CanAccess>

          <Divider />

          <Typography variant="h5" sx={{ paddingTop: 2 }}>
            Config
          </Typography>
          <JsonForms
            schema={configSchema}
            uischema={uiSchema}
            data={config}
            renderers={materialRenderers}
            cells={materialCells}
            readonly
          />

          <CanAccess resource="partners" action="identity">
            <Divider />
            <PartnerIdentityConfig partner={partner} refetch={refetch} />
          </CanAccess>

          <Divider />

          <List
            resource={'partners'}
            breadcrumb={false}
            createButtonProps={{
              onClick: () => createPartnerModalProps.show(),
            }}
          >
            {partner?.childPartners.map(({ id, name }) => (
              <ListItem key={id}>
                <ListItemText primary={<MUILink href={`/partners/show/${id}`}>{name}</MUILink>} />
              </ListItem>
            ))}
          </List>
        </Stack>
      </Show>
      <EditBrandImagesModal {...editBrandImagesModalFormProps} {...imageUploadModalProps} />
      {partner && (
        <>
          <ReportDrawer {...reportDrawer} partner={partner} />
          <CreatePartnerDialog
            partnerId={partner.id}
            {...createPartnerModalProps}
            openDrawer={() => addChildPartnerDrawerProps.show()}
          />
          <AddChildPartnerDrawer
            partnerId={partner.id}
            {...addChildPartnerDrawerProps}
            onSuccess={() => {
              refetch();
            }}
          />
        </>
      )}
    </CanAccess>
  );
};
export default PartnerShow;
