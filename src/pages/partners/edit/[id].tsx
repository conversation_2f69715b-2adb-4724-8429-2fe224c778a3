import ColorPicker from '@components/forms/ColorPicker';
import HiddenInput from '@components/forms/HiddenInput';
import JsonForm from '@components/forms/JsonForm';
import TextInput from '@components/forms/TextInput';
import { Divider, Stack, Typography } from '@mui/material';
import { CanAccess } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import { FormProvider } from 'react-hook-form';
import configSchema from 'schemas/partners/config.json';
import uiSchema from 'schemas/partners/ui.json';

const PartnerEdit = () => {
  const formProps = useForm({
    refineCoreProps: {
      meta: {
        fields: [
          'id',
          'externalId',
          'name',
          'email',
          'phone',
          'config',
          'gleanInviteToken',
          { partnerWhitelabelings: [{ nodes: ['id', 'senderEmail', 'brandColor'] }] },
        ],
      },
      redirect: 'show',
    },
  });
  const { saveButtonProps, getValues, handleSubmit, setValue } = formProps;

  const onSubmit = async (_, e) => {
    const { phone } = getValues();
    if (!phone) setValue('phone', null);
    saveButtonProps.onClick(e);
    return saveButtonProps.onClick(e);
  };

  return (
    <CanAccess>
      <Edit saveButtonProps={{ ...saveButtonProps, onClick: handleSubmit(onSubmit) }}>
        <FormProvider {...formProps}>
          <Stack component="form" gap={1} autoComplete="off">
            <TextInput name="externalId" label="External ID" required />
            <TextInput name="name" label="Name" required />
            <TextInput name="email" label="Email" type="email" required />
            <TextInput name="phone" label="Phone" type="tel" />
            <HiddenInput name="partnerWhitelabelings.0.id" />
            <TextInput
              name="partnerWhitelabelings.0.senderEmail"
              label="Whitelabeled Sender Email"
              type="email"
              defaultValue={null}
            />
            <TextInput
              name="gleanInviteToken"
              label="Glean Invite Token"
              helperText="This should *not* include the complete url, just the token value"
            />
            <ColorPicker name="partnerWhitelabelings.0.brandColor" />
            <Divider />
            <Typography variant="h5">Config</Typography>
            <JsonForm name="config" schema={configSchema} uiSchema={uiSchema} />
          </Stack>
        </FormProvider>
      </Edit>
    </CanAccess>
  );
};

export default PartnerEdit;
