import AutocompleteInput from '@components/forms/AutocompleteInput';
import TextInput from '@components/forms/TextInput';
import { Data } from '@measured/puck';
import { Stack } from '@mui/material';
import { CanAccess, useNotification, useUpdate } from '@refinedev/core';
import { Create } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import axios, { AxiosError } from 'axios';
import { useRouter } from 'next/router';
import { FormProvider } from 'react-hook-form';

interface IExternalApiUser {
  appId: string;
  partnerId: string;
  email: string;
}

const ExternalApiUserCreate = () => {
  const { open: notify } = useNotification();
  const formProps = useForm<IExternalApiUser>({
    refineCoreProps: { redirect: 'show' },
  });
  const router = useRouter();
  const {
    saveButtonProps,
    getValues,
    refineCore: { formLoading },
    handleSubmit,
  } = formProps;

  async function onSubmit() {
    const data = getValues();

    try {
      await axios.post(
        '/api/platform/identity/externalApiUser/save',
        {
          appId: data.appId,
          partnerId: data.partnerId,
          email: data.email,
        },
        { headers: { 'Content-Type': 'application/json' } },
      );
      notify?.({
        message: 'successfully added external api user',
        type: 'success',
      });
      router.back();
    } catch (e) {
      const msg = e instanceof AxiosError ? e.response?.data?.error : (e as Error).message;
      notify?.({ message: `Issue: ${msg}`, type: 'error' });
    }
  }

  return (
    <CanAccess>
      <Create
        isLoading={formProps.refineCore.formLoading}
        saveButtonProps={{ onClick: handleSubmit(onSubmit) }}
      >
        <FormProvider {...formProps}>
          <Stack component="form" autoComplete="off">
            <AutocompleteInput name="partnerId" label="Partner" resource="partners" required />
            <TextInput name="appId" label="App ID" required />
            <TextInput name="email" label="Email" type="email" required />
          </Stack>
        </FormProvider>
      </Create>
    </CanAccess>
  );
};

export default ExternalApiUserCreate;
