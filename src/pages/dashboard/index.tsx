import { Stats, SystemStatus } from '@components/dashboard/index';
import { Box, Container, Typography } from '@mui/material';

const DashboardPage = () => {
  return (
    <Box
      component="main"
      sx={{
        flexGrow: 1,
      }}
    >
      <Container maxWidth={false}>
        <Typography variant="h4" color="textPrimary" sx={{ paddingY: 3 }}>
          Beam Internal Tooling
        </Typography>
        <Box sx={{ display: 'flex', flexDirection: 'column', flexWrap: 'wrap', gap: 3 }}>
          <Stats />
          <SystemStatus />
        </Box>
      </Container>
    </Box>
  );
};
export default DashboardPage;
