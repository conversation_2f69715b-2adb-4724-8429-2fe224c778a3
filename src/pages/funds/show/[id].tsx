import PaymentsList from '@components/payments/PaymentsList';
import { materialCells, materialRenderers } from '@jsonforms/material-renderers';
import { JsonForms } from '@jsonforms/react';
import {
  Divider,
  Link,
  List as MuiList,
  ListItem,
  ListItemText,
  Stack,
  Typography,
} from '@mui/material';
import { CanAccess, useShow } from '@refinedev/core';
import { Show } from '@refinedev/mui';
import useFundingSource from 'hooks/funds/useFundingSource';
import configSchema from 'schemas/fundingSources/config.json';
import uiSchema from 'schemas/fundingSources/ui.json';
import { displayCurrency } from 'utils/currency';
import { aggregateFulfillments } from 'utils/operations';

export default function ViewFund() {
  const {
    queryResult: { data, isLoading },
  } = useShow({
    meta: {
      fields: [
        'id',
        'name',
        'startingBalance',
        {
          partner: ['id', 'name'],
        },
        aggregateFulfillments(['SUCCESS', 'INITIATED', 'FAILED', 'AUTHORIZED']),
      ],
    },
  });
  const fund = data?.data;

  const { fundingSourceKeys } = useFundingSource(fund?.id as string);

  // TODO refresh button does not work
  return (
    <CanAccess>
      <Show isLoading={isLoading} title={<Typography variant="h5">{fund?.name}</Typography>}>
        <Stack gap={1}>
          <MuiList
            sx={{
              width: '100%',
              bgcolor: 'background.paper',
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
            }}
          >
            <ListItem>
              <ListItemText primary={fund?.name} secondary="Name" />
            </ListItem>
            <ListItem>
              <ListItemText primary={fund?.id} secondary="ID" />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={displayCurrency(fund?.startingBalance)}
                secondary="Starting Balance"
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={displayCurrency(
                  fund?.startingBalance - fund?.fulfillments?.aggregates?.sum?.approvedAmount,
                )}
                secondary="Remaining Balance"
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={
                  <Link href={`/partners/show/${fund?.partner.id}`}>{fund?.partner.name}</Link>
                }
                secondary="Partner"
              />
            </ListItem>
          </MuiList>
        </Stack>

        <Divider />

        <Typography variant="h5" marginTop={5} marginBottom={5}>
          Payment Funding Source Keys
        </Typography>
        <JsonForms
          schema={configSchema}
          uischema={uiSchema}
          data={fundingSourceKeys}
          renderers={materialRenderers}
          cells={materialCells}
          readonly
        />

        <Divider />

        {fund?.id && <PaymentsList filter={{ fundId: fund.id as string }} />}
      </Show>
    </CanAccess>
  );
}
