import type { GridColDef } from '@mui/x-data-grid';
import { CanAccess } from '@refinedev/core';
import { List, ShowButton, useDataGrid } from '@refinedev/mui';
import React from 'react';
import { displayCurrency } from 'utils/currency';
import { DataGrid } from '../../components/data-grid/DataGrid';

const FundList = () => {
  const { dataGridProps } = useDataGrid({
    meta: {
      fields: [{ nodes: ['id', 'name', 'startingBalance', { partner: ['id', 'name'] }] }],
    },
  });

  const columns = React.useMemo<GridColDef[]>(
    () => [
      {
        field: 'id',
        headerName: 'Id',
        flex: 1,
      },
      {
        field: 'name',
        headerName: 'Name',
        flex: 1,
      },
      {
        field: 'partner.name',
        headerName: 'Partner',
        flex: 1,
        valueGetter: (params) => params.row.partner?.name,
      },
      {
        field: 'startingBalance',
        headerName: 'Starting Balance',
        flex: 1,
        valueGetter: (params) => displayCurrency(params.row.startingBalance),
        filterable: false,
      },
      // TODO make generic component, using this everywhere
      {
        field: 'actions',
        headerName: 'Actions',
        renderCell: function render({ row }) {
          return (
            <>
              <ShowButton resource="funds" recordItemId={row.id} />
            </>
          );
        },
        align: 'center',
        headerAlign: 'center',
        minWidth: 80,
        filterable: false,
      },
    ],
    [],
  );

  return (
    <CanAccess>
      <List>
        <DataGrid {...dataGridProps} columns={columns} />
      </List>
    </CanAccess>
  );
};

export default FundList;
