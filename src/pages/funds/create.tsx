import AutocompleteInput from '@components/forms/AutocompleteInput';
import FundFields from '@components/funds/FundFields';
import { CanAccess } from '@refinedev/core';
import { Create } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import { FormProvider } from 'react-hook-form';

const FundCreate = () => {
  const fundingSourceformProps = useForm({
    refineCoreProps: {
      dataProviderName: 'payment',
      resource: 'fundingSources',
      redirect: false,
    },
  });

  const {
    saveButtonProps: { onClick: onClickFundingSource },
  } = fundingSourceformProps;

  const platformFormProps = useForm();
  const {
    saveButtonProps: { disabled, onClick },
    refineCore: { formLoading },
    handleSubmit,
  } = platformFormProps;

  const onSubmit = (_, e) => {
    onClickFundingSource(e);
    return onClick(e);
  };

  return (
    <CanAccess>
      <Create
        isLoading={formLoading}
        saveButtonProps={{ disabled, onClick: handleSubmit(onSubmit) }}
      >
        <FormProvider {...platformFormProps}>
          <AutocompleteInput name="partnerId" label="Partner" resource="partners" required />
          <FundFields paymentsFormProps={fundingSourceformProps} />
        </FormProvider>
      </Create>
    </CanAccess>
  );
};

export default FundCreate;
