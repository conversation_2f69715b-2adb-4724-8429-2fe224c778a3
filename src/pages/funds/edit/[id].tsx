import FundFields from '@components/funds/FundFields';
import { Box } from '@mui/material';
import { CanAccess, useCreate, useUpdate } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import promisify from '@utils/promisify';
import useFundingSource from 'hooks/funds/useFundingSource';
import { useEffect } from 'react';
import { FormProvider } from 'react-hook-form';
import { aggregateFulfillments } from 'utils/operations';

export default function EditFund() {
  const formProps = useForm({
    refineCoreProps: {
      meta: {
        fields: [
          'id',
          'name',
          'startingBalance',
          'defaultPaymentFieldKey',
          aggregateFulfillments(['SUCCESS', 'INITIATED', 'FAILED', 'AUTHORIZED']),
        ],
      },
      redirect: 'show',
    },
  });
  const {
    saveButtonProps: { disabled, onClick },
    handleSubmit,
    refineCore: { queryResult },
  } = formProps;

  const fund = queryResult?.data?.data;

  const { fundingSource } = useFundingSource(fund?.id as string);

  const fundingSourceFormProps = useForm({
    refineCoreProps: {
      action: fundingSource ? 'edit' : 'create',
      dataProviderName: 'payment',
      resource: 'fundingSources',
      meta: {
        fields: ['id', 'referenceId', 'provider', 'clientId', 'keys'],
      },
      redirect: false,
    },
  });

  const {
    refineCore: { setId: setFundingSourceId },
    saveButtonProps: { onClick: onClickFundingSource },
  } = fundingSourceFormProps;

  useEffect(() => {
    if (fundingSource) {
      setFundingSourceId(fundingSource.id);
    }
  }, [fundingSource, setFundingSourceId]);

  const { mutate: create } = useCreate();
  const { mutate: update } = useUpdate();
  const updateFundingSource = async (e) => {
    if (!fundingSource) return;

    const fundingSourceValues = fundingSourceFormProps.getValues();
    if (fundingSource.keys?.programId === fundingSourceValues.keys?.programId)
      return onClickFundingSource(e);

    await promisify(update, {
      resource: 'fundingSources',
      dataProviderName: 'payment',
      id: fundingSource.id as string,
      values: {
        referenceId: `${fundingSource.referenceId}-old-${Date.now()}`,
      },
    });

    // TODO: What if it's being changed "back" to a previous value?
    // Worth trying to find an already existing funding source and resetting the
    // reference id?
    await promisify(create, {
      resource: 'fundingSources',
      dataProviderName: 'payment',
      values: fundingSourceValues,
    });
  };

  const onSubmit = async (_, e) => {
    await updateFundingSource(e);
    return onClick(e);
  };

  return (
    <CanAccess>
      <Edit saveButtonProps={{ disabled, onClick: handleSubmit(onSubmit) }}>
        <FormProvider {...formProps}>
          <Box
            component="form"
            sx={{ display: 'flex', flexDirection: 'column' }}
            autoComplete="off"
          >
            <FundFields
              paymentsFormProps={fundingSourceFormProps}
              initialData={{
                id: (fund?.id ?? '') as string,
                awardedBalance: Number(
                  queryResult?.data?.data?.fulfillments?.aggregates?.sum?.approvedAmount ?? '0',
                ),
              }}
            />
          </Box>
        </FormProvider>
      </Edit>
    </CanAccess>
  );
}
