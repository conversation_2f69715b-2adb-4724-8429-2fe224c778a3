import ApplicationsList from '@components/applications/ApplicationsList';
import ChangeOwnerDrawer from '@components/cases/ChangeOwnerDrawer';
import ChangeStatusDrawer from '@components/cases/ChangeStatusDrawer';
import LinkApplicationDrawer from '@components/cases/LinkApplicationDrawer';
import Link from '@components/navigation/Link';
import PaymentsList from '@components/payments/PaymentsList';
import PermissionsList from '@components/permissions/PermissionsList';
import WorkflowEventsList from '@components/workflows/WorkflowEventsList';
import { Button, Divider, List, ListItem, ListItemText, Stack, Typography } from '@mui/material';
import { CanAccess, useModal, useShow } from '@refinedev/core';
import { Show } from '@refinedev/mui';
import { useModalForm } from '@refinedev/react-hook-form';
import { displayDate } from '@utils/date';
import { isMultiPartyApplication } from '@utils/multiparty';
import { Relation } from 'pages/api/types/identity';
import { useState } from 'react';

export default function ShowCase() {
  const [showPermissionsSection, setShowPermissionsSection] = useState(false);
  const {
    queryResult: { data, isLoading, refetch },
  } = useShow({
    meta: {
      fields: [
        'id',
        'name',
        'status',
        'createdAt',
        'decisionReachedAt',
        {
          applicationsList: [
            'id',
            'submittedAt',
            {
              submitter: [
                'id',
                'name',
                { applicantProfile: ['id', { applicantType: ['id', 'name'] }] },
              ],
            },
          ],
        },
        {
          program: [
            'id',
            'name',
            'config',
            'partnerId',
            { programApplicantTypesList: ['id', 'applicantTypeId', 'nameOverride'] },
          ],
        },
      ],
    },
  });

  const case_ = data?.data;

  const statusDrawerFormProps = useModalForm({
    refineCoreProps: {
      resource: 'cases',
      action: 'edit',
      redirect: false,
      meta: { fields: ['id', 'status'] },
      id: case_?.id,
    },
  });

  const multiPartyLinkDrawer = useModal();
  const changeOwnerDrawer = useModal();

  return (
    <CanAccess>
      <Show
        isLoading={isLoading}
        title={<Typography variant="h5">{case_?.name}</Typography>}
        headerButtons={({ defaultButtons }) => (
          <>
            {isMultiPartyApplication(case_) && (
              <Button onClick={(): void => multiPartyLinkDrawer.show()}>Link Multi Party</Button>
            )}
            {!isMultiPartyApplication(case_) && (
              <Button onClick={(): void => changeOwnerDrawer.show()}>Change Owner</Button>
            )}
            <Button onClick={(): void => statusDrawerFormProps.modal.show()}>Change Status</Button>
            {defaultButtons}
          </>
        )}
      >
        <Stack gap={1}>
          <List
            sx={{
              width: '100%',
              bgcolor: 'background.paper',
              display: 'grid',
              gridTemplateColumns: '1fr 1fr 1fr',
            }}
          >
            <ListItem>
              <ListItemText
                primary={
                  <Link to={`/programs/show/${case_?.program?.id}`}>{case_?.program?.name}</Link>
                }
                secondary="Program"
              />
            </ListItem>
            <ListItem>
              <ListItemText primary={case_?.status} secondary="Status" />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={displayDate(case_?.decisionReachedAt)}
                secondary="Decision Reached At"
              />
            </ListItem>
          </List>

          {case_?.id && (
            <>
              <Divider />
              <ApplicationsList filter={{ caseId: case_.id as string }} />
              <Divider />
              <PaymentsList filter={{ caseId: case_.id as string }} />
              <Divider />
              <WorkflowEventsList filter={{ entityId: case_.id, entityType: 'case' }} />

              {/* There is a bug with permissions list that causes the browser to crash in some situations */}
              <Button
                variant="outlined"
                onClick={(): void => setShowPermissionsSection(!showPermissionsSection)}
              >
                {showPermissionsSection ? 'Hide permissions' : 'Show permissions'}
              </Button>
              {showPermissionsSection ? (
                <PermissionsList
                  resource={{ objectId: case_.id as string, objectType: Relation.CASE }}
                />
              ) : null}
            </>
          )}
        </Stack>
        <ChangeStatusDrawer {...statusDrawerFormProps} />
        {case_ && <LinkApplicationDrawer {...multiPartyLinkDrawer} case={case_} />}
        {case_ && (
          <ChangeOwnerDrawer {...changeOwnerDrawer} case={case_} onSuccess={() => refetch()} />
        )}
      </Show>
    </CanAccess>
  );
}
