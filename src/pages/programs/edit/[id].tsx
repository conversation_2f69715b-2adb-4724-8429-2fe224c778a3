import JsonForm from '@components/forms/JsonForm';
import SelectInput from '@components/forms/SelectInput';
import TextInput from '@components/forms/TextInput';
import { Divider, Stack, Typography } from '@mui/material';
import { CanAccess } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import { FormProvider } from 'react-hook-form';
import configSchema from 'schemas/programs/config.json';
import uiSchema from 'schemas/programs/ui.json';

export default function EditProgram() {
  const formProps = useForm({
    refineCoreProps: {
      meta: {
        fields: ['id', 'name', 'status', 'config'],
      },
      redirect: 'show',
    },
  });

  return (
    <CanAccess>
      <Edit saveButtonProps={formProps.saveButtonProps}>
        <Stack component="form" gap={1} autoComplete="off">
          <FormProvider {...formProps}>
            <TextInput name="name" label="Name" required />
            <SelectInput
              name="status"
              label="Status"
              options={[
                { id: 'OPEN', name: 'Open' },
                { id: 'REFERRAL_ONLY', name: 'Referral-Only' },
                { id: 'CLOSED', name: 'Closed' },
              ]}
            />
            <Divider />
            <Typography variant="h5">Config</Typography>
            <JsonForm name="config" schema={configSchema} uiSchema={uiSchema} />
          </FormProvider>
        </Stack>
      </Edit>
    </CanAccess>
  );
}
