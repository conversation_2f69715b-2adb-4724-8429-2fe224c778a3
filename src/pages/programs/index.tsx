import type { GridColDef } from '@mui/x-data-grid';
import { CanAccess } from '@refinedev/core';
import { List, type ListProps, ShowButton, useDataGrid } from '@refinedev/mui';
import React from 'react';
import { DataGrid } from '../../components/data-grid/DataGrid';

const ProgramsList = ({ filter }: { filter: { ids: string[] } }) => {
  const { dataGridProps } = useDataGrid({
    resource: 'programs',
    filters: {
      ...(!!filter?.ids && { permanent: [{ field: 'id', operator: 'in', value: filter.ids }] }),
    },
    meta: {
      fields: [{ nodes: ['id', 'name', 'status', { partner: ['id', 'name'] }] }],
    },
    ...(!!filter?.ids && { syncWithLocation: false }),
  });

  const columns = React.useMemo<GridColDef[]>(
    () => [
      {
        field: 'id',
        headerName: 'Id',
        flex: 1,
      },
      {
        field: 'name',
        headerName: 'Name',
        flex: 1,
      },
      {
        field: 'status',
        headerName: 'Status',
      },
      {
        field: 'partner.name',
        headerName: 'Partner',
        flex: 1,
        valueGetter: (params) => params.row.partner?.name,
      },
      {
        field: 'actions',
        headerName: 'Actions',
        renderCell: function render({ row }) {
          return (
            <>
              <ShowButton resource="programs" recordItemId={row.id} />
            </>
          );
        },
        align: 'center',
        headerAlign: 'center',
        minWidth: 80,
        filterable: false,
      },
    ],
    [],
  );

  return (
    <CanAccess>
      <List
        title="Programs"
        resource="programs"
        {...(filter && { breadcrumb: false, canCreate: false })}
      >
        <DataGrid {...dataGridProps} columns={columns} />
      </List>
    </CanAccess>
  );
};

export default ProgramsList;
