import AutocompleteInput from '@components/forms/AutocompleteInput';
import HiddenInput from '@components/forms/HiddenInput';
import CreateProgramFields, {
  type ProgramFieldsProps,
} from '@components/programs/CreateProgramFields';
import { Stack } from '@mui/material';
import { CanAccess, useCreate, useOne, useParsed } from '@refinedev/core';
import { Create } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import promisify from '@utils/promisify';
import useAuthorization from 'hooks/identity/useAuthorization';
import { Relation } from 'pages/api/types/identity';
import { type FieldValues, FormProvider } from 'react-hook-form';

export default function CreateProgram(): JSX.Element {
  const { params } = useParsed();

  const formProps = useForm<FieldValues>({
    defaultValues: { partnerId: params?.partnerId },
    refineCoreProps: { redirect: 'show' },
  });
  const {
    refineCore: { formLoading },
    saveButtonProps: { disabled, onClick },
    handleSubmit,
    setValue,
    watch,
  } = formProps;

  const partnerId = watch('partnerId');
  const { data } = useOne({
    resource: 'partners',
    id: partnerId,
    meta: { fields: ['id', 'externalId', 'parentId', { fundsList: ['id', 'name'] }] },
  });
  const partner = data?.data;

  // TODO: Implement create many
  const { mutate: createMutation } = useCreate();
  const authorization = useAuthorization();

  const onSubmit = async (data, e) => {
    const {
      id: programId,
      programApplicantTypes: { create: programApplicantTypes },
      verificationConfiguration,
    } = data;
    const appConfigs = programApplicantTypes
      .map(({ applicantTypeId, configurationId }) => ({
        applicantTypeId,
        configurationId,
        programId,
      }))
      .filter(({ configurationId }) => !!configurationId);

    for (const idx of programApplicantTypes.keys())
      setValue(`programApplicantTypes.create.${idx}.configurationId`, undefined);
    setValue('verificationConfiguration', undefined);

    await Promise.all([
      ...appConfigs.map((cf) =>
        promisify(createMutation, {
          resource: 'program_application_configurations',
          dataProviderName: 'config',
          values: cf,
        }),
      ),
      ...(verificationConfiguration
        ? [
            promisify(createMutation, {
              resource: 'configs',
              dataProviderName: 'verification',
              values: { ...verificationConfiguration, programId },
            }),
          ]
        : []),
      authorization.createRelationships({
        partnerId,
        relationships: [
          {
            relation: 'org',
            object: { objectId: programId, objectType: Relation.PROGRAM },
            subject: { objectId: partnerId, objectType: Relation.ORGANIZATION },
          },
        ],
      }),
    ]);

    return onClick(e);
  };

  return (
    <CanAccess>
      <Create
        isLoading={formLoading}
        saveButtonProps={{ disabled, onClick: handleSubmit(onSubmit) }}
      >
        <FormProvider {...formProps}>
          <Stack component="form" autoComplete="off" gap={2}>
            {params?.partnerId ? (
              <HiddenInput name="partnerId" value={params.partnerId} />
            ) : (
              <AutocompleteInput name="partnerId" label="Partner" resource="partners" required />
            )}
            {partner && (
              <CreateProgramFields
                funds={partner.fundsList}
                partner={partner as ProgramFieldsProps['partner']}
              />
            )}
          </Stack>
        </FormProvider>
      </Create>
    </CanAccess>
  );
}
