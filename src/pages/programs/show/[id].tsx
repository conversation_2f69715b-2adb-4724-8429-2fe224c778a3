import ProgramApplicantTypesList from '@components/applicantTypes/ProgramApplicantTypesList';
import ModelSelection from '@components/documents/ModelSelection';
import { AddFeatureDrawer } from '@components/features/AddFeatureDrawer';
import { EditFeatureDrawer } from '@components/features/EditFeatureDrawer';
import FeatureToggle from '@components/features/FeatureToggle';
import {
  type EditBrandImagesCustomModalProps,
  EditBrandImagesModal,
} from '@components/forms/images/EditBrandImagesModal';
import EditableBrandingImage from '@components/forms/images/EditableBrandingImage';
import { ProgramFundList } from '@components/funds/ProgramFunds';
import NotificationTemplateList from '@components/notificationTemplates/NotificationTemplateList';
import VerificationConfigurations from '@components/verification/VerificationConfigurations';
import { materialCells, materialRenderers } from '@jsonforms/material-renderers';
import { JsonForms } from '@jsonforms/react';
import {
  Divider,
  Link,
  ListItem,
  ListItemText,
  ListSubheader,
  List as MUIList,
  Stack,
  Typography,
} from '@mui/material';
import { CanAccess, useShow } from '@refinedev/core';
import { List, Show } from '@refinedev/mui';
import { useModalForm } from '@refinedev/react-hook-form';
import useFeatures from 'hooks/features/useFeatures';
import { Fragment, useEffect, useState } from 'react';
import configSchema from 'schemas/programs/config.json';
import uiSchema from 'schemas/programs/ui.json';

export default function ViewProgram() {
  const [programFeaturesBucket, setProgramFeaturesBucket] = useState({});
  const {
    query: { data, isLoading, refetch },
  } = useShow({
    meta: {
      fields: [
        'id',
        'name',
        'status',
        'config',
        'heroImage',
        {
          partner: ['id', 'name', 'externalId', 'parentId'],
        },
        {
          programApplicantTypes: [
            { nodes: ['id', 'nameOverride', { applicantType: ['id', 'name'] }] },
          ],
        },
        {
          programFeatures: [
            { nodes: ['id', 'featureId', 'enabled', { feature: ['name', 'description'] }] },
          ],
        },
        {
          programFunds: [{ nodes: [{ fund: ['id', 'name', 'startingBalance'] }] }],
        },
      ],
    },
  });

  const { lookupDeps } = useFeatures();

  const [imageUploadModalProps, setImageUploadModalProps] =
    useState<EditBrandImagesCustomModalProps>({
      partnerExternalId: data?.data?.partner?.externalId,
      fieldToUpdate: '',
      fieldLabel: '',
      imageFilename: '',
    });

  const editBrandImagesModalFormProps = useModalForm({
    refineCoreProps: {
      action: 'edit',
      resource: 'program',
      redirect: false,
      meta: {
        fields: ['id', 'heroImage', { partner: ['externalId'] }],
      },
      invalidates: ['all'],
    },
  });

  const {
    modal: { show: showEditBrandImagesModal },
  } = editBrandImagesModalFormProps;

  const program = data?.data;
  const config = program?.config ?? '{}';
  const createFeatureDrawerFormProps = useModalForm({
    syncWithLocation: false,
    refineCoreProps: {
      resource: 'programFeatures',
      action: 'create',
      redirect: false,
      onMutationSuccess: () => {
        refetch();
      },
    },
  });

  const editFeatureDrawerFormProps = useModalForm({
    syncWithLocation: false,
    refineCoreProps: {
      resource: 'programFeatures',
      meta: { fields: ['id', 'featureId', 'enabled', { feature: ['name', 'description'] }] },
      action: 'edit',
      redirect: false,
      onMutationSuccess: () => {
        refetch();
      },
    },
  });

  useEffect(() => {
    if (!program?.programFeatures) return;

    const updatedBucket = {};
    for (const item of program.programFeatures) {
      const feature = item?.feature;
      const featureName = feature?.name;
      const bucketName = featureName?.split(':')[0] ?? 'Uncategorized';

      const itemWithDeps = { ...item, feature: { ...item.feature, deps: lookupDeps(featureName) } };
      if (updatedBucket[bucketName]) {
        updatedBucket[bucketName].push(itemWithDeps);
      } else {
        updatedBucket[bucketName] = [itemWithDeps];
      }
    }

    setProgramFeaturesBucket(updatedBucket);
  }, [program, lookupDeps]);

  return (
    <CanAccess>
      <Show isLoading={isLoading} title={<Typography variant="h5">{program?.name}</Typography>}>
        <Stack gap={1}>
          <MUIList
            sx={{
              width: '100%',
              bgcolor: 'background.paper',
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
            }}
          >
            <ListItem>
              <ListItemText primary={program?.name} secondary="Name" />
            </ListItem>
            <ListItem>
              <ListItemText primary={program?.id} secondary="ID" />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={
                  <Link href={`/partners/show/${program?.partner.id}`}>
                    {program?.partner.name}
                  </Link>
                }
                secondary="Partner"
              />
            </ListItem>
            <ListItem>
              <ListItemText primary={program?.status} secondary="Status" />
            </ListItem>
            <ListItem>
              <EditableBrandingImage
                label="Program Logo"
                imageUrl={program?.heroImage}
                openEditModal={() => {
                  setImageUploadModalProps({
                    partnerExternalId: program?.partner.externalId,
                    fieldToUpdate: 'heroImage',
                    fieldLabel: 'Hero Image',
                    imageFilename: `program-${program?.id}`,
                  });
                  showEditBrandImagesModal(program?.id);
                }}
              />
            </ListItem>
          </MUIList>
          {config.reapplicationRules?.length > 0 && (
            <>
              <Typography variant="h6">Reapplication Rules</Typography>
              <MUIList sx={{ width: '100%', bgcolor: 'background.paper' }}>
                {config.reapplicationRules.map((rule) => (
                  <ListItem key={rule.type}>
                    <ListItemText primary={rule.type} secondary="Type" />
                    <ListItemText primary={rule.value} secondary="Value" />
                    {rule.unit && <ListItemText primary={rule.unit} secondary="Unit" />}
                  </ListItem>
                ))}
              </MUIList>
            </>
          )}

          {program && (
            <>
              <ProgramFundList programId={program.id} partnerId={program.partner.id} />
              <List
                resource="program_features"
                breadcrumb={false}
                createButtonProps={{
                  onClick: () => createFeatureDrawerFormProps.modal.show(),
                  children: 'Add Feature',
                }}
              >
                {Object.keys(programFeaturesBucket)
                  .sort()
                  .map((programKey) => (
                    <Fragment key={programKey}>
                      <ListSubheader>{programKey}</ListSubheader>
                      {programFeaturesBucket[programKey].map((programFeature) => (
                        <FeatureToggle
                          key={programFeature.id}
                          featureSetting={programFeature}
                          enabled={programFeature.enabled}
                          handleToggle={() =>
                            editFeatureDrawerFormProps.modal.show(programFeature.id)
                          }
                        />
                      ))}
                    </Fragment>
                  ))}
              </List>
              <AddFeatureDrawer
                {...createFeatureDrawerFormProps}
                entity={{ id: program.id as string, resource: 'program' }}
                currentFeatures={program.programFeatures}
              />
              <EditFeatureDrawer
                {...editFeatureDrawerFormProps}
                resource="programFeatures"
                currentFeatures={program.programFeatures}
              />

              <Divider />
              <ProgramApplicantTypesList program={program} />

              <Divider />
              <ModelSelection program={program} refetch={refetch} isLoading={isLoading} />

              <Divider />
              <Typography variant="h5">Config</Typography>
              <JsonForms
                schema={configSchema}
                uischema={uiSchema}
                data={config}
                renderers={materialRenderers}
                cells={materialCells}
                readonly
              />
              <Divider />
              <VerificationConfigurations program={program} />
              <Divider />
              <NotificationTemplateList
                filter={{ partnerId: program.partner.id, programId: program.id as string }}
              />
            </>
          )}
        </Stack>
      </Show>
      <EditBrandImagesModal {...editBrandImagesModalFormProps} {...imageUploadModalProps} />
    </CanAccess>
  );
}
