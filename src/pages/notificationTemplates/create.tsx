import HiddenInput from '@components/forms/HiddenInput';
import SelectInput from '@components/forms/SelectInput';
import TextInput from '@components/forms/TextInput';
import TemplatePreview from '@components/notificationTemplates/NotificationTemplatePreview';
import TemplateContentFields from '@components/notificationTemplates/TemplateContentFields';
import { Stack, Typography } from '@mui/material';
import { CanAccess, useParsed } from '@refinedev/core';
import { Create } from '@refinedev/mui';
import { useForm as useRefineForm } from '@refinedev/react-hook-form';
import { NotificationChannel, type NotificationTemplate } from '@utils/notificationTemplate';
import { pick } from '@utils/set';
import useNotificationTypes from 'hooks/notificationTemplates/useNotificationTypes';
import { FormProvider, useForm } from 'react-hook-form';
import type { NotificationType } from 'types/notification';

export default function CreateTemplate(): JSX.Element {
  const { params } = useParsed();

  const formProps = useRefineForm<{ partnerId?: string; programId?: string; type: string }>({
    defaultValues: { partnerId: params?.partnerId, programId: params?.programId },
    refineCoreProps: { redirect: 'show' },
  });

  const contentFormProps = useForm();
  const notificationTypes = useNotificationTypes(formProps.watch('channel'));

  const onSubmit = (e) => {
    const formData = contentFormProps.getValues();
    const channel = formProps.getValues('channel');
    if (channel === NotificationChannel.EMAIL) {
      formProps.setValue('content', pick(formData, ['subject', 'preText', 'button', 'postText']));
    } else if (channel === NotificationChannel.SMS) {
      formProps.setValue('content', pick(formData, ['preText', 'link', 'postText']));
    }
    formProps.saveButtonProps.onClick(e);
  };

  return (
    <CanAccess>
      <Create
        isLoading={formProps.refineCore.formLoading}
        saveButtonProps={{
          disabled: formProps.saveButtonProps.disabled,
          onClick: onSubmit,
        }}
      >
        <Stack direction="row" gap={2}>
          <Stack gap={2} sx={{ flex: '1' }}>
            <FormProvider {...formProps}>
              <HiddenInput name="content" />
              <SelectInput
                name="channel"
                label="Notification Channel"
                options={[
                  { id: NotificationChannel.EMAIL, name: 'Email' },
                  { id: NotificationChannel.SMS, name: 'SMS' },
                ]}
              />
              {params?.partnerId && <HiddenInput name="partnerId" value={params?.partnerId} />}
              {params?.programId && <HiddenInput name="programId" value={params?.programId} />}
              {params?.partnerId || params?.programId ? (
                <SelectInput
                  name="type"
                  label="Notification Type"
                  options={notificationTypes.map((type) => ({ id: type, name: type }))}
                />
              ) : (
                <TextInput name="type" label="Notification Type" required />
              )}
            </FormProvider>
            <FormProvider {...contentFormProps}>
              <TemplateContentFields
                notificationType={formProps.watch('type') as NotificationType}
                notificationChannel={formProps.watch('channel')}
                partnerId={params?.partnerId}
              />
            </FormProvider>
          </Stack>
          <Stack sx={{ flex: '1', paddingLeft: '8px', borderLeft: '1px solid' }}>
            <Typography variant="h6">Preview</Typography>
            <TemplatePreview
              template={
                {
                  ...formProps.watch(),
                  content: contentFormProps.watch(),
                } as NotificationTemplate
              }
            />
          </Stack>
        </Stack>
      </Create>
    </CanAccess>
  );
}
