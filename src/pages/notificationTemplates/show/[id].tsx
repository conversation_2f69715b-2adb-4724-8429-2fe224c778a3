import Link from '@components/navigation/Link';
import TemplatePreview from '@components/notificationTemplates/NotificationTemplatePreview';
import { Divider, List, ListItem, ListItemText, Stack, Typography } from '@mui/material';
import { CanAccess, useShow } from '@refinedev/core';
import { Show } from '@refinedev/mui';
import type { NotificationTemplate } from '@utils/notificationTemplate';

export default function ViewTemplate(): JSX.Element {
  const {
    queryResult: { data, isLoading },
  } = useShow({
    meta: {
      fields: [
        'id',
        'type',
        'channel',
        'content',
        { partner: ['id', 'name'] },
        { program: ['id', 'name'] },
      ],
    },
  });

  const template = data?.data;

  return (
    <CanAccess>
      <Show
        isLoading={isLoading}
        title={<Typography variant="h5">Notification Template</Typography>}
      >
        <Stack gap={1}>
          <List
            sx={{
              width: '100%',
              bgcolor: 'background.paper',
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
            }}
          >
            <ListItem>
              <ListItemText primary={template?.type} secondary="Notification Type" />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={template?.channel?.toUpperCase()}
                secondary="Notification Channel"
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={
                  template?.partner ? (
                    <Link to={`/partners/show/${template?.partner.id}`}>
                      {template?.partner.name}
                    </Link>
                  ) : (
                    '-'
                  )
                }
                secondary="Partner"
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={
                  template?.program ? (
                    <Link to={`/programs/show/${template?.program.id}`}>
                      {template?.program.name}
                    </Link>
                  ) : (
                    '-'
                  )
                }
                secondary="Program"
              />
            </ListItem>
          </List>

          <Divider />

          <Typography variant="h6">Preview</Typography>

          {template && <TemplatePreview template={template as NotificationTemplate} />}
        </Stack>
      </Show>
    </CanAccess>
  );
}
