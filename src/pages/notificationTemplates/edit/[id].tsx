import HiddenInput from '@components/forms/HiddenInput';
import TemplatePreview from '@components/notificationTemplates/NotificationTemplatePreview';
import TemplateContentFields from '@components/notificationTemplates/TemplateContentFields';
import { Stack, Typography } from '@mui/material';
import { CanAccess } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import { useForm as useRefineForm } from '@refinedev/react-hook-form';
import { NotificationChannel, type NotificationTemplate } from '@utils/notificationTemplate';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { FormProvider } from 'react-hook-form';
import type { NotificationType } from 'types/notification';

export default function EditNotificationTemplate() {
  const formProps = useRefineForm({
    refineCoreProps: {
      meta: {
        fields: ['id', 'partnerId', 'type', 'channel', 'content'],
      },
      redirect: 'show',
    },
  });

  const contentFormProps = useForm();
  const template = formProps.refineCore.queryResult?.data?.data;

  // biome-ignore lint/correctness/useExhaustiveDependencies: intentional exclusion
  useEffect(() => {
    if (formProps.refineCore.formLoading) return;
    if (template) {
      const { content } = template;
      contentFormProps.setValue('preText', content.preText);
      contentFormProps.setValue('postText', content.postText);
      if (template.channel === NotificationChannel.EMAIL) {
        contentFormProps.setValue('subject', content.subject);
        contentFormProps.setValue('button', content.button);
      }
      if (template.channel === NotificationChannel.SMS) {
        contentFormProps.setValue('link', content.link);
      }
    }
  }, [template, formProps.refineCore.formLoading]);

  const onSubmit = (e) => {
    const content = contentFormProps.getValues();
    formProps.setValue('content', content);
    formProps.saveButtonProps.onClick(e);
  };

  return (
    <CanAccess>
      <Edit
        saveButtonProps={{
          disabled: formProps.saveButtonProps.disabled,
          onClick: onSubmit,
        }}
      >
        <Stack direction="row" gap={2}>
          <Stack gap={2} sx={{ flex: '1' }}>
            <FormProvider {...formProps}>
              <HiddenInput name="content" />
            </FormProvider>
            <FormProvider {...contentFormProps}>
              <TemplateContentFields
                notificationType={template?.type as NotificationType}
                notificationChannel={template?.channel}
                partnerId={template?.partnerId}
              />
            </FormProvider>
          </Stack>
          <Stack sx={{ flex: '1', paddingLeft: '8px', borderLeft: '1px solid' }}>
            <Typography variant="h6">Preview</Typography>
            <TemplatePreview
              template={
                {
                  ...template,
                  content: contentFormProps.watch(),
                } as NotificationTemplate
              }
            />
          </Stack>
        </Stack>
      </Edit>
    </CanAccess>
  );
}
