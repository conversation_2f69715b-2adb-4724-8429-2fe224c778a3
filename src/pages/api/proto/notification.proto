syntax = "proto3";

package notification;

service NotificationService {
  rpc sendEmail (SendEmailRequest) returns (SendEmailResponse);
  rpc sendSms (SendSmsRequest) returns (SendSmsResponse);
  rpc notify (NotifyRequest) returns (NotifyResponse);
}

enum NotificationType {
  UnknownType = 0;
  // Authentication: reserved 1-10
  AuthenticationChangePrimaryEmail = 1;
  AuthenticationResetPassword = 2;
  AuthenticationVerifyEmail = 3;
  AuthenticationMagicLink = 4;
  AuthenticationIdentityVerifyEmail = 5;
  // Status Changes: reserved 11-20
  ApplicationDenied = 11;
  ApplicationIncomplete = 12;
  ApplicationSubmitted = 13;
  ApplicationWithdrawn = 14;
  ApplicationApproved = 15;
  // Payments: reserved 21-40
  PaymentsClaimFunds = 21;
  PaymentsFundsClaimed = 22;
  PaymentsFundsIssued = 23;
  PaymentSentToOtherParty = 24;
  PaymentsApplicantPaymentReset = 25;
  PaymentsAdvocatePaymentReset = 26;
  PaymentsRecurringPaymentInitiated = 27;
  PaymentsRecurringPaymentsScheduled = 28;
  PaymentsRecurringPaymentsScheduledOtherParty = 29;
  // Multiparty: reserved 41-45
  MultipartyInvitation = 41;
  // Misc: 81+
  ProgramReferral = 81;
  // Case Comment
  CaseComment = 100;
}

message Context {
  string partner_id = 1;
  string program_id = 2;
}

message NotificationConfig {
  NotificationType type = 1;
  Context context = 2;
  map<string, string> variables = 3;
  map<string, string> tags = 4;
}

message EmailRecipient {
  string name = 1;
  string email = 2;
  map<string, string> variables = 3;
}

message EmailProviderParameters {
  string subject = 1;
  string body = 2;
  string from = 3;
  string replyTo = 4;
  map<string, string> tags = 5;
}

message SendEmailRequest {
  repeated EmailRecipient recipients = 1;
  oneof config {
    NotificationConfig notification_config = 2;
    EmailProviderParameters provider_parameters = 3;
  }
}

message SendEmailResponse {
}

message SmsRecipient {
  string name = 1;
  string phone = 2;
  map<string, string> variables = 3;
}

message SmsProviderParameters {
  string body = 1;
}

message SendSmsRequest {
  repeated SmsRecipient recipients = 1;
  oneof config {
    NotificationConfig notification_config = 2;
    SmsProviderParameters provider_parameters = 3;
  }
}

message SendSmsResponse {
}

message Recipient {
  string name = 1;
  string phone = 2;
  string email = 3;
  map<string, string> variables = 4;
}

enum NotificationChannel {
  sms = 1;
  email = 2;
}

message ProviderParameters {
  SmsProviderParameters sms = 1;
  EmailProviderParameters email = 2;
}

message NotifyRequest {
  repeated NotificationChannel channels = 1;
  repeated Recipient recipients = 2;
  oneof config {
    NotificationConfig notification_config = 3;
    ProviderParameters provider_parameters = 4;
  }
}

message NotifyResponse {
}