syntax = "proto3";

package identity;

message CreateUserRequest {
  string partner_id = 1;
  // enum this
  repeated string roles = 2;
  optional string email = 3;
  optional string name = 4;
  optional string password = 5;
  optional string phone = 6;
  optional string applicant_type_id = 7;
}

message CreateUserResponse {
  string message = 1;
  string id = 2;
  message Applicant {
    string id = 1;
    string partner_id = 2;
  }
  message Advocate {
    string id = 1;
    string core_user_id = 2;
    string partner_id = 3;
  }
  repeated Applicant applicants = 3;
  repeated Advocate advocates = 4;
}

message GetUserRequest {
  oneof user_identifier {
    string core_user_id = 1;
    string gcip_uid = 2;
    string identity_user_id = 3;
  }
}

message User { 
  string id = 1;
  string name = 2;
  string email = 3;
  string zed_token = 4;
}

message UserResponse {
  string message = 1;
  optional User user = 2;
}

message UpdateUserRequest {
  optional string user_id = 1 [deprecated = true];
  optional string tenant_id = 2 [deprecated = true];
  optional string email = 3;
  optional string name = 4;
  optional string phone = 5;
  oneof user_identifier {
    string core_user_id = 6;
    string identity_user_id = 7;
  }
  optional string gcip_uid = 8 [deprecated = true];
  string partner_id = 9;
  repeated string roles = 10;
}

message DoesUserExistRequest {
  string user_id = 1;
}

message ResetPasswordRequest {
  string email = 1;
  string partner_id = 2;
  string tenant_id = 3;
}

message SendMagicLinkRequest {
  string email = 1;
  string partner_id = 2;
  string tenant_id = 3;
  string timezone = 4;
  optional string continue_url = 5;
}

message SendVerificationEmailRequest {
  string email = 1;
  string partner_id = 2;
  string tenant_id = 3;
}

message VerifyEmailRequest {
  string token = 1;
  string tenant_id = 2;
}
