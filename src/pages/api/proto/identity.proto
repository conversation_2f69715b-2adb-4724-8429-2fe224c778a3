syntax = "proto3";

import "authentication.proto";
import "authorization.proto";
import "tenant.proto";
import "user.proto";

package identity;

service IdentityServer {
  // Import Partner into GCIP
  rpc migrate (MigratePartnerRequest) returns (Response);
  // Import Partner into SpiceDB
  rpc fixPayeeRelations (FixPayeeRelationsRequest) returns (Response);
  rpc fixBulkOverridePaymentsRelations (FixBulkOverridePaymentsRequest) returns (Response);
  rpc migratePermissions (MigratePermissionsRequest) returns (Response);
  rpc migrateFiscalPermissions (MigrateFiscalPermissionsRequest) returns (Response);

  // Authentication Methods
  rpc sendMagicLink (SendMagicLinkRequest) returns (Response);
  rpc sendMagicLinkV2 (SendMagicLinkRequest) returns (Response);

  // Authorization Methods
  rpc checkPermission (CheckPermissionRequest) returns (CanAccessResponse);
  rpc createAccessRequest (CreateAccessRequest) returns (Response);
  rpc createRelationships (CreateRelationshipsRequest) returns (RelationshipResponse);
  rpc getAccessRequests (GetAccessRequests) returns (AccessRequestList);
  rpc reviewAccessRequest (ReviewAccessRequest) returns (Response);
  rpc lookupPermissions (LookupPermissionsRequest) returns (LookupPermissionsResponse);
  
  // Session Management Methods
  rpc beginSession (SessionRequest) returns (SessionResponse);
  rpc endSession (ValidateRequest) returns (Response);
  rpc validateSession (ValidateRequest) returns (SessionResponse);

  // User Management Methods
  rpc createUser (CreateUserRequest) returns (CreateUserResponse);
  rpc getUser (GetUserRequest) returns (UserResponse);
  rpc readPortalRoles (ReadPortalRolesRequest) returns (ReadPortalRolesResponse);
  rpc resetPassword (ResetPasswordRequest) returns (Response);
  rpc sendVerificationEmail (SendVerificationEmailRequest) returns (Response);
  rpc updateUser (UpdateUserRequest) returns (Response);
  rpc verifyEmail (VerifyEmailRequest) returns (Response);

  // Security Methods
  rpc recaptcha (RecaptchaRequest) returns (RecaptchaResponse);
  
  // Tenant Management Methods
  rpc upsertTenant (UpsertTenantRequest) returns (Tenant);
  rpc retrieveTenant (RetrieveTenantRequest) returns (Tenant);
}

message MigratePartnerRequest {
  string partner_id = 1;
}

message FixPayeeRelationsRequest {
  string partner_id = 1;
}

message FixBulkOverridePaymentsRequest {
  string partner_id = 1;
}

message MigrateFiscalPermissionsRequest {
  string partner_id = 1;
}

message MigratePermissionsRequest {
  string partner_id = 1;
  repeated ObjectType objects = 2;
  optional bool use_stream = 3;
}

message Response {
  string message = 1;
}