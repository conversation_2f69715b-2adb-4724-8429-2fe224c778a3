syntax = "proto3";

package identity;

// Tenant Options
message TenantOptions {
  bool anonymousSignInEnabled = 1;

  message EmailSignInProviderConfig {
    required bool enabled = 1;
    bool passwordRequired = 2;
  }  
  message EmailPrivacyConfig {
    bool enableImprovedEmailPrivacy = 1;
  }
  message MultiFactorConfig {
    string state = 1; //  'ENABLED' | 'DISABLED'
    repeated string factorIds = 2; // only 'phone' supported
    message ProviderConfig {
      string state = 1; 
      message TotpMultiFactorProviderConfig {
         int32 adjacentIntervals = 1;
      }
      TotpMultiFactorProviderConfig totpProviderConfig = 2;
    }
    repeated ProviderConfig providerConfigs = 3;
  }
  message RecaptchaConfig {
    bool useAccountDefender = 1;
    string emailPasswordEnforcementState = 2; // 'OFF' | 'AUDIT' | 'ENFORCE'
    // missing 	recaptchaKeys, managedRules
  }
  message PasswordPolicyConfig {
    bool forceUpgradeOnSignin = 1;
    string enforcementState = 2; // 'ENFORCE' | 'OFF';
    message CustomStrengthOptionsConfig {
      int32 maxLength = 1;
      int32 minLength = 2;
      bool requireLowercase = 3;
      bool requireNonAlphanumeric = 4;
      bool requireNumeric = 5;
      bool requireUppercase = 6;
    }
    CustomStrengthOptionsConfig constraints = 3;
  }

  EmailPrivacyConfig emailPrivacyConfig = 2;
  EmailSignInProviderConfig emailSignInConfig = 3;
  MultiFactorConfig	multiFactorConfig = 4;
  PasswordPolicyConfig passwordPolicyConfig = 5;
  RecaptchaConfig recaptchaConfig = 6;
  // missing SmsRegionConfig, testPhoneNumbers
}

message UpsertTenantRequest {
  string partnerId = 1;
  string role = 2;
  optional TenantOptions options = 3;
}

message RetrieveTenantRequest {
  string tenantId = 1;
}

message Tenant {
  string tenantId = 1;
  string displayName = 2;
  TenantOptions options = 3;
}