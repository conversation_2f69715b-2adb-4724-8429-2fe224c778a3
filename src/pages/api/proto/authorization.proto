syntax = "proto3";

import "user.proto";

package identity;

enum ObjectType {
  UNKNOWN = 0;
  PLATFORM = 1;
  ORGANIZATION = 2;
  USER = 5;
  VENDOR = 10;
  PROGRAM = 16;
  FUND = 17;
  CASE = 25;
  APPLICATION = 26;
  FULFILLMENT = 40;
  PAYMENT = 41;
  PAYEE = 42;
}

message AccessRequest {
  string id = 1;
  string partner_id = 2;
  string resource_id = 3;
  ObjectType resource_type = 4;
  string subject_id = 5;
  ObjectType subject_type = 6;
  string relation = 7;
  bool approved = 8;
  string request_detail = 9;
  string review_detail = 10;
  string created_at = 11;
  string updated_at = 12;
  User requester = 13;
  optional User reviewer = 14;
}

message AccessRequestFilter {
  string partner_id = 1;
  optional ObjectReference resource = 2;
  optional ObjectReference subject = 3;
  optional string relation = 4;
  optional bool approved = 5;
}

message AccessRequestList {
  string message = 1;
  repeated AccessRequest access_requests = 2;
  int32 count = 3;
}

message CanAccessResponse {
  string message = 1;
  bool can_access = 2;
  string token = 3;
  string id = 4;
}

message CreateAccessRequest {
  ObjectReference resource = 1;
  ObjectReference subject = 2;
  string relation = 3;
  string partner_id = 4;
  optional string detail = 5;
}

message CheckPermissionRequest {
  ObjectReference resource = 1;
  ObjectReference subject = 2;
  string action = 3;
  optional string zed_token = 4;
  optional bool fully_consistent = 5;
}

message OffsetPagination {
  int32 page = 1;
  int32 take = 2;
}

message CursorPagination {
  string cursor = 1;
  int32 take = 2;
}

message GetAccessRequests {
  AccessRequestFilter filter = 1;
  optional OffsetPagination pagination = 2;
}

message CreateRelationshipRequest {
  ObjectReference object = 1;
  ObjectReference subject = 2;
  string relation = 3;
}

message CreateRelationshipsRequest {
  repeated CreateRelationshipRequest relationships = 1;
}

message LookupPermissionsRequest {
  oneof source {
    ObjectReference resource = 1;
    ObjectReference subject = 2;
  }
  ObjectType object_type_filter = 3;
  string permission = 4;
  // NOTE: pagination is currently unsupported when using 
  // source = 'resource'
  optional CursorPagination pagination = 9;
}

message LookupPermissionsResponse {
  string message = 1;
  repeated ObjectReference objects = 2;
}

message ObjectReference {
  string objectId = 1;
  ObjectType objectType = 2;
}

message ReadPortalRolesRequest {
  oneof user_identifier {
    string core_user_id = 1;
    string identity_user_id = 3;
  }
  string partner_id = 2;
}

message ReadPortalRolesResponse {
  string message = 1;
  repeated string roles = 2;
}

message RelationshipResponse {
  string message = 1;
  repeated string zed_tokens = 2;
}

message ReviewAccessRequest {
  string request_id = 1;
  string reviewer_id = 2;
  bool approved = 3;
  string detail = 4;
}