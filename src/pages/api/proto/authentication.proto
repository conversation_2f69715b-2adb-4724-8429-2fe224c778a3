syntax = "proto3";

package identity;

enum AuthenticationMechanism {
  UNKNOWN = 0;
  BASIC = 1;
  EMAIL_TOKEN = 2;
  SAML = 3;
}

message SessionRequest {
  optional string user_id = 1 [deprecated = true];
  string partner_id = 2;
  string token = 3;
  string tenant_id = 4;
  AuthenticationMechanism authentication_mechanism = 5;
  string headers = 6;
}

message ValidateRequest {
  string token = 1;
  string tenant_id = 2;
}

enum RecaptchaAction {
  UNKNOWN = 0;
  SIGNUP = 1;
  LOGIN = 2;
  RECOVER = 3;
}

message RecaptchaRequest {
  string token = 1;
  RecaptchaAction action = 2;
}

message RecaptchaResponse {
  float score = 1;
  string details = 2;
}

message Claims {
  string tenant_id = 1;
  string gcip_uid = 2;
  string user_id = 4;
  string partner_id = 3;
  optional string admin_id = 5;
  repeated string roles = 6;
  string session_id = 7;
  string identity_user_id = 8;
  string parent_partner_id = 9;
}

message SessionResponse {
  string message = 1;
  Claims claims = 2;
}