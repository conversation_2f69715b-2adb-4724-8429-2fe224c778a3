import type { NextApiRequest, NextApiResponse } from 'next';
import services from '../../../../../graphile-server/postgraphile';
import runMiddleware from '../../../../../graphile-server/runMiddleware';

// GraphQL route that handles queries
export default async (req: NextApiRequest, res: NextApiResponse) => {
  const service = req.query.service as string;

  if (req.method === 'GET') {
    res.redirect(302, `/api/graphiql/${service}`);
  }

  if (Object.hasOwn(services, service)) {
    await runMiddleware(req, res, services[service]);
    res.status(200).end();
  } else {
    res.status(404).end();
  }
};

export const config = {
  api: {
    bodyParser: false,
  },
};
