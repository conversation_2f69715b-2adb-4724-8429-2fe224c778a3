import type { NextApiRequest, NextApiResponse } from 'next';

import services from '../../../../graphile-server/postgraphile';
import runMiddleware from '../../../../graphile-server/runMiddleware';

// Graphiql route that handles rendering graphiql
// https://github.com/graphql/graphiql
// An interactive in-browser GraphQL IDE
export default async (req: NextApiRequest, res: NextApiResponse) => {
  const service = req.query.service as string;

  if (Object.hasOwn(services, service)) {
    await runMiddleware(req, res, services[service]);
    res.status(200).end();
  } else {
    res.status(200).end();
  }
};
