import ImageKit from 'imagekit';
import type { NextApiRequest, NextApiResponse } from 'next';

const imagekit = new ImageKit({
  publicKey: process.env.PUBLIC_IMAGEKIT_PUBLIC_KEY ?? '',
  privateKey: process.env.IMAGEKIT_PRIVATE_KEY ?? '',
  urlEndpoint: process.env.PUBLIC_IMAGEKIT_URL_ENDPOINT ?? '',
});

// GraphQL route that handles queries
export default async (req: NextApiRequest, res: NextApiResponse) => {
  const authenticationParameters = imagekit.getAuthenticationParameters();
  res.json(authenticationParameters);
};
