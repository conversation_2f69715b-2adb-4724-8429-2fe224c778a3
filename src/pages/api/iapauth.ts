import { type LoginTicket, OAuth2Client, type TokenPayload } from 'google-auth-library';
import type { NextApiRequest, NextApiResponse } from 'next';

import { type Principal, PrismaClient } from '@prisma/clients/sorcery';
const prisma = new PrismaClient();

export interface IAPAuthResponse {
  authenticated: boolean;
  tokenPayload?: TokenPayload;
  user?: Principal;
  error?: string;
}

/**
 * Authenticates a user using Google Identity-Aware Proxy (IAP)
 * and returns a validated JWT and user information if successful.
 * For testing failure modes see:
 * https://cloud.google.com/iap/docs/query-parameters-and-headers-howto#testing_jwt_verification
 *
 * @param req The Next.js API request object.
 * @param res The Next.js API response object.
 * @returns A void Promise that resolves when the authentication process is complete.
 */
export default async (
  req: NextApiRequest,
  res: NextApiResponse<IAPAuthResponse>,
): Promise<void> => {
  // biome-ignore lint/suspicious/noExplicitAny: error can be anything
  const resNotAuthenticated = (error: any = null, code: 401 | 403 = 401) =>
    res.status(code).json({ authenticated: false, error: error ?? undefined });

  // First, get the JWT from the Google IAP request header
  const iapJwt = req.headers['x-goog-iap-jwt-assertion'] as string;

  // no jwt header means we're either not behind IAP or not authenticated
  if (!iapJwt) {
    return resNotAuthenticated('JWT header not found');
  }

  // Verify the JWT
  try {
    // Get the AUD from Identity-Aware Proxy Settings
    // See: https://cloud.google.com/iap/docs/signed-headers-howto#verifying_the_jwt_payload
    const expectedAudience = process.env.JWT_AUDIENCE_CODE;
    const issuers = ['https://cloud.google.com/iap'];

    const oAuth2Client = new OAuth2Client();
    const iapPublicKeysResponse = await oAuth2Client.getIapPublicKeys();
    const ticket: LoginTicket = await oAuth2Client.verifySignedJwtWithCertsAsync(
      iapJwt,
      iapPublicKeysResponse.pubkeys,
      expectedAudience,
      issuers,
    ); // this will throw an error if the JWT is not valid

    // Get the user and their roles from the DB
    const user = await fetchUserDetailsFromDB(ticket?.getPayload()?.email);

    if (user?.roles?.length > 0) {
      return res.status(200).json({
        authenticated: true,
        tokenPayload: ticket.getPayload(),
        user,
      });
    }
    return resNotAuthenticated('User found but has no roles', 403);
    // biome-ignore lint/suspicious/noExplicitAny: error can be any type
  } catch (error: any) {
    return resNotAuthenticated(error.message);
  }
};

const fetchUserDetailsFromDB = async (email) => {
  const user = await prisma.principal
    .findFirstOrThrow({
      where: {
        email: email,
      },
    })
    .catch((error) => {
      if (error.code === 'P2025') {
        throw new Error('JWT validated but user not found in Sorcery');
      }
      throw error;
    });
  return user;
};
