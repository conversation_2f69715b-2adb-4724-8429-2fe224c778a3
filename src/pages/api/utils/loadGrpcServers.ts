import { join } from 'node:path';
import type * as grpc from '@grpc/grpc-js';
import { loadPackageDefinition } from '@grpc/grpc-js';
import { loadSync } from '@grpc/proto-loader';
import type { HealthClient } from '../types/health';
import type { IdentityServerClient } from '../types/identity';
import type { NotificationServiceClient } from '../types/notification';

// biome-ignore lint/suspicious/noExplicitAny: could be any
export type SubtypeConstructor<Constructor extends new (...args: any) => any, Subtype> = {
  new (...args: ConstructorParameters<Constructor>): Subtype;
};

const packageDefinition = loadSync(
  [
    join(process.cwd(), 'src/pages/api/proto/authentication.proto'),
    join(process.cwd(), 'src/pages/api/proto/authorization.proto'),
    join(process.cwd(), 'src/pages/api/proto/health.proto'),
    join(process.cwd(), 'src/pages/api/proto/identity.proto'),
    join(process.cwd(), 'src/pages/api/proto/notification.proto'),
    join(process.cwd(), 'src/pages/api/proto/tenant.proto'),
    join(process.cwd(), 'src/pages/api/proto/user.proto'),
  ],
  {
    longs: String,
    enums: String,
    defaults: true,
    oneofs: true,
  },
);

const {
  identity: { IdentityServer },
  notification: { NotificationService },
  grpc: {
    health: {
      v1: { Health: HealthServer },
    },
  },
} = loadPackageDefinition(packageDefinition) as unknown as {
  identity: {
    IdentityServer: SubtypeConstructor<typeof grpc.Client, IdentityServerClient>;
  };
  notification: {
    NotificationService: SubtypeConstructor<typeof grpc.Client, NotificationServiceClient>;
  };
  grpc: {
    health: {
      v1: {
        Health: SubtypeConstructor<typeof grpc.Client, HealthClient>;
      };
    };
  };
};

export { IdentityServer, NotificationService, HealthServer };
