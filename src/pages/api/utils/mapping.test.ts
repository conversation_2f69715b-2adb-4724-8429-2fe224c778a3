import crypto, { UUID } from 'node:crypto';
import { describe, expect, it, jest } from '@jest/globals';
import type {
  ApplicantTypes as ApplicantType,
  Partners as Partner,
  Programs as Program,
  Users as User,
} from '@prisma/clients/platform';
import { makeFakeRecipient } from '@test/mocks';
import type { Recipient } from 'types/schedulePayments';
import { mapRecipientToPlatformEntities, mapUserEntityToPayee } from './mapping';

describe('mapRecipientToPlatformEntities', () => {
  it('should return platform entities based on recipient info', () => {
    jest
      .spyOn(crypto, 'randomUUID')
      .mockReturnValueOnce('randId' as UUID)
      .mockReturnValueOnce('mockUserId' as UUID)
      .mockReturnValueOnce('mockCaseId' as UUID)
      .mockReturnValueOnce('mockApplicationId' as UUID)
      .mockReturnValueOnce('mockVersionId' as UUID);
    const fakeRecipient = makeFakeRecipient({
      salesForceId: 'mockSalesForceId',
      email: '<EMAIL>',
      paymentMethod: 'ach',
      accountNumber: '8524694',
      routingNumber: '*********',
      accountType: 'savings',
      paymentAmount: '10000',
    });
    expect(
      mapRecipientToPlatformEntities({
        partner: { id: 'mockPartnerId' } as Partner,
        program: { id: 'mockProgramId' } as Program,
        applicantType: { id: 'mockApplicantTypeId' } as ApplicantType,
        recipient: fakeRecipient,
      }),
    ).toEqual({
      user: {
        email: '<EMAIL>',
        id: 'mockUserId',
        legacyId: 'mockSalesForceId',
        name: 'Test User',
        partnerId: 'mockPartnerId',
      },
      applicantProfile: {
        applicantTypeId: 'mockApplicantTypeId',
        userId: 'mockUserId',
      },
      case: {
        id: 'mockCaseId',
        name: 'Test User',
        programId: 'mockProgramId',
        status: 'InReview',
      },
      application: {
        caseId: 'mockCaseId',
        id: 'mockApplicationId',
        submittedAt: expect.anything(),
        submitterId: 'mockUserId',
      },
      applicationVersion: {
        applicationId: 'mockApplicationId',
        creatorId: 'mockUserId',
        id: 'mockVersionId',
      },
      applicationAnswers: [
        {
          key: 'salesForceId',
          value: fakeRecipient.salesForceId,
          versionId: 'mockVersionId',
        },
        {
          key: 'organization',
          value: fakeRecipient.organization,
          versionId: 'mockVersionId',
        },
        {
          key: 'program',
          value: fakeRecipient.program,
          versionId: 'mockVersionId',
        },
        {
          key: 'stipendType',
          value: fakeRecipient.stipendType,
          versionId: 'mockVersionId',
        },
        {
          key: 'cohort',
          value: fakeRecipient.cohort,
          versionId: 'mockVersionId',
        },
        {
          key: 'name',
          value: `${fakeRecipient.firstName} ${fakeRecipient.lastName}`,
          versionId: 'mockVersionId',
        },
      ],
    });
  });
});

describe('mapUserToPayee', () => {
  it('should include basic info only if payment method is not ach', () => {
    expect(
      mapUserEntityToPayee(
        {
          email: '<EMAIL>',
          id: 'mockUserId',
          legacyId: 'mockSalesForceId',
          name: 'Test User',
          partnerId: 'mockPartnerId',
        } as User,
        makeFakeRecipient({
          salesForceId: 'mockSalesForceId',
          email: '<EMAIL>',
          paymentMethod: 'check',
          paymentAmount: '10000',
        }) as Recipient,
      ),
    ).toEqual({
      id: 'mockUserId',
      legacyId: 'mockSalesForceId',
      partnerId: 'mockPartnerId',
      name: 'Test User',
      email: '<EMAIL>',
      mailingAddress: {
        addressLine1: '1 Main St',
        city: 'Hanover',
        state: 'NH',
        zip: '03755',
      },
      payeeType: 'User',
    });
  });
  it('should include basic info and bank account info if payment method is ach', () => {
    expect(
      mapUserEntityToPayee(
        {
          email: '<EMAIL>',
          id: 'mockUserId',
          legacyId: 'mockSalesForceId',
          name: 'Test User',
          partnerId: 'mockPartnerId',
        } as User,
        makeFakeRecipient({
          salesForceId: 'mockSalesForceId',
          email: '<EMAIL>',
          paymentMethod: 'ach',
          accountNumber: '8524694',
          accountType: 'savings',
          routingNumber: '*********',
          paymentAmount: '10000',
        }),
      ),
    ).toEqual({
      id: 'mockUserId',
      legacyId: 'mockSalesForceId',
      partnerId: 'mockPartnerId',
      name: 'Test User',
      email: '<EMAIL>',
      payeeType: 'User',
      mailingAddress: {
        addressLine1: '1 Main St',
        city: 'Hanover',
        state: 'NH',
        zip: '03755',
      },
      bankAccount: {
        accountNumber: '8524694',
        accountType: 'savings',
        routingNumber: '*********',
      },
    });
  });
});
