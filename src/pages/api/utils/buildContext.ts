import clients from '@prisma/clients';
import type { Operations } from 'types/operations';
import buildOperations from '../operations';
import buildServices, { type Services } from '../services';

export interface Context {
  operations: Operations;
  services: Services;
}

export async function buildContext(): Promise<Context> {
  const services = await buildServices(clients);
  const operations = await buildOperations(clients, services);
  return {
    services,
    operations,
  };
}
