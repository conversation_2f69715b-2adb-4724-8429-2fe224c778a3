import sizeof from 'object-sizeof';

export async function queryAndBatchAction<Input, Result>({
  action,
  batchSize,
  totalCount,
  query,
}: {
  batchSize: number;
  totalCount: number;
  query: (skip: number, take: number) => Promise<Input[]>;
  action: (args: Input[]) => Promise<Result | Result[]>;
}) {
  const results: Result[] = [];

  const numBatches = Math.ceil(totalCount / batchSize);
  const batches = [...Array(numBatches)];

  for (const batchNumber of batches.keys()) {
    const chunk: Input[] = await query(batchSize * batchNumber, batchSize);
    const actionResult = await action(chunk);
    results.push(...(Array.isArray(actionResult) ? actionResult : [actionResult]));
  }
  return results;
}

export function batchArray<Input>({
  batchSize,
  totalCount,
  array,
}: {
  batchSize: number;
  totalCount: number;
  array: Input[];
}) {
  const results: Input[][] = [];

  const numBatches = Math.ceil(totalCount / batchSize);
  const batches = [...Array(numBatches)];

  for (const batchNumber of batches.keys()) {
    const chunk: Input[] = array.slice(
      batchSize * batchNumber,
      batchSize * batchNumber + batchSize,
    );
    results.push(chunk);
  }
  return results;
}

// biome-ignore lint/suspicious/noExplicitAny: any array type
export function getBatchSize(input: Array<any>): number {
  const totalSizeInMB = input.reduce((acc, curr) => acc + sizeof(curr), 0) / 100000;
  return Math.ceil(input.length / Math.ceil(totalSizeInMB));
}
