import { describe, expect, it, jest } from '@jest/globals';
import { batchArray, getBatchSize, queryAndBatchAction } from './batch';

describe('queryAndBatchAction', () => {
  it('should not query and execute the action if total count is not valid', async () => {
    const mockAction = jest.fn<(args: unknown[]) => Promise<unknown>>();
    const mockQuery = jest.fn<(skip: number, take: number) => Promise<unknown[]>>();
    const result = await queryAndBatchAction({
      action: mockAction,
      batchSize: 10,
      totalCount: 0,
      query: mockQuery,
    });
    expect(mockAction).toHaveBeenCalledTimes(0);
    expect(mockAction).toHaveBeenCalledTimes(0);
    expect(result).toEqual([]);
  });
  it('should query and execute the action once if total count is more than 0', async () => {
    const mockAction = jest.fn<(args: unknown[]) => Promise<unknown>>();
    const mockQuery = jest
      .fn<(skip: number, take: number) => Promise<unknown[]>>()
      .mockResolvedValue(['test']);
    await queryAndBatchAction({
      action: mockAction,
      batchSize: 10,
      totalCount: 1,
      query: mockQuery,
    });
    expect(mockAction).toHaveBeenCalledTimes(1);
    expect(mockQuery).toHaveBeenCalledTimes(1);
  });
  it('should query and execute the action once based on number of batches', async () => {
    const mockAction = jest.fn<(args: unknown[]) => Promise<unknown>>();
    const mockQuery = jest
      .fn<(skip: number, take: number) => Promise<unknown[]>>()
      .mockResolvedValueOnce(Array(10).fill('test'))
      .mockResolvedValueOnce(Array(8).fill('test'));
    await queryAndBatchAction({
      action: mockAction,
      batchSize: 10,
      totalCount: 18,
      query: mockQuery,
    });
    expect(mockAction).toHaveBeenCalledTimes(2);
    expect(mockQuery).toHaveBeenCalledTimes(2);
    expect(mockQuery).toHaveBeenCalledWith(0, 10);
    expect(mockQuery).toHaveBeenLastCalledWith(10, 10);
  });
  it('should return the results of all the actions', async () => {
    const mockAction = jest
      .fn<(args: unknown[]) => Promise<unknown>>()
      .mockResolvedValue('success');
    const mockQuery = jest
      .fn<(skip: number, take: number) => Promise<unknown[]>>()
      .mockResolvedValue(Array(10).fill('test'));
    const result = await queryAndBatchAction({
      action: mockAction,
      batchSize: 10,
      totalCount: 20,
      query: mockQuery,
    });
    expect(result).toEqual(['success', 'success']);
  });
});

describe('batchArray', () => {
  it('should split up input array into sub arrays based on batchSize', () => {
    const input = [...Array(5).keys()];
    const batchSize = 2;
    expect(batchArray({ batchSize, totalCount: input.length, array: input })).toStrictEqual([
      [0, 1],
      [2, 3],
      [4],
    ]);
  });
  it('should return one chunk if batch size is greater than input length', () => {
    const input = [...Array(5).keys()];
    const batchSize = 6;
    expect(batchArray({ batchSize, totalCount: input.length, array: input })).toStrictEqual([
      input,
    ]);
  });
  it('should result size matches the input size', () => {
    const input = [...Array(3394).keys()];
    const batchSize = 179;
    expect(
      batchArray({ batchSize, totalCount: input.length, array: input }).reduce(
        (cc, chunk) => cc + chunk.length,
        0,
      ),
    ).toStrictEqual(3394);
  });
});

describe('getBatchSize', () => {
  const sampleInput = (key: number) => ({
    cohort: 'Cohort',
    email: `test+${key}@gmail.com`,
    firstName: `Test ${key}`,
    lastName: 'User',
    organization: 'Beam Test',
    participantAddress: '1 Some Address',
    participantZipCode: 'zipCode',
    paymentAmount: '88000',
    paymentDate: '2024-11-09T05:00:00.000Z',
    paymentMethod: 'physicalCard',
    program: 'Program Test',
    programAddress: '1 Some Address',
    programCity: 'City',
    programState: 'State',
    programZipCode: '19122',
    salesForceId: `SaleForce ${key}`,
    stipendType: 'stipendType',
  });
  it('should return a batchSize based on object size', () => {
    expect(getBatchSize([...Array(5).keys()])).toEqual(5);
    expect(getBatchSize([...Array(3394).keys()].map(sampleInput))).toEqual(213);
    expect(getBatchSize([...Array(2).keys()].map(sampleInput))).toEqual(2);
  });
});
