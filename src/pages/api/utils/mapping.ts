import { randomUUID } from 'node:crypto';
import {
  type Addresses as Address,
  type ApplicantTypes as ApplicantType,
  Casestatus as CaseStatus,
  type Partners as Partner,
  Paymentmethod as PaymentMethod,
  Paymentpattern as PaymentPattern,
  Paymentstatus as PaymentStatus,
  type Programs as Program,
  Scheduletype as ScheduleType,
  type Users as User,
} from '@prisma/clients/platform';
import type {
  Accounttype as AccountType,
  Recipients as PaymentRecipientEntity,
} from '@prisma/generated/paymentClient';
import { parseAddress } from '@utils/address';
import dayjs from '@utils/dayJsConfig';
import { getFirstName, getLastName } from '@utils/formatting';
import { type Payee, PayeeType } from 'types/payee';
import type { Recipient } from 'types/schedulePayments';

export const mapRecipientToPlatformEntities = ({
  partner,
  program,
  applicantType,
  recipient,
}: {
  applicantType: ApplicantType;
  partner: Partner;
  program: Program;
  recipient: Recipient;
}) => {
  const userId = randomUUID();
  const caseId = randomUUID();
  const applicationId = randomUUID();
  const versionId = randomUUID();
  const { firstName, lastName, email, salesForceId } = recipient;
  const name = `${firstName} ${lastName}`;

  return {
    user: {
      id: userId,
      name,
      email,
      legacyId: salesForceId,
      partnerId: partner.id,
    },
    applicantProfile: { userId: userId, applicantTypeId: applicantType.id },
    case: { name, id: caseId, programId: program.id, status: CaseStatus.InReview },
    application: {
      caseId: caseId,
      submitterId: userId,
      id: applicationId,
      submittedAt: dayjs().toISOString(),
    },
    applicationVersion: {
      applicationId: applicationId,
      creatorId: userId,
      id: versionId,
    },
    applicationAnswers: ['salesForceId', 'organization', 'program', 'stipendType', 'cohort']
      .map((key) => ({
        versionId: versionId,
        key: key,
        value: recipient[key],
      }))
      .concat([
        {
          versionId: versionId,
          key: 'name',
          value: name,
        },
      ]),
  };
};

const mapMailingAddress = (address: string): Omit<Address, 'id'> => {
  // TODO: using geocoder?
  return parseAddress(address);
};

export const mapUserEntityToPayee = (user: User, recipient: Recipient) => {
  return {
    payeeType: PayeeType.User,
    ...user,
    ...(recipient.paymentMethod === PaymentMethod.ach && {
      bankAccount: {
        routingNumber: recipient.routingNumber,
        accountType: recipient.accountType,
        accountNumber: recipient.accountNumber,
      },
    }),
    mailingAddress: mapMailingAddress(
      `${recipient.programAddress}, ${recipient.programCity}, ${recipient.programState} ${recipient.programZipCode}`,
    ),
  } as Payee;
};

export const createPayments = ({
  paymentAmount,
  paymentDate,
  paymentMethod,
  caseId,
  userId,
  fundId,
}) => {
  const fulfillmentId = randomUUID();
  const paymentId = randomUUID();
  const amount = Number(paymentAmount);
  const count = 1;
  const totalAmount = amount * count;
  return {
    paymentPattern: {
      fulfillmentId: fulfillmentId,
      amount,
      pattern: PaymentPattern.oneTime,
      start: paymentDate,
      count,
    },
    fulfillment: {
      id: fulfillmentId,
      fundId: fundId,
      caseId: caseId,
      approvedAmount: totalAmount,
      scheduleType: ScheduleType.recurring, // Should we change this?
    },
    payment: {
      id: paymentId,
      fulfillmentId: fulfillmentId,
      payeeId: userId,
      payeeType: PayeeType.User,
      amount: totalAmount,
      method: paymentMethod,
      status: PaymentStatus.authorized,
    },
  };
};

export const mapUserToPaymentEntities = ({
  user,
  cardId,
  accountType,
  recipient,
}: {
  user: User;
  cardId: number;
  accountType: AccountType;
  recipient?: PaymentRecipientEntity;
}) => {
  const recipientId = recipient ? recipient.id : randomUUID();
  return {
    account: {
      referenceId: `${user.id}-${accountType}`,
      type: accountType,
      keys: { cardId },
      recipientId,
    },
    ...(!recipient && {
      recipient: {
        id: recipientId,
        referenceId: user.id,
        keys: { firstName: getFirstName(user.name), lastName: getLastName(user.name) },
      },
    }),
  };
};
