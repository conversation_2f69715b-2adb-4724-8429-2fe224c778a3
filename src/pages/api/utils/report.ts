import { EncryptionTransformer } from '@utils/crypto';
import { centsToDollars } from '@utils/currency';
import { ReportTypes, type TaxUser } from '../types/report';

const transformer = new EncryptionTransformer({
  key: process.env.PLATFORM_ENCRYPTION_KEY as string,
  algorithm: 'aes-256-cbc',
  ivLength: 16,
});

enum W9TaxClassifications {
  INDIVIDUAL = 'Individual',
  BUSINESS = 'Business',
}

enum W9TinValues {
  SSN = 'SocialSecurityNumber',
  EIN = 'EmployerIdentificationNumber',
}

export const Headers_1099_Report = [
  { label: 'Reference ID (Optional)' },
  { label: `Recipient's Name`, field: 'name' },
  { label: '"Federal ID type (1=EIN, 2=SSN, 3=ITIN, 4=ATIN)"', field: 'taxIdType' },
  { label: `Recipient's Federal ID No.`, field: 'taxId' },
  { label: `Recipient's Second Name (optional)`, field: 'businessName' },
  { label: 'Street Address', field: 'addressLine1' },
  { label: 'Street Address Line 2', field: 'addressLine2' },
  { label: 'City', field: 'city' },
  { label: 'State (2 letters)', field: 'state' },
  { label: 'Zip', field: 'zip' },
  { label: 'Foreign City/Province/State/Postal Code/Country (leave blank if US)' },
  { label: `Recipient's Email`, field: 'email' },
  { label: `Acc't No. (optional)` },
  { label: 'Office Code (optional)' },
  { label: 'Box 1 Rents' },
  { label: 'Box 2 Royalties' },
  { label: 'Box 3 Other Income', field: 'awardedAmount' },
  { label: 'Box 4 Fed Income Tax withheld' },
  { label: 'Box 5 Fishing Boat Proceeds' },
  { label: 'Box 6 Medical & Health Care' },
  {
    label:
      '"Box 7 (blank = false, 1=true) Payer made direct sales of $5,000 or more of consumer products to a buyer (recipient) for resale"',
  },
  { label: 'Box 8 Substitute Payments' },
  { label: 'Box 9 Crop Insurance Proceeds' },
  { label: 'Box 10 Gross Proceeds Paid to an Attorney' },
  { label: 'Box 11 Fish purchased for resale' },
  { label: 'Box 12 Section 409A deferrals' },
  { label: `"Box 13 FATCA filing req't (1=yes, blank=no)"`, field: 'exemptFATCACode' },
  { label: 'Box 14 Excess golden parachute payments' },
  { label: 'Box 15 Nonqualified deferred compensation' },
  { label: 'Box 16a State Tax withheld' },
  { label: 'Box 16b Local Tax withheld' },
  { label: 'Box 17a State (2-letters)' },
  { label: 'Box 17 Payer State ID No.' },
  { label: 'Box 17b Locality' },
  { label: 'Box 18a State Income' },
  { label: 'Box 18b Local Income' },
  { label: '"Second TIN Notice (1=true, else blank)"' },
];

const get1099ReportQuery = (partnerId: string, userIds: string[]) => ({
  where: {
    id: { in: userIds },
    partnerId,
    deactivatedAt: null,
    admins: { none: {} },
  },
  include: {
    taxForms: {
      include: {
        document: true,
      },
    },
  },
});

// biome-ignore lint/suspicious/noExplicitAny: could be any schema prisma where and include
export const Query_Reports: Record<ReportTypes, (...args: any) => { where: any; include: any }> = {
  [ReportTypes.Tax_Compliance_1099]: get1099ReportQuery,
};

// Federal ID type (1=EIN, 2=SSN, 3=ITIN, 4=ATIN)
const getTaxType = (taxClassification: W9TaxClassifications, tin: W9TinValues) => {
  if (
    (taxClassification === W9TaxClassifications.INDIVIDUAL && tin === W9TinValues.EIN) ||
    taxClassification === W9TaxClassifications.BUSINESS
  )
    return '1';
  if (taxClassification === W9TaxClassifications.INDIVIDUAL && tin === W9TinValues.SSN) return '2';

  return '';
};

export function mapUserData(user, awardedAmountByPayeeId: Map<string, number>): TaxUser {
  const { id, name, taxId, taxForms = [] } = user;
  const [taxForm] = taxForms;
  const taxData = taxForm?.data;
  const transformedTaxId = (taxId && transformer.from(taxId)) || '';
  return {
    displayId: `${user.displayId}`,
    name,
    email: user.email,
    taxIdType: getTaxType(taxData?.taxClassification, taxData?.tin),
    taxId: transformedTaxId,
    ...(taxData?.taxClassification === W9TaxClassifications.BUSINESS && {
      businessName: taxData?.name,
      exemptFATCACode: taxData?.exemptFATCACode ? '1' : '',
    }),
    addressLine1: taxData?.addressLine1 ?? '',
    addressLine2: taxData?.addressLine2 ?? '',
    city: taxData?.city ?? '',
    state: taxData?.state ?? '',
    zip: taxData?.zip ?? '',
    documentKey: taxForm?.document?.documentKey ?? '',
    awardedAmount: centsToDollars(awardedAmountByPayeeId.get(id) ?? 0),
  };
}
