import type { JsonObject } from '@prisma/client/runtime/library';
import clients from '@prisma/clients';
import { logger } from '@utils/logger';
import type { NextApiRequest, NextApiResponse } from 'next';
import NodeCache from 'node-cache';

const DEFAULT_ACCESS = {
  admin: {
    admins: ['list', 'show', 'edit', 'create'],
    analytics_resources: ['list', 'show', 'edit', 'create'],
    applicant_profile_configurations: ['show', 'edit', 'create', 'delete'],
    applicant_types: ['list', 'show', 'edit', 'create', 'delete'],
    application_configurations_courses: ['list', 'create', 'edit', 'delete'],
    application_configurations: ['list', 'show', 'edit', 'create'],
    cases: ['list', 'show'],
    changelogs: ['list', 'show', 'edit', 'create', 'delete'],
    configurations: ['list', 'show'],
    eligibility_config: ['show'],
    features: ['list', 'show', 'edit', 'create'],
    fulfillments: ['list', 'show'],
    funds: ['list', 'show', 'edit', 'create'],
    notification_templates: ['list', 'show', 'edit', 'create', 'delete'],
    migrations: ['list'],
    partner_features: ['list', 'show', 'edit', 'create'],
    partner_incidents: ['list', 'show', 'edit', 'create'],
    partners: ['list', 'show', 'edit', 'create', 'identity', 'reports'],
    'partner-admins': ['list', 'show', 'edit', 'create'],
    partner_communication_channels_configurations: ['list', 'show', 'create'],
    payments: ['list', 'show'],
    'payments-schedules': ['list', 'show'],
    'payments-section': ['list', 'show'],
    'payments-settings': ['list', 'show', 'edit'],
    'transfer-payments': ['list', 'show'],
    program_applicant_types: ['list', 'edit', 'create', 'delete'],
    program_features: ['list', 'show', 'edit', 'create'],
    program_funds: ['list', 'show', 'create'],
    programs: ['list', 'show', 'edit', 'create'],
    schedules: ['list', 'show', 'edit'],
    users: ['list', 'show'],
    core_users: ['show', 'edit', 'deactivate', 'delete'],
    external_api_users: ['list', 'create', 'delete'],
    vendor_types: ['list', 'show', 'create'],
    verification_configurations: ['create', 'edit'],
    workflow_events: ['list', 'show'],
    incident_messages: ['list', 'show'],

    // Sorcery internals
    principals: ['list', 'show', 'edit', 'create', 'delete'],
    audit_logs: ['list', 'show'],
    role_permissions: ['list', 'show', 'edit', 'create'],
    'iam-admin': ['list'],
    settings: ['list'],
  },
  pst: {
    admins: ['list', 'show', 'edit', 'create'],
    analytics_resources: ['list', 'show', 'edit', 'create'],
    applicant_profile_configurations: ['show', 'edit', 'create', 'delete'],
    applicant_types: ['list', 'show', 'edit', 'create', 'delete'],
    application_configurations_courses: ['list', 'create', 'edit', 'delete'],
    application_configurations: ['list', 'show', 'edit', 'create'],
    cases: ['list', 'show'],
    changelogs: ['list', 'show', 'edit', 'create', 'delete'],
    configurations: ['list', 'show'],
    funds: ['list', 'show', 'edit', 'create'],
    migrations: [],
    notification_templates: ['list', 'show', 'edit', 'create', 'delete'],
    partner_incidents: ['list', 'show', 'edit', 'create'],
    partners: ['list', 'show', 'edit', 'create'],
    'partner-admins': ['list', 'show', 'edit', 'create'],
    partner_communication_channels_configurations: ['list', 'show', 'create'],
    payments: ['list', 'show'],
    'payments-schedules': ['list', 'show'],
    'payments-section': ['list', 'show'],
    'payments-settings': ['list', 'show'],
    'transfer-payments': ['list', 'show'],
    program_applicant_types: ['list', 'edit', 'create', 'delete'],
    program_features: ['list', 'show', 'edit', 'create'],
    program_funds: ['list', 'show', 'create'],
    programs: ['list', 'show', 'edit', 'create'],
    users: ['list', 'show'],
    core_users: ['show', 'edit', 'deactivate'],
    external_api_users: ['list', 'create', 'delete'],
    vendor_types: ['list', 'show', 'create'],
    workflow_events: ['list', 'show'],
    incident_messages: ['list', 'show'],
  },
};

const AccessCache = new NodeCache({ stdTTL: 60 * 60 });
/**
 * @param req The Next.js API request object.
 * @param res The Next.js API response object.
 * @returns A void Promise that resolves when the authentication process is complete.
 */
export default async (req: NextApiRequest, res: NextApiResponse<JsonObject>): Promise<void> => {
  const { role } = req.body;
  let access = DEFAULT_ACCESS[role];
  const queryKey = role;
  const savedCache = AccessCache.get(queryKey);
  if (!savedCache) {
    try {
      const permission = await clients.sorcery.rolePermissions.findFirst({ where: { role } });
      if (permission?.access) {
        access = permission.access;
        AccessCache.set(queryKey, permission.access);
      }
    } catch (error) {
      logger.error({ error }, `Unexpected error, retrieving role (${role}) permissions >`);
    }
  } else {
    access = AccessCache.get(queryKey);
  }
  return res.status(200).json(access);
};
