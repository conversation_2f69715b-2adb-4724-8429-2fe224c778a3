import { logger } from '@utils/logger';
import { AxiosError } from 'axios';
import type { NextApiRequest, NextApiResponse } from 'next';
import { buildContext } from 'pages/api/utils/buildContext';

// See: https://github.com/prisma/studio/issues/614#issuecomment-795213237
// @ts-ignore
BigInt.prototype.toJSON = function () {
  return this.toString();
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const ctx = await buildContext();
    const { action } = req.query;

    const response = await ctx.operations.platform.scheduleRecurringPayments.run({
      ...req.body,
      action,
    });
    res.status(200).json({ message: 'success', data: response });
  } catch (e) {
    logger.error({ error: e }, 'Scheduling payments encountered an error =>');
    const response = e instanceof AxiosError ? e.response : null;
    return res.status(500).send(response?.data || { error: (e as Error).message });
  }
}
