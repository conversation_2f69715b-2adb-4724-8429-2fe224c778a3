import prismaClients from '@prisma/clients';
import { logger } from '@utils/logger';
import type { NextApiRequest, NextApiResponse } from 'next';
import DeleteUserOperation from 'pages/api/operations/compliance/DeleteUserOperation';
import { buildContext } from 'pages/api/utils/buildContext';
import { DeleteTypes } from 'types/requests';

// See: https://github.com/prisma/studio/issues/614#issuecomment-795213237
// @ts-ignore
BigInt.prototype.toJSON = function () {
  return this.toString();
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'DELETE') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const entityId = req.query.id as string;

    const { type, author, entityType, partnerId, requestType } = req.body;
    logger.info(`Deleting (${type}) user with id: ${entityId}`);

    if (!entityId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    const operation = new DeleteUserOperation(prismaClients.platform, prismaClients.compliance);
    const result = await operation.run({
      entityId,
      entityType,
      partnerId,
      requestType,
      type: type as DeleteTypes,
      author: author ? { id: author } : undefined,
    });

    const ctx = await buildContext();
    await ctx.operations.audits.create.run({
      resource: 'users',
      action: type === DeleteTypes.HARD ? 'DELETE_USER' : 'updateMany',
      author,
      meta: { id: entityId },
      data: result,
    });

    return res.status(200).json({ message: 'success', data: result });
  } catch (error) {
    console.error('Error deleting user:', error);
    return res.status(500).json({ error: (error as Error).message });
  }
}
