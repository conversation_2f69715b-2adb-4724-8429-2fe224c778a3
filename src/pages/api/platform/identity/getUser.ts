import { logger } from '@utils/logger';
import { AxiosError } from 'axios';
import type { NextApiRequest, NextApiResponse } from 'next';
import { buildContext } from 'pages/api/utils/buildContext';

export default async (req: NextApiRequest, res: NextApiResponse) => {
  try {
    const { userIdentifier, coreUserId, identityUserId } = req.body;
    if (!userIdentifier || !coreUserId || !identityUserId) throw new Error('missing input value');
    const ctx = await buildContext();
    const response = await ctx.services.identities.getUser({
      userIdentifier,
      coreUserId,
      identityUserId,
    });
    return res.json(response);
  } catch (e) {
    logger.error({ error: e }, 'identity users creation error =>');
    const response = e instanceof AxiosError ? e.response : null;
    return res.status(500).send(response?.data || { error: (e as Error).message });
  }
};
