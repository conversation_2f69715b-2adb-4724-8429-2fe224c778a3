import prismaClients from '@prisma/clients';
import { logger } from '@utils/logger';
import { AxiosError } from 'axios';
import type { NextApiRequest, NextApiResponse } from 'next';
import { buildContext } from 'pages/api/utils/buildContext';

export default async (req: NextApiRequest, res: NextApiResponse) => {
  try {
    const { relationships, partnerId } = req.body;
    if (!relationships?.length) throw new Error('missing required field: "relationships"');

    const ctx = await buildContext();

    logger.info({ relationships, partnerId }, 'Identity auth relationships creation starts ...');
    await ctx.services.identities.createRelationships({ relationships });

    return res.json({ message: 'success' });
  } catch (e) {
    logger.error({ error: e }, 'Identity auth relationships creation error =>');
    const response = e instanceof AxiosError ? e.response : null;
    return res.status(500).send(response?.data || { error: (e as Error).message });
  }
};
