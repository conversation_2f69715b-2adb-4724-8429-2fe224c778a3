import { logger } from '@utils/logger';
import { AxiosError } from 'axios';
import type { NextApiRequest, NextApiResponse } from 'next';
import { buildContext } from 'pages/api/utils/buildContext';

export default async (req: NextApiRequest, res: NextApiResponse) => {
  try {
    const { partnerId } = req.body;

    if (!partnerId) throw new Error('partner ID required');
    const ctx = await buildContext();

    const response = await ctx.services.identities.fixBulkOverridePaymentsRelations({ partnerId });
    return res.json(response);
  } catch (e) {
    logger.error({ error: e }, 'fix Bulk Override Payments relations error =>');
    const response = e instanceof AxiosError ? e.response : null;
    return res.status(500).send(response?.data || { error: (e as Error).message });
  }
};
