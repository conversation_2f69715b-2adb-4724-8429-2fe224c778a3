import { logger } from '@utils/logger';
import { AxiosError } from 'axios';
import type { NextApiRequest, NextApiResponse } from 'next';
import { buildContext } from 'pages/api/utils/buildContext';

export default async (req: NextApiRequest, res: NextApiResponse) => {
  try {
    const { partnerId, coreUserId } = req.body;
    if (!coreUserId || !partnerId) throw new Error('missing required fields: coreUserId/partnerId');
    const ctx = await buildContext();
    const response = await ctx.services.identities.readPortalRoles({
      userIdentifier: 'coreUserId',
      coreUserId,
      partnerId,
    });
    return res.json(response);
  } catch (e) {
    logger.error({ error: e }, 'Reading portal roles encountered an error');
    const response = e instanceof AxiosError ? e.response : null;
    return res.status(500).send(response?.data || { error: (e as Error).message });
  }
};
