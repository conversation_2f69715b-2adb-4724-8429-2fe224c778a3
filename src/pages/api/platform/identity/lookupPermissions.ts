import { logger } from '@utils/logger';
import { AxiosError } from 'axios';
import type { NextApiRequest, NextApiResponse } from 'next';
import { buildContext } from 'pages/api/utils/buildContext';

export default async (req: NextApiRequest, res: NextApiResponse) => {
  try {
    const {
      resource,
      subject,
      objectTypeFilter,
      permission,
      source,
      pagination = { take: 100 },
    } = req.body;
    if (!permission) throw new Error('missing required fields');
    const ctx = await buildContext();
    const response = await ctx.services.identities.lookupPermissions({
      resource,
      subject,
      objectTypeFilter,
      permission,
      source,
      pagination,
    });
    return res.json(response);
  } catch (e) {
    logger.error({ error: e }, 'encountered an error');
    const response = e instanceof AxiosError ? e.response : null;
    return res.status(500).send(response?.data || { error: (e as Error).message });
  }
};
