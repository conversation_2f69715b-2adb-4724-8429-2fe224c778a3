import { logger } from '@utils/logger';
import { AxiosError } from 'axios';
import type { NextApiRequest, NextApiResponse } from 'next';
import { buildContext } from 'pages/api/utils/buildContext';

export default async (req: NextApiRequest, res: NextApiResponse) => {
  try {
    const { partnerId, coreUserId, ...userUpdate } = req.body;
    if (!coreUserId || !partnerId) throw new Error('missing required field: id/partnerId');
    const ctx = await buildContext();

    const response = await ctx.services.identities.updateUser({
      userIdentifier: 'coreUserId',
      coreUserId: coreUserId,
      partnerId,
      ...userUpdate,
      _email: 'email',
      _name: 'name',
      _phone: 'phone',
      // deprecated: should be removed later
      _userId: 'userId',
      _gcipUid: 'gcipUid',
      _tenantId: 'tenantId',
    });
    return res.json(response);
  } catch (e) {
    logger.error({ error: e }, 'Updating user encountered an error');
    const response = e instanceof AxiosError ? e.response : null;
    return res.status(500).send(response?.data || { error: (e as Error).message });
  }
};
