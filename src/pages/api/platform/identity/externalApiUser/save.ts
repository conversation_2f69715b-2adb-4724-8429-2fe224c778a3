// See: https://github.com/prisma/studio/issues/614#issuecomment-795213237

import { logger } from '@utils/logger';
import { AxiosError } from 'axios';
import type { NextApiRequest, NextApiResponse } from 'next';
import { buildContext } from 'pages/api/utils/buildContext';

// @ts-ignore
BigInt.prototype.toJSON = function () {
  return this.toString();
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { appId, partnerId, email } = req.body;

    if (!appId || !partnerId || !email) throw new Error('appId, partnerId, and email required');

    const context = await buildContext();

    await context.operations.identity.saveExternalApiUser.run({
      appId,
      partnerId,
      email,
    });

    res.status(200).json({ message: 'success', data: { success: true } });
  } catch (e) {
    logger.error({ error: e }, 'External Api User Save encountered an error =>');
    const response = e instanceof AxiosError ? e.response : null;
    return res.status(500).send(response?.data || { error: (e as Error).message });
  }
}
