import { logger } from '@utils/logger';
import { AxiosError } from 'axios';
import type { NextApiRequest, NextApiResponse } from 'next';
import { buildContext } from 'pages/api/utils/buildContext';

export default async (req: NextApiRequest, res: NextApiResponse) => {
  try {
    const { users } = req.body;
    if (!users?.length) throw new Error('missing input value');
    if (users?.some((user) => !user.email)) throw new Error('missing required field: "email"');
    const ctx = await buildContext();
    const responses = await Promise.all(
      users.map((user) => ctx.services.identities.createUser(user)),
    );
    return res.json(responses);
  } catch (e) {
    logger.error({ error: e }, 'identity users creation error =>');
    const response = e instanceof AxiosError ? e.response : null;
    return res.status(500).send(response?.data || { error: (e as Error).message });
  }
};
