import { logger } from '@utils/logger';
import { AxiosError } from 'axios';
import type { NextApiRequest, NextApiResponse } from 'next';
import { buildContext } from 'pages/api/utils/buildContext';

export default async (req: NextApiRequest, res: NextApiResponse) => {
  try {
    const { partnerId, role, options } = req.body;

    if (!partnerId || !role) throw new Error('partner ID/role required');

    const ctx = await buildContext();
    const response = await ctx.services.identities.upsertTenant({ partnerId, role, options });
    return res.json(response);
  } catch (e) {
    logger.error({ error: e }, 'grpc tenant upsert error =>');
    const response = e instanceof AxiosError ? e.response : null;
    return res.status(500).send(response?.data || { error: (e as Error).message });
  }
};
