import { PrismaClient } from '@prisma/clients/platform';
import type { NextApiRequest, NextApiResponse } from 'next';

const prismaClient = new PrismaClient();

const STATS_CONFIG = {
  admins: {
    copy: 'Number of Advocates',
    get: () => prismaClient.admins.count(),
  },
  applications: {
    copy: 'Number of Applications',
    get: () => prismaClient.applications.count(),
  },
  partners: {
    copy: 'Number of Partners',
    get: () => prismaClient.partners.count(),
  },
};

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const stats = await Promise.all(
    Object.entries(STATS_CONFIG).map(async ([key, { copy, get }]) => ({
      key,
      copy,
      value: await get(),
    })),
  );
  res.status(200).json(stats);
};

export default handler;
