// See: https://github.com/prisma/studio/issues/614#issuecomment-795213237

import { logger } from '@utils/logger';
import { AxiosError } from 'axios';
import type { NextApiRequest, NextApiResponse } from 'next';
import { buildContext } from 'pages/api/utils/buildContext';

// @ts-ignore
BigInt.prototype.toJSON = function () {
  return this.toString();
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { updates, creations, deletes, configId } = req.body;

    if (!updates || !creations || !deletes || !configId)
      throw new Error('updates, creations, and deletions required');

    const context = await buildContext();

    await context.operations.platform.saveEligibilityQuestions.run({
      updates,
      creations,
      deletes,
      configId,
    });

    res.status(200).json({ message: 'success', data: { success: true } });
  } catch (e) {
    logger.error({ error: e }, 'Eligibility Questions save encountered an error =>');
    const response = e instanceof AxiosError ? e.response : null;
    return res.status(500).send(response?.data || { error: (e as Error).message });
  }
}
