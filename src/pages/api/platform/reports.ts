import clients from '@prisma/clients';
import { logger } from '@utils/logger';
import { AxiosError } from 'axios';
import type { NextApiRequest, NextApiResponse } from 'next';
import { ReportTypes } from '../types/report';
import { buildContext } from '../utils/buildContext';

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  try {
    const { type, author, password, ...params } = req.body;
    const ctx = await buildContext();
    // biome-ignore lint/suspicious/noExplicitAny: Push Response
    let response: any;

    switch (type) {
      case ReportTypes.Tax_Compliance_1099:
        response = await ctx.services.reports.create1099Report({ ...params, password });
        break;

      default:
        throw new Error('type is not supported');
    }

    logger.info('Saving the report response in logs!');
    await clients.sorcery.auditLog.create({
      data: {
        resource: 'system_report',
        action: 'create',
        data: { response },
        meta: { type, ...params },
        author,
      },
    });

    return res.json(response);
  } catch (e) {
    const response = e instanceof AxiosError ? e.response : null;
    return res.status(500).send(response?.data || { error: (e as Error).message });
  }
};
export default handler;
