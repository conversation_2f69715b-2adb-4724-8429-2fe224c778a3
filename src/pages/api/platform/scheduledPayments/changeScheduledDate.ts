import { AxiosError } from 'axios';
import type { NextApiRequest, NextApiResponse } from 'next';
import { buildContext } from 'pages/api/utils/buildContext';

// See: https://github.com/prisma/studio/issues/614#issuecomment-795213237
// @ts-ignore
BigInt.prototype.toJSON = function () {
  return this.toString();
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { paymentIds, scheduledFor } = req.body;
    if (!paymentIds.length) {
      throw new Error('Invalid request');
    }

    const ctx = await buildContext();

    const response = await ctx.operations.scheduler.updateJobs.run({
      ids: paymentIds,
      scheduledFor: scheduledFor,
    });
    await ctx.operations.platform.updatePayments.run({
      ids: paymentIds,
      scheduledFor: scheduledFor,
    });
    res.status(200).json({ message: 'success', data: response });
  } catch (e) {
    const response = e instanceof AxiosError ? e.response : null;
    return res.status(500).send(response?.data || { error: (e as Error).message });
  }
}
