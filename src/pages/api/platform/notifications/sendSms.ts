import { logger } from '@utils/logger';
import { AxiosError } from 'axios';
import type { NextApiRequest, NextApiResponse } from 'next';
import { buildContext } from 'pages/api/utils/buildContext';

export default async (req: NextApiRequest, res: NextApiResponse) => {
  try {
    const body = req.body;
    const ctx = await buildContext();
    const response = await ctx.services.notifications.sendSms(body);
    return res.json(response);
  } catch (e) {
    logger.error({ error: e }, 'notification send sms =>');
    const response = e instanceof AxiosError ? e.response : null;
    return res.status(500).send(response?.data || { error: (e as Error).message });
  }
};
