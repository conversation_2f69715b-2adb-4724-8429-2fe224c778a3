import { logger } from '@utils/logger';
import { AxiosError } from 'axios';
import type { NextApiRequest, NextApiResponse } from 'next';
import { buildContext } from 'pages/api/utils/buildContext';

// See: https://github.com/prisma/studio/issues/614#issuecomment-795213237
// @ts-ignore
BigInt.prototype.toJSON = function () {
  return this.toString();
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const { author, courses, programId, key } = req.body;
    logger.info(`Updating courses for program with id: ${programId}`);

    if (!programId) throw new Error('Program ID is required.');

    const ctx = await buildContext();

    const response = await ctx.operations.platform.updateCourses.run({
      programId,
      courses,
      key,
    });

    await ctx.operations.audits.create.run({
      resource: 'applicationAnswers',
      action: 'updateMany',
      author,
      meta: { id: programId },
      data: { versionIds: response },
    });

    res.status(200).json({ message: 'success', data: response });
  } catch (e) {
    logger.error({ error: e }, 'Application answers update encountered an error =>');
    const response = e instanceof AxiosError ? e.response : null;
    return res.status(500).send(response?.data || { error: (e as Error).message });
  }
}
