import type * as grpc from '@grpc/grpc-js';

interface Response {
  message: string;
}

interface EmailRecipient {
  name: string;
  email: string;
  variables?: { [key: string]: string };
}

interface EmailSender {
  from: string;
  replyTo: string;
}

interface SmsRecipient {
  name: string;
  phone: string;
  variables?: { [key: string]: string };
}

enum MESSAGE_TAG_TYPES {
  CASE_ID = 'case_id',
  COMMENT_ID = 'comment_id',
  PARTNER_ID = 'partner_id',
  PROGRAM_ID = 'program_id',
  USER_ID = 'user_id',
}

export interface SendProviderEmailRequest {
  sender: EmailSender;
  subject: string;
  body: string;
  recipients: EmailRecipient[];
  tags?: Partial<Record<MESSAGE_TAG_TYPES, string>>;
}

export interface SendProviderSmsRequest {
  recipients: SmsRecipient[];
  body: string;
}

export interface NotificationServiceClient extends grpc.Client {
  sendEmail(
    argument: SendProviderEmailRequest,
    callback: grpc.requestCallback<Response>,
  ): grpc.ClientUnaryCall;
  sendSms(
    argument: SendProviderSmsRequest,
    callback: grpc.requestCallback<Response>,
  ): grpc.ClientUnaryCall;
}
