import type { Readable } from 'node:stream';

export enum ReportTypes {
  Tax_Compliance_1099 = '1099',
}

export interface File {
  content: Readable | Buffer;
  filename: string;
  mimetype?: string;
  encoding?: string;
}

export interface PushResponse {
  type: 'password' | 'file' | 'url';
  token?: string;
  daysRemaining?: number;
  viewsRemaining?: number;
  previewURL: string;
}

export interface UploadInput {
  directory: string;
  prefix: string;
  file: File & { mimetype: string; content: Readable };
  useTimestamp?: boolean;
}

export interface UploadResponse {
  documentKey: string;
  filename: string;
  mimetype: string;
}

export interface TaxUser {
  displayId: string;
  name: string;
  email: string;
  taxIdType: string;
  taxId: string;
  businessName?: string;
  exemptFATCACode?: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  zip: string;
  documentKey: string;
  awardedAmount: string;
}
