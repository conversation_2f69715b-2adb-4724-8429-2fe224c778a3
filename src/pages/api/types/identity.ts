import type * as grpc from '@grpc/grpc-js';

export interface User {
  id: string;
  name: string;
  email: string;
  zedToken: string;
}
export interface Response {
  message: string;
}

export interface UserResponse {
  message: string;
  user: User;
}

export enum Role {
  Manager = 'manager',
  StandardPayment = 'standard_payment',
  Standard = 'standard',
  ViewOnly = 'view_only',
}

export enum Relation {
  UNKNOWN = 'UNKNOWN',
  PLATFORM = 'PLATFORM',
  ORGANIZATION = 'ORGANIZATION',
  USER = 'USER',
  VENDOR = 'VENDOR',
  PROGRAM = 'PROGRAM',
  FUND = 'FUND',
  CASE = 'CASE',
  APPLICATION = 'APPLICATION',
  FULFILLMENT = 'FULFILLMENT',
  PAYMENT = 'PAYMENT',
  PAYEE = 'PAYEE',
}

// We should normalize this name
export enum RelationType {
  Admin = 'admin',
  Platform = 'platform',
  Parent = 'parent',
  Applicant = 'applicant',
  Vendor = 'vendor',
  Org = 'org',
  Program = 'program',
  Assignee = 'assignee',
  Editor = 'editor',
  Case = 'case',
  Submitter = 'submitter',
  Fund = 'fund',
  Payee = 'payee',
  Fulfillment = 'fulfillment',
}

export interface ObjectReference {
  objectId: string;
  objectType: Relation;
}

export interface CreateRelationshipRequest {
  object: ObjectReference;
  subject: ObjectReference;
  relation: string;
}
export interface CreateRelationshipsRequest {
  relationships: CreateRelationshipRequest[];
}

export interface IdentityServerClient extends grpc.Client {
  createUser(
    argument: { partnerId: string; roles: string[]; email?: string; name?: string },
    callback: grpc.requestCallback<UserResponse>,
  ): grpc.ClientUnaryCall;
  getUser(
    argument: {
      userIdentifier: 'coreUserId' | 'identityUserId';
      coreUserId?: string;
      identityUserId?: string;
    },
    callback: grpc.requestCallback<Response>,
  ): grpc.ClientUnaryCall;
  updateUser(
    argument: {
      userIdentifier: 'coreUserId' | 'identityUserId';
      tenantId: string;
      partnerId: string;
      coreUserId?: string;
      identityUserId?: string;
      phone?: string;
      name?: string;
      email?: string;
      roles?: string[];
    },
    callback: grpc.requestCallback<Response>,
  ): grpc.ClientUnaryCall;
  readPortalRoles(
    argument: {
      userIdentifier: 'coreUserId' | 'identityUserId';
      partnerId: string;
      coreUserId?: string;
      identityUserId?: string;
    },
    callback: grpc.requestCallback<Response>,
  ): grpc.ClientUnaryCall;
  fixPayeeRelations(
    argument: { partnerId: string },
    callback: grpc.requestCallback<Response>,
  ): grpc.ClientUnaryCall;
  fixBulkOverridePaymentsRelations(
    argument: { partnerId: string },
    callback: grpc.requestCallback<Response>,
  ): grpc.ClientUnaryCall;
  migrate(
    argument: { partnerId: string },
    callback: grpc.requestCallback<Response>,
  ): grpc.ClientUnaryCall;
  migratePermissions(
    argument: { partnerId: string; objectTypes: string[]; useStream: boolean },
    callback: grpc.requestCallback<Response>,
  ): grpc.ClientUnaryCall;
  migrateFiscalPermissions(
    argument: { partnerId: string },
    callback: grpc.requestCallback<Response>,
  ): grpc.ClientUnaryCall;
  upsertTenant(
    argument: {
      partnerId: string;
      role: string;
      // biome-ignore lint/suspicious/noExplicitAny: identity options values
      options?: Record<string, any>;
    },
    callback: grpc.requestCallback<Response>,
  ): grpc.ClientUnaryCall;
  retrieveTenant(
    argument: { tenantId: string },
    callback: grpc.requestCallback<Response>,
  ): grpc.ClientUnaryCall;
  createRelationships(
    argument: CreateRelationshipsRequest,
    callback: grpc.requestCallback<Response>,
  ): grpc.ClientUnaryCall;
  lookupPermissions(
    argument: {
      resource?: ObjectReference | null;
      subject?: ObjectReference | null;
      objectTypeFilter?: Relation;
      permission?: string;
      source?: 'resource' | 'subject';
      pagination?: { cursor?: string; take?: number };
    },
    callback: grpc.requestCallback<Response>,
  ): grpc.ClientUnaryCall;
}
