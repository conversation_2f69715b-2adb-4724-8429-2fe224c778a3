import type { PrismaClient as ComplianceClient } from '@prisma/clients/compliance';
import type { PrismaClient as PlatformClient, PrismaPromise } from '@prisma/clients/platform';
import dayjs from 'dayjs';
import { DeleteTypes } from 'types/requests';

const Schema_Names = [
  'applicationDocuments',
  'applicationAddresses',
  'applicationAnswers',
  'applicationScores',
  'applicationVerifications',
  'applicationVersions',
  'payments',
  'fulfillmentMata',
  'paymentPatterns',
  'fulfillments',
  'partnerCases',
  'assignments',
  'caseDocuments',
  'caseNotes',
  'comments',
  'applications',
  'cases',
  'enrollmentServiceServices',
  'enrollmentServices',
  'enrollmentOutcomeOutcomes',
  'enrollmentOutcomes',
  'enrollments',
  'profileAddresses',
  'profileAnswers',
  'profileDocuments',
  'profileNotes',
  'applicantProfiles',
  'userTokens',
  'taxForms',
  'programReferrals',
  'documents',
  'workflowEvents',
];

export default class DeleteUserOperation {
  private platformClient: PlatformClient;
  private complianceClient: ComplianceClient;

  constructor(platformClient: PlatformClient, complianceClient: ComplianceClient) {
    this.platformClient = platformClient;
    this.complianceClient = complianceClient;
  }

  private async queryDataAndMap(userId: string): Promise<{
    userId: string;
    profileId: string | undefined;
    caseIds: string[];
    applicationIds: string[];
    versionIds: string[];
    enrollmentIds: string[];
    fulfillmentIds: string[];
  }> {
    const user = await this.platformClient.users.findFirst({
      where: { id: userId },
      include: {
        applicantProfile: true,
        enrollments: true,
        applications: {
          include: { applicationVersions: true, case: { include: { fulfillments: true } } },
        },
      },
    });
    if (!user) throw new Error('User not found.');
    return {
      userId,
      profileId: user.applicantProfile?.id,
      caseIds: user.applications?.map((app) => app.caseId) ?? [],
      applicationIds: user.applications?.map((app) => app.id) ?? [],
      versionIds:
        user.applications.flatMap((app) => app.applicationVersions)?.map((version) => version.id) ??
        [],
      fulfillmentIds:
        user.applications
          ?.map((application) => application.case)
          ?.flatMap((_case) => _case.fulfillments)
          .map((fulfillment) => fulfillment.id) ?? [],

      enrollmentIds: user.enrollments?.map((enrollment) => enrollment.id) ?? [],
    };
  }

  private generateQueries({
    userId,
    profileId,
    caseIds,
    applicationIds,
    versionIds,
    fulfillmentIds,
    enrollmentIds,
  }) {
    return {
      ...(applicationIds?.length && {
        applicationDocuments: { applicationId: { in: applicationIds } },
        applicationAddresses: { applicationId: { in: applicationIds } },
      }),
      ...(versionIds?.length && {
        applicationAnswers: { versionId: { in: versionIds } },
        applicationScores: { applicationVersionId: { in: versionIds } },
        applicationVerifications: { applicationVersionId: { in: versionIds } },
        applicationVersions: { id: { in: versionIds } },
      }),
      ...(fulfillmentIds?.length && {
        payments: { fulfillmentId: { in: fulfillmentIds } },
        fulfillmentMeta: { fulfillmentId: { in: fulfillmentIds } },
        paymentPatterns: { fulfillmentId: { in: fulfillmentIds } },
        fulfillments: { id: { in: fulfillmentIds } },
      }),
      ...(caseIds?.length && {
        partnerCases: { caseId: { in: caseIds } },
        assignments: { caseId: { in: caseIds } },
        caseDocuments: { caseId: { in: caseIds } },
        caseNotes: { caseId: { in: caseIds } },
        comments: { caseId: { in: caseIds } },
        applications: { caseId: { in: caseIds } },
        cases: { id: { in: caseIds } },
      }),
      ...(enrollmentIds?.length && {
        enrollmentOutcomeOutcomes: {
          enrollmentOutcome: { enrollment: { id: { in: enrollmentIds } } },
        },
        enrollmentOutcomes: { enrollment: { id: { in: enrollmentIds } } },
        enrollmentServiceServices: {
          enrollmentService: { enrollment: { id: { in: enrollmentIds } } },
        },
        enrollmentServices: { enrollment: { id: { in: enrollmentIds } } },
        enrollments: { id: { in: enrollmentIds } },
      }),
      ...(profileId && {
        profileAddresses: { profileId },
        profileAnswers: { profileId },
        profileDocuments: { profileId },
        profileNotes: { profileId },
      }),

      applicantProfiles: { userId },
      userTokens: { userId },
      userDocuments: { userId },
      taxForms: { userId },
      programReferrals: { userId },
      documents: { uploaderId: userId },
      workflowEvents: {
        OR: [
          caseIds?.length && { entityId: { in: caseIds }, entityType: 'case' },
          fulfillmentIds?.length && {
            entityId: { in: fulfillmentIds },
            entityType: 'fulfillment',
          },
          { entityId: userId, entityType: 'user' },
        ].filter(Boolean),
      },
    };
  }

  public async run({
    entityId,
    entityType,
    partnerId,
    requestType,
    type = DeleteTypes.SOFT,
    author,
  }: {
    entityId: string;
    entityType: string;
    partnerId: string;
    requestType: string;
    type: DeleteTypes;
    author?: { id: string };
  }): Promise<Record<string, { count: number }>> {
    const user = await this.queryDataAndMap(entityId);
    if (!user) throw new Error('User not found.');

    const isHardDelete = type === DeleteTypes.HARD;
    if (isHardDelete) {
      await this.complianceClient.requests.create({
        data: {
          entity_id: entityId,
          entity_type: entityType,
          partner_id: partnerId,
          request_type: requestType,
          action: 'DELETE_USER',
        },
      });
      return { user: { count: 1 } };
    }
    const action = 'updateMany';
    const now = dayjs().utc().format();

    const schemaQueries = this.generateQueries(user);
    const schemaNames = Schema_Names.filter((schema) => !!schemaQueries[schema]);

    // biome-ignore lint/suspicious/noExplicitAny: any of listed schemas deleteMany or updateMany function
    const transactions: PrismaPromise<any>[] = schemaNames.map((schema) => {
      return this.platformClient[schema][action]({
        where: schemaQueries[schema],
        ...(!isHardDelete && {
          data: { deactivatedAt: now },
        }),
      });
    });

    transactions.push(
      isHardDelete
        ? this.platformClient.users.delete({ where: { id: entityId } })
        : this.platformClient.users.update({
            where: { id: entityId },
            data: { deactivatedAt: now },
          }),
    );

    const response = await this.platformClient.$transaction(transactions);

    return [...schemaNames, 'user'].reduce((acc, schema, idx) => {
      acc[schema] = response[idx];
      return acc;
    }, {});
  }
}
