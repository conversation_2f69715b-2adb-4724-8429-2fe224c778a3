import type { PrismaClients } from '@prisma/clients';
import type { Operations } from 'types/operations';
import type { Services } from '../services';
import buildAuditOperations from './audits';
import buildComplianceOperations from './compliance';
import buildIdentityOperations from './identity';
import buildPaymentOperations from './payment';
import buildPlatformOperations from './platform';
import buildSchedulerOperations from './scheduler';

const build = (clients: PrismaClients, services: Services): Operations => {
  const audits = buildAuditOperations(clients);
  const scheduler = buildSchedulerOperations(clients);
  const platform = buildPlatformOperations(clients, services);
  const payment = buildPaymentOperations(clients);
  const identity = buildIdentityOperations(clients);
  const compliance = buildComplianceOperations(clients);

  return {
    audits,
    scheduler,
    platform,
    payment,
    identity,
    compliance,
  };
};

export default build;
