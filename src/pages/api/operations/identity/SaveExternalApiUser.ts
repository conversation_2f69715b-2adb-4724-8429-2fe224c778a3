import type { PrismaClient as IdentityClient, PrismaPromise } from '@prisma/clients/identity';

export default class SaveExternalApiUserOperation {
  private identityClient: IdentityClient;

  constructor(client: IdentityClient) {
    this.identityClient = client;
  }

  public async run({ appId, partnerId, email }) {
    // biome-ignore lint/suspicious/noExplicitAny: updateMany function
    const transactions: PrismaPromise<any>[] = [];

    let user = await this.identityClient.users.findFirst({
      where: {
        email,
      },
    });

    if (!user) {
      user = await this.identityClient.users.create({
        data: {
          email: email,
        },
      });
    }
    await this.identityClient.externalApiUsers.create({
      data: {
        appId: appId,
        userId: user.id,
        partnerId: partnerId,
      },
    });

    return { success: true };
  }
}
