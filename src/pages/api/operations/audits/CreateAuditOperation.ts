import type { AuditLog, PrismaClient as SorceryClient } from '@prisma/clients/sorcery';

export default class CreateAuditOperation {
  private sorceryClient: SorceryClient;

  constructor(client: SorceryClient) {
    this.sorceryClient = client;
  }

  public async run({ resource, action, author, meta, data, previousData }): Promise<AuditLog> {
    return this.sorceryClient.auditLog.create({
      data: {
        resource,
        author,
        action,
        meta,
        data,
        previousData,
      },
    });
  }
}
