import type { PrismaClients } from '@prisma/clients';
import type { Operations } from 'types/operations';
import CreateUsioAccountsOperation from './CreateUsioAccountsOperation';

const build = (clients: PrismaClients): Operations['payment'] => {
  const createUsioAccounts = new CreateUsioAccountsOperation(clients.platform, clients.payment);

  return {
    createUsioAccounts,
  };
};

export default build;
