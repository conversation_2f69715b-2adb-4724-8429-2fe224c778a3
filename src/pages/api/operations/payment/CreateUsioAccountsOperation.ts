import type {
  Accounts as AccountEntity,
  PrismaClient as PaymentClient,
  PrismaPromise,
  Recipients as RecipientEntity,
} from '@prisma/clients/payment';
import type {
  JsonObject,
  PrismaClient as PlatformClient,
  Users as User,
} from '@prisma/clients/platform';
import { logger } from '@utils/logger';
import { queryAndBatchAction } from 'pages/api/utils/batch';
import { mapUserToPaymentEntities } from 'pages/api/utils/mapping';

export type Account = { email: string; cardId: number; type: 'physicalCard' | 'virtualCard' };

export default class CreateUsioAccountsOperation {
  private platformClient: PlatformClient;
  private paymentClient: PaymentClient;
  private batchSize = 1000;

  constructor(platformClient: PlatformClient, paymentClient: PaymentClient) {
    this.platformClient = platformClient;
    this.paymentClient = paymentClient;
  }

  private findByCardId(accounts: AccountEntity[], cardId: number) {
    return accounts?.find(
      (acc: AccountEntity) => (acc.keys as JsonObject)?.cardId?.toString() === cardId.toString(),
    );
  }

  private async validateAndFindAccounts(
    accounts: Account[],
    existingUsersByEmail: Map<string, User>,
  ): Promise<{ newAccounts: Account[]; errors: Array<{ id: string; message: string }> }> {
    let newAccounts: Account[] = [];
    const accountMap = new Map();
    const errors: Array<{ id: string; message: string }> = [];

    for (const record of accounts) {
      const { cardId, email } = record;
      const messages: Array<string> = [];
      if (!['email', 'cardId'].every((key) => record[key]))
        messages.push('basic information (email/cardId) is required');
      else if (!existingUsersByEmail.get(email))
        messages.push('no related core User found, please load users first');
      else {
        // Check for duplicate cardId
        if (accountMap.has(cardId)) {
          messages.push('duplicate card ID');
        } else accountMap.set(cardId, true);

        // Check for duplicate emails
        if (accountMap.has(email)) {
          messages.push('duplicate email');
        } else accountMap.set(email, true);
      }
      if (messages.length) errors.push({ id: email, message: messages.join(',') });
    }

    if (!errors?.length) {
      const existingAccounts = await this.paymentClient.accounts.findMany({
        where: {
          deactivatedAt: null,
          OR: accounts.map((acc: Account) => ({
            referenceId: `${(existingUsersByEmail.get(acc.email) as User).id}-${
              acc.type ?? 'physicalCard'
            }`,
          })),
        },
      });

      newAccounts = accounts.filter((acc) => !this.findByCardId(existingAccounts, acc.cardId));
      if (newAccounts?.length !== accounts.length) {
        const skippedAccounts = accounts.filter((acc) =>
          this.findByCardId(existingAccounts, acc.cardId),
        );
        logger.info(
          { skippedAccounts },
          `${this.constructor.name} Create: skipped these accounts ...`,
        );
      }
    }

    return { newAccounts, errors };
  }

  public async run({
    partnerId,
    accounts: rawAccounts,
  }: {
    partnerId: string;
    accounts: Account[];
  }): Promise<{ status: string; errors?: { id: string; message: string }[] }> {
    const accounts = rawAccounts.map((account) => ({
      ...account,
      email: account.email.toLowerCase(),
    }));
    const partner = await this.platformClient.partners.findFirstOrThrow({
      where: { id: partnerId },
    });

    const existingUsers = await this.platformClient.users.findMany({
      where: {
        partnerId: partner.id,
        deactivatedAt: null,
        OR: accounts.map((acc: Account) => ({ email: acc.email })),
      },
    });

    const existingUsersByEmail = new Map<string, User>();
    for (const user of existingUsers) existingUsersByEmail.set(user.email as string, user);

    const { errors, newAccounts } = await this.validateAndFindAccounts(
      accounts,
      existingUsersByEmail,
    );
    if (errors?.length) {
      logger.warn({ errors }, `${this.constructor.name} Create: data validation error >`);
      return { errors, status: 'error' };
    }

    if (!newAccounts?.length) return { status: 'success' };

    logger.info(`${this.constructor.name} Create: going to create ${newAccounts.length} accounts`);

    await queryAndBatchAction<Account, { message: string }>({
      batchSize: this.batchSize,
      totalCount: newAccounts.length,
      query: async (skip, take) => newAccounts.slice(skip, skip + take),
      action: async (records: Account[]) => {
        logger.info(
          `${this.constructor.name} Create: action starts (accounts: ${records.length}) ...`,
        );
        const existingRecipients = await this.paymentClient.recipients.findMany({
          where: {
            deactivatedAt: null,
            OR: records.map(({ email }: Account) => ({
              referenceId: (existingUsersByEmail.get(email) as User).id,
            })),
          },
        });
        const existingRecipientsByReferenceId = new Map<string, RecipientEntity>();
        for (const recipient of existingRecipients)
          existingRecipientsByReferenceId.set(recipient.referenceId as string, recipient);

        const mappedAccounts = records.map(({ cardId, email, type = 'physicalCard' }) => {
          const user = existingUsersByEmail.get(email) as User;
          const recipient = existingRecipientsByReferenceId.get(user.id) as RecipientEntity;
          return mapUserToPaymentEntities({
            user,
            recipient,
            cardId,
            accountType: type,
          });
        });

        // biome-ignore lint/suspicious/noExplicitAny: createMany function
        const transactions: PrismaPromise<any>[] = [];

        const recipientsToCreate = mappedAccounts.map(({ recipient }) => recipient).filter(Boolean);
        if (recipientsToCreate.length)
          // @ts-ignore
          transactions.push(this.paymentClient.recipients.createMany({ data: recipientsToCreate }));

        transactions.push(
          this.paymentClient.accounts.createMany({
            data: mappedAccounts.map(({ account }) => account),
          }),
        );

        await this.paymentClient.$transaction(transactions);
        return { message: 'complete' };
      },
    });

    return { status: 'success' };
  }
}
