// @ts-nocheck
import crypto, { UUID } from 'node:crypto';
import { beforeEach, describe, expect, it, jest } from '@jest/globals';
import type { PrismaClient as PaymentClient } from '@prisma/clients/payment';
import type { PrismaClient as PlatformClient, Users } from '@prisma/clients/platform';
import { logger } from '@utils/logger';
import Operation, { Account } from './CreateUsioAccountsOperation';

describe('CreateUsioAccountsOperation', () => {
  let operation: Operation;
  let findManyUserFn: jest.Mock;
  let findManyAccountFn: jest.Mock;
  let findManyRecipientFn: jest.Mock;
  let createManyAccountFn: jest.Mock;
  let createManyRecipientFn: jest.Mock;
  let paymentTransactionFn: jest.Mock;
  beforeEach(() => {
    findManyUserFn = jest.fn().mockResolvedValue([]);
    findManyAccountFn = jest.fn().mockResolvedValue([]);
    createManyAccountFn = jest.fn();
    findManyRecipientFn = jest.fn().mockResolvedValue([]);
    createManyRecipientFn = jest.fn();
    paymentTransactionFn = jest.fn();
    operation = new Operation(
      {
        partners: {
          findFirstOrThrow: jest.fn().mockResolvedValue({ id: 'mockParentId' }),
        },
        users: {
          findMany: findManyUserFn,
        },
      } as unknown as PlatformClient,
      {
        $transaction: paymentTransactionFn,
        accounts: {
          findMany: findManyAccountFn,
          createMany: createManyAccountFn,
        },
        recipients: {
          findMany: findManyRecipientFn,
          createMany: createManyRecipientFn,
        },
      } as unknown as PaymentClient,
    );
  });

  describe('validation', () => {
    it('should return an error status if there is duplicate records based on cardId or email', async () => {
      findManyUserFn.mockResolvedValueOnce([{ id: 'mockUserId', email: '<EMAIL>' }]);
      const result = await operation.run({
        partnerId: 'mockPartnerId',
        accounts: [
          { cardId: 12345, type: 'physicalCard', email: '<EMAIL>' },
          { cardId: 1234, email: '<EMAIL>', type: 'physicalCard' },
        ],
      });
      expect(result).toEqual({
        errors: [{ id: '<EMAIL>', message: 'duplicate email' }],
        status: 'error',
      });
      expect(paymentTransactionFn).not.toHaveBeenCalled();
    });
    it('should return an error status if cardId/email does not exist in some accounts', async () => {
      findManyUserFn.mockResolvedValueOnce([{ id: 'mockUserId', email: '<EMAIL>' }]);
      const result = await operation.run({
        partnerId: 'mockPartnerId',
        accounts: [
          { cardId: 12345, type: 'physicalCard', email: '' },
          { cardId: undefined, email: '<EMAIL>', type: 'physicalCard' },
        ] as Account[],
      });
      expect(result).toEqual({
        errors: [
          {
            id: '',
            message: 'basic information (email/cardId) is required',
          },
          { id: '<EMAIL>', message: 'basic information (email/cardId) is required' },
        ],
        status: 'error',
      });
      expect(paymentTransactionFn).not.toHaveBeenCalled();
    });
    it('should return an error status if core user does not exists', async () => {
      const result = await operation.run({
        partnerId: 'mockPartnerId',
        accounts: [{ cardId: 12345, type: 'physicalCard', email: '<EMAIL>' }] as Account[],
      });
      expect(result).toEqual({
        errors: [
          {
            id: '<EMAIL>',
            message: 'no related core User found, please load users first',
          },
        ],
        status: 'error',
      });
      expect(paymentTransactionFn).not.toHaveBeenCalled();
    });
  });
  it('should return a success msg if there is corresponding account for all requested accounts in db', async () => {
    findManyUserFn.mockResolvedValueOnce([{ id: 'mockUserId', email: '<EMAIL>' }]);
    findManyAccountFn.mockResolvedValueOnce([
      { id: 'mockAccountId', referenceId: 'mockUserId-physicalCard', keys: { cardId: 12345 } },
    ]);
    const result = await operation.run({
      partnerId: 'mockPartnerId',
      accounts: [{ cardId: 12345, type: 'physicalCard', email: '<EMAIL>' }] as Account[],
    });
    expect(result).toEqual({ status: 'success' });
    expect(findManyAccountFn).toHaveBeenCalledWith({
      where: { OR: [{ referenceId: 'mockUserId-physicalCard' }], deactivatedAt: null },
    });
    expect(paymentTransactionFn).not.toHaveBeenCalled();
  });
  it('should log and ignore accounts that has been already created', async () => {
    findManyUserFn.mockResolvedValueOnce([
      { id: 'mockUserId', email: '<EMAIL>', name: 'Test User' },
      { id: 'mockUser2Id', email: '<EMAIL>', name: 'Test User2' },
    ]);
    findManyAccountFn.mockResolvedValueOnce([
      { id: 'mockAccountId', referenceId: 'mockUserId-physicalCard', keys: { cardId: 12345 } },
    ]);
    const result = await operation.run({
      partnerId: 'mockPartnerId',
      accounts: [
        { cardId: 12345, type: 'physicalCard', email: '<EMAIL>' },
        { cardId: 56789, type: 'physicalCard', email: '<EMAIL>' },
      ] as Account[],
    });
    expect(result).toEqual({ status: 'success' });
    expect(findManyAccountFn).toHaveBeenCalledWith({
      where: {
        OR: [
          { referenceId: 'mockUserId-physicalCard' },
          { referenceId: 'mockUser2Id-physicalCard' },
        ],
        deactivatedAt: null,
      },
    });
    expect(logger.info).toHaveBeenCalledWith(
      { skippedAccounts: [{ cardId: 12345, email: '<EMAIL>', type: 'physicalCard' }] },
      'CreateUsioAccountsOperation Create: skipped these accounts ...',
    );
    expect(paymentTransactionFn).toHaveBeenCalled();
  });
  it('should ignore recipients that has been already created but call create accounts', async () => {
    findManyUserFn.mockResolvedValueOnce([
      { id: 'mockUserId', email: '<EMAIL>', name: 'Test User' },
    ]);
    findManyRecipientFn.mockResolvedValueOnce([
      {
        id: 'mockExistingRecipientId',
        referenceId: 'mockUserId',
      },
    ]);
    const result = await operation.run({
      partnerId: 'mockPartnerId',
      accounts: [{ cardId: 12345, type: 'physicalCard', email: '<EMAIL>' }] as Account[],
    });
    expect(result).toEqual({ status: 'success' });
    expect(createManyRecipientFn).not.toHaveBeenCalled();
    expect(createManyAccountFn).toHaveBeenCalledWith({
      data: [
        {
          referenceId: 'mockUserId-physicalCard',
          recipientId: 'mockExistingRecipientId',
          type: 'physicalCard',
          keys: { cardId: 12345 },
        },
      ],
    });
    expect(paymentTransactionFn).toHaveBeenCalled();
  });
  it('should create transaction for creating accounts/recipients', async () => {
    jest.spyOn(crypto, 'randomUUID').mockReturnValueOnce('mockRecipientId' as UUID);
    findManyUserFn.mockResolvedValueOnce([
      { id: 'mockUserId', email: '<EMAIL>', name: 'Test User' },
    ]);
    const result = await operation.run({
      partnerId: 'mockPartnerId',
      accounts: [{ cardId: 12345, type: 'physicalCard', email: '<EMAIL>' }] as Account[],
    });
    expect(result).toEqual({ status: 'success' });
    expect(createManyRecipientFn).toHaveBeenCalledWith({
      data: [
        {
          id: 'mockRecipientId',
          referenceId: 'mockUserId',
          keys: { firstName: 'Test', lastName: 'User' },
        },
      ],
    });
    expect(createManyAccountFn).toHaveBeenCalledWith({
      data: [
        {
          referenceId: 'mockUserId-physicalCard',
          recipientId: 'mockRecipientId',
          type: 'physicalCard',
          keys: { cardId: 12345 },
        },
      ],
    });
    expect(paymentTransactionFn).toHaveBeenCalled();
  });
});
