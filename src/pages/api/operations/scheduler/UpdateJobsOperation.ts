import type { PrismaClient as SchedulerClient } from '@prisma/clients/scheduler';

export default class UpdateJobsOperation {
  private schedulerClient: SchedulerClient;

  constructor(client: SchedulerClient) {
    this.schedulerClient = client;
  }

  public async run({ scheduledFor, ids }) {
    if (!scheduledFor || !ids?.length) return [];

    return await this.schedulerClient.jobs.updateMany({
      where: {
        id: {
          in: ids,
        },
      },
      data: { scheduledFor },
    });
  }
}
