import type { PrismaClients } from '@prisma/clients';
import type { Services } from 'pages/api/services';

import type { Operations } from 'types/operations';
import DeleteUserOperation from '../compliance/DeleteUserOperation';
import SaveEligibilityQuestionsOperation from './SaveEligibilityConfigQuestions';
import ScheduleRecurringPaymentsOperation from './ScheduleRecurringPaymentsOperation';
import UpdateCoursesOperation from './UpdateCoursesOperation';
import UpdatePaymentsOperation from './UpdatePaymentsOperation';

const build = (clients: PrismaClients, services: Services): Operations['platform'] => {
  const updateCourses = new UpdateCoursesOperation(clients.platform);
  const updatePayments = new UpdatePaymentsOperation(clients.platform);
  const scheduleRecurringPayments = new ScheduleRecurringPaymentsOperation(
    clients.platform,
    clients.payment,
    services.payments,
  );
  const saveEligibilityQuestions = new SaveEligibilityQuestionsOperation(clients.platform);

  return {
    updateCourses,
    updatePayments,
    scheduleRecurringPayments,
    saveEligibilityQuestions,
  };
};

export default build;
