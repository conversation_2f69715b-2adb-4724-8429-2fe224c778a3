import type { PrismaClient as PlatformClient, PrismaPromise } from '@prisma/clients/platform';

export default class SaveEligibilityQuestionsOperation {
  private platformClient: PlatformClient;

  constructor(client: PlatformClient) {
    this.platformClient = client;
  }

  public async run({ updates, deletes, creations, configId }) {
    // biome-ignore lint/suspicious/noExplicitAny: updateMany function
    const transactions: PrismaPromise<any>[] = [];

    for (const update of updates) {
      transactions.push(
        this.platformClient.eligibilityQuestions.update({
          where: { id: update.id },
          data: update.value,
        }),
      );
    }

    if (deletes.length) {
      transactions.push(
        this.platformClient.eligibilityConfigQuestions.deleteMany({
          where: {
            id: {
              in: deletes,
            },
          },
        }),
      );
    }

    await this.platformClient.$transaction(transactions);
    for (const creation of creations) {
      const newQuestion = await this.platformClient.eligibilityQuestions.create({
        data: { ...creation.value },
      });
      await this.platformClient.eligibilityConfigQuestions.create({
        data: {
          questionId: newQuestion.id,
          configId: configId,
        },
      });
    }
    return { success: true };
  }
}
