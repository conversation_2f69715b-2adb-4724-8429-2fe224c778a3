import type { PrismaClient as PlatformClient } from '@prisma/clients/platform';
import { logger } from '@utils/logger';

export default class UpdatePaymentsOperation {
  private platformClient: PlatformClient;

  constructor(client: PlatformClient) {
    this.platformClient = client;
  }

  public async run({ scheduledFor, ids }) {
    if (!scheduledFor || !ids?.length) return [];

    logger.info({ ids, scheduledFor }, 'Updating payments schedule time ...');
    return await this.platformClient.payments.updateMany({
      where: {
        id: {
          in: ids,
        },
      },
      data: { scheduledFor },
    });
  }
}
