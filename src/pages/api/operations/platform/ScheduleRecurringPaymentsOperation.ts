import type { PrismaClient as PaymentClient } from '@prisma/clients/payment';
import {
  type ApplicantTypes as ApplicantType,
  Bulkoperationstatus as BulkOperationStatus,
  type Cases as Case,
  Casestatus as CaseStatus,
  Paymentmethod as PaymentMethod,
  Paymentstatus as PaymentStatus,
  type PrismaClient as PlatformClient,
  type Users as User,
} from '@prisma/clients/platform';
import dayjs from '@utils/dayJsConfig';
import { logger } from '@utils/logger';
import type PaymentService from 'pages/api/services/PaymentService';
import { queryAndBatchAction } from 'pages/api/utils/batch';
import {
  createPayments,
  mapRecipientToPlatformEntities,
  mapUserEntityToPayee,
} from 'pages/api/utils/mapping';
import {
  PaymentServiceResponseStatus,
  type PopulatedFund,
  type PopulatedPayment,
} from 'types/payment';
import type {
  Recipient,
  SchedulePaymentsRequest,
  SchedulePaymentsResponse,
} from 'types/schedulePayments';

export default class ScheduleRecurringPaymentsOperation {
  private platformClient: PlatformClient;
  private paymentClient: PaymentClient;
  private paymentService: PaymentService;
  private activeWindow: number;
  private batchSize = 1000;

  constructor(
    platformClient: PlatformClient,
    paymentClient: PaymentClient,
    paymentService: PaymentService,
  ) {
    this.platformClient = platformClient;
    this.paymentClient = paymentClient;
    this.paymentService = paymentService;
    this.activeWindow = Number(process.env.PUBLIC_SCHEDULER_ACTIVE_WINDOW ?? '120');
  }

  private getUsersQueryCondition(recipients: Recipient[]) {
    return recipients.map((row: Recipient) => ({
      email: row.email.toLowerCase(),
      legacyId: row.salesForceId,
    }));
  }

  private hasPaymentAccount(paymentMethod: PaymentMethod) {
    return paymentMethod === PaymentMethod.ach || paymentMethod === PaymentMethod.physicalCard;
  }

  private validateRecipient(recipient: Recipient): string | undefined {
    if (!['salesForceId', 'email', 'firstName', 'lastName'].every((key) => recipient[key]))
      return 'basic information (salesForceId/email/name) is required';
    if (!recipient.paymentMethod || !recipient.paymentAmount)
      return 'payment method/amount is required';
    if (
      !['programAddress', 'programCity', 'programState', 'programZipCode'].every(
        (key) => recipient[key],
      )
    )
      return 'program address is required';

    if (
      recipient.paymentMethod === PaymentMethod.ach &&
      !(recipient.accountNumber && recipient.routingNumber && recipient.accountType)
    )
      return 'accountNumber/routingNumber/accountType is required for ach transaction';
  }

  private async validateAndFindProgram(programId: string, withType = true) {
    let applicantType: ApplicantType | undefined = undefined;
    const program = await this.platformClient.programs.findFirstOrThrow({
      where: { id: programId },
      include: {
        partner: true,
        programFunds: { include: { fund: true } },
      },
    });

    if (!program?.programFunds?.length || !program?.programFunds?.[0]?.fund)
      throw new Error('fund not found');
    if (!program?.partner) throw new Error('partner not found.');

    if (withType) {
      applicantType = await this.platformClient.applicantTypes.findFirstOrThrow({
        where: { partnerId: null, name: 'Applicant' },
      });
      if (!applicantType) throw new Error('no applicant type found');
    }

    return {
      program,
      applicantType,
      fund: { ...program.programFunds[0].fund, partner: program.partner },
      partner: program.partner,
    };
  }

  private validateRecipients(recipients: Recipient[]): SchedulePaymentsResponse['errors'] {
    const recipientMap = new Map();
    const errors: Array<{ id: string; message: string }> = [];

    for (const record of recipients) {
      const { salesForceId, email } = record;
      const messages: Array<string> = [];
      // Check for duplicate salesForceId
      if (recipientMap.has(salesForceId)) {
        messages.push('duplicate salesForceId');
      } else recipientMap.set(salesForceId, true);

      // Check for duplicate emails
      if (recipientMap.has(email)) {
        messages.push('duplicate email');
      } else recipientMap.set(email, true);

      // Validate recipient
      const error = this.validateRecipient(record);
      if (error) messages.push(error);

      if (messages.length) errors.push({ id: salesForceId, message: messages.join(',') });
    }

    if (errors.length) {
      logger.warn({ errors }, `${this.constructor.name} Load:: data validation error >`);
      return errors as SchedulePaymentsResponse['errors'];
    }
    return [];
  }

  private async createPaymentAccounts(fund: PopulatedFund, recipients: Recipient[]) {
    const recipientsWithAccount = recipients.filter(({ paymentMethod }) =>
      this.hasPaymentAccount(paymentMethod),
    );
    if (!recipientsWithAccount.length) return;
    logger.info(
      `${this.constructor.name} load: upserting payment accounts for (${recipientsWithAccount.length}) recipients...`,
    );

    const queryCondition = this.getUsersQueryCondition(recipientsWithAccount);

    const userCount = await this.platformClient.users.count({
      where: { partnerId: fund.partner?.id, deactivatedAt: null, OR: queryCondition },
    });

    if (!userCount) throw new Error('users not found');

    await queryAndBatchAction<User, { message: string }>({
      batchSize: this.batchSize,
      totalCount: userCount,
      query: async (skip, take) =>
        this.platformClient.users.findMany({
          where: { partnerId: fund.partner?.id, deactivatedAt: null, OR: queryCondition },
          skip,
          take,
        }),
      action: async (users: User[]) => {
        logger.info(
          `${this.constructor.name} load: upserting payment accounts (payees: ${users.length}) ...`,
        );

        const promises = users.map((user) => {
          const recipient = recipients.find(
            ({ salesForceId }) => user.legacyId === salesForceId,
          ) as Recipient;
          const payee = mapUserEntityToPayee(user, recipient);
          return this.paymentService.upsertAccount(payee, fund, recipient.paymentMethod);
        });

        const responses = await Promise.all(promises);
        if (responses.some((res) => res.status === PaymentServiceResponseStatus.Failed)) {
          logger.error(
            { responses },
            `${this.constructor.name} load: upserting payment accounts encountered errors =>`,
          );
          throw new Error('upserting payment accounts encountered an error');
        }
        return { message: 'complete' };
      },
    });
  }

  private async import({ programId, recipients, withAccountCreation = false }) {
    const errors = this.validateRecipients(recipients);
    if (errors?.length) return { errors, status: 'error' };

    const { program, applicantType, fund, partner } = await this.validateAndFindProgram(programId);
    const existingUsers = await this.platformClient.users.findMany({
      where: {
        partnerId: partner.id,
        deactivatedAt: null,
        OR: recipients.map((row: Recipient) => ({ email: row.email, legacyId: row.salesForceId })),
      },
    });
    const usersByLegacyId = new Map<string, User>();
    for (const user of existingUsers) usersByLegacyId.set(user.legacyId as string, user);

    const newRecipients = recipients.filter(
      ({ salesForceId }) => !usersByLegacyId.get(salesForceId),
    );

    if (newRecipients.length) {
      logger.info(
        `${this.constructor.name} Load: going to load ${newRecipients.length} recipients`,
      );
      await queryAndBatchAction<Recipient, { message: string }>({
        batchSize: this.batchSize,
        totalCount: newRecipients.length,
        query: async (skip, take) => newRecipients.slice(skip, skip + take),
        action: async (records: Recipient[]) => {
          logger.info(
            `${this.constructor.name} load: action starts (recipients: ${records.length}) ...`,
          );
          const mappedUsers = records.map((recipient) =>
            mapRecipientToPlatformEntities({
              program,
              applicantType: applicantType as ApplicantType,
              recipient,
              partner,
            }),
          );
          await this.platformClient.$transaction([
            this.platformClient.users.createMany({ data: mappedUsers.map(({ user }) => user) }),
            this.platformClient.applicantProfiles.createMany({
              data: mappedUsers.map(({ applicantProfile }) => applicantProfile),
            }),
            this.platformClient.cases.createMany({
              data: mappedUsers.map(({ case: case_ }) => case_),
            }),
            this.platformClient.applications.createMany({
              data: mappedUsers.map(({ application }) => application),
            }),
            this.platformClient.applicationVersions.createMany({
              data: mappedUsers.map(({ applicationVersion }) => applicationVersion),
            }),
            this.platformClient.applicationAnswers.createMany({
              data: mappedUsers.flatMap(({ applicationAnswers }) => applicationAnswers),
            }),
          ]);

          return { message: 'complete' };
        },
      });
    }

    if (withAccountCreation) await this.createPaymentAccounts(fund, recipients);
    return { status: 'success' };
  }

  private async resetCorePayment(payment: PopulatedPayment) {
    try {
      logger.info(`Resetting payment -> ${payment.id}`);
      await this.platformClient.$transaction([
        this.platformClient.cases.update({
          where: { id: payment.fulfillment.caseId },
          data: { status: CaseStatus.InReview },
        }),
        this.platformClient.paymentPatterns.deleteMany({
          where: { fulfillmentId: payment.fulfillment.id },
        }),
        this.platformClient.payments.delete({ where: { id: payment.id } }),
        this.platformClient.fulfillments.delete({ where: { id: payment.fulfillment.id } }),
      ]);
    } catch (error) {
      logger.error({ error }, `resetting payment ${payment.id} encounter an error =>`);
    }
  }

  private async scheduleSinglePayment({ payment, recipient, bulkOperation }) {
    try {
      logger.info(`${this.constructor.name} schedule: action starts for ${payment.id}`);

      const response = await this.paymentService.schedulePayment({
        ...payment,
        payee: mapUserEntityToPayee(payment.user, recipient),
      });
      logger.info(
        { response },
        `${this.constructor.name} schedule: payment service response for ${payment.id}`,
      );
      if (response.status === PaymentServiceResponseStatus.Failed)
        throw new Error('Scheduling payment failed');

      const scheduledPayments = response.payload.schedule.map(({ id, scheduledFor }) => ({
        id,
        fulfillmentId: payment.fulfillment.id,
        amount: payment.fulfillment.paymentPattern?.amount,
        status: PaymentStatus.authorized,
        method: payment.method,
        payeeId: payment.payeeId,
        payeeType: payment.payeeType,
        scheduledFor,
      }));
      await this.platformClient.payments.createMany({ data: scheduledPayments });
      await this.platformClient.payments.update({
        where: { id: payment.id },
        data: { deactivatedAt: dayjs().toISOString() },
      });
      await this.platformClient.workflowEvents.create({
        data: {
          entityId: payment.fulfillment.id,
          entityType: 'fulfillment',
          bulkOperationId: bulkOperation.id,
          adminId: bulkOperation.adminId,
          details: 'scheduled one-time payment',
          action: 'PaymentUpdate', // should we add new action??
        },
      });
      return { message: 'complete' };
    } catch (error) {
      logger.error({ error }, `${this.constructor.name} schedule: unexpected error ${payment.id}`);
      await this.resetCorePayment(payment);
      return { message: 'error' };
    }
  }

  private async schedulePayments({ recipients, fund, partner, bulkOperation }) {
    logger.info(
      `${this.constructor.name} schedule: action starts (recipients: ${recipients.length}) ...`,
    );
    // query data
    const users = await this.platformClient.users.findMany({
      where: {
        partnerId: partner.id,
        deactivatedAt: null,
        OR: this.getUsersQueryCondition(recipients),
      },
    });
    const applications = await this.platformClient.applications.findMany({
      where: { deactivatedAt: null, submitterId: { in: users.map((user) => user.id) } },
      include: { case: true },
    });
    // map data
    const recipientBySaleForceId = new Map<string, Recipient>();
    for (const recipient of recipients)
      recipientBySaleForceId.set(recipient.salesForceId as string, recipient);

    const caseBySubmitterId = new Map<string, Case>();
    for (const app of applications) caseBySubmitterId.set(app.submitterId as string, app.case);

    const mappedData = users.map((user) => {
      const { paymentMethod, paymentAmount, paymentDate } = recipientBySaleForceId.get(
        user.legacyId as string,
      ) as Recipient;
      return createPayments({
        userId: user.id,
        caseId: caseBySubmitterId.get(user.id)?.id,
        fundId: fund.id,
        paymentMethod,
        paymentAmount,
        paymentDate,
      });
    });

    // scheduling data
    await this.platformClient.$transaction([
      this.platformClient.cases.updateMany({
        where: { id: { in: mappedData.map(({ fulfillment }) => fulfillment.caseId) } },
        data: { status: CaseStatus.Approved },
      }),
      this.platformClient.fulfillments.createMany({
        data: mappedData.map(({ fulfillment }) => fulfillment),
      }),
      this.platformClient.paymentPatterns.createMany({
        data: mappedData.map(({ paymentPattern }) => paymentPattern),
      }),
      this.platformClient.payments.createMany({
        data: mappedData.map(({ payment }) => payment),
      }),
    ]);

    const payments = await this.platformClient.payments.findMany({
      where: { id: { in: mappedData.map(({ payment }) => payment.id) } },
      include: {
        fulfillment: {
          include: { paymentPattern: true, fund: { include: { partner: true } } },
        },
      },
    });
    const payeeUsers = await this.platformClient.users.findMany({
      where: { id: { in: payments.map((payment) => payment.payeeId) } },
    });

    const errors: string[] = [];
    for (const payment of payments) {
      const response = await this.scheduleSinglePayment({
        payment,
        recipient: recipientBySaleForceId.get(
          payeeUsers.find((payee) => payee.id === payment.payeeId)?.legacyId as string,
        ),
        bulkOperation,
      });
      if (response.message === 'error') errors.push(payment.id);
    }

    if (errors.length) throw new Error('Scheduling payments encountered an error, check the logs');

    return { message: 'complete' };
  }

  private async skipScheduledPayments(recipients: Recipient[]) {
    const payments = await this.platformClient.payments.findMany({
      where: {
        deactivatedAt: null,
        OR: recipients.map((rec) => ({
          user: { legacyId: rec.salesForceId },
          method: rec.paymentMethod,
          amount: Number(rec.paymentAmount),
          status: PaymentStatus.authorized,
          scheduledFor: {
            gte: dayjs(rec.paymentDate).startOf('day').toISOString(),
            lt: dayjs(rec.paymentDate).endOf('day').toISOString(),
          },
        })),
      },
    });

    const users = await this.platformClient.users.findMany({
      where: { id: { in: payments.map((payment) => payment.payeeId) } },
    });
    const skippedIds = users.map((user) => user.legacyId);
    const updatedRecipients = recipients.filter((rec) => !skippedIds.includes(rec.salesForceId));
    if (skippedIds.length)
      logger.warn({ skippedIds }, `${this.constructor.name} schedule: skipping payments ->`);

    return updatedRecipients;
  }

  private async validateAndFindSchedules(
    partner,
    recipients: Recipient[],
  ): Promise<{ recipients?: Array<Recipient>; errors?: SchedulePaymentsResponse['errors'] }> {
    const invalidRecipients = recipients.filter(
      (rec) => !dayjs(rec.paymentDate).isAfter(dayjs().add(this.activeWindow, 'minute')),
    );
    if (invalidRecipients.length)
      return {
        errors: invalidRecipients.map((rec) => ({
          id: rec.salesForceId,
          message: 'Payment date should be after Window',
        })),
      };

    const existingUsers = await this.platformClient.users.findMany({
      where: {
        partnerId: partner.id,
        deactivatedAt: null,
        OR: this.getUsersQueryCondition(recipients),
      },
    });

    const recipientsRelations = recipients.map(({ salesForceId, paymentMethod }) => {
      const lookup = existingUsers?.find(({ legacyId }) => legacyId === salesForceId);
      return {
        salesForceId,
        userId: lookup?.id,
        accountReferenceId: this.hasPaymentAccount(paymentMethod)
          ? `${lookup?.id}-${paymentMethod}`
          : undefined,
      };
    });

    if (existingUsers?.length !== recipients.length)
      return {
        errors: recipientsRelations
          .filter((rec) => !rec.userId)
          .map((rec) => ({
            id: rec.salesForceId,
            message: 'no core user relation found',
          })),
      };

    const recipientsWithAccounts = recipientsRelations.filter((rec) => !!rec.accountReferenceId);
    if (recipientsWithAccounts.length) {
      const paymentAccounts = await this.paymentClient.accounts.findMany({
        where: {
          deactivatedAt: null,
          OR: recipientsWithAccounts.map(({ accountReferenceId }) => ({
            referenceId: accountReferenceId,
          })),
        },
      });
      if (paymentAccounts?.length !== recipientsWithAccounts.length)
        return {
          errors: recipientsWithAccounts
            .filter(
              (rec) => !paymentAccounts?.find((acc) => acc.referenceId === rec.accountReferenceId),
            )
            .map((rec) => ({
              id: rec.salesForceId,
              message: 'no payment account relation found',
            })),
        };
    }

    const recipientsToSchedule = await this.skipScheduledPayments(recipients);
    return { recipients: recipientsToSchedule };
  }

  private async schedule({
    adminId,
    programId,
    recipients,
  }: Omit<SchedulePaymentsRequest, 'action'>) {
    const { fund, partner } = await this.validateAndFindProgram(programId, false);
    const { recipients: validatedRecipients, errors } = await this.validateAndFindSchedules(
      partner,
      recipients,
    );
    if (errors?.length) return { errors, status: 'error' };
    if (!validatedRecipients?.length) return { status: 'success' };

    logger.info(
      `${this.constructor.name} schedule: time to schedule ${validatedRecipients.length} payments`,
    );
    const bulkOperation = await this.platformClient.bulkOperations.create({
      data: {
        operation: 'ScheduleRecurringPayments',
        status: BulkOperationStatus.InProgress,
        payload: { numberOfPayments: recipients.length },
        adminId,
      },
    });
    try {
      await queryAndBatchAction<Recipient, { message: string }>({
        batchSize: this.batchSize,
        totalCount: validatedRecipients.length,
        query: async (skip, take) => validatedRecipients.slice(skip, skip + take),
        action: (bulk) => this.schedulePayments({ fund, partner, recipients: bulk, bulkOperation }),
      });

      await this.platformClient.bulkOperations.update({
        where: { id: bulkOperation.id },
        data: { status: BulkOperationStatus.Completed, completedAt: new Date() },
      });
      return { status: 'success' };
    } catch (err) {
      await this.platformClient.bulkOperations.update({
        where: { id: bulkOperation.id },
        data: { status: BulkOperationStatus.Failed },
      });
      throw err;
    }
  }

  public async run({
    action,
    ...request
  }: SchedulePaymentsRequest): Promise<SchedulePaymentsResponse> {
    switch (action) {
      case 'import':
        return this.import(request);
      case 'schedule':
        return this.schedule(request);

      default:
        throw new Error('action is not supported');
    }
  }
}
