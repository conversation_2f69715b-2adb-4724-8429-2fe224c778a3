import {
  type ApplicationAnswers as ApplicationAnswerEntity,
  type JsonObject,
  type PrismaClient as PlatformClient,
  Prisma,
  type PrismaPromise,
} from '@prisma/clients/platform';
import { CourseKeys } from 'types/ApplicationAnswer';
import { logger } from 'utils/logger';

export default class UpdateCoursesOperation {
  private platformClient: PlatformClient;

  constructor(client: PlatformClient) {
    this.platformClient = client;
  }

  private getVersionIds(answers: Partial<ApplicationAnswerEntity>[]) {
    return [...new Set(answers.map((answer) => answer.versionId))] as string[];
  }

  private getCredit(duration: string): string {
    const [hours, minutes] = duration.split(':').map(Number);
    const credit = (hours + minutes / 60).toFixed(2);
    if (credit === 'NaN' || !credit) throw new Error('duration is invalid');
    return `${Number.parseFloat(credit)}`;
  }

  private async recalculateTotalCreditHours({ programId, courses, keys }) {
    const courseNames = courses.map(({ course }) => course);
    const affectedAnswers = await this.platformClient.applicationAnswers.findMany({
      select: { versionId: true },
      where: {
        value: { in: courseNames },
        applicationVersion: { application: { case: { programId } } },
        deactivatedAt: null,
        OR: keys.map((key: string) => ({ key: { contains: key, mode: 'insensitive' } })),
      },
    });
    if (!affectedAnswers.length) return [];

    const versionIds = this.getVersionIds(affectedAnswers);

    return keys.map((key: string) => {
      const courseKey = `${key}.courses.%.creditHours`;
      const totalCreditKey = `${key}.totalCreditHours`;
      return this.platformClient.$queryRaw(Prisma.sql`
        WITH calculated_credit_hours
        AS ( SELECT version_id,
            SUM(
                CASE
                    WHEN deactivated_at IS NULL THEN CAST(value as FLOAT)
                    ELSE 0
                    END
                ) AS total_credits
        FROM application_answers
        WHERE version_id::TEXT IN (${Prisma.join(versionIds)}) 
        AND key LIKE ${courseKey}
        GROUP BY version_id)
        
        UPDATE application_answers AS aa
        SET value = ctc.total_credits
        FROM calculated_credit_hours AS ctc
        WHERE aa.version_id = ctc.version_id
          AND aa.key LIKE ${totalCreditKey};`);
    });
  }

  private async updateCourseAnswers({ programId, courses, keys }) {
    const answersCount = await this.platformClient.applicationAnswers.count({
      where: {
        value: { in: courses.map(({ course }) => course) },
        applicationVersion: { application: { case: { programId: programId } } },
        deactivatedAt: null,
        OR: keys.map((key: string) => ({ key: { contains: key, mode: 'insensitive' } })),
      },
    });
    if (!answersCount) return [];

    logger.info(`Renaming: Number of application answers affected: ${answersCount}`);

    return courses.map(({ course, name }) =>
      this.platformClient.applicationAnswers.updateMany({
        where: {
          deactivatedAt: null,
          value: course,
          applicationVersion: { application: { case: { programId } } },
          OR: keys.map((key: string) => ({ key: { contains: key, mode: 'insensitive' } })),
        },
        data: { value: name },
      }),
    );
  }

  private async removeCourseAnswers({ programId, courses, keys }) {
    const courseNames = courses.map(({ course }) => course);
    const affectedAnswers = await this.platformClient.applicationAnswers.findMany({
      where: {
        value: { in: courseNames },
        applicationVersion: { application: { case: { programId } } },
        deactivatedAt: null,
        OR: keys.map((key: string) => ({ key: { contains: key, mode: 'insensitive' } })),
      },
    });
    if (!affectedAnswers) return [];

    const ids = this.getVersionIds(affectedAnswers);
    logger.info(`DeactivatingAnswers: Number of application versions affected: ${ids.length}`);

    return [
      this.platformClient.applicationAnswers.updateMany({
        where: {
          OR: [
            { id: { in: affectedAnswers.map(({ id }) => id) } },
            ...affectedAnswers.map(({ key, versionId }) => ({
              key: key.replace('name', 'creditHours'),
              versionId,
            })),
            ...affectedAnswers.map(({ key, versionId }) => ({
              key: { contains: key.replace('name', 'courseProof') },
              versionId,
            })),
          ],
        },
        data: { deactivatedAt: new Date() },
      }),
    ];
  }

  private async updateCreditAnswers({ programId, courses, keys }) {
    const courseNames = courses.map(({ course }) => course);
    const affectedAnswers = await this.platformClient.applicationAnswers.findMany({
      where: {
        value: { in: courseNames },
        applicationVersion: { application: { case: { programId } } },
        deactivatedAt: null,
        OR: keys.map((key: string) => ({ key: { contains: key, mode: 'insensitive' } })),
      },
    });
    if (!affectedAnswers?.length) return [];

    const ids = this.getVersionIds(affectedAnswers);
    logger.info(`ChangingCreditHours: Number of application versions affected: ${ids.length}`);

    return courses.map(({ course, credit }) => {
      const answers = affectedAnswers.filter((answer) => answer.value === course);
      return this.platformClient.applicationAnswers.updateMany({
        where: {
          deactivatedAt: null,
          OR: answers.map(({ versionId, key }) => ({
            versionId,
            key: key.replace('name', 'creditHours'),
          })),
        },
        data: { value: credit },
      });
    });
  }

  private async validateAndReturn({
    courses,
    programId,
    key,
  }): Promise<{ answers: Partial<ApplicationAnswerEntity>[]; keys: string[] }> {
    const program = await this.platformClient.programs.findUniqueOrThrow({
      where: { id: programId },
    });
    const applicationConfiguration = ((program.config as JsonObject)?.applicationConfiguration ??
      '') as string;

    if (!Object.values(CourseKeys).includes(key)) throw new Error('no key found.');

    const answers = await this.platformClient.applicationAnswers.findMany({
      select: { versionId: true },
      where: {
        value: { in: courses.map(({ course }) => course) },
        applicationVersion: { application: { case: { programId } } },
        deactivatedAt: null,
        key: { contains: key, mode: 'insensitive' },
      },
    });
    return { answers, keys: [key] };
  }

  public async run({ courses, programId, key }): Promise<string[]> {
    const { answers, keys } = await this.validateAndReturn({ courses, programId, key });
    if (!answers.length) return [];

    const versionIds = this.getVersionIds(answers);
    logger.info(`UpdatingCourses: ${versionIds.length} application versions affected ...`);

    // biome-ignore lint/suspicious/noExplicitAny: updateMany function
    const transactions: PrismaPromise<any>[] = [];

    const coursesToRename = courses?.filter(({ action, name }) => action === 'Update' && !!name);
    const coursesToCredit = courses
      ?.filter(({ action, duration }) => action === 'Update' && !!duration)
      .map((course) => ({ ...course, credit: this.getCredit(course.duration) }));
    const coursesToRemove = courses?.filter(({ action }) => action === 'Remove');

    if (coursesToRemove.length) {
      const promises = await this.removeCourseAnswers({
        programId,
        keys,
        courses: coursesToRemove,
      });
      transactions.push(...promises);
    }

    if (coursesToCredit?.length) {
      const promises = await this.updateCreditAnswers({
        programId,
        keys,
        courses: coursesToCredit,
      });
      transactions.push(...promises);
    }

    // recalculate
    if (coursesToCredit?.length || coursesToRemove?.length) {
      const promises = await this.recalculateTotalCreditHours({
        programId,
        keys,
        courses: [...(coursesToCredit ?? []), ...(coursesToRemove ?? [])],
      });
      transactions.push(...promises);
    }

    if (coursesToRename?.length) {
      const promises = await this.updateCourseAnswers({
        programId,
        courses: coursesToRename,
        keys,
      });
      transactions.push(...promises);
    }

    await this.platformClient.$transaction(transactions);
    logger.info(`UpdatingCourses: process for programId: ${programId} finished...`);

    return versionIds;
  }
}
