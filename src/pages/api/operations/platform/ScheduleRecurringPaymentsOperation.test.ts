// @ts-nocheck
import crypto, { UUID } from 'node:crypto';
import { beforeEach, describe, expect, it, jest } from '@jest/globals';
import type { PrismaClient as PaymentClient } from '@prisma/clients/payment';
import type { PrismaClient as PlatformClient } from '@prisma/clients/platform';
import { makeFakeRecipient } from '@test/mocks';
import { logger } from '@utils/logger';
import dayjs from 'dayjs';
import type PaymentService from 'pages/api/services/PaymentService';
import type { SchedulePaymentsRequest } from 'types/schedulePayments';
import Operation from './ScheduleRecurringPaymentsOperation';

describe('ScheduleRecurringPaymentsOperation', () => {
  let operation: Operation;

  let accountsFindManyFn: jest.Mock;
  let answersCreateManyFn: jest.Mock;
  let applicationsCreateManyFn: jest.Mock;
  let applicationsFindManyFn: jest.Mock;
  let applicantTypesFindFirstFn: jest.Mock;
  let casesCreateManyFn: jest.Mock;
  let casesUpdateFn: jest.Mock;
  let casesUpdateManyFn: jest.Mock;
  let fulfillmentsCreateManyFn: jest.Mock;
  let fulfillmentsDeleteFn: jest.Mock;
  let operationsUpdateFn: jest.Mock;
  let patternsCreateManyFn: jest.Mock;
  let patternsDeleteFn: jest.Mock;
  let paymentsCreateManyFn: jest.Mock;
  let paymentsDeleteFn: jest.Mock;
  let paymentsFindManyFn: jest.Mock;
  let paymentsUpdateFn: jest.Mock;
  let profilesCreateManyFn: jest.Mock;
  let programsFindFirstFn: jest.Mock;
  let schedulePaymentFn: jest.Mock;
  let transactionFn: jest.Mock;
  let upsertPaymentAccountFn: jest.Mock;
  let usersCreateManyFn: jest.Mock;
  let usersFindManyFn: jest.Mock;
  let versionsCreateManyFn: jest.Mock;
  let workflowEventsCreateFn: jest.Mock;
  beforeEach(() => {
    accountsFindManyFn = jest.fn();
    answersCreateManyFn = jest.fn();
    applicantTypesFindFirstFn = jest.fn().mockResolvedValue({ id: 'mockApplicantTypeId' });
    applicationsCreateManyFn = jest.fn();
    applicationsFindManyFn = jest.fn();
    casesCreateManyFn = jest.fn();
    casesUpdateFn = jest.fn();
    casesUpdateManyFn = jest.fn();
    fulfillmentsCreateManyFn = jest.fn();
    fulfillmentsDeleteFn = jest.fn();
    operationsUpdateFn = jest.fn();
    patternsCreateManyFn = jest.fn();
    patternsDeleteFn = jest.fn();
    paymentsCreateManyFn = jest.fn();
    paymentsDeleteFn = jest.fn();
    paymentsFindManyFn = jest.fn();
    paymentsUpdateFn = jest.fn();
    profilesCreateManyFn = jest.fn();
    programsFindFirstFn = jest.fn().mockResolvedValue({
      id: 'mockProgramId',
      partner: { id: 'mockPartnerId', name: 'Test Partner' },
      programFunds: [{ fund: { id: 'mockFundId', name: 'Program Fund' } }],
    });
    schedulePaymentFn = jest.fn().mockResolvedValue({
      status: 'success',
      payload: {
        schedule: [{ id: 'mockScheduledPaymentId', scheduledFor: 'scheduledDate' }],
      },
    });
    transactionFn = jest.fn();
    upsertPaymentAccountFn = jest.fn().mockResolvedValue({ status: 'success' });
    usersCreateManyFn = jest.fn();
    usersFindManyFn = jest.fn();
    versionsCreateManyFn = jest.fn();
    workflowEventsCreateFn = jest.fn();

    operation = new Operation(
      {
        $transaction: transactionFn,
        applicantProfiles: {
          createMany: profilesCreateManyFn,
        },
        applicantTypes: {
          findFirstOrThrow: applicantTypesFindFirstFn,
        },
        applicationAnswers: {
          createMany: answersCreateManyFn,
        },
        applicationVersions: {
          createMany: versionsCreateManyFn,
        },
        applications: {
          createMany: applicationsCreateManyFn,
          findMany: applicationsFindManyFn,
        },
        cases: {
          createMany: casesCreateManyFn,
          update: casesUpdateFn,
          updateMany: casesUpdateManyFn,
        },
        bulkOperations: {
          create: jest.fn().mockResolvedValueOnce({
            id: 'mockBulkOperationId',
            adminId: 'mockAdminId',
          }),
          update: operationsUpdateFn,
        },
        fulfillments: {
          createMany: fulfillmentsCreateManyFn,
          delete: fulfillmentsDeleteFn,
        },
        paymentPatterns: {
          createMany: patternsCreateManyFn,
          delete: patternsDeleteFn,
        },
        payments: {
          createMany: paymentsCreateManyFn,
          delete: paymentsDeleteFn,
          findMany: paymentsFindManyFn,
          update: paymentsUpdateFn,
        },
        programs: {
          findFirstOrThrow: programsFindFirstFn,
        },
        users: {
          count: jest.fn().mockResolvedValueOnce(1),
          createMany: usersCreateManyFn,
          findMany: usersFindManyFn,
        },
        workflowEvents: {
          create: workflowEventsCreateFn,
        },
      } as unknown as PlatformClient,
      {
        accounts: {
          findMany: accountsFindManyFn,
        },
      } as unknown as PaymentClient,
      {
        schedulePayment: schedulePaymentFn,
        upsertAccount: upsertPaymentAccountFn,
      } as unknown as PaymentService,
    );
  });
  describe('import', () => {
    describe('validation', () => {
      it('should return an error status if there is duplicate records based on salesForceId or email', async () => {
        const result = await operation.run({
          action: 'import',
          programId: 'mockProgramId',
          recipients: [
            makeFakeRecipient({ salesForceId: 'sameId' }),
            makeFakeRecipient({ salesForceId: 'sameId', email: '<EMAIL>' }),
            makeFakeRecipient({ salesForceId: 'differentId', email: '<EMAIL>' }),
          ],
        } as unknown as SchedulePaymentsRequest);
        expect(result).toEqual({
          errors: [
            { id: 'sameId', message: 'duplicate salesForceId' },
            { id: 'differentId', message: 'duplicate email' },
          ],
          status: 'error',
        });
      });
      it('should return an error status if salesForceId/email/name does not exist in some recipients', async () => {
        const result = await operation.run({
          action: 'import',
          programId: 'mockProgramId',
          recipients: [
            makeFakeRecipient({ firstName: undefined, salesForceId: 'mockSalesForceId' }),
          ],
        } as unknown as SchedulePaymentsRequest);
        expect(result).toEqual({
          errors: [
            {
              id: 'mockSalesForceId',
              message: 'basic information (salesForceId/email/name) is required',
            },
          ],
          status: 'error',
        });
      });
      it('should return an error status if payment method/amount does not exist in some recipients', async () => {
        const result = await operation.run({
          action: 'import',
          programId: 'mockProgramId',
          recipients: [
            makeFakeRecipient(),
            makeFakeRecipient({ salesForceId: 'mockSalesForceId2', paymentMethod: undefined }),
          ],
        } as unknown as SchedulePaymentsRequest);
        expect(result).toEqual({
          errors: [
            {
              id: 'mockSalesForceId2',
              message: 'payment method/amount is required',
            },
          ],
          status: 'error',
        });
      });
      it('should return an error status if required field for ach transaction does not exist in some recipients', async () => {
        const result = await operation.run({
          action: 'import',
          programId: 'mockProgramId',
          recipients: [
            makeFakeRecipient({
              salesForceId: 'mockSalesForceId',
              accountNumber: '',
              routingNumber: '',
            }),
            makeFakeRecipient(),
          ],
        } as unknown as SchedulePaymentsRequest);
        expect(result).toEqual({
          errors: [
            {
              id: 'mockSalesForceId',
              message: 'accountNumber/routingNumber/accountType is required for ach transaction',
            },
          ],
          status: 'error',
        });
      });
      it('should return an error status if address does not exist in some recipients', async () => {
        const result = await operation.run({
          action: 'import',
          programId: 'mockProgramId',
          recipients: [makeFakeRecipient({ salesForceId: 'mockSalesForceId', programAddress: '' })],
        } as unknown as SchedulePaymentsRequest);
        expect(result).toEqual({
          errors: [
            {
              id: 'mockSalesForceId',
              message: 'program address is required',
            },
          ],
          status: 'error',
        });
      });
      it('should throw an error if fund not found', async () => {
        programsFindFirstFn.mockResolvedValueOnce({
          id: 'mockProgramId',
          partner: { id: 'mockPartnerId', name: 'Test Partner' },
          programFunds: [],
        });
        expect(
          operation.run({
            action: 'import',
            programId: 'mockProgramId',
            recipients: [makeFakeRecipient()],
          } as unknown as SchedulePaymentsRequest),
        ).rejects.toThrow('fund not found');
      });
      it('should throw an error if partner not found', async () => {
        programsFindFirstFn.mockResolvedValueOnce({
          id: 'mockProgramId',
          partner: undefined,
          programFunds: [{ fund: { id: 'mockFundId' } }],
        });
        expect(
          operation.run({
            action: 'import',
            programId: 'mockProgramId',
            recipients: [makeFakeRecipient()],
          } as unknown as SchedulePaymentsRequest),
        ).rejects.toThrow('partner not found');
      });
      it('should throw an error if applicant type not found', async () => {
        applicantTypesFindFirstFn.mockResolvedValueOnce(undefined);
        expect(
          operation.run({
            action: 'import',
            programId: 'mockProgramId',
            recipients: [makeFakeRecipient()],
          } as unknown as SchedulePaymentsRequest),
        ).rejects.toThrow('no applicant type found');
      });
    });
    it('should create transaction for creating core user/application', async () => {
      usersFindManyFn.mockResolvedValueOnce([]).mockResolvedValueOnce([
        {
          id: 'mockUserId',
          legacyId: 'mockSalesForceId',
          name: 'Test User',
          email: '<EMAIL>',
        },
      ]);
      const fakeRecipient = makeFakeRecipient({ salesForceId: 'mockSalesForceId' });
      const fakeName = `${fakeRecipient.firstName} ${fakeRecipient.lastName}`;
      jest
        .spyOn(crypto, 'randomUUID')
        .mockReturnValueOnce('mockUserId' as UUID)
        .mockReturnValueOnce('mockCaseId' as UUID)
        .mockReturnValueOnce('mockApplicationId' as UUID)
        .mockReturnValueOnce('mockVersionId' as UUID);

      // RUN
      const result = await operation.run({
        action: 'import',
        programId: 'mockProgramId',
        recipients: [fakeRecipient],
      } as unknown as SchedulePaymentsRequest);

      // EXPECT
      expect(result).toEqual({
        status: 'success',
      });
      expect(usersCreateManyFn).toHaveBeenCalledWith({
        data: [
          {
            email: fakeRecipient.email,
            id: 'mockUserId',
            legacyId: fakeRecipient.salesForceId,
            name: fakeName,
            partnerId: 'mockPartnerId',
          },
        ],
      });
      expect(applicationsCreateManyFn).toHaveBeenCalledWith({
        data: [
          {
            caseId: 'mockCaseId',
            id: 'mockApplicationId',
            submittedAt: expect.anything(),
            submitterId: 'mockUserId',
          },
        ],
      });
      expect(casesCreateManyFn).toHaveBeenCalledWith({
        data: [
          {
            id: 'mockCaseId',
            name: fakeName,
            programId: 'mockProgramId',
            status: 'InReview',
          },
        ],
      });
      expect(versionsCreateManyFn).toHaveBeenCalledWith({
        data: [
          {
            applicationId: 'mockApplicationId',
            creatorId: 'mockUserId',
            id: 'mockVersionId',
          },
        ],
      });
      expect(answersCreateManyFn).toHaveBeenCalledWith({
        data: [
          {
            key: 'salesForceId',
            value: fakeRecipient.salesForceId,
            versionId: 'mockVersionId',
          },
          {
            key: 'organization',
            value: fakeRecipient.organization,
            versionId: 'mockVersionId',
          },
          {
            key: 'program',
            value: fakeRecipient.program,
            versionId: 'mockVersionId',
          },
          {
            key: 'stipendType',
            value: fakeRecipient.stipendType,
            versionId: 'mockVersionId',
          },
          {
            key: 'cohort',
            value: fakeRecipient.cohort,
            versionId: 'mockVersionId',
          },
          { key: 'name', value: fakeName, versionId: 'mockVersionId' },
        ],
      });
      expect(profilesCreateManyFn).toHaveBeenCalledWith({
        data: [
          {
            applicantTypeId: 'mockApplicantTypeId',
            userId: 'mockUserId',
          },
        ],
      });
      expect(transactionFn).toHaveBeenCalledTimes(1);
    });
    it('should call payment service for creating payment accounts if withAccountCreation is true', async () => {
      const fakeRecipient = makeFakeRecipient({
        salesForceId: 'mockSalesForceId',
        accountNumber: '8524694',
        routingNumber: '*********',
      });
      usersFindManyFn.mockResolvedValueOnce([]).mockResolvedValueOnce([
        {
          id: 'mockUserId',
          legacyId: 'mockSalesForceId',
          name: 'Test User',
          email: '<EMAIL>',
        },
      ]);
      // RUN
      const result = await operation.run({
        action: 'import',
        programId: 'mockProgramId',
        recipients: [fakeRecipient],
        withAccountCreation: true,
      } as unknown as SchedulePaymentsRequest);
      // EXPECT
      expect(result).toEqual({
        status: 'success',
      });
      expect(usersCreateManyFn).toHaveBeenCalledTimes(1);
      expect(applicationsCreateManyFn).toHaveBeenCalledTimes(1);
      expect(casesCreateManyFn).toHaveBeenCalledTimes(1);
      expect(versionsCreateManyFn).toHaveBeenCalledTimes(1);
      expect(answersCreateManyFn).toHaveBeenCalledTimes(1);
      expect(profilesCreateManyFn).toHaveBeenCalledTimes(1);
      expect(transactionFn).toHaveBeenCalledTimes(1);
      expect(upsertPaymentAccountFn).toHaveBeenNthCalledWith(
        1,
        {
          legacyId: 'mockSalesForceId',
          bankAccount: {
            accountNumber: '8524694',
            accountType: 'savings',
            routingNumber: '*********',
          },
          email: '<EMAIL>',
          id: 'mockUserId',
          mailingAddress: {
            addressLine1: '1 Main St',
            city: 'Hanover',
            state: 'NH',
            zip: '03755',
          },
          name: 'Test User',
          payeeType: 'User',
        },
        expect.objectContaining({
          id: 'mockFundId',
          name: 'Program Fund',
          partner: { id: 'mockPartnerId', name: 'Test Partner' },
        }),
        'ach',
      );
    });
    it('should ignore user that has been already created and call payment accounts creation if withAccountCreation is true', async () => {
      usersFindManyFn
        .mockResolvedValueOnce([
          {
            legacyId: 'existingSalesForceId',
            name: 'Existing User',
            email: '<EMAIL>',
          },
        ])
        .mockResolvedValueOnce([
          {
            id: 'mockExistingUserId',
            legacyId: 'existingSalesForceId',
            name: 'Existing User',
            email: '<EMAIL>',
          },
          {
            id: 'mockUserId',
            legacyId: 'mockSalesForceId',
            name: 'Test User',
            email: '<EMAIL>',
          },
        ]);
      const existingRecipient = makeFakeRecipient({
        salesForceId: 'existingSalesForceId',
        firstName: 'Existing',
        lastName: 'User',
        email: '<EMAIL>',
        paymentMethod: 'physicalCard',
        paymentAmount: '10000',
      });
      const newFakeRecipient = makeFakeRecipient({
        salesForceId: 'mockSalesForceId',
        email: '<EMAIL>',
        paymentMethod: 'ach',
        accountNumber: '8524694',
        routingNumber: '*********',
        accountType: 'savings',
        paymentAmount: '10000',
      });

      jest
        .spyOn(crypto, 'randomUUID')
        .mockReturnValueOnce('mockUserId' as UUID)
        .mockReturnValueOnce('mockCaseId' as UUID)
        .mockReturnValueOnce('mockApplicationId' as UUID)
        .mockReturnValueOnce('mockVersionId' as UUID);

      const result = await operation.run({
        action: 'import',
        programId: 'mockProgramId',
        recipients: [existingRecipient, newFakeRecipient],
        withAccountCreation: true,
      } as unknown as SchedulePaymentsRequest);
      expect(result).toEqual({
        status: 'success',
      });
      expect(usersCreateManyFn).toHaveBeenCalledWith({
        data: [
          {
            email: '<EMAIL>',
            id: 'mockUserId',
            legacyId: 'mockSalesForceId',
            name: 'Test User',
            partnerId: 'mockPartnerId',
          },
        ],
      });
      expect(applicationsCreateManyFn).toHaveBeenCalledWith({
        data: [
          {
            caseId: 'mockCaseId',
            id: 'mockApplicationId',
            submittedAt: expect.anything(),
            submitterId: 'mockUserId',
          },
        ],
      });
      expect(casesCreateManyFn).toHaveBeenCalledWith({
        data: [
          {
            id: 'mockCaseId',
            name: 'Test User',
            programId: 'mockProgramId',
            status: 'InReview',
          },
        ],
      });
      expect(versionsCreateManyFn).toHaveBeenCalledWith({
        data: [
          {
            applicationId: 'mockApplicationId',
            creatorId: 'mockUserId',
            id: 'mockVersionId',
          },
        ],
      });
      expect(answersCreateManyFn).toHaveBeenCalledWith({
        data: [
          {
            key: 'salesForceId',
            value: newFakeRecipient.salesForceId,
            versionId: 'mockVersionId',
          },
          {
            key: 'organization',
            value: newFakeRecipient.organization,
            versionId: 'mockVersionId',
          },
          {
            key: 'program',
            value: newFakeRecipient.program,
            versionId: 'mockVersionId',
          },
          {
            key: 'stipendType',
            value: newFakeRecipient.stipendType,
            versionId: 'mockVersionId',
          },
          {
            key: 'cohort',
            value: newFakeRecipient.cohort,
            versionId: 'mockVersionId',
          },
          {
            key: 'name',
            value: `${newFakeRecipient.firstName} ${newFakeRecipient.lastName}`,
            versionId: 'mockVersionId',
          },
        ],
      });
      expect(profilesCreateManyFn).toHaveBeenCalledWith({
        data: [
          {
            applicantTypeId: 'mockApplicantTypeId',
            userId: 'mockUserId',
          },
        ],
      });
      expect(transactionFn).toHaveBeenCalledTimes(1);
      expect(upsertPaymentAccountFn).toHaveBeenCalledTimes(2);
      expect(upsertPaymentAccountFn).toHaveBeenCalledWith(
        {
          legacyId: 'mockSalesForceId',
          bankAccount: {
            accountNumber: '8524694',
            accountType: 'savings',
            routingNumber: '*********',
          },
          email: '<EMAIL>',
          id: 'mockUserId',
          mailingAddress: {
            addressLine1: '1 Main St',
            city: 'Hanover',
            state: 'NH',
            zip: '03755',
          },
          name: 'Test User',
          payeeType: 'User',
        },
        expect.objectContaining({
          id: 'mockFundId',
          name: 'Program Fund',
          partner: { id: 'mockPartnerId', name: 'Test Partner' },
        }),
        'ach',
      );
      expect(upsertPaymentAccountFn).toHaveBeenCalledWith(
        {
          legacyId: 'existingSalesForceId',
          email: '<EMAIL>',
          id: 'mockExistingUserId',
          mailingAddress: {
            addressLine1: '1 Main St',
            city: 'Hanover',
            state: 'NH',
            zip: '03755',
          },
          name: 'Existing User',
          payeeType: 'User',
        },
        expect.objectContaining({
          id: 'mockFundId',
          name: 'Program Fund',
          partner: { id: 'mockPartnerId', name: 'Test Partner' },
        }),
        'physicalCard',
      );
    });
    it('should ignore payment accounts creation if flag is off', async () => {
      usersFindManyFn.mockResolvedValueOnce([]).mockResolvedValueOnce([
        {
          id: 'mockUserId',
          legacyId: 'mockSalesForceId',
          name: 'Test User',
          email: '<EMAIL>',
        },
      ]);
      const fakeRecipient = makeFakeRecipient({
        salesForceId: 'mockSalesForceId',
        email: '<EMAIL>',
        paymentMethod: 'ach',
        accountNumber: '8524694',
        routingNumber: '*********',
        accountType: 'savings',
        paymentAmount: '10000',
      });
      const result = await operation.run({
        action: 'import',
        programId: 'mockProgramId',
        recipients: [fakeRecipient],
        withAccountCreation: false,
      } as unknown as SchedulePaymentsRequest);
      expect(result).toEqual({
        status: 'success',
      });
      expect(upsertPaymentAccountFn).not.toHaveBeenCalled();
    });
  });

  describe('schedule', () => {
    it('should return an error if payment date is not a date in future', async () => {
      const result = await operation.run({
        action: 'schedule',
        adminId: 'mockAdminId',
        programId: 'mockProgramId',
        recipients: [
          makeFakeRecipient({
            salesForceId: 'mockSalesForceId',
            paymentMethod: 'ach',
            accountNumber: '8524694',
            routingNumber: '*********',
            accountType: 'savings',
            paymentAmount: '10000',
            paymentDate: '2000/02/02',
          }),
        ],
      } as unknown as SchedulePaymentsRequest);
      expect(result).toEqual({
        errors: [
          {
            id: 'mockSalesForceId',
            message: 'Payment date should be after Window',
          },
        ],
        status: 'error',
      });
    });
    it('should return an error if core users not found', async () => {
      usersFindManyFn.mockResolvedValueOnce([]);
      const result = await operation.run({
        action: 'schedule',
        adminId: 'mockAdminId',
        programId: 'mockProgramId',
        recipients: [
          makeFakeRecipient({
            salesForceId: 'mockSalesForceId',
            paymentMethod: 'ach',
            accountNumber: '8524694',
            routingNumber: '*********',
            accountType: 'savings',
            paymentAmount: '10000',
            paymentDate: dayjs().add(1, 'day').toISOString(),
          }),
        ],
      } as unknown as SchedulePaymentsRequest);
      expect(result).toEqual({
        errors: [
          {
            id: 'mockSalesForceId',
            message: 'no core user relation found',
          },
        ],
        status: 'error',
      });
    });
    it('should return an error if payment accounts not found for ach/physical card methods', async () => {
      usersFindManyFn.mockResolvedValueOnce([
        {
          id: 'mockUserId',
          legacyId: 'mockSalesForceId',
        },
        {
          id: 'mockUserId2',
          legacyId: 'mockSalesForceId2',
        },
      ]);
      const result = await operation.run({
        action: 'schedule',
        adminId: 'mockAdminId',
        programId: 'mockProgramId',
        recipients: [
          makeFakeRecipient({
            salesForceId: 'mockSalesForceId',
            paymentMethod: 'ach',
            accountNumber: '8524694',
            routingNumber: '*********',
            accountType: 'savings',
            paymentAmount: '10000',
            paymentDate: dayjs().add(1, 'day').toISOString(),
          }),
          makeFakeRecipient({
            salesForceId: 'mockSalesForceId2',
            paymentMethod: 'check',
            paymentAmount: '10000',
            paymentDate: dayjs().add(1, 'day').toISOString(),
          }),
        ],
      } as unknown as SchedulePaymentsRequest);
      expect(result).toEqual({
        errors: [
          {
            id: 'mockSalesForceId',
            message: 'no payment account relation found',
          },
        ],
        status: 'error',
      });
    });
    it('should return a success response if all payments has been schedule before', async () => {
      usersFindManyFn.mockResolvedValueOnce([
        {
          id: 'mockUserId',
          legacyId: 'mockSalesForceId',
        },
      ]);
      accountsFindManyFn.mockResolvedValueOnce([
        {
          id: 'mockAccountId',
          referenceId: 'mockUserId-ach',
        },
      ]);
      paymentsFindManyFn.mockResolvedValueOnce([
        {
          id: 'mockPaymentId',
          user: { legacyId: 'mockSalesForceId' },
        },
      ]);
      const result = await operation.run({
        action: 'schedule',
        adminId: 'mockAdminId',
        programId: 'mockProgramId',
        recipients: [
          makeFakeRecipient({
            salesForceId: 'mockSalesForceId',
            paymentMethod: 'ach',
            accountNumber: '8524694',
            routingNumber: '*********',
            accountType: 'savings',
            paymentAmount: '10000',
            paymentDate: dayjs().add(1, 'day').toISOString(),
          }),
        ],
      } as unknown as SchedulePaymentsRequest);
      expect(result).toEqual({ status: 'success' });
    });
    it('should log and skip recipients that have scheduled payment', async () => {
      usersFindManyFn.mockResolvedValueOnce([
        { id: 'mockUserId1', legacyId: 'mockScheduledBefore' },
        { id: 'mockUserId2', legacyId: 'mockSalesForceId' },
      ]);
      accountsFindManyFn.mockResolvedValueOnce([
        { id: 'mockAccountId1', referenceId: 'mockUserId1-ach' },
        { id: 'mockAccountId2', referenceId: 'mockUserId2-ach' },
      ]);
      applicationsFindManyFn.mockResolvedValueOnce([
        {
          id: 'mockAppId',
          submitterId: 'mockUserId',
          case: { id: 'mockCaseId' },
        },
      ]);
      paymentsFindManyFn
        .mockResolvedValueOnce([
          {
            id: 'mockPaymentId',
            user: { legacyId: 'mockScheduledBefore' },
          },
        ])
        .mockResolvedValueOnce([
          {
            id: 'mockPaymentId',
            user: { legacyId: 'mockSalesForceId' },
          },
        ]);
      usersFindManyFn.mockResolvedValueOnce([
        {
          id: 'mockUserId',
          legacyId: 'mockSalesForceId',
        },
      ]);
      jest.spyOn(operation, 'scheduleSinglePayment').mockResolvedValueOnce({ message: 'complete' });
      await operation.run({
        action: 'schedule',
        adminId: 'mockAdminId',
        programId: 'mockProgramId',
        recipients: [
          makeFakeRecipient({
            salesForceId: 'mockScheduledBefore',
            paymentMethod: 'ach',
            accountNumber: '8524694',
            routingNumber: '*********',
            accountType: 'savings',
            paymentAmount: '10000',
            paymentDate: dayjs().add(1, 'day').toISOString(),
          }),
          makeFakeRecipient({
            salesForceId: 'mockSalesForceId',
            paymentMethod: 'ach',
            accountNumber: '8524694',
            routingNumber: '*********',
            accountType: 'savings',
            paymentAmount: '10000',
            paymentDate: dayjs().add(1, 'day').toISOString(),
          }),
        ],
      } as unknown as SchedulePaymentsRequest);
      expect(logger.warn).toHaveBeenCalledWith(
        { skippedIds: ['mockScheduledBefore'] },
        'ScheduleRecurringPaymentsOperation schedule: skipping payments ->',
      );
    });
    it('should return success and mark bulk operation as complete', async () => {
      const paymentDate = dayjs().add(1, 'day').toISOString();
      const fakeRecipient = makeFakeRecipient({
        salesForceId: 'mockSalesForceId',
        paymentMethod: 'ach',
        accountNumber: '8524694',
        routingNumber: '*********',
        accountType: 'savings',
        paymentAmount: '10000',
        paymentDate: paymentDate,
      });
      usersFindManyFn.mockResolvedValueOnce([{ id: 'mockUserId', legacyId: 'mockSalesForceId' }]);
      accountsFindManyFn.mockResolvedValueOnce([
        { id: 'mockAccountId', referenceId: 'mockUserId-ach' },
      ]);
      applicationsFindManyFn.mockResolvedValueOnce([
        {
          id: 'mockAppId',
          submitterId: 'mockUserId',
          case: { id: 'mockCaseId' },
        },
      ]);
      paymentsFindManyFn.mockResolvedValueOnce([]).mockResolvedValueOnce([
        {
          id: 'mockPaymentId',
          payeeId: 'mockUserId',
          payeeType: 'User',
          amount: 10000,
          method: 'ach',
          user: { legacyId: 'mockSalesForceId' },
          fulfillment: {
            id: 'mockFulfillmentId',
            caseId: 'mockCaseId',
            paymentPattern: {
              id: 'mockPaymentPatternId',
              amount: 10000,
              pattern: 'weekly',
            },
          },
        },
      ]);
      usersFindManyFn.mockResolvedValueOnce([
        {
          id: 'mockUserId',
          legacyId: 'mockSalesForceId',
        },
      ]);

      const result = await operation.run({
        action: 'schedule',
        adminId: 'mockAdminId',
        programId: 'mockProgramId',
        recipients: [fakeRecipient],
      } as unknown as SchedulePaymentsRequest);

      // EXPECT
      expect(result).toEqual({ status: 'success' });
      expect(casesUpdateManyFn).toHaveBeenCalledWith({
        data: { status: 'Approved' },
        where: { id: { in: ['mockCaseId'] } },
      });
      expect(fulfillmentsCreateManyFn).toHaveBeenCalledWith({
        data: [
          {
            approvedAmount: 10000,
            caseId: 'mockCaseId',
            fundId: 'mockFundId',
            id: expect.anything(),
            scheduleType: 'recurring',
          },
        ],
      });
      expect(patternsCreateManyFn).toHaveBeenCalledWith({
        data: [
          {
            amount: 10000,
            count: 1,
            fulfillmentId: expect.anything(),
            pattern: 'oneTime',
            start: paymentDate,
          },
        ],
      });
      expect(paymentsCreateManyFn).toHaveBeenNthCalledWith(1, {
        data: [
          {
            amount: 10000,
            fulfillmentId: expect.anything(),
            id: expect.anything(),
            method: 'ach',
            payeeId: 'mockUserId',
            payeeType: 'User',
            status: 'authorized',
          },
        ],
      });
      expect(paymentsCreateManyFn).toHaveBeenNthCalledWith(2, {
        data: [
          {
            amount: 10000,
            fulfillmentId: 'mockFulfillmentId',
            id: 'mockScheduledPaymentId',
            method: 'ach',
            payeeId: 'mockUserId',
            payeeType: 'User',
            scheduledFor: 'scheduledDate',
            status: 'authorized',
          },
        ],
      });
      expect(workflowEventsCreateFn).toHaveBeenCalledWith({
        data: {
          action: 'PaymentUpdate',
          adminId: 'mockAdminId',
          bulkOperationId: 'mockBulkOperationId',
          details: 'scheduled one-time payment',
          entityId: 'mockFulfillmentId',
          entityType: 'fulfillment',
        },
      });
      expect(operationsUpdateFn).toHaveBeenCalledWith({
        data: {
          completedAt: expect.anything(),
          status: 'Completed',
        },
        where: {
          id: 'mockBulkOperationId',
        },
      });
    });
    it('should return success and mark bulk operation as complete for checks recipients', async () => {
      const paymentDate = dayjs().add(1, 'day').toISOString();
      const fakeRecipient = makeFakeRecipient({
        salesForceId: 'mockSalesForceId',
        paymentMethod: 'check',
        paymentAmount: '10000',
        paymentDate: paymentDate,
      });
      usersFindManyFn.mockResolvedValueOnce([{ id: 'mockUserId', legacyId: 'mockSalesForceId' }]);
      applicationsFindManyFn.mockResolvedValueOnce([
        {
          id: 'mockAppId',
          submitterId: 'mockUserId',
          case: { id: 'mockCaseId' },
        },
      ]);
      paymentsFindManyFn.mockResolvedValueOnce([]).mockResolvedValueOnce([
        {
          id: 'mockPaymentId',
          payeeId: 'mockUserId',
          payeeType: 'User',
          amount: 10000,
          method: 'check',
          user: { legacyId: 'mockSalesForceId' },
          fulfillment: {
            id: 'mockFulfillmentId',
            caseId: 'mockCaseId',
            paymentPattern: {
              id: 'mockPaymentPatternId',
              amount: 10000,
              pattern: 'weekly',
            },
          },
        },
      ]);
      usersFindManyFn.mockResolvedValueOnce([
        {
          id: 'mockUserId',
          legacyId: 'mockSalesForceId',
        },
      ]);

      const result = await operation.run({
        action: 'schedule',
        adminId: 'mockAdminId',
        programId: 'mockProgramId',
        recipients: [fakeRecipient],
      } as unknown as SchedulePaymentsRequest);

      // EXPECT
      expect(result).toEqual({ status: 'success' });
      expect(casesUpdateManyFn).toHaveBeenCalledWith({
        data: { status: 'Approved' },
        where: { id: { in: ['mockCaseId'] } },
      });
      expect(fulfillmentsCreateManyFn).toHaveBeenCalledWith({
        data: [
          {
            approvedAmount: 10000,
            caseId: 'mockCaseId',
            fundId: 'mockFundId',
            id: expect.anything(),
            scheduleType: 'recurring',
          },
        ],
      });
      expect(patternsCreateManyFn).toHaveBeenCalledWith({
        data: [
          {
            amount: 10000,
            count: 1,
            fulfillmentId: expect.anything(),
            pattern: 'oneTime',
            start: paymentDate,
          },
        ],
      });
      expect(paymentsCreateManyFn).toHaveBeenNthCalledWith(1, {
        data: [
          {
            amount: 10000,
            fulfillmentId: expect.anything(),
            id: expect.anything(),
            method: 'check',
            payeeId: 'mockUserId',
            payeeType: 'User',
            status: 'authorized',
          },
        ],
      });
      expect(paymentsCreateManyFn).toHaveBeenNthCalledWith(2, {
        data: [
          {
            amount: 10000,
            fulfillmentId: 'mockFulfillmentId',
            id: 'mockScheduledPaymentId',
            method: 'check',
            payeeId: 'mockUserId',
            payeeType: 'User',
            scheduledFor: 'scheduledDate',
            status: 'authorized',
          },
        ],
      });
      expect(workflowEventsCreateFn).toHaveBeenCalledWith({
        data: {
          action: 'PaymentUpdate',
          adminId: 'mockAdminId',
          bulkOperationId: 'mockBulkOperationId',
          details: 'scheduled one-time payment',
          entityId: 'mockFulfillmentId',
          entityType: 'fulfillment',
        },
      });
      expect(operationsUpdateFn).toHaveBeenCalledWith({
        data: {
          completedAt: expect.anything(),
          status: 'Completed',
        },
        where: {
          id: 'mockBulkOperationId',
        },
      });
      expect(accountsFindManyFn).not.toHaveBeenCalled();
    });
    it('should revert if payment service throws an error', async () => {
      const paymentDate = dayjs().add(1, 'day').toISOString();
      usersFindManyFn.mockResolvedValueOnce([{ id: 'mockUserId', legacyId: 'mockSalesForceId' }]);
      accountsFindManyFn.mockResolvedValueOnce([
        { id: 'mockAccountId', referenceId: 'mockUserId-ach' },
      ]);
      applicationsFindManyFn.mockResolvedValueOnce([
        {
          id: 'mockAppId',
          submitterId: 'mockUserId',
          case: { id: 'mockCaseId' },
        },
      ]);
      paymentsFindManyFn.mockResolvedValueOnce([]).mockResolvedValueOnce([
        {
          id: 'mockPaymentId',
          payeeId: 'mockUserId',
          payeeType: 'User',
          amount: 10000,
          method: 'ach',
          user: { legacyId: 'mockSalesForceId' },
          fulfillment: {
            id: 'mockFulfillmentId',
            caseId: 'mockCaseId',
            paymentPattern: {
              id: 'mockPaymentPatternId',
              amount: 10000,
              pattern: 'weekly',
            },
          },
        },
      ]);
      usersFindManyFn.mockResolvedValueOnce([
        {
          id: 'mockUserId',
          legacyId: 'mockSalesForceId',
        },
      ]);
      schedulePaymentFn.mockResolvedValueOnce({ status: 'failed' });

      await expect(
        operation.run({
          action: 'schedule',
          adminId: 'mockAdminId',
          programId: 'mockProgramId',
          recipients: [
            makeFakeRecipient({
              salesForceId: 'mockSalesForceId',
              paymentMethod: 'ach',
              accountNumber: '8524694',
              routingNumber: '*********',
              accountType: 'savings',
              paymentAmount: '10000',
              paymentDate: paymentDate,
            }),
          ],
        } as unknown as SchedulePaymentsRequest),
      ).rejects.toThrow('Scheduling payments encountered an error, check the logs');

      // EXPECT
      expect(casesUpdateFn).toHaveBeenCalledTimes(1);
      expect(fulfillmentsCreateManyFn).toHaveBeenCalledTimes(1);
      expect(patternsCreateManyFn).toHaveBeenCalledTimes(1);
      expect(paymentsCreateManyFn).toHaveBeenCalledTimes(1);
      expect(workflowEventsCreateFn).not.toHaveBeenCalled();

      expect(patternsDeleteFn).toHaveBeenCalledWith({
        where: { fulfillmentId: 'mockFulfillmentId' },
      });
      expect(fulfillmentsDeleteFn).toHaveBeenCalledWith({
        where: { id: 'mockFulfillmentId' },
      });
      expect(paymentsDeleteFn).toHaveBeenCalledWith({
        where: { id: 'mockPaymentId' },
      });
      expect(casesUpdateFn).toHaveBeenCalledWith({
        data: { status: 'InReview' },
        where: { id: 'mockCaseId' },
      });
      expect(operationsUpdateFn).toHaveBeenCalledWith({
        data: { status: 'Failed' },
        where: {
          id: 'mockBulkOperationId',
        },
      });
    });
  });
});
