import { beforeEach, describe, expect, it } from '@jest/globals';
import type { PrismaClient as PlatformClient } from '@prisma/clients/platform';
import { mockDeep, mockReset } from 'jest-mock-extended';
import Operation from './SaveEligibilityConfigQuestions';

describe('SaveEligibilityConfigQuestions', () => {
  const mockPrismaClient = mockDeep<PlatformClient>();
  beforeEach(() => {
    mockReset(mockPrismaClient);
  });
  it('should call repo to update config questions', async () => {
    const operation = new Operation(mockPrismaClient);

    const input = {
      updates: [
        {
          id: 'mockQuestion',
          value: {
            name: 'foo',
          },
        },
      ],
      deletes: [],
      creations: [],
      configId: 'configId',
    };
    await operation.run(input);
    expect(mockPrismaClient.eligibilityQuestions.update).toHaveBeenCalledWith({
      data: {
        name: 'foo',
      },
      where: { id: 'mockQuestion' },
    });
    expect(mockPrismaClient.$transaction).toHaveBeenCalledTimes(1);
  });
  it('should call repo to delete config questions', async () => {
    const operation = new Operation(mockPrismaClient);

    const input = {
      updates: [],
      deletes: ['mockId'],
      creations: [],
      configId: 'configId',
    };
    await operation.run(input);
    expect(mockPrismaClient.eligibilityConfigQuestions.deleteMany).toHaveBeenCalledWith({
      where: { id: { in: ['mockId'] } },
    });
    expect(mockPrismaClient.$transaction).toHaveBeenCalledTimes(1);
  });
  it('should call repo to create config questions', async () => {
    const operation = new Operation(mockPrismaClient);
    mockPrismaClient.eligibilityQuestions.create.mockReturnValue({ id: 'newId' });

    const input = {
      updates: [],
      deletes: [],
      creations: [{ value: { name: 'foo', description: 'great question' } }],
      configId: 'configId',
    };
    await operation.run(input);
    expect(mockPrismaClient.eligibilityQuestions.create).toHaveBeenCalledWith({
      data: { name: 'foo', description: 'great question' },
    });
    expect(mockPrismaClient.eligibilityConfigQuestions.create).toHaveBeenCalledWith({
      data: { configId: 'configId', questionId: 'newId' },
    });
    expect(mockPrismaClient.$transaction).toHaveBeenCalledWith([]);
  });
});
