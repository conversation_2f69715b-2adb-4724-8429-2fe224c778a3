import { describe, expect, it, jest } from '@jest/globals';
import { Paymentmethod as PaymentMethod } from '@prisma/clients/platform';
import axios from 'axios';
import { AccountType } from 'types/account';
import { type Payee, PayeeType } from 'types/payee';
import {
  PaymentServiceResponseStatus,
  PopulatedFund,
  PopulatedPayment,
  Provider,
} from 'types/payment';
import {
  createAccountPayload,
  createPaymentPayload,
  createRecipientPayload,
  handleError,
  makeValidEmailAddress,
} from './utils';

jest.mock('axios');

describe('PaymentService utils', () => {
  describe('makeValidEmailAddress', () => {
    describe('when the email does not contain a + sign', () => {
      it('returns the email unchanged', () => {
        expect(makeValidEmailAddress({ email: '<EMAIL>' } as Payee)).toEqual(
          '<EMAIL>',
        );
      });
    });

    describe('when the email contains a + sign', () => {
      it('removes everything between the + (inclusive) and @ (exclusive)', () => {
        expect(makeValidEmailAddress({ email: '<EMAIL>' } as Payee)).toEqual(
          '<EMAIL>',
        );
      });
    });

    describe('when the email contains multiple + signs', () => {
      it('removes everything between the *first* + (inclusive) and @ (exclusive)', () => {
        expect(makeValidEmailAddress({ email: '<EMAIL>' } as Payee)).toEqual(
          '<EMAIL>',
        );
      });
    });

    describe('when the payee does not have an email', () => {
      it('returns a dummy in the <NAME_EMAIL>', () => {
        expect(makeValidEmailAddress({ id: 'payee_id' } as Payee)).toEqual('<EMAIL>');
      });
    });
  });
  describe('createRecipientPayload', () => {
    it('splits out first and last name for User payee type', () => {
      const recipient = createRecipientPayload(
        {
          payeeType: PayeeType.User,
          id: 'mockUserId',
          email: '<EMAIL>',
          name: 'Test User',
          mailingAddress: { addressLine1: '1 Main St', city: 'Hanover', state: 'NH', zip: '03755' },
        } as unknown as Payee,
        { id: 'mockFundId' } as PopulatedFund,
      );

      expect(recipient).toStrictEqual({
        referenceId: 'mockUserId',
        fundingSourceReferenceId: 'mockFundId',
        provider: Provider.JPMC,
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        address: {
          street: '1 Main St',
          unit: undefined,
          city: 'Hanover',
          state: 'NH',
          zip: '03755',
        },
      });
    });

    it('makes the email address valid for JPMC (removes anything with foo)', () => {
      const recipient = createRecipientPayload(
        {
          payeeType: PayeeType.User,
          id: 'mockUserId',
          email: '<EMAIL>',
          name: 'Test User',
          mailingAddress: { addressLine1: '1 Main St', city: 'Hanover', state: 'NH', zip: '03755' },
        } as unknown as Payee,
        { id: 'mockFundId' } as PopulatedFund,
      );

      expect(recipient).toStrictEqual({
        referenceId: 'mockUserId',
        fundingSourceReferenceId: 'mockFundId',
        provider: Provider.JPMC,
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        address: {
          street: '1 Main St',
          unit: undefined,
          city: 'Hanover',
          state: 'NH',
          zip: '03755',
        },
      });
    });
  });

  describe('createAccountPayload', () => {
    it('returns a JPMC ACH account for Direct Deposit', () => {
      expect(
        createAccountPayload(
          {
            id: 'mock-payee-id',
            bankAccount: {
              accountType: AccountType.Checking,
              accountNumber: 'accNo',
              routingNumber: 'routingNo',
            },
          } as unknown as Payee,
          { id: 'mockFundId' } as PopulatedFund,
          PaymentMethod.ach,
        ),
      ).toEqual({
        referenceId: 'mock-payee-id-ach',
        recipientReferenceId: 'mock-payee-id',
        provider: Provider.JPMC,
        account: {
          accountType: PaymentMethod.ach,
          accountInfo: {
            accountType: AccountType.Checking,
            accountNumber: 'accNo',
            routingNumber: 'routingNo',
          },
        },
      });
    });

    it('throws an error if payee has no bank account for a Direct Deposit account', () => {
      expect(() =>
        createAccountPayload(
          { id: 'mock-payee-id' } as Payee,
          { id: 'mockFundId' } as PopulatedFund,
          PaymentMethod.ach,
        ),
      ).toThrow(new Error('Payee has no bank account details mock-payee-id'));
    });

    it('returns a JPMC Zelle account with the payee email for Zelle', () => {
      expect(
        createAccountPayload(
          { id: 'mock-payee-id', email: '<EMAIL>' } as Payee,
          { id: 'mockFundId' } as PopulatedFund,
          PaymentMethod.zelle,
        ),
      ).toEqual({
        referenceId: 'mock-payee-id-zelle',
        recipientReferenceId: 'mock-payee-id',
        provider: Provider.JPMC,
        account: { accountType: PaymentMethod.zelle, accountInfo: { email: '<EMAIL>' } },
      });
    });

    it('returns a USIO Prepaid account with necessary additional information', () => {
      expect(
        createAccountPayload(
          {
            id: 'mock-payee-id',
            email: '<EMAIL>',
            name: 'Test User',
            mailingAddress: {
              addressLine1: '1 Main St',
              city: 'Hanover',
              state: 'NH',
              zip: '03755',
            },
            payeeType: 'User',
          } as unknown as Payee,
          { id: 'mockFundId', partner: { name: 'mockPartner' } } as PopulatedFund,
          PaymentMethod.virtualCard,
        ),
      ).toEqual({
        referenceId: 'mock-payee-id-virtualCard',
        recipientReferenceId: 'mock-payee-id',
        fundingSourceReferenceId: 'mockFundId',
        provider: Provider.USIO,
        address: {
          city: 'Hanover',
          state: 'NH',
          street: '1 Main St',
          unit: undefined,
          zip: '03755',
        },
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        payer: 'mockPartner',
        account: { accountType: 'virtualCard' },
      });
    });

    it('throws an error if the payment method is not Zelle, DirectDeposit, or any prepaid cards', () => {
      expect(() =>
        createAccountPayload(
          { id: 'mock-payee-id' } as Payee,
          { id: 'mockFundId' } as PopulatedFund,
          PaymentMethod.check,
        ),
      ).toThrow(new Error('cannot create payment account for check'));
    });
  });

  describe('createPaymentPayload', () => {
    it('should return a payment with accountReferenceId if method is DirectDeposit', () => {
      const payment = createPaymentPayload(
        {
          id: 'mockPaymentId',
          amount: 220000,
          method: PaymentMethod.ach,
          payee: { payeeType: PayeeType.User, id: 'mockUserId' },
          fulfillment: { id: 'mockFulfillmentId', fund: { id: 'mockFundId' } },
        } as PopulatedPayment,
        '/callbackUrl/',
      );
      expect(payment).toStrictEqual({
        referenceId: 'mockPaymentId',
        recipientReferenceId: 'mockUserId',
        fundingSourceReferenceId: 'mockFundId',
        provider: 'jpmc',
        amount: 220000,
        paymentMethod: PaymentMethod.ach,
        accountReferenceId: 'mockUserId-ach',
        callbackUrl: '/callbackUrl/',
      });
    });

    it('should return a payment with accountReferenceId if method is Zelle', () => {
      const payment = createPaymentPayload(
        {
          id: 'mockPaymentId',
          amount: 220000,
          method: PaymentMethod.zelle,
          payee: { payeeType: PayeeType.User, id: 'mockUserId' },
          fulfillment: { id: 'mockFulfillmentId', fund: { id: 'mockFundId' } },
        } as PopulatedPayment,
        '/callbackUrl/',
      );
      expect(payment).toStrictEqual({
        referenceId: 'mockPaymentId',
        recipientReferenceId: 'mockUserId',
        fundingSourceReferenceId: 'mockFundId',
        provider: 'jpmc',
        amount: 220000,
        paymentMethod: PaymentMethod.zelle,
        accountReferenceId: 'mockUserId-zelle',
        callbackUrl: '/callbackUrl/',
      });
    });

    it('should return a payment with payer/payee if method is Check', () => {
      const payment = createPaymentPayload(
        {
          id: 'mockPaymentId',
          amount: 220000,
          method: PaymentMethod.check,
          payee: { payeeType: PayeeType.User, id: 'mockUserId', name: 'Test User' },
          fulfillment: {
            id: 'mockFulfillmentId',
            fund: { id: 'mockFundId', partner: { name: 'Test Partner' } },
          },
          note: 'stub note',
        } as unknown as PopulatedPayment,
        '/callbackUrl/',
      );
      expect(payment).toStrictEqual({
        referenceId: 'mockPaymentId',
        recipientReferenceId: 'mockUserId',
        fundingSourceReferenceId: 'mockFundId',
        provider: 'jpmc',
        amount: 220000,
        paymentMethod: PaymentMethod.check,
        payer: 'Test Partner',
        payee: 'Test User',
        callbackUrl: '/callbackUrl/',
        note: 'stub note',
      });
    });

    it('should return a payment with address/name/payer/accountReferenceId if method is Physical Card', () => {
      const payment = createPaymentPayload(
        {
          id: 'mockPaymentId',
          amount: 220000,
          method: PaymentMethod.physicalCard,
          payee: {
            payeeType: PayeeType.User,
            id: 'mockUserId',
            name: 'Test User',
            email: '<EMAIL>',
            mailingAddress: {
              addressLine1: '1 Main St',
              city: 'Exeter',
              state: 'NH',
              zip: '12345',
            },
          },
          fulfillment: {
            id: 'mockFulfillmentId',
            fund: { id: 'mockFundId', partner: { name: 'Test Partner' } },
          },
          note: 'stub note',
        } as unknown as PopulatedPayment,
        '/callbackUrl/',
      );

      expect(payment).toStrictEqual({
        referenceId: 'mockPaymentId',
        recipientReferenceId: 'mockUserId',
        fundingSourceReferenceId: 'mockFundId',
        accountReferenceId: 'mockUserId-physicalCard',
        provider: 'usio',
        amount: 220000,
        callbackUrl: '/callbackUrl/',
        paymentMethod: PaymentMethod.physicalCard,
        payer: 'Test Partner',
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        address: {
          street: '1 Main St',
          unit: undefined,
          city: 'Exeter',
          state: 'NH',
          zip: '12345',
        },
      });
    });

    it('should return a payment with address/name/payer/accountReferenceId if method is Virtual Card', () => {
      const payment = createPaymentPayload(
        {
          id: 'mockPaymentId',
          amount: 220000,
          method: PaymentMethod.virtualCard,
          payee: {
            payeeType: PayeeType.User,
            id: 'mockUserId',
            name: 'Test User',
            email: '<EMAIL>',
            mailingAddress: {
              addressLine1: '1 Main St',
              city: 'Exeter',
              state: 'NH',
              zip: '12345',
            },
          },
          fulfillment: {
            id: 'mockFulfillmentId',
            fund: { id: 'mockFundId', partner: { name: 'Test Partner' } },
          },
          note: 'stub note',
        } as unknown as PopulatedPayment,
        '/callbackUrl/',
      );

      expect(payment).toStrictEqual({
        referenceId: 'mockPaymentId',
        recipientReferenceId: 'mockUserId',
        accountReferenceId: 'mockUserId-virtualCard',
        fundingSourceReferenceId: 'mockFundId',
        provider: 'usio',
        amount: 220000,
        callbackUrl: '/callbackUrl/',
        paymentMethod: PaymentMethod.virtualCard,
        payer: 'Test Partner',
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        address: {
          street: '1 Main St',
          unit: undefined,
          city: 'Exeter',
          state: 'NH',
          zip: '12345',
        },
      });
    });
  });

  describe('handleError', () => {
    it('returns a non-retryable error response if error does not contain data', () => {
      jest.spyOn(axios, 'isAxiosError').mockReturnValueOnce(false);

      const error = handleError(new Error('Failed to reach server'));

      expect(error).toStrictEqual({
        status: PaymentServiceResponseStatus.Failed,
        error: {
          message: 'Failed to reach server',
          retryable: false,
          errorCode: undefined,
        },
      });
    });

    it('returns a retryable error response if error contains retryable data', () => {
      jest.spyOn(axios, 'isAxiosError').mockReturnValueOnce(true);

      const error = handleError({
        response: {
          data: {
            status: 'error',
            message: 'invalid routing number',
            retryable: true,
            errorCode: 'A-001',
          },
        },
        message: 'error',
      });

      expect(error).toStrictEqual({
        status: PaymentServiceResponseStatus.Failed,
        error: {
          message: 'invalid routing number',
          retryable: true,
          errorCode: 'A-001',
        },
      });
    });
    it('returns a non-retryable error response if error contains non-retryable data', () => {
      jest.spyOn(axios, 'isAxiosError').mockReturnValueOnce(true);

      const error = handleError({
        response: {
          data: {
            status: 'error',
            message: 'no funding source found',
            retryable: false,
          },
        },
        message: 'error',
      });

      expect(error).toStrictEqual({
        status: PaymentServiceResponseStatus.Failed,
        error: {
          message: 'no funding source found',
          retryable: false,
          errorCode: undefined,
        },
      });
    });
  });
});
