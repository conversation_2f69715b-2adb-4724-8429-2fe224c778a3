import {
  type Addresses as Address,
  Paymentmethod as PaymentMethod,
} from '@prisma/clients/platform';
import { getFirstName, getLastName } from '@utils/formatting';
import axios, { type AxiosResponse } from 'axios';
import type { CreateBaseAccount, CreateJPMCAccount, CreateUSIOAccount } from 'types/account';
import type { Payee } from 'types/payee';
import {
  type PaymentAddress,
  type PaymentServiceResponse,
  PaymentServiceResponseStatus,
  type PopulatedFund,
  type PopulatedPayment,
  Provider,
} from 'types/payment';
import type { CreateRecipient } from 'types/recipient';

export function makeValidEmailAddress(payee: Payee): string {
  const email = payee.email ?? `${payee.id}@email.com`;
  if (!email.includes('+')) return email;
  const [prefix, suffix] = email.split('@');
  return [prefix.split('+')[0], suffix].join('@');
}

export function splitName(payee: Payee): { firstName: string; lastName: string } {
  return {
    firstName: getFirstName(payee.name),
    lastName: getLastName(payee.name),
  };
}

export function convertAddress(address: Address): PaymentAddress {
  if (!address.addressLine1)
    throw new Error('Address not valid for payment: has no street address');
  return {
    street: address.addressLine1,
    unit: address.addressLine2 as string,
    city: address.city as string,
    state: address.state as string,
    zip: address.zip as string,
  };
}

export function createRecipientPayload(
  payee: Payee,
  fund: PopulatedFund,
  provider = Provider.JPMC,
): CreateRecipient {
  return {
    referenceId: payee.id,
    fundingSourceReferenceId: fund.id,
    provider,
    ...splitName(payee),
    email: makeValidEmailAddress(payee),
    address: convertAddress(payee.mailingAddress),
  };
}

export function createAccountPayload(
  payee: Payee,
  fund: PopulatedFund,
  method: PaymentMethod,
): CreateJPMCAccount | CreateUSIOAccount {
  const baseAccount: CreateBaseAccount = {
    referenceId: `${payee.id}-${method}`,
    recipientReferenceId: payee.id,
  };
  switch (method) {
    case PaymentMethod.ach:
      if (!payee.bankAccount) throw new Error(`Payee has no bank account details ${payee.id}`);
      return {
        ...baseAccount,
        provider: Provider.JPMC,
        account: { accountType: method, accountInfo: payee.bankAccount },
      } as CreateJPMCAccount;

    case PaymentMethod.zelle:
      return {
        ...baseAccount,
        provider: Provider.JPMC,
        account: { accountType: method, accountInfo: { email: payee.email } },
      } as CreateJPMCAccount;

    case PaymentMethod.virtualCard:
    case PaymentMethod.physicalCard:
      return {
        ...baseAccount,
        fundingSourceReferenceId: fund.id,
        provider: Provider.USIO,
        payer: fund?.partner?.name,
        ...splitName(payee),
        email: makeValidEmailAddress(payee),
        address: convertAddress(payee.mailingAddress),
        account: {
          accountType: method,
        },
      } as CreateUSIOAccount;
    default:
      throw new Error(`cannot create payment account for ${method}`);
  }
}

export function createPaymentPayload(payment: PopulatedPayment, callbackUrl: string) {
  const { method, amount, fulfillment, payee } = payment;
  const address = payee.mailingAddress;
  const paymentBase = {
    referenceId: payment.id,
    recipientReferenceId: payee.id,
    fundingSourceReferenceId: payment.fulfillment.fund.id,
    amount,
    callbackUrl,
  };

  switch (method) {
    case PaymentMethod.ach:
    case PaymentMethod.zelle:
      return {
        ...paymentBase,
        paymentMethod: method,
        provider: Provider.JPMC,
        accountReferenceId: `${payee.id}-${method}`,
      };
    case PaymentMethod.check:
      return {
        ...paymentBase,
        paymentMethod: method,
        provider: Provider.JPMC,
        payer: fulfillment.fund?.partner?.name,
        payee: payee.name,
        note: payment.note,
      };
    case PaymentMethod.physicalCard:
    case PaymentMethod.virtualCard:
      return {
        ...paymentBase,
        accountReferenceId: `${payee.id}-${method}`,
        paymentMethod: method,
        provider: Provider.USIO,
        payer: fulfillment.fund?.partner?.name,
        ...splitName(payee),
        email: payee.email,
        address: address ? convertAddress(address) : undefined,
      };
    default:
      throw new Error(`no implementation for ${method}`);
  }
}

export function createSchedulePayload(payment: PopulatedPayment) {
  const { paymentPattern } = payment.fulfillment;
  return {
    referenceId: payment.fulfillment.id,
    amountPerTransaction: paymentPattern?.amount,
    numberOfPayments: paymentPattern?.count,
    start: paymentPattern?.start,
    pattern: paymentPattern?.pattern,
    dryRun: false,
  };
}

export function handleError(error: unknown): PaymentServiceResponse<never> {
  let response: { retryable?: boolean; message: string; errorCode?: string } | undefined =
    undefined;
  if (axios.isAxiosError(error) && error?.response?.data) {
    ({ data: response } = error.response as AxiosResponse<{
      retryable: boolean;
      errorCode?: string;
      message: string;
    }>);
  }
  return {
    status: PaymentServiceResponseStatus.Failed,
    error: {
      message: response?.message ?? (error as Error).message,
      retryable: response?.retryable ?? false,
      errorCode: response?.errorCode,
    },
  };
}
