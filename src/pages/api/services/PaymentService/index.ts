import { Paymentmethod as PaymentMethod } from '@prisma/clients/platform';
import dayjs from '@utils/dayJsConfig';
import { logger } from '@utils/logger';
import { buildUrl } from '@utils/url';
import axios, { type AxiosResponse } from 'axios';
import { StatusCodes } from 'http-status-codes';
import jwt, { type JwtPayload } from 'jsonwebtoken';
import type { Payee } from 'types/payee';
import {
  type PaymentServiceResponse,
  PaymentServiceResponseStatus,
  type PopulatedFund,
  type PopulatedPayment,
  Provider,
  type ScheduledPayment,
} from 'types/payment';
import {
  createAccountPayload,
  createPaymentPayload,
  createRecipientPayload,
  createSchedulePayload,
  handleError,
} from './utils';

export enum Endpoint {
  Account = 'accounts',
  Authenticate = 'auth',
  Transactions = 'transactions',
}

export default class PaymentService {
  private baseUrl: string;
  private clientId: string;
  private clientSecret: string;
  private authToken?: string;
  private webhookUrl: string;

  constructor() {
    const { PAYMENTS_URL, PAYMENTS_AUTH_ID, PAYMENTS_AUTH_SECRET, PAYMENTS_WEBHOOK_URL } =
      process.env;
    if (!PAYMENTS_URL)
      throw new Error('PaymentService.constructor: missing env variable - PAYMENTS_URL');
    if (!PAYMENTS_AUTH_ID)
      throw new Error('PaymentService.constructor: missing env variable - PAYMENTS_AUTH_ID');
    if (!PAYMENTS_AUTH_SECRET)
      throw new Error('PaymentService.constructor: missing env variable - PAYMENTS_AUTH_SECRET');
    if (!PAYMENTS_WEBHOOK_URL)
      throw new Error('PaymentService.constructor: missing env variable - PAYMENTS_WEBHOOK_URL');
    this.baseUrl = PAYMENTS_URL;
    this.clientId = PAYMENTS_AUTH_ID;
    this.clientSecret = PAYMENTS_AUTH_SECRET;
    this.webhookUrl = buildUrl({
      base: PAYMENTS_WEBHOOK_URL,
      path: '/webhook/payments',
    }).toString();
  }

  private getUrl(endpoint: Endpoint, suffix = '', version = 1): string {
    return [`/v${version}`, endpoint, suffix].filter(Boolean).join('/');
  }

  public isValidAuthToken(token: JwtPayload): boolean {
    // Does the token exist / is a non empty string?
    if (!token) return false;
    // Will the token be expired soon? (10 minutes from now or less)
    return !dayjs.utc().add(10, 'minutes').isAfter(token.exp);
  }

  public async fetchAuthToken(): Promise<string> {
    const decodedToken = jwt.decode(this.authToken ?? '') as JwtPayload;

    if (!this.isValidAuthToken(decodedToken)) {
      this.authToken = await this.authenticate();
    }

    return this.authToken ?? '';
  }

  public async request<T>({
    url,
    data,
    options,
  }: {
    url: string;
    // biome-ignore lint/suspicious/noExplicitAny: reason
    data?: any;
    options?: {
      excludeAuthHeader: boolean;
    };
  }): Promise<AxiosResponse<T>> {
    let headers: { authorization: string } | undefined = undefined;
    try {
      if (!options?.excludeAuthHeader) headers = { authorization: await this.fetchAuthToken() };
    } catch (error) {
      if (axios.isAxiosError(error) && error.response?.status === StatusCodes.UNAUTHORIZED) {
        logger.error(`${this.constructor.name}.authenticate: Invalid credentials`);
        throw new Error('invalid credentials');
      }
      logger.error(
        `${this.constructor.name}.authenticate: unexpected error >`,
        axios.isAxiosError(error) ? error.toJSON() : error,
      );
      throw new Error('unexpected error during authentication');
    }
    return axios.post(`${this.baseUrl}${url}`, data, { headers });
  }

  public async authenticate(): Promise<string | undefined> {
    const response: AxiosResponse<{ token: string }> = await this.request({
      url: this.getUrl(Endpoint.Authenticate),
      data: {
        clientId: this.clientId,
        clientSecret: this.clientSecret,
      },
      options: { excludeAuthHeader: true },
    });

    return response.data.token;
  }

  private getProvider(paymentMethod: PaymentMethod) {
    switch (paymentMethod) {
      case PaymentMethod.virtualCard:
      case PaymentMethod.physicalCard:
        return Provider.USIO;
      default:
        return Provider.JPMC;
    }
  }

  public async upsertAccount(
    payee: Payee,
    fund: PopulatedFund,
    method: PaymentMethod,
  ): Promise<PaymentServiceResponse<Record<string, never>>> {
    try {
      const provider = this.getProvider(method);
      const payload = {
        recipient: createRecipientPayload(payee, fund, provider),
        account: createAccountPayload(payee, fund, method),
      };
      logger.info(
        `${this.constructor.name}.upsertAccount: begin for ${payee.payeeType} ${payee.id}`,
      );

      const {
        data: { transaction },
      } = await this.request<{ transaction: Record<string, never> }>({
        url: this.getUrl(Endpoint.Transactions),
        data: payload,
      });
      logger.info(
        { transaction },
        `${this.constructor.name}.upsertAccount: complete for ${payee.payeeType} ${payee.id}`,
      );

      return { status: PaymentServiceResponseStatus.Success, payload: {} };
    } catch (err) {
      return handleError(err);
    }
  }

  public async schedulePayment(
    payment: PopulatedPayment,
  ): Promise<PaymentServiceResponse<{ schedule: ScheduledPayment[] }>> {
    try {
      const { method, payee, fulfillment } = payment;
      if (!payee) throw new Error(`Payee not found for payment ${payment.id}`);
      if (!fulfillment) throw new Error(`Fulfillment not found for payment ${payment.id}`);
      if (!fulfillment?.paymentPattern)
        throw new Error(`No recurring payment pattern found for payment ${payment.id}`);

      const provider = this.getProvider(method as PaymentMethod);
      const payload = {
        recipient: createRecipientPayload(payee, fulfillment.fund, provider),
        payment: createPaymentPayload(payment, this.webhookUrl),
        schedule: createSchedulePayload(payment),
      };
      logger.info(`${this.constructor.name}.schedulePayment: Begin Transaction for ${payment.id}`);
      const response = await this.request<{ transaction: Record<string, never> }>({
        url: this.getUrl(Endpoint.Transactions),
        data: payload,
      });

      const {
        transaction: { schedule },
      } = response.data;
      logger.info({ schedule }, `${this.constructor.name}.schedulePayment: End Transaction`);

      return {
        status: PaymentServiceResponseStatus.Success,
        payload: { schedule },
      };
    } catch (error) {
      logger.error(
        { error },
        `${this.constructor.name}.schedulePayment: transaction for ${payment.id}, unexpected err ->`,
      );
      return handleError(error);
    }
  }
}
