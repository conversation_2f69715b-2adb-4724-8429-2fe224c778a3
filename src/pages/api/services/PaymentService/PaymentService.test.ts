import { describe } from 'node:test';
import { afterAll, beforeEach, expect, it, jest } from '@jest/globals';
import { Paymentmethod as PaymentMethod } from '@prisma/clients/platform';
import { mockAxios } from '@test/setup/globals';
import type { AxiosError, AxiosResponse } from 'axios';
import jwt, { type JwtPayload } from 'jsonwebtoken';
import { AccountType } from 'types/account';
import { type Payee, PayeeType } from 'types/payee';
import {
  PaymentServiceResponseStatus,
  type PopulatedFund,
  PopulatedPayment,
  Provider,
} from 'types/payment';
import dayjs from 'utils/dayJsConfig';
import PaymentService from '.';

const mockInvalidToken = '';
const originalEnv = { ...process.env };

describe('PaymentService', () => {
  beforeEach(() => {
    process.env.PAYMENTS_URL = 'mockPaymentsUrl';
    process.env.PAYMENTS_AUTH_ID = 'mockClientId';
    process.env.PAYMENTS_AUTH_SECRET = 'mockAuthSecret';
  });

  afterAll(() => {
    process.env = { ...originalEnv };
  });

  describe('constructor', () => {
    it('should throw an error if the payments url is not defined', () => {
      // biome-ignore lint/performance/noDelete: env testing, no performance impact
      delete process.env.PAYMENTS_URL;
      expect(() => new PaymentService()).toThrow();
    });

    it('should throw an error if the client id is not defined', () => {
      // biome-ignore lint/performance/noDelete: env testing, no performance impact
      delete process.env.PAYMENTS_AUTH_ID;
      expect(() => new PaymentService()).toThrow();
    });

    it('should throw an error if the client secret is not defined', () => {
      // biome-ignore lint/performance/noDelete: env testing, no performance impact
      delete process.env.PAYMENTS_AUTH_SECRET;
      expect(() => new PaymentService()).toThrow();
    });

    it('should not throw when all required environment variables are defined', () => {
      process.env.PAYMENTS_URL = 'a valid string';
      process.env.PAYMENTS_AUTH_ID = 'some client id';
      process.env.PAYMENTS_AUTH_SECRET = 'some auth secret';
      expect(() => new PaymentService()).not.toThrow();
    });
  });

  describe('isValidAuthToken', () => {
    it('should return false if the token is falsy', () => {
      const paymentService = new PaymentService();
      expect(paymentService.isValidAuthToken('' as unknown as JwtPayload)).toBe(false);
      expect(paymentService.isValidAuthToken(undefined as unknown as JwtPayload)).toBe(false);
      expect(paymentService.isValidAuthToken(null as unknown as JwtPayload)).toBe(false);
    });
    it('should return false if the token has already expired', () => {
      const paymentService = new PaymentService();
      const token = { exp: dayjs().toDate().getTime() } as JwtPayload; // Set expiry date to now
      expect(paymentService.isValidAuthToken(token)).toBe(false);
    });
    it('should return false if the token is expiring within 10 minutes', () => {
      const paymentService = new PaymentService();
      const token = { exp: dayjs().add(9, 'minutes').toDate().getTime() } as JwtPayload; // Set expiry date to now + 9 minutes
      expect(paymentService.isValidAuthToken(token)).toBe(false);
    });
    it('should return true for a token not set to expire for more than 10 minutes from now', () => {
      const paymentService = new PaymentService();
      const token = { exp: dayjs().add(11, 'minutes').toDate().getTime() } as JwtPayload; // Set expiry date to 11 minutes from now
      expect(paymentService.isValidAuthToken(token)).toBe(true);
    });
  });

  describe('fetchAuthToken', () => {
    it('should call the authenticate method when the auth token is invalid', async () => {
      const paymentService = new PaymentService();

      paymentService.authenticate = jest.fn<() => Promise<string | undefined>>();
      // biome-ignore lint/complexity/useLiteralKeys: this is private
      paymentService['authToken'] = mockInvalidToken;

      // biome-ignore lint/complexity/useLiteralKeys: this is private
      await paymentService['fetchAuthToken']();
      expect(paymentService.authenticate).toHaveBeenCalled();
    });

    it('should NOT call authenticate method when the auth token is valid', async () => {
      const paymentService = new PaymentService();

      paymentService.authenticate = jest.fn<() => Promise<string | undefined>>();
      // biome-ignore lint/complexity/useLiteralKeys: this is private
      paymentService['authToken'] = jwt.sign(
        { exp: dayjs().add(20, 'minutes').toDate().getTime() },
        'secret',
      );

      // biome-ignore lint/complexity/useLiteralKeys: this is private
      await paymentService['fetchAuthToken']();
      expect(paymentService.authenticate).not.toHaveBeenCalled();
    });
  });

  describe('authenticate', () => {
    it('should return auth token when no errors occur during the request', async () => {
      const paymentService = new PaymentService();

      mockAxios.post.mockResolvedValueOnce({
        status: 200,
        data: {
          token: 'mockTokenString',
        },
        statusText: 'ok',
      } as AxiosResponse);

      expect(await paymentService.authenticate()).toBe('mockTokenString');
    });

    it('should throw an error if the request fails', async () => {
      const paymentService = new PaymentService();

      mockAxios.post.mockRejectedValueOnce(new Error('Failed to reach server'));

      await expect(paymentService.authenticate()).rejects.toThrow();
    });
  });

  describe('upsertAccount', () => {
    it('should return a non-retryable failed response if axios throws a generic error', async () => {
      const paymentService = new PaymentService();

      mockAxios.isAxiosError.mockReturnValueOnce(false);

      const mockRequest = jest
        .fn<() => Promise<AxiosResponse>>()
        .mockRejectedValueOnce(new Error('Failed to reach server'));
      paymentService.request = mockRequest;

      const response = await paymentService.upsertAccount(
        {
          id: 'mock-payee-id',
          payeeType: PayeeType.User,
          name: 'Test User',
          email: '<EMAIL>',
          bankAccount: {
            accountType: AccountType.Checking,
            accountNumber: 'testAccNo',
            routingNumber: 'testRoutNo',
          },
          mailingAddress: { addressLine1: '1 Main St', city: 'Hanover', state: 'NH', zip: '03755' },
        } as unknown as Payee,
        { id: 'mock-fund-id' } as PopulatedFund,
        PaymentMethod.ach,
      );

      expect(mockRequest).toHaveBeenCalledWith({
        url: '/v1/transactions',
        data: {
          recipient: {
            referenceId: 'mock-payee-id',
            fundingSourceReferenceId: 'mock-fund-id',
            provider: Provider.JPMC,
            firstName: 'Test',
            lastName: 'User',
            email: '<EMAIL>',
            address: {
              street: '1 Main St',
              unit: undefined,
              city: 'Hanover',
              state: 'NH',
              zip: '03755',
            },
          },
          account: {
            referenceId: 'mock-payee-id-ach',
            recipientReferenceId: 'mock-payee-id',
            provider: Provider.JPMC,
            account: {
              accountType: PaymentMethod.ach,
              accountInfo: {
                accountType: AccountType.Checking,
                accountNumber: 'testAccNo',
                routingNumber: 'testRoutNo',
              },
            },
          },
        },
      });
      expect(response).toEqual({
        status: PaymentServiceResponseStatus.Failed,
        error: {
          message: 'Failed to reach server',
          retryable: false,
          errorCode: undefined,
        },
      });
    });

    it('should return a non-retryable failed response for a non-retryable payment error', async () => {
      const paymentService = new PaymentService();

      mockAxios.isAxiosError.mockReturnValueOnce(true);

      const error = new Error('error') as AxiosError;
      error.response = {
        data: {
          status: 'error',
          message: 'no funding source found',
          provider: 'jpmc',
          retryable: false,
        },
      } as AxiosResponse;
      const mockRequest = jest.fn<() => Promise<AxiosResponse>>().mockRejectedValueOnce(error);
      paymentService.request = mockRequest;

      const response = await paymentService.upsertAccount(
        {
          id: 'mock-payee-id',
          payeeType: PayeeType.User,
          name: 'Test User',
          email: '<EMAIL>',
          bankAccount: {
            accountType: AccountType.Checking,
            accountNumber: 'testAccNo',
            routingNumber: 'testRoutNo',
          },
          mailingAddress: {
            addressLine1: '1 Main St',
            city: 'Brooklyn',
            state: 'NY',
            zip: '10010',
          },
        } as unknown as Payee,
        { id: 'mock-fund-id' } as PopulatedFund,
        PaymentMethod.ach,
      );

      expect(mockRequest).toHaveBeenCalledWith({
        url: '/v1/transactions',
        data: {
          recipient: {
            referenceId: 'mock-payee-id',
            fundingSourceReferenceId: 'mock-fund-id',
            provider: Provider.JPMC,
            firstName: 'Test',
            lastName: 'User',
            email: '<EMAIL>',
            address: {
              street: '1 Main St',
              unit: undefined,
              city: 'Brooklyn',
              state: 'NY',
              zip: '10010',
            },
          },
          account: {
            referenceId: 'mock-payee-id-ach',
            recipientReferenceId: 'mock-payee-id',
            provider: Provider.JPMC,
            account: {
              accountType: PaymentMethod.ach,
              accountInfo: {
                accountType: AccountType.Checking,
                accountNumber: 'testAccNo',
                routingNumber: 'testRoutNo',
              },
            },
          },
        },
      });
      expect(response).toEqual({
        status: PaymentServiceResponseStatus.Failed,
        error: {
          message: 'no funding source found',
          retryable: false,
          errorCode: undefined,
        },
      });
    });

    it('should return a retryable failed response for a retryable error', async () => {
      const paymentService = new PaymentService();

      mockAxios.isAxiosError.mockReturnValueOnce(true);

      const error = new Error('error') as AxiosError;
      error.response = {
        data: {
          status: 'error',
          message: 'account not found',
          provider: 'jpmc',
          retryable: true,
          errorCode: 'A-002',
        },
      } as AxiosResponse;
      const mockRequest = jest.fn<() => Promise<AxiosResponse>>().mockRejectedValueOnce(error);
      paymentService.request = mockRequest;

      const response = await paymentService.upsertAccount(
        {
          id: 'mock-payee-id',
          payeeType: PayeeType.User,
          name: 'Test User',
          email: '<EMAIL>',
          bankAccount: {
            accountType: AccountType.Checking,
            accountNumber: 'testAccNo',
            routingNumber: 'testRoutNo',
          },
          mailingAddress: {
            addressLine1: '1 Main St',
            city: 'Brooklyn',
            state: 'NY',
            zip: '10010',
          },
        } as unknown as Payee,
        { id: 'mock-fund-id' } as PopulatedFund,
        PaymentMethod.ach,
      );

      expect(mockRequest).toHaveBeenCalledWith({
        url: '/v1/transactions',
        data: {
          recipient: {
            referenceId: 'mock-payee-id',
            fundingSourceReferenceId: 'mock-fund-id',
            provider: Provider.JPMC,
            firstName: 'Test',
            lastName: 'User',
            email: '<EMAIL>',
            address: {
              street: '1 Main St',
              unit: undefined,
              city: 'Brooklyn',
              state: 'NY',
              zip: '10010',
            },
          },
          account: {
            referenceId: 'mock-payee-id-ach',
            recipientReferenceId: 'mock-payee-id',
            provider: Provider.JPMC,
            account: {
              accountType: PaymentMethod.ach,
              accountInfo: {
                accountType: AccountType.Checking,
                accountNumber: 'testAccNo',
                routingNumber: 'testRoutNo',
              },
            },
          },
        },
      });
      expect(response).toEqual({
        status: PaymentServiceResponseStatus.Failed,
        error: {
          message: 'account not found',
          retryable: true,
          errorCode: 'A-002',
        },
      });
    });

    it('should return a success status if the response is successful', async () => {
      const paymentService = new PaymentService();
      const mockRequest = jest.fn<() => Promise<AxiosResponse>>().mockResolvedValueOnce({
        data: {
          transaction: {
            recipient: { referenceId: 'mock-payee-id' },
            account: { referenceId: 'mock-payee-id-ach' },
          },
        },
      } as AxiosResponse);
      paymentService.request = mockRequest;

      const response = await paymentService.upsertAccount(
        {
          id: 'mock-payee-id',
          payeeType: PayeeType.User,
          name: 'Test User',
          email: '<EMAIL>',
          bankAccount: {
            accountType: AccountType.Checking,
            accountNumber: 'testAccNo',
            routingNumber: 'testRoutNo',
          },
          mailingAddress: {
            addressLine1: '1 Main St',
            city: 'Brooklyn',
            state: 'NY',
            zip: '10010',
          },
        } as unknown as Payee,
        { id: 'mock-fund-id' } as PopulatedFund,
        PaymentMethod.ach,
      );

      expect(mockRequest).toHaveBeenCalledWith({
        url: '/v1/transactions',
        data: {
          recipient: {
            referenceId: 'mock-payee-id',
            fundingSourceReferenceId: 'mock-fund-id',
            provider: Provider.JPMC,
            firstName: 'Test',
            lastName: 'User',
            email: '<EMAIL>',
            address: {
              street: '1 Main St',
              unit: undefined,
              city: 'Brooklyn',
              state: 'NY',
              zip: '10010',
            },
          },
          account: {
            referenceId: 'mock-payee-id-ach',
            recipientReferenceId: 'mock-payee-id',
            provider: Provider.JPMC,
            account: {
              accountType: PaymentMethod.ach,
              accountInfo: {
                accountType: AccountType.Checking,
                accountNumber: 'testAccNo',
                routingNumber: 'testRoutNo',
              },
            },
          },
        },
      });
      expect(response).toEqual({ status: PaymentServiceResponseStatus.Success, payload: {} });
    });
  });

  describe('schedulePayment', () => {
    it('should return status error if payee not found', async () => {
      const paymentService = new PaymentService();
      await expect(
        paymentService.schedulePayment({
          id: 'mock-payment-id',
          method: 'ach',
          amount: 20000,
        } as unknown as PopulatedPayment),
      ).resolves.toEqual({
        error: {
          errorCode: undefined,
          message: 'Payee not found for payment mock-payment-id',
          retryable: false,
        },
        status: 'failed',
      });
    });
    it('should return status error if fulfillment not found', async () => {
      const paymentService = new PaymentService();
      await expect(
        paymentService.schedulePayment({
          id: 'mock-payment-id',
          method: 'ach',
          amount: 20000,
          payee: {
            id: 'mock-payee-id',
            payeeType: 'User',
          },
        } as unknown as PopulatedPayment),
      ).resolves.toEqual({
        error: {
          errorCode: undefined,
          message: 'Fulfillment not found for payment mock-payment-id',
          retryable: false,
        },
        status: 'failed',
      });
    });
    it('should return status error if payment pattern not found', async () => {
      const paymentService = new PaymentService();
      await expect(
        paymentService.schedulePayment({
          id: 'mock-payment-id',
          method: 'ach',
          amount: 20000,
          payee: {
            id: 'mock-payee-id',
            payeeType: 'User',
          },
          fulfillment: {
            id: 'mock-fulfillment-id',
            fund: { id: 'mock-fund-id' },
          },
        } as unknown as PopulatedPayment),
      ).resolves.toEqual({
        error: {
          errorCode: undefined,
          message: 'No recurring payment pattern found for payment mock-payment-id',
          retryable: false,
        },
        status: 'failed',
      });
    });

    it('should create a schedule payload and call transaction endpoint', async () => {
      const paymentService = new PaymentService();
      const mockRequest = jest.fn<() => Promise<AxiosResponse>>().mockResolvedValue({
        status: 200,
        data: {
          transaction: {
            schedule: [
              {
                id: 'payment-1',
                scheduleFor: 'schedule-date',
              },
              {
                id: 'payment-2',
                scheduleFor: 'schedule-date',
              },
            ],
          },
        },
        statusText: 'ok',
      } as AxiosResponse);
      paymentService.request = mockRequest;

      const startDate = dayjs();
      const result = await paymentService.schedulePayment({
        id: 'mock-payment-id',
        method: 'ach',
        amount: 20000,
        payee: {
          id: 'mock-payee-id',
          name: 'Mock Test',
          email: '<EMAIL>',
          payeeType: 'User',
          bankAccount: {
            accountType: AccountType.Checking,
            accountNumber: 'testAccNo',
            routingNumber: 'testRoutNo',
          },
          mailingAddress: {
            addressLine1: '1 Main St',
            city: 'Brooklyn',
            state: 'NY',
            zip: '10010',
          },
        },
        fulfillment: {
          id: 'mock-fulfillment-id',
          paymentPattern: {
            start: startDate,
            count: 2,
            pattern: 'weekly',
            amount: 10000,
          },
          fund: {
            id: 'mock-fund-id',
          },
        },
      } as unknown as PopulatedPayment);

      expect(mockRequest).toHaveBeenCalledWith({
        url: '/v1/transactions',
        data: {
          recipient: {
            referenceId: 'mock-payee-id',
            fundingSourceReferenceId: 'mock-fund-id',
            provider: 'jpmc',
            firstName: 'Mock',
            lastName: 'Test',
            email: '<EMAIL>',
            address: {
              street: '1 Main St',
              unit: undefined,
              city: 'Brooklyn',
              state: 'NY',
              zip: '10010',
            },
          },
          payment: {
            referenceId: 'mock-payment-id',
            recipientReferenceId: 'mock-payee-id',
            fundingSourceReferenceId: 'mock-fund-id',
            amount: 20000,
            callbackUrl: 'http://mockwebhookurl/webhook/payments',
            paymentMethod: 'ach',
            provider: 'jpmc',
            accountReferenceId: 'mock-payee-id-ach',
          },
          schedule: {
            referenceId: 'mock-fulfillment-id',
            amountPerTransaction: 10000,
            numberOfPayments: 2,
            start: startDate,
            pattern: 'weekly',
            dryRun: false,
          },
        },
      });
      expect(result).toEqual({
        payload: {
          schedule: [
            { id: 'payment-1', scheduleFor: 'schedule-date' },
            { id: 'payment-2', scheduleFor: 'schedule-date' },
          ],
        },
        status: 'success',
      });
    });
    it('should create a prepaid cards schedule payload and call transaction endpoint', async () => {
      const paymentService = new PaymentService();
      const mockRequest = jest.fn<() => Promise<AxiosResponse>>().mockResolvedValue({
        status: 200,
        data: {
          transaction: {
            schedule: [
              {
                id: 'payment-1',
                scheduleFor: 'schedule-date',
              },
              {
                id: 'payment-2',
                scheduleFor: 'schedule-date',
              },
            ],
          },
        },
        statusText: 'ok',
      } as AxiosResponse);
      paymentService.request = mockRequest;

      const startDate = dayjs();
      const result = await paymentService.schedulePayment({
        id: 'mock-payment-id',
        method: 'physicalCard',
        amount: 20000,
        payee: {
          id: 'mock-payee-id',
          name: 'Mock Test',
          email: '<EMAIL>',
          payeeType: 'User',
          mailingAddress: {
            addressLine1: '1 Main St',
            city: 'Brooklyn',
            state: 'NY',
            zip: '10010',
          },
        },
        fulfillment: {
          id: 'mock-fulfillment-id',
          paymentPattern: {
            start: startDate,
            count: 2,
            pattern: 'weekly',
            amount: 10000,
          },
          fund: {
            id: 'mock-fund-id',
            partner: { id: 'mock-partner-id', name: 'mock partner' },
          },
        },
      } as unknown as PopulatedPayment);

      expect(mockRequest).toHaveBeenCalledWith({
        url: '/v1/transactions',
        data: {
          recipient: {
            referenceId: 'mock-payee-id',
            fundingSourceReferenceId: 'mock-fund-id',
            provider: 'usio',
            firstName: 'Mock',
            lastName: 'Test',
            email: '<EMAIL>',
            address: {
              street: '1 Main St',
              unit: undefined,
              city: 'Brooklyn',
              state: 'NY',
              zip: '10010',
            },
          },
          payment: {
            referenceId: 'mock-payment-id',
            recipientReferenceId: 'mock-payee-id',
            accountReferenceId: 'mock-payee-id-physicalCard',
            fundingSourceReferenceId: 'mock-fund-id',
            amount: 20000,
            callbackUrl: 'http://mockwebhookurl/webhook/payments',
            paymentMethod: 'physicalCard',
            payer: 'mock partner',
            provider: 'usio',
            firstName: 'Mock',
            lastName: 'Test',
            email: '<EMAIL>',
            address: {
              street: '1 Main St',
              unit: undefined,
              city: 'Brooklyn',
              state: 'NY',
              zip: '10010',
            },
          },
          schedule: {
            referenceId: 'mock-fulfillment-id',
            amountPerTransaction: 10000,
            numberOfPayments: 2,
            start: startDate,
            pattern: 'weekly',
            dryRun: false,
          },
        },
      });
      expect(result).toEqual({
        payload: {
          schedule: [
            { id: 'payment-1', scheduleFor: 'schedule-date' },
            { id: 'payment-2', scheduleFor: 'schedule-date' },
          ],
        },
        status: 'success',
      });
    });
  });
});
