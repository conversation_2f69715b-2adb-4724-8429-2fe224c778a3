import { Readable } from 'node:stream';
import type { Partners as Partner, PrismaClient, Users } from '@prisma/clients/platform';
import { logger } from '@utils/logger';
import { type File, type PushResponse, ReportTypes, type TaxUser } from '../types/report';
import { queryAndBatchAction } from '../utils/batch';
import { Headers_1099_Report, Query_Reports, mapUserData } from '../utils/report';
import ArchiveService from './ArchiveService';
import type { CloudStorage } from './CloudStorage';
import { FileRepository } from './FileHandler';
import type { PushService } from './PushService';

const BATCH_SIZE = 1000;
const AWARDED_AMOUNT_THRESHOLD = 60000;
type UserWithDoc = { name: string; displayId: string; documentKey: string };

export class ReportService {
  private coreClient: PrismaClient;
  private fileRepository: FileRepository;
  private cloudStorage: CloudStorage;
  private pushService: PushService;
  private BATCH_SIZE: number;

  constructor(coreClient: PrismaClient, cloudStorage: CloudStorage, pushService: PushService) {
    this.coreClient = coreClient;
    this.BATCH_SIZE = BATCH_SIZE;
    this.fileRepository = new FileRepository(cloudStorage);
    this.cloudStorage = cloudStorage;
    this.pushService = pushService;
  }

  private async downloadFiles(prefix: string, users: UserWithDoc[]): Promise<File[]> {
    logger.info(`${this.constructor.name}: downloading W9 starts (docs: ${users.length}) ...`);
    const docs = await this.cloudStorage.getFiles(
      users.map((user) => `${prefix}/${user.documentKey}`),
    );
    const files = users.map(({ name, displayId }, idx) => ({
      filename: `${name}-${displayId}-W9.pdf`,
      content: docs[idx][0],
    }));
    logger.info(`${this.constructor.name}: downloading W9 ends ...`);
    return files;
  }

  private async queryPayees({
    partnerId,
    reportYear,
    threshold = AWARDED_AMOUNT_THRESHOLD,
  }: { partnerId: string; reportYear: number; threshold?: number }): Promise<
    Array<{ payeeId: string; awardedAmount: number }>
  > {
    const payees = await this.coreClient.payments.groupBy({
      by: ['payeeId'],
      where: {
        payeeType: 'User',
        status: 'success',
        deactivatedAt: null,
        initiatedAt: {
          gte: new Date(reportYear, 0, 1),
          lte: new Date(reportYear, 11, 31),
        },
        fulfillment: {
          case: { program: { partnerId } },
        },
      },
      _sum: {
        amount: true,
      },
      having: {
        amount: {
          _sum: { gte: threshold },
        },
      },
    });
    return payees?.map((payee) => ({
      payeeId: payee.payeeId,
      awardedAmount: payee._sum.amount ?? 0,
    }));
  }

  private async queryAndMapData({
    partnerId,
    type,
    headers = Headers_1099_Report,
    reportYear,
    awardedAmountThreshold = AWARDED_AMOUNT_THRESHOLD,
  }: {
    partnerId: string;
    type: ReportTypes;
    reportYear: number;
    headers?: Array<{ field?: string; label: string }>;
    awardedAmountThreshold?: number;
  }) {
    logger.info(
      { awardedAmountThreshold },
      `${this.constructor.name}: querying data for partner (${partnerId}) for year ${reportYear} ...`,
    );
    const payees = await this.queryPayees({
      partnerId,
      reportYear,
      threshold: awardedAmountThreshold,
    });
    const totalCount = payees?.length;

    if (!totalCount) throw new Error('no data found');

    logger.info(`${this.constructor.name}: users found: ${totalCount}...`);
    const userIds = payees.map((payee) => payee.payeeId);

    const awardedAmountByPayeeId = new Map<string, number>();
    for (const payee of payees) awardedAmountByPayeeId.set(payee.payeeId, payee.awardedAmount);

    const query = Query_Reports[type](partnerId, userIds);
    const filename = `report_${type}_${reportYear}.csv`;
    const users: TaxUser[] = [];
    const writer = await this.fileRepository.getFileHandler(filename);
    writer.write(`${headers.map((each) => each.label).join(',')}\n`);

    await queryAndBatchAction<Users, { message: string }>({
      batchSize: this.BATCH_SIZE,
      totalCount: totalCount,
      query: async (skip, take) =>
        this.coreClient.users.findMany({
          where: query.where,
          include: query.include,
          skip,
          take,
          orderBy: { name: 'asc' },
        }),
      action: async (records: Users[]) => {
        logger.info(
          `${this.constructor.name}: mapping to report columns for batch (users: ${records.length}) ...`,
        );
        const mappedData = records.map((row) => mapUserData(row, awardedAmountByPayeeId));
        const rows = mappedData.map((user) =>
          headers
            .map((each) => (each.field ? String(user[each.field] || '').replace(',', '') : ''))
            .join(','),
        );
        users.push(...mappedData);

        writer.write(`${rows.join('\n')}\n`);
        return { message: 'complete' };
      },
    });

    writer.close();

    return {
      file: { content: writer.getStream(), filename: writer.getFilename() },
      users,
      count: totalCount,
    };
  }

  private getFilename({ prefix, reportYear, reportType, contentType }) {
    return `${prefix}_report_${reportType}_${reportYear}.${contentType}`;
  }

  public async create1099Report({
    partnerId,
    password,
    includeW9Files = false,
    includeReport = true,
    reportYear,
    awardedAmountThreshold,
  }: {
    partnerId: string;
    password: string;
    includeW9Files?: boolean;
    includeReport?: boolean;
    reportYear: number;
    awardedAmountThreshold?: number;
  }) {
    try {
      if (!reportYear || !password || !partnerId)
        throw new Error('Password/Report Year/Partner are required');

      const partner = await this.coreClient.partners.findFirst({ where: { id: partnerId } });
      if (!partner) throw new Error('partner not found');
      const externalId = partner.externalId;

      const links: PushResponse[] = [];

      const passwordLink = await this.pushService.pushPassword(password, externalId);
      links.push(passwordLink);

      const archiver = new ArchiveService(password);

      const { users, file: reportFile } = await this.queryAndMapData({
        type: ReportTypes.Tax_Compliance_1099,
        partnerId,
        reportYear,
        awardedAmountThreshold,
      });

      if (includeReport && reportFile) archiver.append(reportFile.content, reportFile.filename);

      if (includeW9Files) {
        const usersWithDoc = users?.filter(({ documentKey }) => !!documentKey);
        logger.info(`${this.constructor.name}: docs found: ${usersWithDoc?.length} ...`);
        if (usersWithDoc?.length) {
          await queryAndBatchAction<UserWithDoc, { message: 'success' }>({
            batchSize: this.BATCH_SIZE,
            totalCount: usersWithDoc.length,
            query: async (skip, take) => usersWithDoc.slice(skip, skip + take),
            action: async (records: UserWithDoc[]) => {
              const files = await this.downloadFiles(externalId, records);
              if (files?.length) files.map((file) => archiver.append(file.content, file.filename));
              return { message: 'success' };
            },
          });
        }
      }

      const archiveBuffer = await archiver.toBuffer();
      const filename = this.getFilename({
        prefix: externalId,
        reportType: ReportTypes.Tax_Compliance_1099,
        contentType: 'zip',
        reportYear,
      });
      const file = {
        content: Readable.from(archiveBuffer),
        filename,
        mimetype: 'application/x-zip',
      };

      const upload = await this.cloudStorage.upload({
        directory: partner.externalId,
        file,
        prefix: 'reports',
      });
      // Sign the url expires in 1 day
      const signedURL = await this.cloudStorage.getPreviewUrl({
        directory: partner.externalId,
        documentKey: upload.documentKey,
      });
      if (signedURL) links.push({ previewURL: signedURL, type: 'url' });

      return links;
    } catch (e) {
      logger.error({ error: e }, `${this.constructor.name}.create1099Report: unexpected error >`);
      throw e;
    } finally {
      this.fileRepository.cleanup();
    }
  }
}
