import { credentials } from '@grpc/grpc-js';
import { isLocal } from '@utils/env';
import type { Response } from '../types/identity';
import type { SendProviderEmailRequest, SendProviderSmsRequest } from '../types/notification';
import { HealthServer, NotificationService as NotificationServer } from '../utils/loadGrpcServers';

export default class NotificationService {
  private url: string;

  constructor() {
    const url = process.env.NOTIFICATION_SERVER_URL ?? '';
    if (!url) throw new Error('NOTIFICATION_SERVER_URL is not set');
    this.url = url;
  }

  private getClient() {
    return new NotificationServer(
      this.url,
      isLocal() ? credentials.createInsecure() : credentials.createSsl(),
    );
  }

  private getHealthClient() {
    return new HealthServer(
      this.url,
      isLocal() ? credentials.createInsecure() : credentials.createSsl(),
    );
  }

  public async sendEmail(request: SendProviderEmailRequest): Promise<Response> {
    const client = this.getClient();
    return new Promise((resolve, reject) => {
      client.sendEmail(request, (error?: Error | null, response?: Response) => {
        if (error) reject(error);
        else resolve(response as Response);
        client.close();
      });
    });
  }

  public async sendSms(request: SendProviderSmsRequest): Promise<Response> {
    const client = this.getClient();
    return new Promise((resolve, reject) => {
      client.sendSms(request, (error?: Error | null, response?: Response) => {
        if (error) reject(error);
        else resolve(response as Response);
        client.close();
      });
    });
  }
}
