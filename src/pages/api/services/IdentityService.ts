import { credentials } from '@grpc/grpc-js';
import { isLocal } from '@utils/env';
import { logger } from '@utils/logger';
import type { HealthCheckResponse } from '../types/health';
import type {
  CreateRelationshipsRequest,
  ObjectReference,
  Relation,
  Response,
} from '../types/identity';
import { HealthServer, IdentityServer } from '../utils/loadGrpcServers';

export default class IdentityService {
  private url: string;

  constructor() {
    const url = process.env.IDENTITY_SERVER_URL ?? '';
    if (!url) throw new Error('IDENTITY_SERVER_URL is not set');
    this.url = url;
  }

  private getClient() {
    return new IdentityServer(
      this.url,
      isLocal() ? credentials.createInsecure() : credentials.createSsl(),
    );
  }

  private getHealthClient() {
    return new HealthServer(
      this.url,
      isLocal() ? credentials.createInsecure() : credentials.createSsl(),
    );
  }

  public async createUser(request: {
    partnerId: string;
    roles: string[];
    email?: string;
    name?: string;
  }): Promise<Response> {
    const client = this.getClient();
    return new Promise((resolve, reject) => {
      client.createUser(request, (error?: Error | null, response?: Response) => {
        if (error) reject(error);
        else resolve(response as Response);
        client.close();
      });
    });
  }

  public async getUser(request: {
    userIdentifier: 'coreUserId' | 'identityUserId';
    coreUserId?: string;
    identityUserId?: string;
  }): Promise<Response> {
    const client = this.getClient();
    return new Promise((resolve, reject) => {
      client.getUser(request, (error?: Error | null, response?: Response) => {
        if (error) reject(error);
        else resolve(response as Response);
        client.close();
      });
    });
  }

  public async updateUser(request: {
    userIdentifier: 'coreUserId' | 'identityUserId';
    tenantId: string;
    partnerId: string;
    coreUserId?: string;
    identityUserId?: string;
    phone?: string;
    name?: string;
    email?: string;
    roles?: string[];
  }): Promise<Response> {
    const client = this.getClient();
    return new Promise((resolve, reject) => {
      logger.info({ request }, 'sending request to identity server to update user');
      client.updateUser(request, (error?: Error | null, response?: Response) => {
        if (error) reject(error);
        else resolve(response as Response);
        client.close();
      });
    });
  }

  public async lookupPermissions(request: {
    resource?: ObjectReference | null;
    subject?: ObjectReference | null;
    objectTypeFilter?: Relation;
    permission?: string;
    source?: 'resource' | 'subject';
    pagination?: { cursor?: string; take?: number };
  }): Promise<Response> {
    const client = this.getClient();
    return new Promise((resolve, reject) => {
      client.lookupPermissions(request, (error?: Error | null, response?: Response) => {
        if (error) reject(error);
        else resolve(response as Response);
        client.close();
      });
    });
  }

  public async readPortalRoles(request: {
    userIdentifier: 'coreUserId' | 'identityUserId';
    partnerId: string;
    coreUserId?: string;
    identityUserId?: string;
  }): Promise<Response> {
    const client = this.getClient();
    return new Promise((resolve, reject) => {
      client.readPortalRoles(request, (error?: Error | null, response?: Response) => {
        if (error) reject(error);
        else resolve(response as Response);
        client.close();
      });
    });
  }

  public async fixPayeeRelations(request: {
    partnerId: string;
  }): Promise<Response> {
    const client = this.getClient();
    return new Promise((resolve, reject) => {
      client.fixPayeeRelations(request, (error?: Error | null, response?: Response) => {
        if (error) reject(error);
        else resolve(response as Response);
        client.close();
      });
    });
  }

  public async fixBulkOverridePaymentsRelations(request: {
    partnerId: string;
  }): Promise<Response> {
    const client = this.getClient();
    return new Promise((resolve, reject) => {
      client.fixBulkOverridePaymentsRelations(
        request,
        (error?: Error | null, response?: Response) => {
          if (error) reject(error);
          else resolve(response as Response);
          client.close();
        },
      );
    });
  }

  public async migrate(request: { partnerId: string }): Promise<Response> {
    const client = this.getClient();
    return new Promise((resolve, reject) => {
      client.migrate(request, (error?: Error | null, response?: Response) => {
        if (error) reject(error);
        else resolve(response as Response);
        client.close();
      });
    });
  }

  public async migratePermissions(request: {
    partnerId: string;
    objectTypes: string[];
    useStream: boolean;
  }): Promise<Response> {
    const client = this.getClient();
    return new Promise((resolve, reject) => {
      client.migratePermissions(request, (error?: Error | null, response?: Response) => {
        if (error) reject(error);
        else resolve(response as Response);
        client.close();
      });
    });
  }

  public async migrateFiscalPermissions(request: {
    partnerId: string;
  }): Promise<Response> {
    const client = this.getClient();
    return new Promise((resolve, reject) => {
      client.migrateFiscalPermissions(request, (error?: Error | null, response?: Response) => {
        if (error) reject(error);
        else resolve(response as Response);
        client.close();
      });
    });
  }

  public healthCheck(): Promise<HealthCheckResponse> {
    const client = this.getHealthClient();
    return new Promise((resolve, reject) => {
      client.check({}, (error?: Error | null, response?: HealthCheckResponse) => {
        if (error) reject(error);
        else resolve(response as HealthCheckResponse);
        client.close();
      });
    });
  }

  public async upsertTenant(request: {
    partnerId: string;
    role: string;
    // biome-ignore lint/suspicious/noExplicitAny: identity options values
    options?: Record<string, any>;
  }): Promise<Response> {
    const client = this.getClient();
    return new Promise((resolve, reject) => {
      client.upsertTenant(request, (error?: Error | null, response?: Response) => {
        if (error) reject(error);
        else resolve(response as Response);
        client.close();
      });
    });
  }

  public async retrieveTenant(request: {
    tenantId: string;
  }): Promise<Response> {
    const client = this.getClient();
    return new Promise((resolve, reject) => {
      client.retrieveTenant(request, (error?: Error | null, response?: Response) => {
        if (error) reject(error);
        else resolve(response as Response);
        client.close();
      });
    });
  }

  public createRelationships(request: CreateRelationshipsRequest) {
    const client = this.getClient();
    return new Promise((resolve, reject) => {
      client.createRelationships(request, (error?: Error | null, response?: Response) => {
        if (error) reject(error);
        else resolve(response as Response);
        client.close();
      });
    });
  }
}
