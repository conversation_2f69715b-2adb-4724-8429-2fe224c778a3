import { Storage, TransferManager } from '@google-cloud/storage';
import type { UploadInput, UploadResponse } from '../types/report';

class CloudStorage {
  private bucketName: string;
  private storage: Storage;
  private transferManager: TransferManager;

  constructor() {
    this.bucketName = process.env.CLOUD_STORAGE_BUCKET ?? '';
    this.storage = new Storage();
    this.transferManager = new TransferManager(this.storage.bucket(this.bucketName));
  }

  getFile = async (source: string, destination: string) => {
    await this.storage.bucket(this.bucketName).file(source).download({ destination });
  };

  public async getPreviewUrl({
    directory,
    documentKey,
  }: { directory: string; documentKey: string }): Promise<string | undefined> {
    const bucket = this.storage.bucket(this.bucketName);
    const file = bucket.file(`${directory}/${documentKey}`);
    try {
      const [url] = await file.getSignedUrl({
        action: 'read',
        version: 'v4',
        expires: Date.now() + 24 * 60 * 60 * 1000, // 1 day
      });
      return url;
    } catch (e) {
      return undefined;
    }
  }

  public async upload({
    directory,
    prefix,
    file,
    useTimestamp = true,
  }: UploadInput): Promise<UploadResponse> {
    const bucket = this.storage.bucket(this.bucketName);

    const { filename, mimetype, encoding, content } = file;
    const documentKey = [prefix, `${useTimestamp ? `${new Date().valueOf()}-` : ''}${filename}`]
      .filter(Boolean)
      .join('/');
    const fileUpload = bucket.file(`${directory}/${documentKey}`);

    await new Promise((resolve, reject) => {
      content
        .pipe(
          fileUpload.createWriteStream({
            metadata: {
              contentEncoding: encoding,
              contentType: mimetype,
            },
          }),
        )
        .on('error', reject)
        .on('finish', resolve);
    });

    return {
      filename,
      mimetype,
      documentKey,
    };
  }

  async getFiles(sources: string[]) {
    return this.transferManager.downloadManyFiles(sources, {
      concurrencyLimit: 1000,
    });
  }
}

export { CloudStorage };
