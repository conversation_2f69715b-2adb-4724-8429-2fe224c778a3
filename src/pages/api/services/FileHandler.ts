import { closeSync, createReadStream, writeSync } from 'node:fs';
import { logger } from '@utils/logger';
const temp = require('temp');
import type { CloudStorage } from './CloudStorage';

temp.track();
export class FileHandler {
  private filename: string;
  private getFile?: (source: string, destination: string) => Promise<void>;
  private tempFile: typeof temp.OpenFile;
  private RETRY_MAX: number;

  constructor(filename: string, getFile?: (source: string, destination: string) => Promise<void>) {
    this.filename = filename;
    this.RETRY_MAX = 3;
    this.getFile = getFile;
    this.tempFile = temp.openSync();
  }

  close(): void {
    closeSync(this.tempFile.fd);
  }

  public getStream() {
    return createReadStream(this.tempFile.path);
  }

  public async retrieveFile() {
    if (this.getFile) await this.getFile(this.filename, this.tempFile.path);
  }

  public write(content: string, count = 0, retry = true): void {
    try {
      writeSync(this.tempFile.fd, content);
    } catch (error) {
      if (retry && count < this.RETRY_MAX) {
        logger.warn(
          { error },
          `Issue writing to temp file ${this.tempFile.fd}, retrying ${count + 1} >`,
        );
        this.write(content, count + 1);
      } else {
        logger.error({ error, chunk: content }, 'Unexpected error, skipping chunk >');
        return;
      }
    }
  }

  public getFilename() {
    return this.filename;
  }
}

export class FileRepository {
  private cloudService: CloudStorage;

  constructor(cloudService: CloudStorage) {
    this.cloudService = cloudService;
  }

  public async getFileHandler(filename: string, withRetrieve = false): Promise<FileHandler> {
    const handler = new FileHandler(filename, withRetrieve ? this.cloudService.getFile : undefined);
    if (withRetrieve) await handler.retrieveFile();
    return handler;
  }

  public cleanup(): void {
    temp.cleanupSync();
  }
}
