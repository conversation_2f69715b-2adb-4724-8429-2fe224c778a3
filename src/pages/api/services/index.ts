import type { PrismaClients } from '@prisma/clients';
import { CloudStorage } from './CloudStorage';
import IdentityService from './IdentityService';
import NotificationService from './NotificationService';
import PaymentService from './PaymentService';
import { PushService } from './PushService';
import { ReportService } from './ReportsService';

export interface Services {
  payments: PaymentService;
  identities: IdentityService;
  reports: ReportService;
  notifications: NotificationService;

  // Helpers
  cloudStorage: CloudStorage;
  push: PushService;
}

const build = (clients: PrismaClients): Services => {
  const payments = new PaymentService();
  const cloudStorage = new CloudStorage();
  const identities = new IdentityService();
  const notifications = new NotificationService();
  const push = new PushService();
  const reports = new ReportService(clients.platform, cloudStorage, push);

  return { cloudStorage, identities, notifications, payments, push, reports };
};

export default build;
