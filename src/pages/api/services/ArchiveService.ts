import { type Readable, Writable } from 'node:stream';
import Archiver from 'archiver';
import archiverPlugin from 'archiver-zip-encrypted';

export default class ArchiveService {
  private archiver: Archiver.Archiver;

  constructor(password: string) {
    if (!Archiver?.isRegisteredFormat('zip-encrypted'))
      Archiver?.registerFormat('zip-encrypted', archiverPlugin);

    this.archiver = Archiver.create('zip-encrypted', {
      zlib: { level: 9 },
      encryptionMethod: 'aes256',
      password,
    } as Archiver.ArchiverOptions);
    this.archiver.on('error', (err) => {
      throw err;
    });
    this.archiver.on('end', () => {
      console.log('end archive');
    });
  }

  public append(content: Readable | Buffer, name: string) {
    this.archiver.append(content, { name });
  }

  public async toBuffer() {
    const chunks: Buffer[] = [];
    const writable = new Writable();
    writable._write = (chunk: Buffer, _, callback) => {
      chunks.push(chunk);
      callback();
    };

    // pipe to writable
    this.archiver.pipe(writable);
    await this.archiver.finalize();

    // once done, concatenate chunks
    return Buffer.concat(chunks);
  }
}
