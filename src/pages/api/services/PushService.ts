import { logger } from '@utils/logger';
import axios, { AxiosError } from 'axios';
import FormData from 'form-data';
import type { PushResponse } from '../types/report';

export class PushService {
  private email: string;
  private token: string;
  private url: string;

  constructor() {
    this.email = process.env.PW_PUSH_USER_EMAIL ?? '';
    this.token = process.env.PW_PUSH_TOKEN ?? '';
    this.url = process.env.PW_PUSH_URL ?? '';
  }

  private async request({
    data,
    route,
  }: { data: FormData; route: string }): Promise<
    Pick<PushResponse, 'token' | 'daysRemaining' | 'viewsRemaining'>
  > {
    try {
      if (!this.email || !this.token || !this.url) throw new Error('no credentials found.');
      const baseUrl = new URL(this.url);

      const { data: response } = await axios.post(new URL(route, baseUrl).href, data, {
        headers: {
          ...data.getHeaders(),
          'X-User-Email': this.email,
          'X-User-Token': this.token,
        },
      });

      return {
        token: response.url_token,
        daysRemaining: response.days_remaining,
        viewsRemaining: response.views_remaining,
      };
    } catch (err) {
      let msg = (err as Error).message;
      if (err instanceof AxiosError) msg = err.response?.data?.error;
      logger.error(`Pushing to cloud has an issue => ${msg}`);
      throw err;
    }
  }

  async pushFile(
    file: Buffer | string,
    options?: {
      expireAfterViews?: number;
      expireAfterDays?: number;
      deletableByViewer?: boolean;
      filename?: string;
      contentType?: string;
      knownLength?: number;
    },
  ): Promise<PushResponse> {
    if (!file) throw new Error('no file found.');

    const {
      expireAfterViews = 5,
      expireAfterDays = 1,
      deletableByViewer = true,
      ...appendOptions
    } = options ?? {};
    const form = new FormData();

    form.append('file_push[expire_after_views]', `${expireAfterViews}`);
    form.append('file_push[expire_after_days]', `${expireAfterDays}`);
    form.append('file_push[deletable_by_viewer]', `${deletableByViewer}`);
    form.append('file_push[files][]', file, appendOptions);

    const response = await this.request({ route: 'f.json', data: form });

    return {
      ...response,
      type: 'file',
      previewURL: new URL(`en/f/${response.token}`, this.url).href,
    };
  }

  async pushPassword(
    password: string,
    passphrase: string,
    options?: {
      expireAfterViews?: number;
      expireAfterDays?: number;
      deletableByViewer?: boolean;
    },
  ): Promise<PushResponse> {
    if (!password) throw new Error('no password found.');
    const { expireAfterViews = 3, expireAfterDays = 1, deletableByViewer = true } = options ?? {};
    const form = new FormData();

    form.append('password[expire_after_views]', `${expireAfterViews}`);
    form.append('password[expire_after_days]', `${expireAfterDays}`);
    form.append('password[deletable_by_viewer]', `${deletableByViewer}`);
    form.append('password[payload]', password);
    form.append('password[passphrase]', passphrase);

    const response = await this.request({ route: 'p.json', data: form });
    return {
      ...response,
      type: 'password',
      previewURL: new URL(`en/p/${response.token}`, this.url).href,
    };
  }
}
