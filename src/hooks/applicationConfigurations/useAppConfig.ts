import { type BaseRecord, useList, useOne } from '@refinedev/core';
import type { ApplicationConfiguration } from 'types/appConfig';

export default function useAppConfig(id: string): {
  appConfig?: ApplicationConfiguration;
  programs?: Array<{
    id: string;
    name: string;
    config: BaseRecord;
    partner: { id: string; name: string };
  }>;
  isLoading: boolean;
  refetch: () => void;
} {
  const { data, isLoading, refetch } = useOne({
    resource: 'application_configurations',
    dataProviderName: 'config',
    id: id,
    meta: {
      fields: [
        'id',
        'name',
        'config',
        'partnerId',
        {
          programApplicationConfigurationsByConfigurationId: [{ nodes: ['programId'] }],
        },
      ],
    },
    queryOptions: { enabled: !!id },
  });

  const appConfig = data?.data as ApplicationConfiguration;

  const programIds =
    appConfig?.programApplicationConfigurationsByConfigurationId
      .map(({ programId }) => programId)
      .filter(Boolean) ?? [];

  const { data: programsData } = useList({
    resource: 'programs',
    dataProviderName: 'default',
    meta: { fields: [{ nodes: ['id', 'name', 'config', { partner: ['id', 'name'] }] }] },
    queryOptions: { enabled: programIds.length > 0 },
    filters: [{ field: 'id', operator: 'in', value: programIds }],
  });

  const programs = programsData?.data as Array<{
    id: string;
    name: string;
    config: BaseRecord;
    partner: { id: string; name: string };
  }>;

  return {
    appConfig,
    programs,
    isLoading,
    refetch,
  };
}
