import { useList } from '@refinedev/core';
import type { Course } from 'types/appConfig';

export default function useAffectedApplicationAnswers({
  programIds,
  course,
  questionKey,
}: { programIds: string[]; course?: Course; questionKey: string }) {
  const { data: applicationAnswers, isLoading: isApplicationAnswersLoading } = useList({
    resource: 'applicationAnswers',
    meta: { fields: [{ nodes: ['id'] }] },
    filters: [
      { field: 'deactivatedAt', operator: 'null', value: true },
      { field: 'value', operator: 'eq', value: course?.value },
      { field: 'key', operator: 'contains', value: questionKey },
      {
        field: 'version.application.case.programId',
        operator: 'in',
        value: programIds,
      },
    ],
    queryOptions: {
      enabled: !!programIds?.length && !!course,
    },
  });

  return {
    applicationAnswers,
    isApplicationAnswersLoading,
  };
}
