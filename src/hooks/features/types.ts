// TODO do we need an operator? For now, all prohibits use AND and all requires use OR
interface DependencyGroup {
  operator: 'AND' | 'OR';
  features: string[];
}

interface FeatureDependency {
  prohibits?: DependencyGroup[];
  requires?: DependencyGroup[];
}

type DependencyMap = Record<string, FeatureDependency>;

interface Feature {
  id: string;
  name: string;
  description: string;
  deps?: FeatureDependency;
}

// This is copied from core monorepo types
enum FeatureName {
  // Partner Features
  AnalyticsEmbed = 'Analytics: Embed',
  DocumentsClassify = 'Documents: Classify',
  AuthenticationBeamEmployees = 'Authentication: Beam Employees',
  AuthenticationMFA = 'Authentication: MFA',
  Tags = 'Cases: Tags',
  BulkFileActions = 'Cases: Bulk File Actions',
  ApplicantComments = 'Applicant: Comments',

  // Program Features
  ApplicationScoring = 'Application: Scoring',
  ApplicationVerification = 'Application: Verification',
  MigrationLegacyUsers = 'Migration: Legacy Users',
  PaymentsApplicants = 'Payments: Applicants',
  PaymentsCheck = 'Payments: Check',
  PaymentsClaimFunds = 'Payments: Claim Funds',
  PaymentsDirectDeposit = 'Payments: Direct Deposit',
  PaymentsExternalTracking = 'Payments: External Tracking',
  PaymentsMultiPayment = 'Payments: Multi Payment',
  PaymentsPartnerIssued = 'Payments: Partner Issued',
  PaymentsPrepaidCard = 'Payments: Prepaid Card',
  PaymentsVendors = 'Payments: Vendors',
  PaymentsZelle = 'Payments: Zelle',
  PaymentsRecurring = 'Payments: Recurring',
  ProgramsReferral = 'Programs: Referral',
  SearchAnswers = 'Search: Answers',
  TaxFormsCaseDisplay = 'TaxForms: Case Display',
  TaxFormsW9Collection = 'TaxForms: W9 Collection',
  VerificationDataLookup = 'Verification: Data Lookup',
  WorkflowCore = 'Workflow: Core',
  WorkflowExtended = 'Workflow: Extended',
  WorkflowAutoAssign = 'Workflow: Auto Assign',
  WorkflowCreateApplications = 'Workflow: Create Applications',
  WorkflowServicesAndOutcomes = 'Workflow: Services & Outcomes',
}

export { FeatureName };
export type { DependencyMap, DependencyGroup, Feature, FeatureDependency };
