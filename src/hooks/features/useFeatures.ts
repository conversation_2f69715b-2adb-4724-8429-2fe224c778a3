import { useSelect } from '@refinedev/core';
import { dependencyMap } from './dependencies';
import type { DependencyGroup, FeatureDependency } from './types';

const lookupDeps = (name: string) => dependencyMap[name];
const displayDeps = (dep: DependencyGroup) => {
  const prefix = dep.operator === 'AND' ? 'All of' : 'Any of';
  const features = dep.features.join(', ');
  return `${prefix} - ${features}`;
};
const getRelatedFeatures = (deps?: FeatureDependency) => {
  const prohibits = deps?.prohibits?.flatMap(displayDeps);
  const requires = deps?.requires?.flatMap(displayDeps);
  return { prohibits, requires };
};
const getConflicts = (enabled: string[], deps?: FeatureDependency) => {
  if (!deps) return;
  const toDisable = deps.prohibits?.flatMap((dep: DependencyGroup) =>
    dep.features.filter((feature) => enabled.includes(feature)),
  );
  const toEnable = deps.requires?.flatMap(({ operator, features }: DependencyGroup) => {
    const needEnabling = features.filter((feature) => !enabled.includes(feature));
    switch (operator) {
      case 'AND':
        return needEnabling;
      case 'OR':
        // Only check if none of the requirements are enabled
        if (!features.find((feature) => enabled.includes(feature))) return needEnabling;
        return [];
      default:
        return [];
    }
  });
  return { toDisable, toEnable };
};

const useFeatures = () => {
  const {
    queryResult: { data, isLoading },
  } = useSelect({
    resource: 'features',
    meta: {
      fields: [{ nodes: ['id', 'name', 'description', 'createdAt', 'updatedAt'] }],
    },
  });

  // biome-ignore lint/suspicious/noImplicitAnyLet: <explanation>
  let features;
  if (data?.data) {
    features = data.data.map((feature) => ({
      ...feature,
      deps: lookupDeps(feature.name),
    }));
  }

  return { features, isLoading, lookupDeps, getConflicts, getRelatedFeatures };
};

export default useFeatures;
