import { type DependencyMap, FeatureName } from './types';

const analytics: DependencyMap = { [FeatureName.AnalyticsEmbed]: {} };
const application: DependencyMap = {
  [FeatureName.ApplicationScoring]: {},
  [FeatureName.ApplicationVerification]: {},
};
const programs: DependencyMap = { [FeatureName.ProgramsReferral]: {} };
const payments: DependencyMap = {
  [FeatureName.PaymentsPartnerIssued]: {
    prohibits: [
      {
        operator: 'AND',
        features: [
          FeatureName.PaymentsClaimFunds,
          FeatureName.PaymentsExternalTracking,
          FeatureName.PaymentsZelle,
        ],
      },
    ],
  },
  [FeatureName.PaymentsClaimFunds]: {
    prohibits: [
      {
        operator: 'AND',
        features: [
          FeatureName.PaymentsVendors,
          FeatureName.PaymentsPartnerIssued,
          FeatureName.PaymentsExternalTracking,
          FeatureName.PaymentsRecurring,
        ],
      },
    ],
  },
  [FeatureName.PaymentsVendors]: {
    prohibits: [
      {
        operator: 'AND',
        features: [FeatureName.PaymentsClaimFunds],
      },
    ],
    requires: [
      {
        operator: 'OR',
        features: [
          FeatureName.PaymentsPartnerIssued,
          FeatureName.PaymentsExternalTracking,
          FeatureName.PaymentsRecurring,
        ],
      },
    ],
  },
  [FeatureName.PaymentsApplicants]: {
    requires: [
      {
        operator: 'OR',
        features: [
          FeatureName.PaymentsPartnerIssued,
          FeatureName.PaymentsClaimFunds,
          FeatureName.PaymentsExternalTracking,
        ],
      },
    ],
  },
  [FeatureName.PaymentsMultiPayment]: {
    requires: [
      {
        operator: 'OR',
        features: [FeatureName.PaymentsExternalTracking, FeatureName.PaymentsPartnerIssued],
      },
    ],
    prohibits: [
      {
        operator: 'AND',
        features: [FeatureName.PaymentsClaimFunds],
      },
    ],
  },
  [FeatureName.PaymentsExternalTracking]: {
    prohibits: [
      {
        operator: 'AND',
        features: [FeatureName.PaymentsClaimFunds, FeatureName.PaymentsPartnerIssued],
      },
    ],
  },
  [FeatureName.PaymentsCheck]: {
    requires: [
      {
        operator: 'OR',
        features: [FeatureName.PaymentsClaimFunds, FeatureName.PaymentsPartnerIssued],
      },
    ],
  },
  [FeatureName.PaymentsDirectDeposit]: {
    requires: [
      {
        operator: 'OR',
        features: [FeatureName.PaymentsClaimFunds, FeatureName.PaymentsPartnerIssued],
      },
    ],
  },
  [FeatureName.PaymentsPrepaidCard]: {
    requires: [
      {
        operator: 'OR',
        features: [FeatureName.PaymentsClaimFunds, FeatureName.PaymentsPartnerIssued],
      },
    ],
  },
  [FeatureName.PaymentsZelle]: {
    requires: [
      {
        operator: 'OR',
        features: [FeatureName.PaymentsClaimFunds, FeatureName.PaymentsPartnerIssued],
      },
    ],
  },
  [FeatureName.PaymentsRecurring]: {
    requires: [
      {
        operator: 'OR',
        features: [FeatureName.PaymentsPartnerIssued],
      },
    ],
  },
};
const taxForms: DependencyMap = {
  [FeatureName.TaxFormsW9Collection]: {},
  [FeatureName.TaxFormsCaseDisplay]: {
    requires: [{ operator: 'OR', features: [FeatureName.TaxFormsW9Collection] }],
  },
};
const verification: DependencyMap = {
  [FeatureName.VerificationDataLookup]: {
    requires: [{ operator: 'OR', features: [FeatureName.ApplicationVerification] }],
  },
};
const workflow: DependencyMap = {
  [FeatureName.WorkflowAutoAssign]: {},
  [FeatureName.WorkflowCore]: {
    prohibits: [{ operator: 'AND', features: [FeatureName.WorkflowExtended] }],
  },
  [FeatureName.WorkflowCreateApplications]: {
    prohibits: [{ operator: 'AND', features: [FeatureName.PaymentsClaimFunds] }],
  },
  [FeatureName.WorkflowExtended]: {
    prohibits: [{ operator: 'AND', features: [FeatureName.WorkflowCore] }],
  },
  [FeatureName.WorkflowServicesAndOutcomes]: {},
};

const dependencyMap = {
  ...analytics,
  ...application,
  ...programs,
  ...payments,
  ...taxForms,
  ...verification,
  ...workflow,
};

export { dependencyMap };
