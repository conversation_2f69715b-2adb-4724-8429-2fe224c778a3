import { useCustom } from '@refinedev/core';

interface useUserProps {
  userIdentifier: 'coreUserId' | 'identityUserId' | 'gcipUid';
  id: string;
}

export default function useUser(
  input: useUserProps,
): { id: string; name: string; email: string } | undefined {
  const { data: usesData } = useCustom({
    dataProviderName: 'identity',
    url: '',
    method: 'get',
    meta: {
      operation: 'usersList',
      fields: ['id', 'name', 'email'],
      variables: {
        filter: {
          value: (() => {
            switch (input.userIdentifier) {
              case 'coreUserId':
                return {
                  or: [
                    { applicants: { some: { id: { equalTo: input.id } } } },
                    { advocates: { some: { coreUserId: { equalTo: input.id } } } },
                  ],
                };
              case 'gcipUid':
                return {
                  gcipUsers: { some: { uid: { equalTo: input.id } } },
                };
              default:
                return { id: input.id };
            }
          })(),
          type: 'UserFilter',
          required: true,
        },
      },
    },
  });

  const user = usesData?.data?.[0];

  return user ? { id: user.id as string, name: user.name, email: user.email } : undefined;
}
