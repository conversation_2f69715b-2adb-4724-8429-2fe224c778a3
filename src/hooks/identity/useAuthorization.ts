import type { JsonObject, Partners } from '@prisma/clients/platform';
import { useNotification, useOne } from '@refinedev/core';
import { getConfig } from '@utils/config';
import axios, { AxiosError } from 'axios';
import {
  type CreateRelationshipRequest,
  type ObjectReference,
  type Relation,
  Role,
} from 'pages/api/types/identity';
import { useState } from 'react';

export default function useAuthorization(partnerId?: string) {
  const [isLoading, setLoading] = useState<boolean>(false);
  const { open: notify } = useNotification();

  const { data: platformData } = useOne({
    resource: 'partners',
    id: partnerId,
    queryOptions: { enabled: !!partnerId },
    meta: { fields: ['id', 'config'] },
  });
  const partner = platformData?.data as Partners;

  const resolveTenantId = (role: 'applicant' | 'advocate') => {
    const partnerConfig = partner?.config as JsonObject;
    if (partnerConfig?.identity?.[role]?.tenantId) return partnerConfig.identity[role].tenantId;
    if (role) return getConfig(`PUBLIC_${role.toUpperCase()}_TENANT_ID`);
    return getConfig('APPLICANT_TENANT_ID');
  };

  const createRelationships = async ({
    partnerId,
    relationships,
  }: { partnerId?: string; relationships: CreateRelationshipRequest[] }): Promise<boolean> => {
    try {
      setLoading(true);
      await axios.post(
        '/api/platform/identity/createRelationships',
        { partnerId, relationships },
        { headers: { 'Content-Type': 'application/json' } },
      );
      notify?.({
        message: 'Authorization relationships created successfully.',
        type: 'success',
      });
      return true;
    } catch (e) {
      const msg = e instanceof AxiosError ? e.response?.data?.error : (e as Error).message;
      notify?.({
        message: `Authorization relationships encountered an error: ${msg}`,
        type: 'error',
      });
      return false;
    } finally {
      setLoading(false);
    }
  };

  const createAdmins = async (
    users: Array<{ partnerId: string; role: string; name: string; email: string }>,
  ) => {
    try {
      setLoading(true);
      await axios.post(
        '/api/platform/identity/createUsers',
        {
          users: users.map(({ role, ...user }) => {
            const adminRole = role || Role.StandardPayment;
            return { ...user, roles: [adminRole] };
          }),
        },
        { headers: { 'Content-Type': 'application/json' } },
      );
      notify?.({
        message: 'Admins created successfully.',
        type: 'success',
      });
    } catch (e) {
      const msg = e instanceof AxiosError ? e.response?.data?.error : (e as Error).message;
      notify?.({
        message: `Admin creation encountered an error: ${msg}`,
        type: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const lookupPermissions = async (request: {
    resource?: ObjectReference | null;
    subject?: ObjectReference | null;
    objectTypeFilter?: Relation;
    permission?: string;
    source?: 'resource' | 'subject';
    pagination?: { cursor?: string; take?: number };
  }): Promise<ObjectReference[] | undefined> => {
    try {
      setLoading(true);
      const response = await axios.post('/api/platform/identity/lookupPermissions', request, {
        headers: { 'Content-Type': 'application/json' },
      });
      return response?.data?.objects;
    } catch (e) {
      const msg = e instanceof AxiosError ? e.response?.data?.error : (e as Error).message;
      notify?.({
        message: `Cannot query permissions: ${msg}, contact engineers!`,
        type: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const readPortalRoles = async ({
    userId,
    partnerId,
  }: { userId: string; partnerId: string }): Promise<Role[] | undefined> => {
    try {
      setLoading(true);
      const response = await axios.post(
        '/api/platform/identity/readPortalRoles',
        {
          partnerId: partnerId,
          coreUserId: userId,
        },
        { headers: { 'Content-Type': 'application/json' } },
      );
      return response?.data?.roles;
    } catch (e) {
      const msg = e instanceof AxiosError ? e.response?.data?.error : (e as Error).message;
      notify?.({
        message: `Cannot query admin roles: ${msg}, contact engineers!`,
        type: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const updateUser = async ({
    id,
    partnerId,
    ...props
  }: {
    partnerId: string;
    id: string;
    email?: string;
    name?: string;
    roles?: (Role | 'applicant')[];
  }): Promise<boolean> => {
    try {
      setLoading(true);
      await axios.post(
        '/api/platform/identity/updateUser',
        {
          coreUserId: id,
          partnerId,
          ...props,
        },
        {
          headers: { 'Content-Type': 'application/json' },
        },
      );
      notify?.({
        message: 'User updated',
        type: 'success',
      });
      return true;
    } catch (e) {
      const msg = e instanceof AxiosError ? e.response?.data?.error : (e as Error).message;
      notify?.({
        message: `User update encountered an error: ${msg}`,
        type: 'error',
      });
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    createRelationships,
    createAdmins,
    lookupPermissions,
    readPortalRoles,
    updateUser,
    resolveTenantId,
    isLoading,
  };
}
