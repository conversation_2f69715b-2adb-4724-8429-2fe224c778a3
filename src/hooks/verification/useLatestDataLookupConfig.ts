import { useList } from '@refinedev/core';

export default function useLatestDataLookupConfig(configId: string) {
  const { data, isLoading } = useList({
    resource: 'lookupConfigs',
    dataProviderName: 'verification',
    meta: { fields: [{ nodes: ['id', 'fields'] }] },
    queryOptions: { enabled: !!configId },
    filters: [
      { field: 'configId', operator: 'eq', value: configId },
      { field: 'deactivatedAt', operator: 'null', value: true },
    ],
    pagination: { pageSize: 1 },
    sorters: [{ field: 'createdAt', order: 'desc' }],
  });

  return { isLoading, config: data?.data?.[0] };
}
