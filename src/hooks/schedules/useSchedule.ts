import { useList } from '@refinedev/core';

export function useSchedule(referenceId: string) {
  const { data, isLoading } = useList({
    resource: 'schedules',
    dataProviderName: 'scheduler',
    meta: {
      fields: [{ nodes: ['id', 'referenceId', 'payload'] }],
    },
    filters: [
      { field: 'deactivatedAt', operator: 'null', value: true },
      { field: 'referenceId', operator: 'eq', value: referenceId },
    ],
  });

  const schedule = data?.data?.[0];
  return { schedule, isLoading };
}
