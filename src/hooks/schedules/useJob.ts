import { type BaseKey, type CrudFilters, useList } from '@refinedev/core';

export function useJob(jobId: string) {
  const { data, isLoading } = useList({
    resource: 'jobs',
    dataProviderName: 'scheduler',
    meta: {
      fields: [{ nodes: ['id', 'status', 'scheduleId', 'priority'] }],
    },
    filters: [
      { field: 'deactivatedAt', operator: 'null', value: true },
      { field: 'id', operator: 'eq', value: jobId },
    ],
  });

  const job = data?.data?.[0];
  return { job, isLoading };
}

export function useJobs(jobIds: BaseKey[], filter?: { status: string; operator?: string }) {
  const { data, isLoading } = useList({
    resource: 'jobs',
    dataProviderName: 'scheduler',
    meta: {
      fields: [{ nodes: ['id', 'status', 'scheduleId', 'priority'] }],
    },
    pagination: {
      mode: 'off',
    },
    filters: [
      { field: 'deactivatedAt', operator: 'null', value: true },
      { field: 'id', operator: 'in', value: jobIds },
      filter?.status && {
        field: 'status',
        operator: filter?.operator || 'eq',
        value: filter.status,
      },
    ].filter(Boolean) as CrudFilters,
  });

  const jobs = data?.data;
  return { jobs, isLoading };
}
