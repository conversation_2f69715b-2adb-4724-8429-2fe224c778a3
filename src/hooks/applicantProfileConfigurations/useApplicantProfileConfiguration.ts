import { useOne } from '@refinedev/core';

const useApplicantProfileConfiguration = (id: string, partnerId: string) => {
  const { data: configData, isLoading } = useOne({
    dataProviderName: 'config',
    resource: 'applicantProfileConfigurations',
    meta: {
      fields: ['id', 'applicantTypeId', 'partnerId', 'config'],
    },
    id,
    queryOptions: {
      enabled: !!id,
    },
  });

  const { data: applicantTypeData, isLoading: isApplicantTypeLoading } = useOne({
    resource: 'applicantTypes',
    meta: {
      fields: ['id', 'name'],
    },
    id: configData?.data?.applicantTypeId,
    queryOptions: {
      enabled: !!configData?.data?.applicantTypeId,
    },
  });

  const configuration = configData?.data;

  if (configuration && applicantTypeData?.data)
    configuration.applicantType = applicantTypeData.data;

  return { configuration, isLoading: isApplicantTypeLoading || isLoading };
};

export default useApplicantProfileConfiguration;
