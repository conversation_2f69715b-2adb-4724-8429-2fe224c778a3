import { type BaseKey, useCreate, useList, useUpdate } from '@refinedev/core';
import type { CommunicationChannels, NotificationType } from 'types/notification';

const useCommunicationConfiguration = (partnerId: BaseKey) => {
  const { mutate } = useUpdate();
  const { mutate: create } = useCreate();

  const { data, refetch, isLoading } = useList({
    resource: 'partner_communication_channels_configurations',
    dataProviderName: 'config',
    filters: [{ field: 'partnerId', operator: 'eq', value: partnerId }],
    meta: { fields: [{ nodes: ['id', 'config'] }] },
    queryOptions: { enabled: !!partnerId },
  });
  const configuration = data?.data?.[0];

  const onEnableNotification = async ({
    communicationChannel,
    notificationType,
    enabled,
    onSuccess,
    onSettled,
  }: {
    communicationChannel: CommunicationChannels;
    notificationType: NotificationType;
    enabled: boolean;
    onSuccess?: () => void;
    onSettled?: () => void;
  }) => {
    if (configuration?.id) {
      const currentConfig = configuration.config ?? {};
      console.log({
        ...currentConfig,
        [communicationChannel]: {
          ...currentConfig?.[communicationChannel],
          [notificationType]: enabled,
        },
      });
      await mutate(
        {
          resource: 'partner_communication_channels_configurations',
          dataProviderName: 'config',
          values: {
            config: {
              ...currentConfig,
              [communicationChannel]: {
                ...currentConfig?.[communicationChannel],
                [notificationType]: enabled,
              },
            },
          },
          id: configuration.id,
          successNotification: () => {
            return {
              message: 'Updated Partner notification config',
              type: 'success',
            };
          },
        },
        {
          onSuccess: () => {
            if (onSuccess) onSuccess();
            refetch();
          },
          onSettled,
        },
      );
    } else {
      await create(
        {
          resource: 'partner_communication_channels_configurations',
          dataProviderName: 'config',
          values: {
            partnerId,
            config: {
              [communicationChannel]: { [notificationType]: enabled },
            },
          },
          successNotification: () => {
            return {
              message: 'Updated Partner notification config',
              type: 'success',
            };
          },
        },
        {
          onSuccess,
          onSettled,
        },
      );
    }
  };
  return { configuration, isLoading, onEnableNotification };
};

export default useCommunicationConfiguration;
