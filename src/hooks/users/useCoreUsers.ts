import { type CrudFilter, useList } from '@refinedev/core';

export default function useCoreUsers({
  filters = [],
  columns = [],
  enabled,
}: { filters?: CrudFilter[]; columns?: string[]; enabled?: boolean }) {
  const {
    data: usersData,
    isLoading,
    refetch,
  } = useList({
    dataProviderName: 'default',
    resource: 'users',
    meta: {
      fields: [{ nodes: ['id', 'name', 'displayId', ...columns] }],
    },
    queryOptions: { enabled },
    filters: [{ field: 'deactivatedAt', operator: 'null', value: true }, ...filters],
    pagination: { mode: 'off' },
    liveMode: 'auto',
  });

  return {
    coreUsers: usersData?.data,
    isLoading: enabled && isLoading,
    refetch,
  };
}
