import { useOne } from '@refinedev/core';

export function useUser(userId: string) {
  const { data, isLoading } = useOne({
    dataProviderName: 'default',
    resource: 'users',
    id: userId,
    meta: {
      fields: [
        'id',
        'name',
        'email',
        'phone',
        { applicationsBySubmitterIdList: ['id'] },
        { adminsList: ['id'] },
        { partner: ['id', 'name'] },
      ],
    },
  });
  const user = data?.data;
  return { user, isLoading };
}
