import { useList } from '@refinedev/core';

export default function useUsersByEmail({
  partnerId,
  emails,
}: { partnerId: string; emails: string[] }) {
  const { data, isLoading } = useList({
    resource: 'users',
    meta: {
      fields: [{ nodes: ['id', 'email', 'name', 'legacyId'] }],
    },
    queryOptions: { enabled: !!emails?.length },
    filters: [
      { field: 'deactivatedAt', operator: 'null', value: true },
      { field: 'partnerId', operator: 'eq', value: partnerId },
      {
        field: 'email',
        operator: 'in',
        value: emails,
      },
    ],
    pagination: { mode: 'off' },
    liveMode: 'auto',
  });

  return { users: data?.data, isLoading };
}
