import { useList } from '@refinedev/core';
import { NotificationChannel } from '@utils/notificationTemplate';

export default function useEmailTypes(channel?: NotificationChannel): string[] {
  const { data } = useList({
    resource: 'notification_templates',
    meta: { fields: [{ nodes: ['type'] }] },
    filters: [
      { field: 'partnerId', operator: 'null', value: true },
      { field: 'programId', operator: 'null', value: true },
      { field: 'channel', operator: 'eq', value: channel ?? NotificationChannel.EMAIL },
    ],
    pagination: { mode: 'off' },
  });

  return (data?.data.map(({ type }) => type) ?? []).sort();
}
