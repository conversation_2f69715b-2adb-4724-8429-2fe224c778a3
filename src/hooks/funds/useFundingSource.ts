import { useSelect } from '@refinedev/core';
import { getConfig } from '@utils/config';

const useFundingSource = (referenceId?: string) => {
  const PAYMENT_CLIENT_ID = getConfig('PUBLIC_PAYMENT_CLIENT_ID');
  const {
    queryResult: { data: fundingSourcesData, isLoading },
  } = useSelect({
    dataProviderName: 'payment',
    resource: 'fundingSources',
    filters: [
      { field: 'referenceId', operator: 'eq', value: referenceId },
      { field: 'clientId', operator: 'eq', value: PAYMENT_CLIENT_ID },
    ],
    queryOptions: {
      enabled: !!referenceId && !!PAYMENT_CLIENT_ID,
    },
    meta: {
      fields: [
        {
          nodes: ['id', 'referenceId', 'provider', 'keys'],
        },
      ],
    },
  });

  const fundingSource = fundingSourcesData?.data?.[0];

  return {
    fundingSource,
    fundingSourceKeys: fundingSource?.keys ?? '{}',
    isLoading,
  };
};

export default useFundingSource;
