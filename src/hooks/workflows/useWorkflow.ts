import { useList } from '@refinedev/core';

export function useWorkflow(entityIds: string[]) {
  const { data, isLoading } = useList({
    resource: 'workflowEvents',
    meta: {
      fields: [{ nodes: ['id', 'entityId', 'entityType', { bulkOperation: ['id'] }] }],
    },
    filters: [{ field: 'entityId', operator: 'in', value: entityIds }],
  });

  const events = data?.data;
  return { events, isLoading };
}
