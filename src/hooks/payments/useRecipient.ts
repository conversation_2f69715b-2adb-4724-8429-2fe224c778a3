import { useList } from '@refinedev/core';

export function useRecipient(referenceId: string) {
  const { data, isLoading } = useList({
    resource: 'recipients',
    dataProviderName: 'payment',
    meta: {
      fields: [
        {
          nodes: [
            'id',
            'referenceId',
            {
              accountsList: ['id', 'referenceId', 'type', 'keys', 'deactivatedAt'],
            },
          ],
        },
      ],
    },
    filters: [
      { field: 'deactivatedAt', operator: 'null', value: true },
      { field: 'referenceId', operator: 'eq', value: referenceId },
    ],
  });

  const recipient = data?.data?.[0];
  return { recipient, isLoading };
}
