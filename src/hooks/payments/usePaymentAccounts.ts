import { type CrudFilter, useList } from '@refinedev/core';

export default function usePaymentAccounts({
  filters,
  enabled,
}: { filters: CrudFilter[]; enabled?: boolean }) {
  const {
    data: accountsData,
    isLoading,
    refetch,
  } = useList({
    dataProviderName: 'payment',
    resource: 'accounts',
    meta: {
      fields: [
        { nodes: ['id', 'referenceId', 'keys', 'type', { recipient: ['id', 'referenceId'] }] },
      ],
    },
    queryOptions: { enabled },
    filters: [{ field: 'deactivatedAt', operator: 'null', value: true }, ...filters],
    pagination: { mode: 'off' },
    liveMode: 'auto',
  });

  return {
    paymentAccounts: accountsData?.data,
    isLoading: enabled && isLoading,
    refetch,
  };
}
