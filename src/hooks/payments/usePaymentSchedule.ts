import { useList } from '@refinedev/core';

export function usePaymentSchedule(referenceId: string) {
  const { data, isLoading } = useList({
    resource: 'schedules',
    dataProviderName: 'payment',
    meta: {
      fields: [
        {
          nodes: [
            'id',
            'referenceId',
            'paymentMethod',
            'accountId',
            { account: ['id', 'referenceId', 'type', 'keys'] },
          ],
        },
      ],
    },
    filters: [
      { field: 'deactivatedAt', operator: 'null', value: true },
      { field: 'referenceId', operator: 'eq', value: referenceId },
    ],
  });

  const paymentSchedule = data?.data?.[0];
  return { paymentSchedule, isLoading };
}
