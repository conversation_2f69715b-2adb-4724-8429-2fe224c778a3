import { useSelect } from '@refinedev/core';

interface GlobalApplicantType {
  id: string;
  name: 'Applicant';
}

export default function useGlobalApplicantType(): {
  type?: GlobalApplicantType;
  isLoading: boolean;
} {
  const {
    queryResult: { data, isLoading },
  } = useSelect({
    resource: 'applicant_types',
    meta: { fields: [{ nodes: ['id', 'name', 'partnerId'] }] },
    filters: [{ field: 'partnerId', operator: 'null', value: true }],
  });

  return {
    isLoading,
    type: data?.data.find(({ partnerId }) => !partnerId) as GlobalApplicantType,
  };
}
