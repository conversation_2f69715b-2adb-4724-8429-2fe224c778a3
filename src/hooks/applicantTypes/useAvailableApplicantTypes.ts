import { type CrudFilter, useSelect } from '@refinedev/core';

interface ApplicantType {
  id: string;
  name: string;
}

export default function useAvailableApplicantTypes(
  partner?: {
    id?: string;
    parentId?: string;
  },
  includeGlobalTypes?: boolean,
): ApplicantType[] {
  const {
    queryResult: { data },
  } = useSelect({
    resource: 'applicant_types',
    meta: { fields: [{ nodes: ['id', 'name', 'partnerId'] }] },
    filters: [
      {
        operator: 'or',
        value: [
          includeGlobalTypes && { field: 'partnerId', operator: 'null', value: true },
          partner && {
            field: 'partnerId',
            operator: 'in',
            value: [partner?.id, partner?.parentId].filter(Boolean),
          },
        ].filter(Boolean) as CrudFilter[],
      },
    ],
    queryOptions: { enabled: !!(partner?.id || partner?.parentId || includeGlobalTypes) },
  });

  return (data?.data as ApplicantType[]) ?? [];
}
