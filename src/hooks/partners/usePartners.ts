import { type CrudFilter, useList } from '@refinedev/core';

export default function usePartners({
  filters = [],
  enabled,
}: { filters: CrudFilter[]; enabled?: boolean }) {
  const {
    data: partnersData,
    isLoading,
    refetch,
  } = useList({
    dataProviderName: 'default',
    resource: 'partners',
    meta: {
      fields: [{ nodes: ['id', 'name'] }],
    },
    queryOptions: { enabled },
    filters: filters,
    pagination: { mode: 'off' },
    liveMode: 'auto',
  });

  return {
    partners: partnersData?.data,
    isLoading: enabled && isLoading,
    refetch,
  };
}
