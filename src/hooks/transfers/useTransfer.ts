import { Jobname as <PERSON><PERSON><PERSON> } from '@prisma/clients/scheduler';
import { useSelect, useUpdate } from '@refinedev/core';

const jobMap = {
  [JobName.RecurringPayment]: 'RECURRING_PAYMENT',
  [JobName.USIODailyTransfer]: 'USIODAILY_TRANSFER',
};

const useTransfer = (jobName: JobName) => {
  const {
    queryResult: { data, isLoading },
  } = useSelect({
    dataProviderName: 'scheduler',
    resource: 'schedules',
    filters: [{ field: 'job', operator: 'eq', value: jobMap[jobName] }],
    queryOptions: {
      enabled: !!jobName,
    },
    meta: {
      fields: [
        {
          nodes: ['id', 'referenceId', 'job', 'pattern', 'start', 'payload'],
        },
      ],
    },
  });

  const { mutate } = useUpdate();
  const update = (id, values) =>
    mutate({
      dataProviderName: 'scheduler',
      resource: 'schedules',
      id,
      values,
    });

  return {
    schedules: data,
    isLoading,
    update,
  };
};

export default useTransfer;
