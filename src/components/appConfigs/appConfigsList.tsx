import { DataGrid } from '@components/data-grid/DataGrid';
import Link from '@components/navigation/Link';
import { useSelect } from '@refinedev/core';
import { EditButton, List, ShowButton, useDataGrid } from '@refinedev/mui';

interface AppConfigListProps {
  filter?: {
    programId?: string;
  };
}

export default function AppConfigList({ filter }: AppConfigListProps): JSX.Element {
  const { dataGridProps } = useDataGrid({
    resource: 'application_configurations',
    dataProviderName: 'config',
    meta: {
      fields: [
        {
          nodes: ['id', 'name', 'description', 'partnerId'],
        },
      ],
    },
    ...(filter && { syncWithLocation: false }),
  });

  const {
    options,
    queryResult: { isLoading },
  } = useSelect({
    resource: 'partners',
    hasPagination: false,
    meta: { fields: [{ nodes: ['id', 'name'] }] },
    optionLabel: 'name',
    optionValue: 'id',
  });

  const columns = [
    { field: 'id', headerName: 'Id', flex: 1 },
    {
      field: 'name',
      headerName: 'Config Name',
      minWidth: 200,
    },
    { field: 'description', headerName: 'Description', flex: 1 },
    {
      field: 'partnerId',
      headerName: 'Partner',
      type: 'singleSelect',
      headerAlign: 'center',
      align: 'center',
      flex: 0.5,
      valueOptions: options,
      renderCell: function render({ row }) {
        if (isLoading) return 'loading...';
        const partner = options.find(
          (item) => item.value?.toString() === row.partnerId?.toString(),
        );
        return <Link to={`/partners/show/${partner?.value}`}>{partner?.label}</Link>;
      },
      minWidth: 200,
    },
    {
      field: 'actions',
      headerName: 'Actions',
      renderCell: ({ row }) => (
        <>
          <ShowButton resource="application_configurations" recordItemId={row.id} hideText />
          <EditButton resource="application_configurations" recordItemId={row.id} hideText />
        </>
      ),
      align: 'center',
      headerAlign: 'center',
      minWidth: 80,
      filterable: false,
    },
  ];

  return (
    <List
      resource="application_configurations"
      title="Application Configs"
      {...(filter && { breadcrumb: false })}
    >
      <DataGrid {...dataGridProps} columns={columns} />
    </List>
  );
}
