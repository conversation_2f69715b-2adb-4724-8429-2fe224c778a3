import { <PERSON>ge, <PERSON>, <PERSON><PERSON>, Card, DataList, Dialog, Flex, Text } from '@radix-ui/themes';
import { useDuplicateKeys } from 'providers/DuplicateKeyProvider';
const LocationLevels = {
  0: 'Section',
  1: 'Question Group',
  2: 'Question',
  3: 'Field',
  4: 'Subfield',
};

const LocationColors = {
  0: 'blue',
  1: 'green',
  2: 'amber',
  3: 'mint',
  4: 'indio',
};

export const KeyWarningModal = () => {
  const { duplicates } = useDuplicateKeys();

  return (
    <Dialog.Content>
      <Dialog.Title>Potential issue with {Object.keys(duplicates).length} config keys</Dialog.Title>
      <Dialog.Description size="3" mb="2">
        The following keys are used multiple times througout the application. Please change them to
        avoid issues with reporting, application submissions, and case processing.
      </Dialog.Description>
      <Flex direction="column" mb="3" gap="2">
        {Object.keys(duplicates).map((key) => (
          <Box key={key}>
            <Card size="1">
              <Text weight="bold">{key}</Text>
              <Text> is used {duplicates[key].trace.length} times</Text>
              <DataList.Root size="1" my="3">
                {duplicates[key].trace.map((location) => (
                  <DataList.Item align="start">
                    <DataList.Label style={{ minWidth: 'auto' }}>
                      <Badge variant="surface" color={LocationColors[location.length - 1]}>
                        {LocationLevels[location.length - 1]}{' '}
                      </Badge>
                    </DataList.Label>
                    <DataList.Value>
                      {location.reduce((acc, curr, address) => {
                        return acc.concat(
                          `${address === 1 ? LocationLevels[address] : ''} ${curr} ${
                            address !== location.length - 1 ? '> ' : ''
                          }`,
                        );
                      }, '')}
                    </DataList.Value>
                  </DataList.Item>
                ))}
              </DataList.Root>
            </Card>
          </Box>
        ))}
      </Flex>
      <Flex justify="end">
        <Dialog.Close>
          <Button variant="solid">Okay</Button>
        </Dialog.Close>
      </Flex>
    </Dialog.Content>
  );
};
