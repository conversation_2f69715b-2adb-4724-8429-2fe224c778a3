import CheckboxInput from '@components/forms/CheckboxInput';
import TextInput from '@components/forms/TextInput';
import { CloseOutlined } from '@mui/icons-material';
import { CircularProgress, Drawer, IconButton, Stack, Typography } from '@mui/material';
import { type BaseRecord, useGetIdentity, useNotification, useUpdate } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import {
  decimalToDuration,
  getCourseCondition,
  getCoursesByKey,
  makeCourseConfig,
} from '@utils/appConfig/utils';
import sort from '@utils/sort';
import { isEqual } from '@utils/string';
import axios from 'axios';
import useAffectedApplicationAnswers from 'hooks/applicationConfigurations/useAffectedApplicationAnswers';
import { useEffect, useMemo, useState } from 'react';
import { FormProvider } from 'react-hook-form';
import type { Course } from 'types/appConfig';

interface EditCourseDrawerProps {
  visible: boolean;
  close: () => void;
  appConfig: BaseRecord;
  course?: Course & { duration: number };
  questionKey: string;
  onSuccess: () => void;
}

interface IEditCourseInput {
  name: string;
  duration: string;
  disabled: boolean;
}

export default function EditCourseDrawer({
  visible,
  close,
  appConfig,
  questionKey,
  onSuccess,
  course,
}: EditCourseDrawerProps) {
  const [isLoading, setLoading] = useState<boolean>(false);
  const { mutate } = useUpdate();
  const { open: notify } = useNotification();
  const { data: author } = useGetIdentity();

  const formProps = useForm<IEditCourseInput>({
    refineCoreProps: { redirect: 'show' },
  });

  const {
    saveButtonProps,
    reset,
    getValues,
    setValue,
    handleSubmit,
    setError,
    formState: { errors },
  } = formProps;

  useEffect(() => {
    setValue('name', course?.label);
    setValue('duration', decimalToDuration(course?.duration || 0));
    setValue('disabled', course?.disabled);
  }, [course, setValue]);

  const onClose = () => {
    reset();
    close();
  };

  const programIds = useMemo(
    () =>
      appConfig?.programApplicationConfigurationsByConfigurationId
        .map(({ programId }) => programId)
        .filter(Boolean) ?? [],
    [appConfig],
  );

  const { applicationAnswers, isApplicationAnswersLoading } = useAffectedApplicationAnswers({
    programIds,
    course,
    questionKey,
  });

  const onSubmit = async (e): Promise<void> => {
    setLoading(true);
    if (!appConfig.id) {
      setLoading(false);
      return;
    }
    const { name, duration, disabled } = getValues();
    const { courses: existingCourses, formula: existingFormula } =
      getCoursesByKey(appConfig.config, questionKey) ?? {};

    const existingCourseIndex = existingCourses?.findIndex(
      (existingCourse) => existingCourse.value === course?.value,
    );
    const existingFormulaIndex = existingFormula?.conditions.findIndex(
      ({ if: _if }) => _if.comparators[1].value === course?.value,
    );

    if (!existingCourseIndex || !existingCourses || !existingFormula || !existingFormulaIndex) {
      setLoading(false);
      return;
    }
    if (
      existingCourses?.length &&
      existingCourses.some(({ value }) => course?.value !== name && isEqual(value, name))
    ) {
      setError('name', {
        type: 'manual',
        message: 'This courses already exists',
      });
      setLoading(false);
      return;
    }

    const copyOfExistingCourses = structuredClone(existingCourses);
    const copyOfExistingFormula = structuredClone(existingFormula);
    copyOfExistingCourses[existingCourseIndex] = {
      value: name,
      label: name,
      disabled,
    };

    copyOfExistingFormula.conditions[existingFormulaIndex] = getCourseCondition(name, duration);

    const updatedCourses = sort(copyOfExistingCourses, (course) => course.value);
    const updatedFormula = copyOfExistingFormula;

    const updatedConfig = makeCourseConfig(
      appConfig.config,
      questionKey,
      updatedCourses,
      updatedFormula,
    );

    if (programIds?.length && applicationAnswers?.total) {
      await Promise.all(
        programIds.map((programId) => {
          axios.post(
            '/api/platform/applicationAnswers/courses',
            {
              courses: [
                {
                  course: course?.value,
                  name,
                  duration,
                  action: 'Update',
                },
              ],
              programId: programId,
              author,
              key: questionKey,
            },
            { headers: { 'Content-Type': 'application/json' } },
          );
        }),
      );
      notify?.({
        message: 'Application answers are updated successfully.',
        type: 'success',
      });
    }

    await mutate(
      {
        dataProviderName: 'config',
        resource: 'application_configurations',
        id: appConfig.id,
        values: {
          config: updatedConfig,
        },
      },
      {
        onSuccess: () => {
          onSuccess();
          onClose();
        },
      },
    );

    formProps.saveButtonProps.onClick(e);
    return saveButtonProps.onClick(e);
  };

  return (
    <Drawer
      open={visible}
      anchor="right"
      onClose={onClose}
      PaperProps={{
        sx: {
          padding: 2,
          '& .MuiCardContent-root': { overflowY: 'auto' },
          minWidth: '33%',
          width: 'auto',
        },
      }}
    >
      <Edit
        resource="application_configurations_courses"
        title={
          <>
            <Typography variant="h5" mb={1}>
              Edit Course
            </Typography>{' '}
            <Typography>
              Number of Affected Application Answers:{' '}
              {isApplicationAnswersLoading ? (
                <CircularProgress />
              ) : (
                <span>{applicationAnswers?.total}</span>
              )}
            </Typography>
          </>
        }
        saveButtonProps={{
          disabled: isLoading || saveButtonProps.disabled,
          onClick: handleSubmit(onSubmit),
        }}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={onClose} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        {course && (
          <FormProvider {...formProps}>
            <Stack component="form" gap={2} sx={{ mt: 2 }}>
              <TextInput name="name" label="Name" required />
              <TextInput
                name="duration"
                label="Duration"
                required
                pattern={/(\d*):([0-5][0-9])/}
                helperText="format: hh:mm"
              />
              {errors.duration && (
                <Typography sx={{ color: 'red' }}>
                  Duration must be hh:mm format i.e. 2:45
                </Typography>
              )}
              <CheckboxInput name="disabled" label="disabled" />
            </Stack>
          </FormProvider>
        )}
      </Edit>
    </Drawer>
  );
}
