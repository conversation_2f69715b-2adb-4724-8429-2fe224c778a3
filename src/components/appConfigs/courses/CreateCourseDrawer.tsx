import JsonForm from '@components/forms/JsonForm';
import { CloseOutlined } from '@mui/icons-material';
import { Drawer, IconButton, Stack } from '@mui/material';
import { useNotification, useUpdate } from '@refinedev/core';
import { Create } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import { getCourseCondition, getCoursesByKey, makeCourseConfig } from '@utils/appConfig/utils';
import sort from '@utils/sort';
import { isEqual } from '@utils/string';
import { useState } from 'react';
import { FormProvider } from 'react-hook-form';
import configSchema from 'schemas/appConfigs/coursesConfig.json';
import uiSchema from 'schemas/appConfigs/coursesUI.json';
import { type Course, ExpressionType } from 'types/appConfig';

export default function CreateCoursesDrawer({ visible, close, appConfig, questionKey, onSuccess }) {
  const [isLoading, setLoading] = useState<boolean>(false);
  const { open: notify } = useNotification();
  const { mutate } = useUpdate();

  const formProps = useForm({
    refineCoreProps: { redirect: 'list' },
  });

  const { saveButtonProps, resetField, getValues } = formProps;

  const onClose = () => {
    resetField('config');
    close();
  };

  const validateAndReturn = (existingCourses?: Course[]) => {
    try {
      const { config } = getValues();
      const { courses } = config ?? {};
      if (courses?.some(({ name, duration }) => !name || !duration))
        throw new Error('Course/Duration are required.');
      if (
        existingCourses?.length &&
        existingCourses.some(({ value }) =>
          courses.find(({ name }: { name: string }) => isEqual(value, name)),
        )
      )
        throw new Error('Some courses already exist.');

      return courses;
    } catch (e) {
      const msg = (e as Error).message;
      notify?.({ message: `Issue: ${msg}`, type: 'error' });
      setLoading(false);
    }
  };

  const onSubmit = async (): Promise<void> => {
    setLoading(true);
    const { courses: existingCourses, formula: existingFormula } =
      getCoursesByKey(appConfig.config, questionKey) ?? {};
    const newCourses = validateAndReturn(existingCourses);
    if (!newCourses?.length) return;

    const updatedCourses = sort(
      [
        ...(existingCourses ?? []),
        ...newCourses.map(({ name }) => ({ value: name, label: name, disabled: false })),
      ],
      (course) => course.value,
    );

    const updatedFormula = {
      conditions: [
        ...(existingFormula?.conditions ?? []),
        ...newCourses.map(({ name, duration }) => getCourseCondition(name, duration)),
      ],
      else: { type: ExpressionType.Literal, value: 0 },
      type: ExpressionType.Condition,
    };

    const updatedConfig = makeCourseConfig(
      appConfig.config,
      questionKey,
      updatedCourses,
      updatedFormula,
    );

    await mutate(
      {
        dataProviderName: 'config',
        resource: 'application_configurations',
        id: appConfig.id,
        values: {
          config: updatedConfig,
        },
      },
      {
        onSuccess: () => {
          onSuccess();
          onClose();
        },
      },
    );
  };

  return (
    <Drawer
      open={visible}
      anchor="right"
      onClose={onClose}
      PaperProps={{
        sx: {
          padding: 2,
          '& .MuiCardContent-root': { overflowY: 'auto' },
        },
      }}
    >
      <Create
        resource="application_configurations_courses"
        title={`Add Courses: ${questionKey}`}
        saveButtonProps={{
          disabled: isLoading || saveButtonProps.disabled,
          onClick: onSubmit,
        }}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={onClose} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <FormProvider {...formProps}>
          <Stack component="form" gap={1} sx={{ mt: 2 }}>
            <JsonForm name="config" schema={configSchema} uiSchema={uiSchema} />
          </Stack>
        </FormProvider>
      </Create>
    </Drawer>
  );
}
