import {
  <PERSON><PERSON>,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  List,
  ListItem,
  ListItemText,
  Typography,
} from '@mui/material';
import {
  type BaseRecord,
  useGetIdentity,
  type useModalReturnType,
  useNotification,
  useUpdate,
} from '@refinedev/core';
import { getCoursesByKey, makeCourseConfig } from '@utils/appConfig/utils';
import { isProduction } from '@utils/env';
import axios, { AxiosError } from 'axios';
import useAffectedApplicationAnswers from 'hooks/applicationConfigurations/useAffectedApplicationAnswers';
import { useMemo, useState } from 'react';
import { type Course, ExpressionType } from 'types/appConfig';

type DeleteCourseModalProps = useModalReturnType & {
  onSuccess: () => void;
  appConfig: BaseRecord;
  selectedCourse: Course;
  questionKey: string;
};

export default function DeleteCourseModal({
  onSuccess,
  appConfig,
  selectedCourse,
  questionKey,
  close,
  visible,
}: DeleteCourseModalProps): JSX.Element {
  const [isLoading, setLoading] = useState<boolean>(false);
  const { mutate } = useUpdate();
  const { open: notify } = useNotification();
  const { data: author } = useGetIdentity();

  const programIds = useMemo(
    () =>
      appConfig?.programApplicationConfigurationsByConfigurationId
        .map(({ programId }) => programId)
        .filter(Boolean) ?? [],
    [appConfig],
  );

  const { applicationAnswers, isApplicationAnswersLoading } = useAffectedApplicationAnswers({
    programIds,
    course: selectedCourse,
    questionKey,
  });

  const onAcknowledge = async () => {
    try {
      setLoading(true);
      const { courses: existingCourses, formula: existingFormula } =
        getCoursesByKey(appConfig.config, questionKey) ?? {};

      const updatedCourses = (existingCourses ?? []).filter(
        ({ value }) => value !== selectedCourse.value,
      );
      const updatedFormula = {
        conditions: (existingFormula?.conditions ?? []).filter(
          (condition) => condition.if.comparators[1].value !== selectedCourse.value,
        ),
        else: { type: ExpressionType.Literal, value: 0 },
        type: ExpressionType.Condition,
      };

      const updatedConfig = makeCourseConfig(
        appConfig.config,
        questionKey,
        updatedCourses,
        updatedFormula,
      );

      if (programIds?.length && applicationAnswers?.total) {
        await Promise.all(
          programIds.map((programId) =>
            axios.post(
              '/api/platform/applicationAnswers/courses',
              {
                courses: [{ course: selectedCourse.value, action: 'Remove' }],
                programId,
                author,
                key: questionKey,
              },
              { headers: { 'Content-Type': 'application/json' } },
            ),
          ),
        );
        notify?.({
          message: 'Application answers are deactivated successfully.',
          type: 'success',
        });
      }

      await mutate({
        dataProviderName: 'config',
        resource: 'application_configurations',
        id: appConfig.id as string,
        values: {
          config: updatedConfig,
        },
      });

      onSuccess();
      close();
    } catch (e) {
      const msg = e instanceof AxiosError ? e.response?.data?.error : (e as Error).message;
      notify?.({ message: `Issue: ${msg}`, type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={visible}
      onClose={close}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <DialogTitle>Remove a Course</DialogTitle>
      <DialogContent>
        {isProduction() && (
          <Alert severity="warning" sx={{ marginBottom: '16px' }}>
            You are in the Production environment.
          </Alert>
        )}
        <Typography>Are you sure you want to remove the course?</Typography>
        <List
          sx={{
            width: '100%',
            bgcolor: 'background.paper',
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
          }}
        >
          <ListItem>
            <ListItemText primary={selectedCourse?.value} secondary="Course" />
          </ListItem>
          <ListItem>
            <ListItemText
              primary={applicationAnswers?.total}
              secondary="Number of Affected Application Answers"
            />
          </ListItem>
        </List>
        <Alert severity="warning" sx={{ marginBottom: '16px' }}>
          Removing a course will result in deactivating application answers.
        </Alert>
      </DialogContent>
      <DialogActions>
        <Button onClick={close} disabled={isLoading || isApplicationAnswersLoading}>
          Cancel
        </Button>
        <Button
          disabled={isLoading || isApplicationAnswersLoading}
          variant="contained"
          onClick={onAcknowledge}
        >
          Delete
        </Button>
      </DialogActions>
    </Dialog>
  );
}
