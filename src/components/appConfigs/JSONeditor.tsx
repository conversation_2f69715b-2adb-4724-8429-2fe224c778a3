import { useEffect, useRef } from 'react';
import {
  type JSONEditorPropsOptional,
  type JsonEditor,
  createJSONEditor,
} from 'vanilla-jsoneditor';

// taken from react example
export default function VanillaJSONEditor(props: JSONEditorPropsOptional) {
  const refContainer = useRef<HTMLDivElement | null>(null);
  const refEditor = useRef<JsonEditor | null>(null);
  const refPrevProps = useRef<JSONEditorPropsOptional>(props);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    // create editor
    refEditor.current = createJSONEditor({
      target: refContainer.current as HTMLDivElement,
      props,
    });

    return () => {
      // destroy editor
      if (refEditor.current) {
        refEditor.current.destroy();
        refEditor.current = null;
      }
    };
  }, []);

  // update props
  useEffect(() => {
    if (refEditor.current) {
      // only pass the props that actually changed
      // since the last time to prevent syncing issues
      const changedProps = filterUnchangedProps(props, refPrevProps.current);
      refEditor.current.updateProps(changedProps);
      refPrevProps.current = props;
    }
  }, [props]);

  return (
    <div
      className="vanilla-jsoneditor-react"
      ref={refContainer}
      style={{ display: 'flex', flex: 1, height: '75%' }}
    />
  );
}

function filterUnchangedProps(
  props: JSONEditorPropsOptional,
  prevProps: JSONEditorPropsOptional,
): JSONEditorPropsOptional {
  return Object.fromEntries(
    Object.entries(props).filter(
      ([key, value]) => value !== prevProps[key as keyof JSONEditorPropsOptional],
    ),
  );
}
