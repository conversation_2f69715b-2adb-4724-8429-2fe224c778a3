import { type Config, Drawer, usePuck } from '@measured/puck';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  type SelectChangeEvent,
  Stack,
  Typography,
} from '@mui/material';
import { ComponentNameLookup, type ContentConfig } from '@utils/appConfig';
import { useState } from 'react';
import type { RootProps } from 'types/eligibilityConfig';

const COMPONENT_CATEGORY_SELECTION = 'component_category';
export const ComponentsDrawer = ({
  categories = {},
}: { categories: Config<ContentConfig, RootProps>['categories'] }) => {
  const [value, setValue] = useState(
    sessionStorage.getItem(COMPONENT_CATEGORY_SELECTION) || 'All Fields',
  );

  const handleChange = (event: SelectChangeEvent) => {
    setValue(event.target.value as string);
    sessionStorage.setItem(COMPONENT_CATEGORY_SELECTION, event.target.value as string);
  };
  const { getPermissions } = usePuck();
  const categoriesList = Object.entries(categories);
  const categoriesByTitles = {};
  for (const category of categoriesList) {
    categoriesByTitles[category[1].title || 'uncategorized'] = [
      ...(category?.[1]?.components || []),
    ];
  }

  return (
    <Drawer>
      <Box sx={{ minHeight: '80vh' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', marginBottom: '1rem' }}>
          <Box sx={{ minWidth: 120 }}>
            <FormControl fullWidth variant="standard">
              <InputLabel id="components-select-label">Component Type</InputLabel>
              <Select
                labelId="components-select-label"
                id="components-select"
                value={value}
                label="Component"
                onChange={handleChange}
              >
                <MenuItem value="All Fields">All Fields</MenuItem>
                {categoriesList.map((category, i) => (
                  <MenuItem value={category[1].title}>{category[1].title}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </Box>
        {value === 'All Fields' ? (
          <Stack spacing={2}>
            {Object.keys(categoriesByTitles).map((groupKey) => (
              <Accordion defaultExpanded key={groupKey}>
                <AccordionSummary
                  expandIcon={<ExpandMoreIcon />}
                  aria-controls="panel1-content"
                  id="panel1-header"
                >
                  <Typography component="span">{groupKey}</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Stack spacing={2}>
                    {categoriesByTitles[groupKey].map((component) => {
                      const canInsert = getPermissions({
                        type: component,
                      }).insert;

                      return (
                        <Drawer.Item key={component} name={component} isDragDisabled={!canInsert}>
                          {() => <DraggableItem name={ComponentNameLookup[component]} />}
                        </Drawer.Item>
                      );
                    })}
                  </Stack>
                </AccordionDetails>
              </Accordion>
            ))}
          </Stack>
        ) : (
          categoriesList.map((category) => (
            <Stack spacing={2}>
              {category?.[1]?.components?.map((component) => {
                const canInsert = getPermissions({
                  type: component,
                }).insert;
                const shouldShow = category[1].title === value;

                return shouldShow ? (
                  <Drawer.Item key={component} name={component} isDragDisabled={!canInsert}>
                    {() => <DraggableItem name={ComponentNameLookup[component]} />}
                  </Drawer.Item>
                ) : null;
              })}
            </Stack>
          ))
        )}
      </Box>
    </Drawer>
  );
};

/**
 * In order to add a human readable name to our components
 * we need to use what puck renders and add our custom name prop.
 * Their CSS does a lot of the heavy lifting for drag styles so we are copying verbatim for now.
 */
function DraggableItem({ name }: { name: string }) {
  return (
    <div
      className="_DrawerItem"
      data-puck-drawer-item="true"
      tab-index="0"
      role="button"
      aria-roledescription="draggable"
      aria-describedby="dnd-kit-description-2"
      aria-pressed="false"
      aria-grabbed="false"
      aria-disabled="false"
    >
      <div className="_DrawerItem-draggable_fkqfo_25">
        <div className="_DrawerItem-name_fkqfo_66">{name}</div>
        <div className="">
          <div className="_DragIcon_17p8x_1">
            {/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
            <svg viewBox="0 0 20 20" width="12" fill="currentColor">
              {/* biome-ignore lint/style/useSelfClosingElements: <explanation> */}
              <path d="M7 2a2 2 0 1 0 .001 4.001A2 2 0 0 0 7 2zm0 6a2 2 0 1 0 .001 4.001A2 2 0 0 0 7 8zm0 6a2 2 0 1 0 .001 4.001A2 2 0 0 0 7 14zm6-8a2 2 0 1 0-.001-4.001A2 2 0 0 0 13 6zm0 2a2 2 0 1 0 .001 4.001A2 2 0 0 0 13 8zm0 6a2 2 0 1 0 .001 4.001A2 2 0 0 0 13 14z"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
}
