import { GlobeIcon } from '@radix-ui/react-icons';
import { AlertDialog, Button, Flex } from '@radix-ui/themes';

export const PublishConfirmationButton = ({ onConfirmation }) => {
  return (
    <AlertDialog.Root>
      <AlertDialog.Trigger>
        <Button>
          <GlobeIcon />
          Publish
        </Button>
      </AlertDialog.Trigger>
      <AlertDialog.Content>
        <AlertDialog.Title>Are you sure?</AlertDialog.Title>
        <AlertDialog.Description size="3" mb="2">
          By publishing these changes to an active application configuration, you acknowledge and
          accept the risk of potential impacts on applicants who have already submitted
          applications.
        </AlertDialog.Description>
        <Flex justify="end" gap="3">
          <AlertDialog.Cancel>
            <Button variant="soft" color="gray">
              Cancel
            </Button>
          </AlertDialog.Cancel>
          <AlertDialog.Action>
            <Button variant="solid" onClick={onConfirmation}>
              Confirm
            </Button>
          </AlertDialog.Action>
        </Flex>
      </AlertDialog.Content>
    </AlertDialog.Root>
  );
};
