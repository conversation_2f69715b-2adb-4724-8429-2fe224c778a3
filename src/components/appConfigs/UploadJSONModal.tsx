import { Box, Button, Dialog, DialogActions, DialogContent, DialogTitle } from '@mui/material';
import { SaveButton } from '@refinedev/mui';
import { useForm } from 'react-hook-form';

export const UploadConfigModal = ({ close, visible, handleUploadSubmit }) => {
  const { register, handleSubmit } = useForm();

  const submitForm = (formData) => {
    handleUploadSubmit(formData);
  };

  return (
    <Dialog open={visible} onClose={close} PaperProps={{ sx: { minWidth: 500, minHeight: 150 } }}>
      <DialogTitle>Upload JSON config</DialogTitle>
      <DialogContent>
        <Box
          component="form"
          autoComplete="off"
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyItems: 'center',
          }}
        >
          <input
            type="file"
            {...register('file', {
              required: 'Config is required',
            })}
            accept=".json"
          />
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={close}>Cancel</Button>
        <SaveButton onClick={handleSubmit(submitForm)} />
      </DialogActions>
    </Dialog>
  );
};
