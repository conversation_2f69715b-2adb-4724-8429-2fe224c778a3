import { DataGrid } from '@components/data-grid/DataGrid';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import { IconButton } from '@mui/material';
import { useList, useModal } from '@refinedev/core';
import { List } from '@refinedev/mui';
import { useState } from 'react';
import type { ApplicationConfiguration } from 'types/appConfig';
import DeleteParticipantModal from './DeleteParticipantModal';
import EditParticipantDrawer from './EditParticipantDrawer';

interface ParticipantListProps {
  appConfig: ApplicationConfiguration;
}

export default function ParticipantList({ appConfig }: ParticipantListProps): JSX.Element {
  const [selectedParticipant, setSelectedParticipant] = useState();
  const editDrawer = useModal();
  const deleteDrawer = useModal();

  const participants = appConfig?.config?.participants ?? [];
  const applicantTypeIds = participants
    ?.map(({ applicantTypeId }) => applicantTypeId)
    .filter(Boolean);

  const { data: platformData } = useList({
    resource: 'applicantTypes',
    queryOptions: { enabled: applicantTypeIds?.length > 0 },
    filters: [{ field: 'id', operator: 'in', value: applicantTypeIds }],
    meta: { fields: [{ nodes: ['id', 'name'] }] },
  });

  const columns = [
    {
      field: 'name',
      headerName: 'Name',
      minWidth: 200,
    },
    { field: 'email', headerName: 'Email', flex: 1 },
    {
      field: 'applicantTypeId',
      headerName: 'Applicant Type',
      sortable: false,
      renderCell: ({ row }) => {
        const type = platformData?.data?.find(({ id }) => id === row.applicantTypeId);
        return type?.name;
      },
      align: 'center',
      headerAlign: 'center',
      minWidth: 200,
    },
    {
      field: 'actions',
      headerName: 'Actions',
      renderCell: ({ row }) => (
        <>
          <IconButton
            onClick={() => {
              editDrawer.show();
              setSelectedParticipant(row);
            }}
            color="primary"
          >
            <EditIcon fontSize="small" />
          </IconButton>
          <IconButton
            onClick={() => {
              deleteDrawer.show();
              setSelectedParticipant(row);
            }}
            color="primary"
          >
            <DeleteIcon fontSize="small" color="error" />
          </IconButton>
        </>
      ),
      align: 'center',
      headerAlign: 'center',
      minWidth: 80,
      filterable: false,
    },
  ];

  return (
    <>
      <List
        resource="application_configurations"
        title="Participants"
        breadcrumb={false}
        createButtonProps={{
          onClick: () => {
            editDrawer.show();
          },
        }}
      >
        <DataGrid
          rows={participants ?? []}
          columns={columns}
          autoHeight
          getRowId={(row) => JSON.stringify(row)}
        />
      </List>
      {editDrawer.visible && (
        <EditParticipantDrawer
          {...editDrawer}
          appConfig={appConfig}
          selectedParticipant={selectedParticipant}
          close={() => {
            editDrawer.close();
            setSelectedParticipant(undefined);
          }}
        />
      )}
      {deleteDrawer.visible && !!selectedParticipant && (
        <DeleteParticipantModal
          {...deleteDrawer}
          appConfig={appConfig}
          selectedParticipant={selectedParticipant}
          close={() => {
            deleteDrawer.close();
            setSelectedParticipant(undefined);
          }}
        />
      )}
    </>
  );
}
