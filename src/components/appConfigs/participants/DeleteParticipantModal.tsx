import {
  Al<PERSON>,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  List,
  ListItem,
  ListItemText,
  Typography,
} from '@mui/material';
import { useUpdate } from '@refinedev/core';
import { isProduction } from '@utils/env';
import type { ApplicationConfiguration, Participant } from 'types/appConfig';
import { isSame } from './EditParticipantDrawer';

export default function DeleteParticipantModal({
  visible,
  close,
  appConfig,
  selectedParticipant,
}: {
  visible: boolean;
  close: () => void;
  selectedParticipant: Participant;
  appConfig: ApplicationConfiguration;
}): JSX.Element {
  const { mutate } = useUpdate();

  const onAcknowledge = async () => {
    const participants = appConfig?.config?.participants ? [...appConfig.config.participants] : [];
    const existingIdx = participants?.findIndex((row: Participant) =>
      isSame(row, selectedParticipant),
    );
    if (existingIdx < 0) return;

    participants.splice(existingIdx, 1);
    return mutate(
      {
        dataProviderName: 'config',
        resource: 'application_configurations',
        id: appConfig.id,
        values: {
          config: {
            ...(appConfig?.config ?? {}),
            participants,
          },
        },
      },
      {
        onSuccess: () => {
          close();
        },
      },
    );
  };

  return (
    <Dialog open={visible} onClose={close}>
      <DialogTitle>Remove the participant</DialogTitle>
      <DialogContent>
        {isProduction() && (
          <Alert severity="warning" sx={{ marginBottom: '16px' }}>
            You are in the Production environment.
          </Alert>
        )}
        <Typography>Are you sure you want to Remove this participant?</Typography>
        <List
          sx={{
            width: '100%',
            bgcolor: 'background.paper',
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
          }}
        >
          <ListItem>
            <ListItemText primary={selectedParticipant?.name} secondary="Name" />
          </ListItem>
          <ListItem>
            <ListItemText primary={selectedParticipant?.email} secondary="Email" />
          </ListItem>
        </List>
      </DialogContent>
      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Button onClick={close}>Cancel</Button>
        <Button disabled={!selectedParticipant} variant="contained" onClick={onAcknowledge}>
          Remove
        </Button>
      </DialogActions>
    </Dialog>
  );
}
