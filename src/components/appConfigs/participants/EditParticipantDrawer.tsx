import SelectInput from '@components/forms/SelectInput';
import TextInput from '@components/forms/TextInput';
import { CloseOutlined } from '@mui/icons-material';
import { Alert, Drawer, IconButton, Stack } from '@mui/material';
import { useNotification, useOne, useUpdate } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import { defaultAppConfig } from '@utils/appConfig';
import useAvailableApplicantTypes from 'hooks/applicantTypes/useAvailableApplicantTypes';
import { useEffect, useState } from 'react';
import { FormProvider } from 'react-hook-form';
import type { ApplicationConfiguration, Participant } from 'types/appConfig';

interface EditParticipantDrawerProps {
  visible: boolean;
  close: () => void;
  selectedParticipant?: Participant;
  appConfig: ApplicationConfiguration;
}

export const isSame = (a: Participant, b: Participant) =>
  ['name', 'email', 'applicantTypeId'].every((key) => a[key] === b[key]);

export default function EditParticipantDrawer({
  visible,
  close,
  selectedParticipant,
  appConfig,
}: EditParticipantDrawerProps) {
  const [isLoading, setLoading] = useState<boolean>(false);
  const { mutate } = useUpdate();
  const { open: notify } = useNotification();

  const { data: platformData } = useOne({
    resource: 'partners',
    id: appConfig?.partnerId,
    queryOptions: { enabled: !!appConfig?.partnerId },
    meta: { fields: ['id', 'parentId'] },
  });
  const partner = platformData?.data as { id: string; parentId?: string };
  const existingParticipants = appConfig?.config?.participants ?? [];

  const types = useAvailableApplicantTypes(partner, true);

  const formProps = useForm<Participant>({
    refineCoreProps: { redirect: 'show' },
  });

  const { saveButtonProps, reset, getValues, setValue, handleSubmit, setError } = formProps;

  useEffect(() => {
    setValue('name', selectedParticipant?.name);
    setValue('email', selectedParticipant?.email);
    setValue('applicantTypeId', selectedParticipant?.applicantTypeId);
  }, [selectedParticipant, setValue]);

  const onClose = () => {
    reset();
    close();
  };

  const onUpdate = (participants) => {
    if (!appConfig) throw new Error('config not defined.');
    return mutate(
      {
        dataProviderName: 'config',
        resource: 'application_configurations',
        id: appConfig.id,
        values: {
          config: { ...(appConfig.config ?? defaultAppConfig), participants },
        },
      },
      {
        onSuccess: onClose,
      },
    );
  };

  const validate = (participants: Participant[], participant: Participant) => {
    if (participants?.some((row) => row?.applicantTypeId === participant?.applicantTypeId)) {
      setError('applicantTypeId', {
        type: 'manual',
        message: 'This type already exists.',
      });
      return false;
    }
    return true;
  };

  const onUpdateParticipant = (values: Participant) => {
    if (!selectedParticipant) return;
    const participants_ = [...existingParticipants];
    const idx = participants_.findIndex((row) => isSame(row, selectedParticipant));
    if (idx < 0) throw new Error('Can not find the existing row.');
    participants_[idx] = values;
    return onUpdate(participants_);
  };

  const onCreateParticipant = (values: Participant) => {
    const participants_ = [...existingParticipants];
    if (!validate(participants_, values)) return;
    participants_.push(values);
    return onUpdate(participants_);
  };

  const onSubmit = async (): Promise<void> => {
    try {
      setLoading(true);
      const newParticipant = getValues() as Participant;
      if (selectedParticipant) {
        await onUpdateParticipant(newParticipant);
        return;
      }
      await onCreateParticipant(newParticipant);
    } catch (err) {
      notify?.({ message: 'Can not save the participant', type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Drawer
      open={visible}
      anchor="right"
      onClose={onClose}
      PaperProps={{
        sx: {
          padding: 2,
          maxWidth: '500px',
        },
      }}
    >
      <Edit
        resource="application_configurations"
        title={selectedParticipant ? 'Edit Participant' : 'Create Participant'}
        saveButtonProps={{
          disabled: isLoading || saveButtonProps.disabled,
          onClick: handleSubmit(onSubmit),
        }}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={onClose} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <FormProvider {...formProps}>
          <Stack component="form" gap={2} sx={{ mt: 2 }}>
            <TextInput
              name="name"
              label="Name"
              helperText="Question's key refer to participant's name"
              required
            />
            <TextInput
              name="email"
              label="Email"
              helperText="Question's key refer to participant's email"
              required
            />
            <SelectInput
              name="applicantTypeId"
              label="Applicant Type"
              options={types ?? []}
              required
              disabled={!!selectedParticipant}
            />
            <Alert severity="warning">
              Please be aware that editing a participant may affect the application's linking
              process. You need to ensure that the selected applicant type is available in all
              programs utilizing this configuration.
            </Alert>
          </Stack>
        </FormProvider>
      </Edit>
    </Drawer>
  );
}
