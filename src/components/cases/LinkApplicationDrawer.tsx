import CloseOutlined from '@mui/icons-material/CloseOutlined';
import { Autocomplete, Drawer, IconButton, TextField } from '@mui/material';
import { useUpdate } from '@refinedev/core';
import { Edit, useAutocomplete } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import dayjs from 'dayjs';
import { useEffect } from 'react';
import { Controller, FormProvider } from 'react-hook-form';

export default function LinkApplicationDrawer({ visible, close, case: case_ }): JSX.Element {
  const {
    id: caseId,
    program: { id: programId },
  } = case_;
  const formProps = useForm({
    refineCoreProps: {
      action: 'edit',
      resource: 'cases',
      redirect: false,
      id: undefined,
      meta: { fields: ['id'] },
    },
  });
  const {
    control,
    formState: { errors },
    getValues,
    handleSubmit,
    refineCore,
    reset,
    saveButtonProps,
  } = formProps;

  const { autocompleteProps } = useAutocomplete({
    resource: 'cases',
    meta: {
      fields: [
        {
          nodes: [
            'id',
            'name',
            { applicationsList: ['id'] },
            { fulfillmentsList: ['id', { paymentsList: ['id'] }] },
          ],
        },
      ],
    },
    onSearch: (value) => [{ field: 'name', operator: 'contains', value }],
    filters: [
      { field: 'deactivatedAt', operator: 'null', value: true },
      { field: 'programId', operator: 'eq', value: programId },
      { field: 'id', operator: 'ne', value: caseId },
    ],
  });

  const { mutate } = useUpdate();

  const linkingCaseId = getValues('caseId');
  useEffect(() => {
    if (linkingCaseId) refineCore.setId(linkingCaseId);
  }, [linkingCaseId, refineCore.setId]);

  const onSubmit = async (e): Promise<void> => {
    const linkingCase = autocompleteProps.options.find((c) => c.id === linkingCaseId);
    const now = dayjs().utc().format();

    await mutate({
      resource: 'cases',
      id: caseId,
      values: {
        applications: {
          connectById: { id: linkingCase.applicationsList[0].id },
        },
      },
    });

    await mutate({
      resource: 'cases',
      id: linkingCaseId,
      values: { deactivatedAt: now },
    });

    // We don't actually expect to need this for recurring or multiple payments, but
    // handle it just in case for now.
    await Promise.all(
      linkingCase.fulfillmentsList.flatMap(({ id: fulfillmentId, paymentsList }) =>
        paymentsList.map(({ id }) =>
          mutate({
            resource: 'payments',
            id,
            values: {
              deactivatedAt: now,
              fulfillment: {
                id: fulfillmentId,
                deactivatedAt: now,
              },
            },
          }),
        ),
      ),
    );

    close();
    reset();
  };

  return (
    <Drawer
      open={visible}
      onClose={close}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Edit
        resource="cases"
        title="Link Multi-Party Application"
        saveButtonProps={{ disabled: saveButtonProps.disabled, onClick: handleSubmit(onSubmit) }}
        isLoading={false}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <FormProvider {...formProps}>
          <Controller
            control={control}
            rules={{ required: 'This field is required' }}
            name="caseId"
            defaultValue={null}
            render={({ field }) => (
              <Autocomplete
                {...autocompleteProps}
                {...field}
                onChange={(_, value) => field.onChange(value?.id ?? value)}
                getOptionLabel={(option) =>
                  autocompleteProps?.options?.find((c) => c.id === (option?.id ?? option))?.name ??
                  ''
                }
                getOptionDisabled={(c) => c.applicationsList.length > 1}
                isOptionEqualToValue={(c, value) =>
                  value === undefined || c.id === (value?.id ?? value)
                }
                renderInput={(props) => (
                  <TextField
                    {...props}
                    label="Application to Link"
                    margin="normal"
                    variant="outlined"
                    error={!!errors?.id}
                    helperText={errors?.id?.message as string}
                    required
                  />
                )}
              />
            )}
          />
        </FormProvider>
      </Edit>
    </Drawer>
  );
}
