import HiddenInput from '@components/forms/HiddenInput';
import SelectInput from '@components/forms/SelectInput';
import CloseOutlined from '@mui/icons-material/CloseOutlined';
import { <PERSON><PERSON>, <PERSON><PERSON>T<PERSON>le, Drawer, IconButton, Stack } from '@mui/material';
import { Edit } from '@refinedev/mui';
import type { UseModalFormReturnType } from '@refinedev/react-hook-form';
import { CaseStatuses } from '@utils/statuses';
import { FormProvider } from 'react-hook-form';

export default function ChangeStatusDrawer(props: UseModalFormReturnType) {
  const {
    modal: { visible, close },
    refineCore: { formLoading },
    saveButtonProps,
  } = props;
  return (
    <Drawer
      open={visible}
      onClose={close}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Edit
        resource="cases"
        saveButtonProps={saveButtonProps}
        isLoading={formLoading}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <FormProvider {...props}>
          <Stack component="form" autoComplete="off" gap={1}>
            <SelectInput
              name="status"
              label="Status"
              options={Object.entries(CaseStatuses).map(([id, name]) => ({ id, name }))}
            />
            <HiddenInput name="statusUpdatedAt" value={new Date().toISOString()} />
            <Alert severity="warning">
              <AlertTitle>For Special Circumstances Only</AlertTitle>
              This tool is only intended for use in special cases that are not supported according
              to normal rules. Standard changes should be made in the platform. Be aware that
              certain case and payment statuses are incompatible, and may cause unexpected results.
            </Alert>
          </Stack>
        </FormProvider>
      </Edit>
    </Drawer>
  );
}
