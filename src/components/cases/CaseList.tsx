import { DataGrid } from '@components/data-grid/DataGrid';
import Link from '@components/navigation/Link';
import { FormControl, MenuItem, Select, TextField } from '@mui/material';
import { type GridFilterInputValueProps, getGridStringOperators } from '@mui/x-data-grid';
import type { CrudFilters } from '@refinedev/core';
import { List, ShowButton, useDataGrid } from '@refinedev/mui';
import { displayDate } from '@utils/date';
import { displayId } from '@utils/id';
import { CaseStatuses } from '@utils/statuses';

interface CaseListProps {
  filter?: {
    ids?: string[];
    applicantId?: string;
    programId?: string;
  };
}

function EnumSelectFilter(props: GridFilterInputValueProps) {
  const { item, applyValue } = props;

  const handleFilterChange = (event) => {
    applyValue({ ...item, value: event.target.value });
  };

  return (
    <FormControl fullWidth sx={{ marginTop: 'auto' }}>
      <Select
        value={item.value || ''}
        onChange={handleFilterChange}
        displayEmpty
        size="small"
        variant="standard"
      >
        <MenuItem value="">All</MenuItem>
        {Object.entries(CaseStatuses).map(([key, value]) => (
          <MenuItem key={key} value={key}>
            {value}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
}

const statusFilterOperators = getGridStringOperators()
  .filter((operator) => operator.value === 'equals')
  .map((operator) => ({
    ...operator,
    InputComponent: EnumSelectFilter,
  }));

const columns = [
  {
    field: 'id',
    headerName: 'Id',
    flex: 1,
    filterOperators: getGridStringOperators().filter((operator) => operator.value === 'equals'),
  },
  {
    field: 'program.name',
    headerName: 'Program',
    flex: 1,
    valueGetter: ({ row }) => row.program,
    renderCell: ({ value }) => <Link to={`/programs/show/${value.id}`}>{value.name}</Link>,
    sortable: false,
  },
  {
    field: 'displayId',
    headerName: 'Display Id',
    renderCell: ({ value }) => displayId('C', value),
    filterOperators: getGridStringOperators()
      .filter((operator) => operator.value === 'equals')
      .map((operator) => ({
        ...operator,
        InputComponent: (props: GridFilterInputValueProps) => {
          const { item, applyValue } = props;

          const handleFilterChange = (event) => {
            applyValue({ ...item, value: event.target.value.replace(/[cC]/g, '') });
          };

          return (
            <FormControl fullWidth sx={{ marginTop: 'auto' }}>
              <TextField
                value={item.value || ''}
                onChange={handleFilterChange}
                size="small"
                variant="standard"
              />
            </FormControl>
          );
        },
      })),
    flex: 1,
  },
  {
    field: 'name',
    headerName: 'Applicant',
    flex: 1,
    valueGetter: ({ row }) => row.applicationsList?.[0]?.submitter,
    renderCell: ({ value }) => <Link to={`/users/coreUsers/show/${value?.id}`}>{value?.name}</Link>,
  },
  {
    field: 'status',
    headerName: 'Status',
    flex: 1,
    filterOperators: statusFilterOperators,
  },
  {
    field: 'createdAt',
    headerName: 'Created At',
    flex: 1,
    renderCell: ({ value }) => displayDate(value),
    filterable: false,
  },
  {
    field: 'applicationsMaxSubmittedAt',
    headerName: 'Submitted At',
    flex: 1,
    valueGetter: ({ row }) => row.applicationsList?.[0]?.submittedAt,
    renderCell: ({ value }) => displayDate(value),
    filterable: false,
  },
  {
    field: 'decisionReachedAt',
    headerName: 'Decision Reached',
    flex: 1,
    renderCell: ({ value }) => displayDate(value),
    filterable: false,
  },
  {
    field: 'actions',
    headerName: 'Actions',
    sortable: false,
    renderCell: ({ row }) => <ShowButton resource="cases" recordItemId={row.id} />,
    align: 'center',
    headerAlign: 'center',
    filterable: false,
  },
];

export default function CaseList({ filter }: CaseListProps): JSX.Element {
  const { dataGridProps } = useDataGrid({
    resource: 'cases',
    meta: {
      fields: [
        {
          nodes: [
            'id',
            'displayId',
            'status',
            'createdAt',
            'decisionReachedAt',
            {
              applicationsList: ['id', 'submittedAt', { submitter: ['id', 'name'] }],
            },
            { program: ['id', 'name'] },
          ],
        },
      ],
    },
    sorters: {
      initial: [{ field: 'createdAt', order: 'desc' }],
    },
    filters: {
      defaultBehavior: 'replace',
      permanent: [
        { field: 'deactivatedAt', operator: 'null', value: true },
        filter?.ids && { field: 'id', operator: 'in', value: filter.ids },
        filter?.applicantId && {
          field: 'applications.some.submitterId',
          operation: 'eq',
          value: filter.applicantId,
        },
        filter?.programId && { field: 'programId', operation: 'eq', value: filter.programId },
      ].filter(Boolean) as unknown as CrudFilters,
    },
    ...(filter && { syncWithLocation: false }),
  });

  return (
    <List resource="cases" title="Cases" {...(filter && { breadcrumb: false })}>
      <DataGrid {...dataGridProps} columns={columns} />
    </List>
  );
}
