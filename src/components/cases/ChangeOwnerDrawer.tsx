import CloseOutlined from '@mui/icons-material/CloseOutlined';
import {
  <PERSON>ert,
  AlertTitle,
  Autocomplete,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Drawer,
  IconButton,
  TextField,
  Typography,
} from '@mui/material';
import { useList, useModal, useUpdate } from '@refinedev/core';
import type { ConditionalFilter, LogicalFilter } from '@refinedev/core';
import { Edit, useAutocomplete } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import { displayIdToInt, formatDisplayId } from '@utils/displayId';
import isNum from '@utils/isNum';
import { useState } from 'react';
import { Controller, FormProvider } from 'react-hook-form';

export default function ChangeOwnerDrawer({ visible, close, case: case_, onSuccess }): JSX.Element {
  const [searchInput, setSearchInput] = useState('');
  const {
    id: caseId,
    program: { partnerId },
    applicationsList,
  } = case_;
  const originalSubmitter = applicationsList?.[0]?.submitter;
  const applicationIds = applicationsList?.map(({ id }) => id);

  const formProps = useForm({
    refineCoreProps: {
      action: 'edit',
      resource: 'cases',
      redirect: false,
      id: undefined,
      meta: { fields: ['id'] },
    },
  });
  const {
    handleSubmit,
    reset,
    saveButtonProps,
    getValues,
    control,
    formState: { errors },
    watch,
  } = formProps;

  const { data: paymentsData } = useList({
    resource: 'payments',
    meta: { fields: [{ nodes: ['id'] }] },
    filters: [
      { field: 'deactivatedAt', operator: 'null', value: true },
      { field: 'fulfillment.caseId', operator: 'eq', value: caseId },
      { field: 'payeeId', operator: 'eq', value: originalSubmitter.id },
    ],
  });
  const originalPayments = paymentsData?.data ?? [];

  const { data: versionsData } = useList({
    resource: 'applicationVersions',
    meta: { fields: [{ nodes: ['id'] }] },
    filters: [
      { field: 'deactivatedAt', operator: 'null', value: true },
      { field: 'applicationId', operator: 'in', value: applicationIds },
      { field: 'creatorId', operator: 'eq', value: originalSubmitter.id },
    ],
  });
  const originalVersions = versionsData?.data ?? [];

  const { data: documentsData } = useList({
    resource: 'applicationDocuments',
    meta: { fields: [{ nodes: ['id', { document: ['id'] }] }] },
    filters: [
      { field: 'deactivatedAt', operator: 'null', value: true },
      { field: 'applicationId', operator: 'in', value: applicationIds },
      { field: 'document.uploaderId', operator: 'eq', value: originalSubmitter.id },
    ],
  });
  const originalDocuments = documentsData?.data ?? [];

  const { mutate } = useUpdate();

  const onSubmit = async (): Promise<void> => {
    const { submitter } = getValues();

    if (originalPayments?.length)
      await Promise.all(
        originalPayments.map(({ id }) =>
          mutate({
            resource: 'payments',
            id: id as string,
            values: {
              payeeId: submitter.id,
            },
          }),
        ),
      );
    if (originalVersions?.length)
      await Promise.all(
        originalVersions.map(({ id }) =>
          mutate({
            resource: 'applicationVersions',
            id: id as string,
            values: {
              creatorId: submitter.id,
            },
          }),
        ),
      );
    if (originalDocuments?.length)
      await Promise.all(
        originalDocuments.map(({ document }) =>
          mutate({
            resource: 'documents',
            id: document.id as string,
            values: {
              uploaderId: submitter.id,
            },
          }),
        ),
      );
    await Promise.all(
      applicationsList.map(({ id }) =>
        mutate({
          resource: 'applications',
          id: id as string,
          values: {
            submitterId: submitter.id,
          },
        }),
      ),
    );

    await mutate(
      {
        resource: 'cases',
        id: caseId as string,
        values: {
          name: submitter.name,
        },
      },
      {
        onSuccess,
      },
    );

    close();
    reset();
  };

  const onSearchUser = (value: string): ConditionalFilter[] => {
    setSearchInput(value);

    if (value.length < 3) return [];

    const filters: LogicalFilter[] = [{ field: 'name', operator: 'contains', value }];

    const formattedDisplayId = displayIdToInt(value);
    if (isNum(`${formattedDisplayId}`))
      filters.push({
        field: 'displayId',
        operator: 'eq',
        value: formattedDisplayId,
      } as LogicalFilter);

    return [{ operator: 'or', value: filters }];
  };

  const { autocompleteProps } = useAutocomplete({
    resource: 'users',
    meta: {
      fields: [
        {
          nodes: [
            'id',
            'name',
            'displayId',
            {
              applicationsBySubmitterIdList: [
                'id',
                {
                  case: ['id', { fulfillmentsList: ['id', { paymentsList: ['id'] }] }],
                },
              ],
            },
          ],
        },
      ],
    },
    onSearch: onSearchUser,
    filters: [
      { field: 'deactivatedAt', operator: 'null', value: true },
      { field: 'partnerId', operator: 'eq', value: partnerId },
      { field: 'id', operator: 'ne', value: originalSubmitter.id },
      { field: 'adminsExist', operator: 'eq', value: false },
      { field: 'applicantProfile.applicantType.name', operator: 'eq', value: 'Applicant' },
    ],
    queryOptions: {
      /**
       *  By default, the autocomplete will trigger a fetch and load all users attached to a program.
       *  This crashes the page if there are too many users.
       */
      enabled: searchInput.length >= 3,
    },
  });

  const newOwner = watch('submitter');

  const confirmationDialog = useModal();

  return (
    <Drawer
      open={visible}
      onClose={close}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Edit
        resource="cases"
        title="Change Ownership"
        saveButtonProps={{
          disabled: saveButtonProps.disabled,
          onClick: handleSubmit(() => confirmationDialog.show()),
        }}
        isLoading={false}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <Alert severity="warning">
          <AlertTitle>For Special Circumstances Only</AlertTitle>
          This tool is only intended for use in special cases. Be aware that by changing ownership,
          this case and related applications and payments will be under a new applicant.
        </Alert>
        <FormProvider {...formProps}>
          <Controller
            control={control}
            rules={{ required: 'This field is required' }}
            name="submitter"
            defaultValue={null}
            render={({ field }) => (
              <Autocomplete
                {...autocompleteProps}
                {...field}
                onChange={(_, value) => field.onChange(value)}
                getOptionLabel={(option) => {
                  const selectedOption = autocompleteProps?.options?.find(
                    (c) => c.id === (option?.id ?? option),
                  );
                  return selectedOption
                    ? `${selectedOption.name} - ${formatDisplayId(selectedOption.displayId, 'U')}`
                    : '';
                }}
                isOptionEqualToValue={(c, value) =>
                  value === undefined || c.id === (value?.id ?? value)
                }
                renderInput={(props) => (
                  <TextField
                    {...props}
                    label="New Owner"
                    margin="normal"
                    variant="outlined"
                    error={!!errors?.id}
                    helperText={errors?.id?.message as string}
                    required
                  />
                )}
              />
            )}
          />
        </FormProvider>
      </Edit>
      <Dialog
        open={confirmationDialog.visible}
        onClose={confirmationDialog.close}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle>Is this information correct?</DialogTitle>
        <DialogContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Typography>Please review the content bellow and confirm to proceed.</Typography>
          {newOwner && (
            <>
              <Alert severity="info">
                <AlertTitle>Case: {case_.name}</AlertTitle>
                Original Case has {applicationsList.length} application(s) and{' '}
                {originalPayments.length} payment(s).
              </Alert>
              <Alert severity="info">
                <AlertTitle>Change to: {newOwner.name}</AlertTitle>
                New Owner has {newOwner.applicationsBySubmitterIdList.length} application(s) and{' '}
                {
                  newOwner.applicationsBySubmitterIdList.flatMap((app) =>
                    app.case.fulfillmentsList.flatMap((f) => f.paymentsList),
                  ).length
                }{' '}
                payment(s).
              </Alert>
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              confirmationDialog.close();
            }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              confirmationDialog.close();
              onSubmit();
            }}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </Drawer>
  );
}
