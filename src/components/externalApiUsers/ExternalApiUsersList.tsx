import { DataGrid } from '@components/data-grid/DataGrid';
import { useList } from '@refinedev/core';
import { DeleteButton, List, useDataGrid } from '@refinedev/mui';

export const ExternalApiUsersList = () => {
  const { dataGridProps } = useDataGrid({
    dataProviderName: 'identity',
    resource: 'external_api_users',
    meta: {
      fields: [{ nodes: ['id', 'appId', 'partnerId', { user: ['email'] }] }],
    },
  });

  const partnerIds = dataGridProps.rows.map(({ partnerId }) => partnerId).filter(Boolean);
  const { data: platformData } = useList({
    dataProviderName: 'default',
    resource: 'partners',
    queryOptions: { enabled: partnerIds.length > 0 },
    filters: [{ field: 'id', operator: 'in', value: partnerIds }],
    meta: { fields: [{ nodes: ['id', 'name'] }] },
  });

  const columns = [
    { field: 'id', headerName: 'Id', flex: 1 },
    {
      field: 'appId',
      headerName: 'App ID',
      minWidth: 400,
    },
    {
      field: 'user.email',
      valueGetter: (params) => params.row?.user.email,
      headerName: 'User',
      minWidth: 300,
      filter: 'agTextColumnFilter',
      sortable: false,
    },
    {
      field: 'partnerId',
      headerName: 'Partner',
      sortable: false,
      valueGetter: ({ row }) => {
        const partner = platformData?.data.find(({ id }) => id === row.partnerId);
        return partner?.name;
      },
      minWidth: 200,
      filter: 'agTextColumnFilter',
    },
    {
      field: 'actions',
      headerName: 'Actions',
      sortable: false,
      renderCell: function render({ row }) {
        return (
          <>
            <DeleteButton
              hideText
              dataProviderName="identity"
              resource="external_api_users"
              recordItemId={row.id}
              confirmTitle="This action cannot be undone. Are you sure you want to delete External Api User?"
            />
          </>
        );
      },
      align: 'center',
      headerAlign: 'center',
      minWidth: 80,
    },
  ];

  return (
    <List resource="external_api_users" title="External API Users" {...{ breadcrumb: false }}>
      <DataGrid {...dataGridProps} columns={columns} />
    </List>
  );
};
