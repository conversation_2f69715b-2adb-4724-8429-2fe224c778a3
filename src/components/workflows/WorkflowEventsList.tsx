import { DataGrid } from '@components/data-grid/DataGrid';
import type { BaseKey, CrudFilters } from '@refinedev/core';
import { ShowButton } from '@refinedev/mui';
import { List, useDataGrid } from '@refinedev/mui';
import { displayDate } from '@utils/date';
import { useMemo } from 'react';

interface WorkflowEventsListProps {
  filter?: {
    entityId?: BaseKey;
    entityType?: string;
  };
}

export default function WorkflowEventsList({ filter }: WorkflowEventsListProps): JSX.Element {
  const { dataGridProps } = useDataGrid({
    resource: 'workflowEvents',
    meta: {
      fields: [
        {
          nodes: [
            'id',
            'entityId',
            'entityType',
            'action',
            'previousValue',
            'newValue',
            'details',
            'createdAt',
            { user: ['id', 'email', 'name'] },
            {
              admin: ['id', { user: ['id', 'name'] }],
            },
            { bulkOperation: ['id', 'operation', 'status'] },
          ],
        },
      ],
    },
    sorters: {
      initial: [{ field: 'createdAt', order: 'desc' }],
    },
    filters: {
      defaultBehavior: 'replace',
      permanent: [
        {
          field: 'deactivatedAt',
          operator: 'null' as const,
          value: true,
        },
        filter?.entityId && { field: 'entityId', operator: 'eq' as const, value: filter.entityId },
        filter?.entityType && {
          field: 'entityType',
          operator: 'eq' as const,
          value: filter.entityType,
        },
      ].filter(Boolean) as CrudFilters,
    },
    ...(filter && { syncWithLocation: false }),
  });

  const columns = useMemo(
    () => [
      { field: 'id', headerName: 'Id', flex: 1 },
      {
        field: 'action',
        headerName: 'Action',
        maxWidth: 250,
        flex: 1,
      },
      {
        field: 'user',
        headerName: 'Author',
        valueGetter: (params) => params.row.user?.name || params.row.user?.email || 'System',
        flex: 1,
        maxWidth: 300,
      },
      {
        field: 'previousValue',
        headerName: 'Previous Value',
        sortable: false,
        flex: 1,
      },
      {
        field: 'newValue',
        headerName: 'New Value',
        sortable: false,
        flex: 1,
      },
      {
        field: 'createdAt',
        headerName: 'Created At',
        valueGetter: (params) => displayDate(params.row.createdAt),
        minWidth: 200,
      },
      {
        field: 'actions',
        headerName: 'Actions',
        sortable: false,
        renderCell: ({ row }) => <ShowButton resource="workflow_events" recordItemId={row.id} />,
        align: 'center',
        headerAlign: 'center',
      },
    ],
    [],
  );

  return (
    <>
      <List resource="workflowEvents" title="Workflow Events" breadcrumb={false}>
        <DataGrid {...dataGridProps} columns={columns} />
      </List>
    </>
  );
}
