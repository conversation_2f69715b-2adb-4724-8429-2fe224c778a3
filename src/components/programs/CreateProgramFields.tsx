import AutocompleteInput from '@components/forms/AutocompleteInput';
import HiddenInput from '@components/forms/HiddenInput';
import JsonForm from '@components/forms/JsonForm';
import SelectInput from '@components/forms/SelectInput';
import TextInput from '@components/forms/TextInput';
import BrandingImageInput from '@components/forms/images/BrandingImageInput';
import ImageUploadModal from '@components/forms/images/ImageUploadModal';
import DataLookupFields from '@components/verification/DataLookupFields';
import { Add, Delete } from '@mui/icons-material';
import { Alert, Button, Stack, TextField, Typography } from '@mui/material';
import { useModal } from '@refinedev/core';
import useAvailableApplicantTypes from 'hooks/applicantTypes/useAvailableApplicantTypes';
import useGlobalApplicantType from 'hooks/applicantTypes/useGlobalApplicantType';
import { useCallback, useEffect, useMemo } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import configSchema from 'schemas/programs/config.json';
import uiSchema from 'schemas/programs/ui.json';
import { v4 as uuidv4 } from 'uuid';

export interface ProgramFieldsProps {
  id?: string;
  funds: { id: string; name: string }[];
  prefix?: string;
  partner: { id?: string; externalId: string; parentId?: string };
  additionalApplicantTypes?: { id: string; name: string }[];
}

const DEFAULT_VERIFICATION_CONFIG = {
  service: 'DATA_LOOKUP',
  lookupConfigs: { create: { fields: [] } },
};

export default function CreateProgramFields({
  id,
  funds,
  prefix,
  partner,
  additionalApplicantTypes,
}: ProgramFieldsProps): JSX.Element {
  const brandingModal = useModal();
  const programId = useMemo(() => id ?? uuidv4(), [id]);
  const fieldName = useCallback(
    (name: string): string => [prefix, name].filter(Boolean).join('.'),
    [prefix],
  );
  const { externalId: partnerExternalId } = partner;

  const { setValue, watch } = useFormContext();
  const { fields, append, remove } = useFieldArray({
    name: fieldName('programApplicantTypes.create'),
    rules: { minLength: 1 },
  });

  const { type: globalApplicantType } = useGlobalApplicantType();

  useEffect(() => {
    if (fields.length === 0 && globalApplicantType)
      setValue(fieldName('programApplicantTypes.create'), [
        { applicantTypeId: globalApplicantType.id },
      ]);
  }, [fieldName, fields, globalApplicantType, setValue]);

  const existingApplicantTypes = useAvailableApplicantTypes(partner);
  const availableApplicantTypes = [...existingApplicantTypes, ...(additionalApplicantTypes ?? [])];

  const selectedApplicantTypeIds = (watch(fieldName('programApplicantTypes.create')) ?? []).map(
    ({ applicantTypeId }) => applicantTypeId,
  );
  const selectedApplicantTypes = [
    ...(globalApplicantType ? [globalApplicantType] : []),
    ...availableApplicantTypes,
  ].filter((t) => selectedApplicantTypeIds.includes(t.id));

  return (
    <>
      <HiddenInput name={fieldName('id')} value={programId} />
      <TextInput name={fieldName('name')} label="Name" required />
      <BrandingImageInput
        name={fieldName('heroImage')}
        label="Hero Image"
        openModal={(): void => brandingModal.show()}
      />
      <SelectInput
        name={fieldName('programFunds.create')}
        label="Funds"
        options={funds}
        multiple
        transformers={{ from: (fundId: string) => ({ fundId }), id: ({ fundId }) => fundId }}
      />
      <AutocompleteInput
        name={fieldName('programFeatures.create')}
        label="Features"
        resource="features"
        multiple
        transformers={{
          from: (option: { id: string } | { featureId: string; enabled: boolean }) =>
            'featureId' in option ? option : { featureId: option.id, enabled: true },
          id: (value: { featureId: string; enabled: boolean }) => value.featureId,
        }}
        required
      />
      <Typography variant="h6" sx={{ marginBottom: '8px' }}>
        Applicant Types
      </Typography>
      <Alert severity="info">
        Application configurations must be migrated to the DB in order to get complete multiparty
        support. If you are trying to create a multiparty program and do not see the config you're
        looking for in the list, please alert engineering. If you are creating a
        single-party/standard program, feel free to use the Application Configuration field in the
        config and ignore this section for now.
      </Alert>
      {(fields as { id: string; applicantTypeId: string }[]).map((type, idx) => (
        <Stack key={type.id} gap={1} direction="row" alignItems="flex-start">
          {type.applicantTypeId === globalApplicantType?.id ? (
            <>
              <HiddenInput
                name={fieldName(`programApplicantTypes.create.${idx}.applicantTypeId`)}
              />
              <TextField
                label="Applicant Type"
                value={globalApplicantType.name}
                fullWidth
                disabled
              />
            </>
          ) : (
            <SelectInput
              name={fieldName(`programApplicantTypes.create.${idx}.applicantTypeId`)}
              label="Applicant Type"
              options={availableApplicantTypes}
              required
            />
          )}
          <TextInput
            name={fieldName(`programApplicantTypes.create.${idx}.nameOverride`)}
            label="Name Override"
          />
          <AutocompleteInput
            name={fieldName(`programApplicantTypes.create.${idx}.configurationId`)}
            label="Application Configuration"
            dataProviderName="config"
            resource="application_configurations"
            filters={[
              {
                operator: 'or',
                value: [
                  { field: 'partnerId', operator: 'null', value: true },
                  {
                    field: 'partnerId',
                    operator: 'in',
                    value: [partner.id, partner.parentId].filter(Boolean),
                  },
                ],
              },
            ]}
            // TODO: Make required once all configs are migrated to the DB
            required={false}
          />
          <Button
            size="small"
            variant="text"
            startIcon={<Delete />}
            onClick={() => remove(idx)}
            disabled={type.applicantTypeId === globalApplicantType?.id}
            sx={{ height: '56px', marginLeft: '4px' }}
          >
            Remove
          </Button>
        </Stack>
      ))}
      <Button
        variant="outlined"
        startIcon={<Add />}
        onClick={() => append({})}
        sx={{ alignSelf: 'flex-start' }}
      >
        Add
      </Button>

      <Typography variant="h5" sx={{ marginTop: '16px' }}>
        Config
      </Typography>
      <JsonForm name={fieldName('config')} schema={configSchema} uiSchema={uiSchema} />
      <Typography variant="h5">Verification</Typography>
      {watch(fieldName('verificationConfiguration')) ? (
        <>
          {selectedApplicantTypes.length === 1 ? (
            <HiddenInput
              name={fieldName('verificationConfiguration.applicantTypeId')}
              value={globalApplicantType?.id}
            />
          ) : (
            <SelectInput
              name={fieldName('verificationConfiguration.applicantTypeId')}
              label="Applicant Type"
              options={selectedApplicantTypes}
              required
            />
          )}
          <DataLookupFields
            name={fieldName('verificationConfiguration.lookupConfigs.create.fields')}
          />
        </>
      ) : (
        <Button
          variant="outlined"
          startIcon={<Add />}
          onClick={() =>
            setValue(fieldName('verificationConfiguration'), DEFAULT_VERIFICATION_CONFIG)
          }
          sx={{ alignSelf: 'flex-start' }}
        >
          Add Data Lookup Configuration
        </Button>
      )}

      <ImageUploadModal
        partnerExternalId={partnerExternalId}
        fieldToUpdate={fieldName('heroImage')}
        fieldLabel="Hero Image"
        imageFilename={`program-${programId}`}
        title="Upload"
        modalProps={brandingModal}
      />
    </>
  );
}
