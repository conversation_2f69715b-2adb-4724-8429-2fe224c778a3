import { useGo } from '@refinedev/core';
import { List, ShowButton, useDataGrid } from '@refinedev/mui';
import { DataGrid } from '../data-grid/DataGrid';

const columns = [
  {
    field: 'id',
    headerName: 'ID',
    minWidth: 150,
  },
  {
    field: 'name',
    headerName: 'Name',
    minWidth: 500,
  },
  {
    field: 'actions',
    headerName: 'Actions',
    renderCell: ({ row }) => <ShowButton resource="programs" recordItemId={row.id} />,
    align: 'center',
    headerAlign: 'center',
    minWidth: 80,
    filterable: false,
  },
];

export const PartnerProgramList = ({ partnerId }) => {
  const { dataGridProps } = useDataGrid({
    resource: 'programs',
    meta: {
      fields: [
        {
          nodes: ['id', 'name'],
        },
      ],
    },
    filters: {
      defaultBehavior: 'replace',
      permanent: [{ field: 'partnerId', operator: 'eq', value: partnerId }],
    },
    syncWithLocation: false,
  });
  const go = useGo();

  return (
    <>
      <List
        resource="programs"
        breadcrumb={false}
        createButtonProps={{ onClick: () => go({ to: '/programs/create', query: { partnerId } }) }}
      >
        <DataGrid {...dataGridProps} columns={columns} autoHeight syncWithLocation={false} />
      </List>
    </>
  );
};
