import { extractError } from '@components/forms/utils';
import { <PERSON>, Stack, TextField, Typography } from '@mui/material';
import type { ReactNode } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import Preview from './Preview';

interface MarkdownEditorProps {
  label: string;
  name: string;
  instructions?: ReactNode;
  required?: boolean;
  preview?: boolean;
}

export default function MarkdownEditor({
  label,
  name,
  instructions,
  required = false,
  preview = true,
}: MarkdownEditorProps): JSX.Element {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  // TODO: Add rich editing/formatting help?
  return (
    <Controller
      control={control}
      name={name}
      rules={{ ...(required && { required: 'This field is required' }) }}
      defaultValue=""
      render={({ field }) => (
        <Stack spacing={2}>
          <Typography variant="subtitle2">
            NOTE: this field accepts{' '}
            <Link target="_blank" href="https://commonmark.org/help/">
              markdown syntax
            </Link>{' '}
            for formatting. Please use backslashes ("\") for empty lines.
          </Typography>
          {instructions}
          <TextField
            {...field}
            label={label}
            multiline
            rows={10}
            sx={{ width: '100%' }}
            error={!!extractError(errors, name)}
            helperText={extractError(errors, name)?.message}
          />
          {field?.value && preview && <Preview title="Preview">{field.value}</Preview>}
        </Stack>
      )}
    />
  );
}
