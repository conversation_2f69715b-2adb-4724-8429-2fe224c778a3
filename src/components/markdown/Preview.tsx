import { Box, Typography } from '@mui/material';
import type { ReactNode } from 'react';
import Markdown from './Markdown';

export default function Preview({ children, title }: { children: string; title?: ReactNode }) {
  return (
    <>
      {title && <Typography variant="subtitle2">{title}</Typography>}
      <Box
        component="span"
        sx={{ display: 'block', p: 1, m: 1, border: '1px solid', borderRadius: 2 }}
      >
        <Markdown>{children}</Markdown>
      </Box>
    </>
  );
}
