import { Link, type SxProps, Typography } from '@mui/material';
import ReactMarkdown from 'react-markdown';

type Alignment = 'left' | 'center' | 'right';
type Tag = 'h1' | 'h2' | 'h3' | 'p' | 'span' | 'dt' | 'dd' | 'div' | 'li';

interface MarkdownProps {
  children: string;
  alignment?: Alignment;
  tag?: Tag;
  sx?: SxProps;
}

export default function Markdown({ children, tag, ...props }: MarkdownProps) {
  return (
    <ReactMarkdown
      components={{
        p: ({ children }) => <Typography {...props}>{children}</Typography>,
        a: ({ children, href }) => (
          <Link
            href={href}
            component="a"
            target="_blank"
            rel={href?.startsWith('mailto:') ? 'noopener noreferrer' : undefined}
          >
            {children}
          </Link>
        ),
        ol: ({ children }) => <ol>{children}</ol>,
        ul: ({ children }) => <ul>{children}</ul>,
        li: ({ children }) => <li>{children}</li>,
      }}
    >
      {children}
    </ReactMarkdown>
  );
}
