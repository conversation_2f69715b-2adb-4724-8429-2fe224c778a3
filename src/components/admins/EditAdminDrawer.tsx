import HiddenInput from '@components/forms/HiddenInput';
import SelectInput from '@components/forms/SelectInput';
import TextInput from '@components/forms/TextInput';
import { CloseOutlined } from '@mui/icons-material';
import { Drawer, IconButton, Stack } from '@mui/material';
import { Edit } from '@refinedev/mui';
import useAuthorization from 'hooks/identity/useAuthorization';
import { useEffect } from 'react';
import { FormProvider } from 'react-hook-form';
import { Role_Display } from './CreateAdminDrawer';

export const EditAdminDrawer = (props) => {
  const {
    watch,
    setValue,
    reset,
    onSuccess,
    partnerId,
    saveButtonProps,
    modal: { visible, close },
  } = props;

  const { isLoading: isIdentityLoading, readPortalRoles, updateUser } = useAuthorization();

  const onSubmit = async (): Promise<void> => {
    const formData = props.getValues('user');
    const success = await updateUser({ partnerId, ...formData });
    if (success) {
      await onSuccess();
      reset();
      close();
    }
  };

  const adminUser = watch('user');

  useEffect(() => {
    if (adminUser?.id && !adminUser?.roles?.length) {
      readPortalRoles({
        userId: adminUser.id,
        partnerId,
      }).then((roles) => {
        if (roles?.length) setValue('user.roles', roles);
      });
    }
  }, [adminUser, partnerId, setValue, readPortalRoles]);

  return (
    <Drawer
      open={visible}
      onClose={close}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Edit
        resource="admins"
        saveButtonProps={{
          ...saveButtonProps,
          loading: saveButtonProps.loading || isIdentityLoading,
          disabled: saveButtonProps.disabled || isIdentityLoading,
          onClick: onSubmit,
        }}
        isLoading={isIdentityLoading}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <FormProvider {...props}>
          <Stack component="form" autoComplete="off" gap={1}>
            <HiddenInput name="user.id" />
            <TextInput name="user.name" label="Name" required />
            <TextInput name="user.email" label="Email" type="email" required />
            <SelectInput
              name="user.roles"
              label="Roles"
              multiple
              options={Object.keys(Role_Display).map((role) => ({
                id: role,
                name: Role_Display[role].name,
                secondaryText: Role_Display[role].description,
              }))}
            />
          </Stack>
        </FormProvider>
      </Edit>
    </Drawer>
  );
};
