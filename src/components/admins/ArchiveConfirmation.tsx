import HiddenInput from '@components/forms/HiddenInput';
import { Alert, Button, Dialog, DialogActions, DialogContent, DialogTitle } from '@mui/material';
import { FormProvider } from 'react-hook-form';

export default function ArchiveConfirmation(props) {
  const {
    saveButtonProps,
    modal: { visible, close },
    refineCore,
  } = props;

  const admin = refineCore?.queryResult?.data?.data;
  return (
    <Dialog
      open={visible}
      onClose={close}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <DialogTitle>
        Are you sure you want to {admin?.archivedAt ? 'unArchive' : 'archive'} the admin,{' '}
        {admin?.user?.name ?? 'N/A'}?
      </DialogTitle>
      <DialogContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        {!admin?.archivedAt && (
          <Alert severity="warning">Archiving will remove admin's access to partner portal.</Alert>
        )}
        <FormProvider {...props}>
          <HiddenInput
            name="archivedAt"
            value={(admin?.archivedAt ? null : new Date().toISOString()) as string}
          />
        </FormProvider>
      </DialogContent>
      <DialogActions>
        <Button
          onClick={() => {
            close();
          }}
        >
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={() => {
            saveButtonProps.onClick();
            close();
          }}
        >
          Confirm
        </Button>
      </DialogActions>
    </Dialog>
  );
}
