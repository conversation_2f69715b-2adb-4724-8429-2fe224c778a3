import { CheckCircleOutline, WarningAmber } from '@mui/icons-material';
import ArchiveIcon from '@mui/icons-material/Archive';
import { Chip, Tooltip } from '@mui/material';
import { EditButton, List, useDataGrid } from '@refinedev/mui';
import { useModalForm } from '@refinedev/react-hook-form';
import { useMemo } from 'react';
import { DataGrid } from '../data-grid/DataGrid';
import ArchiveConfirmation from './ArchiveConfirmation';
import { CreateAdminDrawer } from './CreateAdminDrawer';
import { EditAdminDrawer } from './EditAdminDrawer';

export const PartnerAdminList = ({ partnerId }) => {
  const { dataGridProps: usersDataGridProps, tableQueryResult } = useDataGrid({
    resource: 'admins',
    meta: {
      fields: [
        {
          nodes: [
            'id',
            'archivedAt',
            {
              user: ['id', 'name', 'email', 'validatedEmail', 'partnerId'],
            },
          ],
        },
      ],
    },
    filters: {
      defaultBehavior: 'merge',
      permanent: [{ field: 'user.partnerId', operator: 'eq', value: partnerId }],
    },
    syncWithLocation: false,
  });

  // MUI DataGrid can handle 1 filter max, so we manually filter by partnerId in case
  // other filters are set by the user. See:
  // https://refine.dev/docs/ui-integrations/material-ui/hooks/use-data-grid/#filtering
  const filteredRows = useMemo(
    () => usersDataGridProps.rows.filter((row) => row?.user?.partnerId === partnerId),
    [partnerId, usersDataGridProps],
  );

  const createDrawerFormProps = useModalForm({
    warnWhenUnsavedChanges: false,
    syncWithLocation: false,
    refineCoreProps: { resource: 'admins', action: 'create', redirect: false },
  });

  const editDrawerFormProps = useModalForm({
    warnWhenUnsavedChanges: false,
    syncWithLocation: false,
    refineCoreProps: {
      resource: 'admins',
      action: 'edit',
      redirect: false,
      meta: {
        fields: ['id', { user: ['id', 'name', 'email'] }],
      },
    },
  });

  const archiveDrawerFormProps = useModalForm({
    warnWhenUnsavedChanges: false,
    syncWithLocation: false,
    refineCoreProps: {
      resource: 'admins',
      action: 'edit',
      redirect: false,
      onMutationSuccess: () => {
        tableQueryResult.refetch();
      },
      meta: {
        fields: ['id', 'archivedAt', { user: ['id', 'name'] }],
      },
    },
  });

  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      minWidth: 150,
    },
    {
      field: 'user.name',
      headerName: 'Name',
      minWidth: 150,
      valueGetter: (params) => params.row?.user.name,
    },
    {
      field: 'user.email',
      headerName: 'Email',
      minWidth: 250,
      valueGetter: (params) => params.row?.user.email,
    },
    {
      field: 'user.validatedEmail',
      headerName: 'Validated Email',
      valueGetter: (params) => params.row?.user?.validatedEmail,
      renderCell: function render({ value }) {
        return (
          <>{value ? <CheckCircleOutline color="success" /> : <WarningAmber color="warning" />}</>
        );
      },
    },
    {
      field: 'user.archivedAt',
      headerName: 'Archived',
      minWidth: 200,
      renderCell: function render({ row }) {
        return (
          <>
            {row.archivedAt && (
              <Tooltip title={row.archivedAt}>
                <Chip variant="outlined" color="warning" size="small" label="archived" />
              </Tooltip>
            )}
          </>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      sortable: false,
      minWidth: 250,
      renderCell: function render({ row }) {
        return (
          <>
            <EditButton
              key={`edit-${row.id}`}
              resource="admins"
              recordItemId={row.id}
              onClick={() => {
                editDrawerFormProps.refineCore.setId(row.id);
                editDrawerFormProps.modal.show(row.id);
              }}
              hideText
            />
            <EditButton
              key={`archive-${row.id}`}
              resource="admins"
              recordItemId={row.id}
              svgIconProps={{
                component: ArchiveIcon,
              }}
              onClick={() => {
                archiveDrawerFormProps.refineCore.setId(row.id);
                archiveDrawerFormProps.modal.show(row.id);
              }}
              hideText
            />
          </>
        );
      },
      align: 'center',
      headerAlign: 'center',
    },
  ];

  return (
    <>
      <List
        resource={'admins'}
        breadcrumb={false}
        createButtonProps={{
          onClick: () => createDrawerFormProps.modal.show(),
        }}
      >
        <DataGrid
          {...usersDataGridProps}
          rows={filteredRows}
          rowCount={filteredRows.length}
          columns={columns}
          autoHeight
          syncWithLocation={false}
        />
      </List>
      <CreateAdminDrawer
        {...createDrawerFormProps}
        partnerId={partnerId}
        onSuccess={() => tableQueryResult.refetch()}
      />
      <EditAdminDrawer
        {...editDrawerFormProps}
        partnerId={partnerId}
        onSuccess={() => tableQueryResult.refetch()}
      />
      <ArchiveConfirmation {...archiveDrawerFormProps} />
    </>
  );
};
