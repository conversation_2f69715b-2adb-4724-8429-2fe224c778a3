import HiddenInput from '@components/forms/HiddenInput';
import SelectInput from '@components/forms/SelectInput';
import TextInput from '@components/forms/TextInput';
import { CloseOutlined } from '@mui/icons-material';
import { Drawer, IconButton, Stack } from '@mui/material';
import { useNotification } from '@refinedev/core';
import { Create } from '@refinedev/mui';
import axios, { AxiosError } from 'axios';
import { Role } from 'pages/api/types/identity';
import { useState } from 'react';
import { FormProvider } from 'react-hook-form';

export const Role_Display = {
  [Role.Manager]: {
    name: 'Admin',
    description:
      'Has full access to manage teams, assign roles, send payments and perform all actions available to standard users.',
  },
  [Role.StandardPayment]: {
    name: 'Standard With Payment',
    description: 'Can review and manage cases, reassign them, and initiate payments.',
  },
  [Role.Standard]: {
    name: 'Standard',
    description:
      'Can review and manage cases, including reassigning them, but cannot initiate payments',
  },
  [Role.ViewOnly]: {
    name: 'View Only',
    description: 'Can view cases but cannot make changes.',
  },
};

export const CreateAdminDrawer = (props) => {
  const {
    saveButtonProps,
    modal: { visible, close },
    partnerId,
    onSuccess,
    reset,
  } = props;

  const { open: notify } = useNotification();
  const [identityLoading, setIdentityLoading] = useState(false);

  const onSubmit = async (): Promise<void> => {
    try {
      setIdentityLoading(true);
      const formData = props.getValues()?.user?.create;
      const role = formData?.role || Role.StandardPayment;
      await axios.post(
        '/api/platform/identity/createUsers',
        {
          users: [
            {
              partnerId: partnerId,
              roles: [role],
              email: formData?.email,
              name: formData?.name,
            },
          ],
        },
        { headers: { 'Content-Type': 'application/json' } },
      );
      notify?.({
        message: 'Admin created',
        type: 'success',
      });
      await onSuccess();
    } catch (e) {
      const msg = e instanceof AxiosError ? e.response?.data?.error : (e as Error).message;
      notify?.({
        message: `Admin creation encountered an error: ${msg}`,
        type: 'error',
      });
    } finally {
      setIdentityLoading(false);
      close();
      reset();
    }
  };

  const loading = identityLoading;

  return (
    <Drawer
      open={visible}
      onClose={close}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Create
        resource="admins"
        saveButtonProps={{
          ...saveButtonProps,
          loading: saveButtonProps.loading || loading,
          disabled: loading || saveButtonProps.disabled,
          onClick: onSubmit,
        }}
        isLoading={loading}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <FormProvider {...props}>
          <Stack component="form" direction="column" gap={1}>
            <HiddenInput name="user.create.partnerId" value={partnerId} />
            <HiddenInput name="user.create.validatedEmail" value={true} />
            <TextInput name="user.create.name" label="Name" required />
            <TextInput name="user.create.email" label="Email" type="email" required />
            <SelectInput
              name="user.create.role"
              label="Role"
              options={Object.keys(Role_Display).map((role) => ({
                id: role,
                name: Role_Display[role].name,
                secondaryText: Role_Display[role].description,
              }))}
            />
          </Stack>
        </FormProvider>
      </Create>
    </Drawer>
  );
};
