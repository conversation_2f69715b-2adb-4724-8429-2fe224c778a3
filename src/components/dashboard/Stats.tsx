import { <PERSON>, Card, CardContent, Grid, Typography } from '@mui/material';
import axios from 'axios';
import { useEffect, useState } from 'react';

const StatCard = (props) => {
  const { copy, value } = props;

  return (
    <Card sx={{ height: '100%' }} {...props}>
      <CardContent>
        <Grid container spacing={3} sx={{ justifyContent: 'space-between' }}>
          <Grid item>
            <Typography color="textSecondary" gutterBottom variant="overline">
              {copy}
            </Typography>
            <Typography color="textPrimary" variant="h4">
              {value}
            </Typography>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export const Stats = () => {
  const [stats, setStats] = useState<{ key: string; copy: string; value: number }[]>([]);
  useEffect(() => {
    axios.get('/api/platform/stats').then((res) => setStats(res.data));
  }, []);

  return <Box sx={{ display: 'flex', gap: 3 }}>{stats.map(StatCard)}</Box>;
};
