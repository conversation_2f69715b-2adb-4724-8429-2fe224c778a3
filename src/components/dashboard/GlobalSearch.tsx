import { Autocomplete, Card, CardContent, TextField } from '@mui/material';

export const GlobalSearch = (props) => (
  <Card square={true} variant="outlined" {...props}>
    <CardContent>
      <Autocomplete
        options={[]}
        renderInput={(params) => (
          <TextField
            {...params}
            label="Global search"
            margin="normal"
            variant="outlined"
            required
          />
        )}
      />
    </CardContent>
  </Card>
);
