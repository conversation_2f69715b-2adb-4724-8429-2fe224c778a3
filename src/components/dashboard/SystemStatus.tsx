import { Box, Card, CardActions, CardHeader, useTheme } from '@mui/material';

export const SystemStatus = () => {
  const isDarkTheme = useTheme().palette.mode === 'dark';

  return (
    <Box sx={{ display: 'flex', gap: 3 }}>
      <Card sx={{ maxWidth: 345 }}>
        <CardHeader title="Beam Platform Status" subheader="app.bybeam.co" />
        <CardActions>
          <iframe
            src={`https://status.bybeam.co/embed-status/214f3f0e/${
              isDarkTheme ? 'dark' : 'light'
            }-sm`}
            title="Beam Platform Status"
            width="230"
            height="61"
            style={{ border: 'none' }}
          />
        </CardActions>
      </Card>
      <Card sx={{ maxWidth: 345 }}>
        <CardHeader
          title="Beam Subsystems Status"
          subheader="GCP, Cloudflare, Mailgun, GitHub, etc"
        />
        <CardActions>
          <iframe
            src={`https://beam-subsystems.instatus.com/embed-status/64111f22/${
              isDarkTheme ? 'dark' : 'light'
            }-sm`}
            title="Beam Subsystems Status"
            width="230"
            height="61"
            style={{ border: 'none' }}
          />
        </CardActions>
      </Card>
    </Box>
  );
};
