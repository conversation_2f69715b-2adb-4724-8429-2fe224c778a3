import {
  <PERSON><PERSON>,
  <PERSON>,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
  colors,
} from '@mui/material';
import type { useModalReturnType } from '@refinedev/core';
import type {
  CommunicationChannels,
  CommunicationChannelsConfig,
  PlatformNotificationType,
} from 'types/notification';

type EditPartnerNotificationConfirmationDialogProps = useModalReturnType & {
  close: () => void;
  currentConfig?: CommunicationChannelsConfig;
  currentChannel: CommunicationChannels;
  currentStatus: boolean;
  notificationType: PlatformNotificationType;
  onConfirm: () => void;
  visible: boolean;
  isLoading: boolean;
};

export const EditPartnerNotificationConfirmationDialog = ({
  close,
  currentChannel,
  currentStatus,
  notificationType,
  onConfirm,
  visible,
  isLoading,
}: EditPartnerNotificationConfirmationDialogProps) => {
  const enabledOrDisabled = !currentStatus ? 'enabled' : 'disabled';

  const proceedAtYourOwnRisk =
    'Editing the notification communication channels of a live partner/program carries some risk! \
  Please ensure that you understand the impact of these changes.';

  return (
    <Dialog
      sx={{ '& .MuiDialog-paper': { width: '80%', maxHeight: 435 } }}
      maxWidth="xs"
      open={visible}
    >
      <DialogTitle>Are you sure?</DialogTitle>
      <DialogContent dividers>
        <Box gap={'1rem'}>
          <Typography variant="body1">
            Do you want to continue with{' '}
            <Typography
              color={!currentStatus ? colors.green['600'] : colors.red['600']}
              display={'inline'}
            >
              <strong>{!currentStatus ? 'enabling' : 'disabling'}</strong>
            </Typography>
            : <code>{notificationType}</code>? This will{' '}
            {!currentStatus ? `turn on all ${currentChannel}` : `turn off all ${currentChannel}`}{' '}
            for this notification type
          </Typography>
          <Alert severity="warning" sx={{ marginTop: '2rem' }}>
            {proceedAtYourOwnRisk}
          </Alert>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button autoFocus onClick={close}>
          Cancel
        </Button>
        <Button onClick={onConfirm} loading={isLoading} variant="contained">
          Set to {enabledOrDisabled}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
