import SelectInput from '@components/forms/SelectInput';
import { CloseOutlined } from '@mui/icons-material';
import { Autocomplete, Box, Drawer, IconButton, Switch, TextField, Tooltip } from '@mui/material';
import type { BaseKey, useModalReturnType } from '@refinedev/core';
import { Create } from '@refinedev/mui';
import useCommunicationConfiguration from 'hooks/communicationConfigurations/useCommunicationConfiguration';
import { useState } from 'react';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import { CommunicationChannels, PlatformNotificationEnum } from 'types/notification';

type AddPartnerNotificationOverrideDrawerProps = useModalReturnType & {
  close: () => void;
  partnerId: BaseKey;
  visible: boolean;
};

export const AddPartnerNotificationOverrideDrawer = ({
  close,
  partnerId,
  visible,
}: AddPartnerNotificationOverrideDrawerProps) => {
  const { configuration, onEnableNotification } = useCommunicationConfiguration(partnerId);
  const options = Object.values(PlatformNotificationEnum);
  const [isLoading, setIsLoading] = useState(false);

  const formProps = useForm();
  const {
    register,
    formState: { errors },
    getValues,
    handleSubmit,
    control,
    reset,
  } = formProps;
  const onClose = () => {
    reset();
    close();
  };

  async function onSubmit() {
    setIsLoading(true);
    const { notificationType, enabled, communicationChannel } = getValues();

    return onEnableNotification({
      notificationType,
      enabled,
      communicationChannel,
      onSuccess: () => {
        onClose();
      },
      onSettled: () => {
        setIsLoading(false);
      },
    });
  }

  return (
    <Drawer
      open={visible}
      onClose={close}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Create
        title="Add Notification Override"
        isLoading={isLoading}
        saveButtonProps={{ onClick: handleSubmit(onSubmit), disabled: isLoading }}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <Box component="form">
          <FormProvider {...formProps}>
            <SelectInput
              label="Communication Channel"
              name="communicationChannel"
              options={Object.values(CommunicationChannels).map((channel) => ({
                id: channel,
                name: channel,
              }))}
              required
            />
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'row',
                flexFlow: 'row',
                alignItems: 'center',
                paddingY: '1rem',
              }}
            >
              <Tooltip title="when this is toggled on notifications WILL be sent for this event type">
                <Switch
                  {...register('enabled')}
                  sx={{ marginRight: '1rem' }}
                  defaultChecked={true}
                />
              </Tooltip>
              <Controller
                control={control}
                name="notificationType"
                rules={{ required: 'This field is required' }}
                defaultValue={null}
                render={({ field }) => (
                  <Autocomplete
                    {...field}
                    options={options}
                    onChange={(_, value) => {
                      field.onChange(value?.id ?? value);
                    }}
                    getOptionDisabled={(option) => {
                      const { communicationChannel } = getValues();
                      return configuration?.config?.[communicationChannel]?.[option] !== undefined;
                    }}
                    isOptionEqualToValue={(option, value) =>
                      value === undefined || option?.toString() === (value?.id ?? value)?.toString()
                    }
                    sx={{ width: '100%' }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Notification Type"
                        margin="normal"
                        variant="outlined"
                        error={!!errors?.notificationType}
                        helperText={errors?.notificationType?.message as string}
                        required
                      />
                    )}
                  />
                )}
              />
            </Box>
          </FormProvider>
        </Box>
      </Create>
    </Drawer>
  );
};
