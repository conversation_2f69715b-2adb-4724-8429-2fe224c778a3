import { extractError } from '@components/forms/utils';
import MarkdownEditor from '@components/markdown/Editor';
import { CloseOutlined } from '@mui/icons-material';
import { Box, Drawer, IconButton, Stack, TextField, Typography } from '@mui/material';
import type { BaseKey } from '@refinedev/core';
import { Create } from '@refinedev/mui';
import type { UseModalFormReturnType } from '@refinedev/react-hook-form';
import { Controller, FormProvider } from 'react-hook-form';

export const AddBannerDrawer = (props: UseModalFormReturnType & { partnerId?: BaseKey }) => {
  const {
    saveButtonProps,
    modal: { visible, close },
    setValue,
    formState: { errors },
    partnerId,
    control,
    refineCore: { onFinish },
    reset,
    handleSubmit,
  } = props;

  setValue('partnerId', partnerId);

  const resetForm = () => reset({ incidentMessage: { create: { message: '', severity: 10 } } });

  const onSubmit = async (val) => {
    await onFinish({
      ...val,
      incidentMessage: {
        create: {
          message: val.incidentMessage.create.message,
          severity: Number(val.incidentMessage.create.severity),
        },
      },
    });
    resetForm();
    close();
  };

  return (
    <Drawer
      open={visible}
      onClose={() => {
        resetForm();
        close();
      }}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Create
        resource="partnerIncidents"
        title="Add Banner Message"
        saveButtonProps={{ ...saveButtonProps, onClick: handleSubmit(onSubmit) }}
        isLoading={false}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <Box
          component="form"
          sx={{ display: 'flex', flexDirection: 'row', flexFlow: 'row', alignItems: 'center' }}
        >
          <FormProvider {...props}>
            <Stack spacing={2}>
              <MarkdownEditor
                name="incidentMessage.create.message"
                label="Message Content"
                instructions={
                  <Typography variant="subtitle2">
                    Additionally, placeholder values for the partner can be inserted using the
                    syntax {'{'}PARTNER_EMAIL{'}'}. PARTNER_EMAIL, PARTNER_PHONE, and PARTNER_NAME
                    are all supported.
                  </Typography>
                }
                required
              />
              <Controller
                control={control}
                name="incidentMessage.create.severity"
                defaultValue={10}
                rules={{ min: 0 }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    value={Number(field.value)}
                    type="number"
                    label="Display Order"
                    sx={{ width: '100%' }}
                    error={!!extractError(errors, 'incidentMessage.create.severity')}
                    helperText={
                      extractError(errors, 'incidentMessage.create.severity')?.message ??
                      'Determines the order of display, from top to bottom, when multiple banners are active. Lower numbers will display at the top.'
                    }
                  />
                )}
              />
            </Stack>
          </FormProvider>
        </Box>
      </Create>
    </Drawer>
  );
};
