import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
} from '@mui/material';
import { useGo } from '@refinedev/core';

export default function CreatePartnerDialog({ visible, close, partnerId, openDrawer }) {
  const go = useGo();

  return (
    <Dialog
      open={visible}
      onClose={close}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <DialogTitle>Create Child Partner</DialogTitle>
      <DialogContent>
        <Typography>Do you want to create a new child partner or use an existing one?</Typography>
      </DialogContent>
      <DialogActions>
        <Button
          onClick={() => {
            openDrawer();
            close();
          }}
        >
          Add An Existing Partner
        </Button>
        <Button
          variant="contained"
          onClick={() => {
            go({
              to: '/partners/create',
              query: { parentId: partnerId },
            });
          }}
        >
          Create New Partner
        </Button>
      </DialogActions>
    </Dialog>
  );
}
