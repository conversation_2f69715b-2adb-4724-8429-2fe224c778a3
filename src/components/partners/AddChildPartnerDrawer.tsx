import AutocompleteInput from '@components/forms/AutocompleteInput';
import { CloseOutlined } from '@mui/icons-material';
import { Drawer, IconButton } from '@mui/material';
import { useUpdate } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import useAuthorization from 'hooks/identity/useAuthorization';
import { Relation } from 'pages/api/types/identity';
import { FormProvider } from 'react-hook-form';

export default function AddChildPartnerDrawer({ visible, close, partnerId: parentId, onSuccess }) {
  const formProps = useForm({ refineCoreProps: { redirect: 'show' } });
  const { handleSubmit, saveButtonProps, watch, getValues, reset } = formProps;
  const authorization = useAuthorization();

  const { mutate } = useUpdate();

  const onClose = () => {
    reset();
    close();
  };

  const onSubmit = async (): Promise<void> => {
    const { childPartners } = getValues();
    await Promise.all(
      childPartners.map(({ id }, idx) =>
        mutate(
          {
            resource: 'partners',
            id,
            values: { parentId },
          },
          {
            onSuccess: () => {
              if (idx + 1 === childPartners.length && onSuccess) {
                onSuccess();
              }
            },
          },
        ),
      ),
    );
    await authorization.createRelationships({
      partnerId: parentId,
      relationships: childPartners.map(({ id }) => ({
        relation: 'parent',
        object: { objectId: id, objectType: Relation.ORGANIZATION },
        subject: { objectId: parentId, objectType: Relation.ORGANIZATION },
      })),
    });
    onClose();
  };

  return (
    <Drawer
      open={visible}
      onClose={onClose}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Edit
        resource="partners"
        title="Add Child Partner(s)"
        saveButtonProps={{
          disabled: !watch('childPartners') || saveButtonProps.disabled,
          onClick: handleSubmit(onSubmit),
        }}
        isLoading={false}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={close} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <FormProvider {...formProps}>
          <AutocompleteInput
            name={'childPartners'}
            label="Partners"
            resource="partners"
            filters={[
              {
                field: 'parentId',
                operator: 'null',
                value: true,
              },
              {
                field: 'childPartnersExist',
                operator: 'eq',
                value: false,
              },
            ]}
            transformers={{
              from: (option) => option,
              id: (value: { id: string }) => (value instanceof Object ? value.id : value),
            }}
            required
            multiple
          />
        </FormProvider>
      </Edit>
    </Drawer>
  );
}
