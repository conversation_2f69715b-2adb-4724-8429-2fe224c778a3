import ErrorIcon from '@mui/icons-material/ErrorOutline';
import { Box, Button, IconButton, Stack, Tooltip } from '@mui/material';
import { type GridColDef, useGridApiRef } from '@mui/x-data-grid';
import { DateTimePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { type BaseKey, useModal, useNotification } from '@refinedev/core';
import { getConfig } from '@utils/config';
import { displayCurrency } from '@utils/currency';
import { sortErrorsToTop } from '@utils/table';
import axios, { AxiosError } from 'axios';
import dayjs from 'dayjs';
import { batchArray, getBatchSize } from 'pages/api/utils/batch';
import { useEffect, useRef, useState } from 'react';
import { JEVS<PERSON>ileMapper, JEVSRecipientColumns } from 'types/jevs';
import ProgressBar from './ProgressBar';
import ScheduleConfirmationModal from './ScheduleConfirmationModal';
import { StyledDataGrid } from './StylizedDataGrid';

export const ScheduleTable = ({
  onBack,
  onNext,
  rows,
  programId,
  adminId,
}: {
  rows: Record<string, unknown>[];
  onBack: () => void;
  onNext: () => void;
  programId: BaseKey;
  adminId: BaseKey;
}) => {
  const { open: notify } = useNotification();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedPaymentDate, setSelectedPaymentDate] = useState<Date>(
    dayjs(rows?.[0]?.paymentDate as string)
      .startOf('day')
      .toDate(),
  );
  const [errors, setErrors] = useState<Record<string, string>>({});
  const errorRef = useRef<HTMLDivElement>(null);
  const tableRef = useGridApiRef();
  const [progress, setProgress] = useState(0);
  const scheduleModal = useModal();
  const SCHEDULER_CONCURRENCY = getConfig('PUBLIC_SCHEDULER_CONCURRENCY') ?? '250';
  const SCHEDULER_FREQUENCY = getConfig('PUBLIC_SCHEDULER_FREQUENCY') ?? '30';

  useEffect(() => {
    if (Object.keys(errors).length) {
      if (errorRef.current) {
        errorRef.current.focus();
      }
    }
  }, [errors]);
  const columns: GridColDef[] = [
    {
      field: JEVSFileMapper[JEVSRecipientColumns.SALESFORCE_ID],
      headerName: JEVSRecipientColumns.SALESFORCE_ID,
      flex: 1,
      renderCell: ({ value }) => (
        <Stack direction="row" alignItems="center">
          <span>{value}</span>
          {errors[value] && (
            <Tooltip title={errors[value]} ref={errorRef.current ? null : errorRef}>
              <IconButton>
                <ErrorIcon />
              </IconButton>
            </Tooltip>
          )}
        </Stack>
      ),
    },
    {
      field: JEVSFileMapper[JEVSRecipientColumns.EMAIL],
      headerName: JEVSRecipientColumns.EMAIL,
      flex: 1,
    },
    {
      field: 'name',
      valueGetter: ({ row }) =>
        `${row[JEVSFileMapper[JEVSRecipientColumns.FIRST_NAME]]} ${
          row[JEVSFileMapper[JEVSRecipientColumns.LAST_NAME]]
        }`,
      flex: 1,
    },
    {
      field: JEVSFileMapper[JEVSRecipientColumns.PAYMENT_AMOUNT],
      headerName: JEVSRecipientColumns.PAYMENT_AMOUNT,
      valueGetter: ({ value }) => displayCurrency(value),
      flex: 1,
    },
    {
      field: JEVSFileMapper[JEVSRecipientColumns.PAYMENT_METHOD],
      headerName: JEVSRecipientColumns.PAYMENT_METHOD,
      flex: 1,
    },
    {
      field: JEVSFileMapper[JEVSRecipientColumns.PAYMENT_DATE],
      headerName: JEVSRecipientColumns.PAYMENT_DATE,
      valueGetter: ({ value }) => dayjs(String(value)).format('MM/DD/YYYY hh:mm A'),
      flex: 1,
    },
    {
      field: JEVSFileMapper[JEVSRecipientColumns.ORGANIZATION],
      headerName: JEVSRecipientColumns.ORGANIZATION,
      flex: 1,
    },
    {
      field: JEVSFileMapper[JEVSRecipientColumns.PROGRAM],
      headerName: JEVSRecipientColumns.PROGRAM,
      flex: 1,
    },
  ];

  function schedulePayments(chunk) {
    return axios.post(
      '/api/platform/payments/scheduleRecurringPayments/schedule',
      {
        recipients: chunk,
        programId,
        adminId,
      },
      { headers: { 'Content-Type': 'application/json' } },
    );
  }

  function mapPaymentDate(date: Date, idx: number): string {
    const increaseMinutes =
      Math.floor(idx / Number(SCHEDULER_CONCURRENCY)) * Number(SCHEDULER_FREQUENCY);
    const paymentDate = dayjs.utc(date).add(increaseMinutes, 'minutes').toISOString();
    return paymentDate;
  }

  const totalCount = rows.length;
  const batchSize = getBatchSize(rows);

  const handleSchedule = async () => {
    setIsLoading(true);
    const newErrorSet = {};
    const paymentsToSchedule = rows
      .filter((row) => row[JEVSFileMapper[JEVSRecipientColumns.PAYMENT_AMOUNT]] !== '0')
      .map((row) => ({
        ...row,
        [JEVSFileMapper[JEVSRecipientColumns.PAYMENT_DATE]]: dayjs
          .utc(selectedPaymentDate)
          .toISOString(),
      }));
    try {
      const chunks = batchArray({
        batchSize,
        totalCount,
        array: paymentsToSchedule,
      });

      for (const [index, chunk] of chunks.entries()) {
        const result = await schedulePayments(chunk);
        const order = index + 1;

        setProgress(Math.ceil(100 * (order / chunks.length)));
        if (result?.data?.data?.errors?.length) {
          for (const newError of result.data.data.errors) {
            newErrorSet[newError.id] = newError.message;
          }
          setErrors(newErrorSet);
          throw new Error('Error scheduling payments');
        }
      }

      notify?.({
        message: 'successfully scheduled payments',
        type: 'success',
      });
      onNext();
    } catch (e) {
      setIsLoading(false);
      const msg = e instanceof AxiosError ? e.response?.data?.error : (e as Error).message;
      notify?.({ message: `Issue: ${msg}`, type: 'error' });
      tableRef.current.setPage(0);
    }
  };

  return (
    <>
      <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%', gap: '8px' }}>
        <Box textAlign="right">
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DateTimePicker
              label="Schedule For"
              value={selectedPaymentDate}
              onChange={(value) => value && setSelectedPaymentDate(value)}
              disablePast
            />
          </LocalizationProvider>
        </Box>
        <StyledDataGrid
          rows={sortErrorsToTop({
            rows,
            errors,
            accessor: JEVSFileMapper[JEVSRecipientColumns.SALESFORCE_ID],
          })}
          getRowId={(row) => String(row[JEVSFileMapper[JEVSRecipientColumns.SALESFORCE_ID]])}
          getRowClassName={(params) =>
            errors[params.row[JEVSFileMapper[JEVSRecipientColumns.SALESFORCE_ID]]]
              ? 'validation-error'
              : ''
          }
          columns={columns}
          apiRef={tableRef}
        />
        <ProgressBar isLoading={batchSize < totalCount && isLoading} progress={progress} />
        <Stack direction="row" mt={5}>
          <Button color="inherit" onClick={onBack} sx={{ mr: 1 }}>
            Back
          </Button>
          <Box sx={{ flex: '1 1 auto' }} />
          <Button
            type="submit"
            onClick={() => scheduleModal.show()}
            loading={isLoading}
            variant="contained"
          >
            Schedule
          </Button>
        </Stack>
      </Box>
      <ScheduleConfirmationModal
        {...scheduleModal}
        numberOfPayments={rows?.length}
        numberOfInvalidPayments={
          rows?.filter((row) => row[JEVSFileMapper[JEVSRecipientColumns.PAYMENT_AMOUNT]] === '0')
            ?.length
        }
        paymentDate={selectedPaymentDate}
        schedulerFrequency={SCHEDULER_FREQUENCY}
        onConfirm={handleSchedule}
      />
    </>
  );
};
