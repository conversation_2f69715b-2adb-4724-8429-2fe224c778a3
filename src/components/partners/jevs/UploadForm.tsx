import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { Alert, Box, Button, Stack } from '@mui/material';
import { styled } from '@mui/material/styles';
import { convertToCents, dollarsToCents } from '@utils/currency';
import { parse } from 'csv-parse/sync';
import { type Dispatch, type SetStateAction, useState } from 'react';
import { useForm } from 'react-hook-form';
import { JEVSFileMapper, JEVSRecipientColumns } from 'types/jevs';

const VisuallyHiddenInput = styled('input')({
  clip: 'rect(0 0 0 0)',
  clipPath: 'inset(50%)',
  height: 1,
  overflow: 'hidden',
  position: 'absolute',
  bottom: 0,
  left: 0,
  whiteSpace: 'nowrap',
  width: 1,
});

export const UploadForm = ({
  onBack,
  onNext,
  setParticipants,
}: {
  onBack: () => void;
  onNext: () => void;
  setParticipants: Dispatch<SetStateAction<Record<string, unknown>[]>>;
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm();
  const [uploaded, setUploaded] = useState<File>();
  const parseRecords = (records) => {
    function mapPaymentMethod(value: string): string {
      const parsedValue = value.trim().toLowerCase();
      switch (parsedValue) {
        case 'prepaid card':
          return 'physicalCard';
        case 'ach or deposit to account':
          return 'ach';
        case 'do not prefer':
        case 'check':
          return 'check';
        default:
      }
      if (['ach', 'check', 'physicalCard'].includes(value)) {
        return value;
      }
      return 'Unsupported Payment Method';
    }
    return records.map((record) => {
      const parsedRecord = {};
      for (const key in record) {
        if (key === JEVSRecipientColumns.PAYMENT_AMOUNT) {
          parsedRecord[JEVSFileMapper[key]] = convertToCents(record[key]);
          continue;
        }

        if (key === JEVSRecipientColumns.PAYMENT_METHOD) {
          parsedRecord[JEVSFileMapper[key]] = mapPaymentMethod(record[key]);
          continue;
        }
        if (key === JEVSRecipientColumns.EMAIL) {
          parsedRecord[JEVSFileMapper[key]] = record[key]?.toLowerCase();
          continue;
        }
        if (JEVSFileMapper[key]) parsedRecord[JEVSFileMapper[key]] = record[key];
      }
      return parsedRecord;
    });
  };
  const handleUploadSubmit = (data) => {
    const file = data.file.item(0);
    const fileReader = new FileReader();

    fileReader.addEventListener(
      'load',
      () => {
        if (fileReader.result) {
          try {
            const input = fileReader.result as string;
            const records = parse(input, {
              columns: true,
              skip_empty_lines: true,
            });
            setParticipants(parseRecords(records));
            onNext();
          } catch (e) {
            console.error(e);
            setError('file', {
              type: 'validate',
              message: 'problem parsing .csv, please check your file',
            });
          }
        }
      },
      false,
    );

    if (file) {
      fileReader.readAsText(file);
    }
  };

  const handleOnChange = (e) => {
    const file = e.target?.files?.[0];
    setUploaded(file ? file : null);
  };

  return (
    <Box component="form" autoComplete="off">
      <Stack justifyContent="center" alignItems="center" sx={{ height: 'calc(100vh - 15rem)' }}>
        <Button
          component="label"
          variant={uploaded ? 'outlined' : 'contained'}
          tabIndex={-1}
          startIcon={<CloudUploadIcon />}
        >
          {uploaded ? uploaded.name : 'Upload file'}
          <VisuallyHiddenInput
            type="file"
            {...register('file', {
              required: true,
            })}
            accept=".csv"
            onChange={handleOnChange}
          />
        </Button>
        {errors.file?.type === 'required' && (
          <Alert severity="error" sx={{ mt: 3 }}>
            A file is required
          </Alert>
        )}
        {errors.file?.type === 'validate' && (
          <Alert severity="error" sx={{ mt: 3 }}>
            {String(errors.file?.message)}
          </Alert>
        )}
      </Stack>
      <Stack direction="row" mt={5}>
        <Button color="inherit" onClick={onBack} sx={{ mr: 1 }}>
          Cancel
        </Button>
        <Box sx={{ flex: '1 1 auto' }} />
        <Button type="submit" onClick={handleSubmit(handleUploadSubmit)} variant="contained">
          Next
        </Button>
      </Stack>
    </Box>
  );
};
