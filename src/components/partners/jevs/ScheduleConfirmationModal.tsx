import { formatDate } from '@jsonforms/material-renderers';
import {
  Alert,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  List,
  ListItem,
  ListItemText,
} from '@mui/material';
import { getConfig } from '@utils/config';
import dayjs from 'dayjs';

export default function ScheduleConfirmationModal({
  close,
  visible,
  onConfirm,
  numberOfPayments,
  numberOfInvalidPayments,
  paymentDate,
  schedulerFrequency,
}) {
  const SCHEDULER_ACTIVE_WINDOW = getConfig('PUBLIC_SCHEDULER_ACTIVE_WINDOW') ?? '120';
  const isValid = dayjs(paymentDate).isAfter(
    dayjs().add(Number(SCHEDULER_ACTIVE_WINDOW), 'minute'),
  );

  return (
    <Dialog open={visible} onClose={close}>
      <DialogTitle>Are you sure you want to proceed with this action?</DialogTitle>
      <DialogContent dividers sx={{ p: 6, gap: 2, display: 'flex', flexDirection: 'column' }}>
        <Alert severity="info">
          This operation schedules payments for a chosen payment date for the scheduler to check
          (every {schedulerFrequency} minutes). Please verify the selected payment date.
        </Alert>
        <Alert severity={isValid ? 'info' : 'error'}>
          The payment date should be after scheduler active window ({SCHEDULER_ACTIVE_WINDOW}{' '}
          minutes)
        </Alert>
        <List
          sx={{
            width: '100%',
            bgcolor: 'background.paper',
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
          }}
        >
          <ListItem>
            <ListItemText primary={numberOfPayments} secondary="Number of payments" />
          </ListItem>
          <ListItem>
            <ListItemText
              primary={numberOfInvalidPayments}
              secondary="Number of invalid payments ($0.00 amount)"
            />
          </ListItem>
          <ListItem>
            <ListItemText
              primary={dayjs(paymentDate).format('MM/DD/YYYY hh:mm A')}
              secondary="Payment Date in Local timezone"
            />
          </ListItem>
          <ListItem>
            <ListItemText
              primary={dayjs.utc(paymentDate).format('MM/DD/YYYY hh:mm A')}
              secondary="Payment Date in UTC"
            />
          </ListItem>
        </List>
      </DialogContent>
      <DialogActions>
        <Button onClick={close}>Cancel</Button>
        <Button
          variant="contained"
          disabled={!isValid}
          onClick={() => {
            onConfirm(onConfirm);
            close();
          }}
        >
          Confirm
        </Button>
      </DialogActions>
    </Dialog>
  );
}
