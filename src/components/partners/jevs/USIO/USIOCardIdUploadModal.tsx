import CloseIcon from '@mui/icons-material/Close';
import {
  Box,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  Stack,
  Step,
  StepButton,
  Stepper,
} from '@mui/material';

import type { BaseKey } from '@refinedev/core';
import { useState } from 'react';
import { ImportTable } from './ImportTable';
import { UploadForm } from './UploadForm';

export enum USIOCardIdImportSteps {
  UPLOAD = 0,
  IMPORT = 1,
}

const USIOCardIdUploadModal = ({
  onClose,
  partnerId,
}: { onClose: () => void; partnerId: BaseKey }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [completed, setCompleted] = useState<{
    [k: number]: boolean;
  }>({});
  const [cards, setCards] = useState<Record<string, unknown>[]>([]);

  const handleNext = () => {
    const newActiveStep =
      isLastStep() && !allStepsCompleted()
        ? // It's the last step, but not all steps have been completed,
          // find the first step that has been completed
          Object.keys(steps).findIndex((key) => !(key in completed))
        : activeStep + 1;
    setActiveStep(newActiveStep);
    if (allStepsCompleted()) {
      onClose();
    }
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleStep = (step: number) => () => {
    setActiveStep(step);
  };

  const handleComplete = () => {
    const newCompleted = completed;
    newCompleted[activeStep] = true;
    setCompleted(newCompleted);
    handleNext();
  };
  const steps = {
    [USIOCardIdImportSteps.UPLOAD]: {
      label: 'Upload file from USIO',
      content: <UploadForm onBack={onClose} onNext={handleComplete} setCards={setCards} />,
    },
    [USIOCardIdImportSteps.IMPORT]: {
      label: 'Import USIO Cards',
      content: (
        <ImportTable
          onBack={handleBack}
          onNext={handleComplete}
          rows={cards}
          partnerId={partnerId}
          onClose={onClose}
        />
      ),
    },
  };

  const totalSteps = () => {
    return Object.keys(steps).length;
  };

  const completedSteps = () => {
    return Object.keys(completed).length;
  };

  const isLastStep = () => {
    return activeStep === totalSteps() - 1;
  };

  const allStepsCompleted = () => {
    return completedSteps() === totalSteps();
  };

  return (
    <Dialog open={true} fullScreen onClose={onClose}>
      <Stack direction="row" justifyContent="space-between" mx="1.5rem" alignItems="center">
        <DialogTitle>Bulk USIO Card Id Importer</DialogTitle>
        <IconButton edge="start" color="inherit" onClick={onClose} aria-label="close">
          <CloseIcon />
        </IconButton>
      </Stack>
      <DialogContent sx={{ pb: 0 }}>
        <Stack>
          <Stepper nonLinear activeStep={activeStep} sx={{ mb: 3 }}>
            {Object.values(steps).map(({ label }, index) => (
              <Step key={label} completed={completed[index]}>
                <StepButton color="inherit" onClick={handleStep(index)}>
                  {label}
                </StepButton>
              </Step>
            ))}
          </Stepper>
          <Box p={3}>{steps[activeStep]?.content}</Box>
        </Stack>
      </DialogContent>
    </Dialog>
  );
};

export default USIOCardIdUploadModal;
