import ErrorIcon from '@mui/icons-material/ErrorOutline';
import { Box, Button, IconButton, Stack, Tooltip } from '@mui/material';
import { type GridColDef, useGridApiRef } from '@mui/x-data-grid';
import { type BaseKey, useNotification } from '@refinedev/core';
import { sortErrorsToTop } from '@utils/table';
import axios, { AxiosError } from 'axios';
import { batchArray, getBatchSize } from 'pages/api/utils/batch';
import { useEffect, useRef, useState } from 'react';
import { USIOImportColumns } from 'types/jevs';
import ProgressBar from '../ProgressBar';
import { StyledDataGrid } from '../StylizedDataGrid';

export const ImportTable = ({
  onBack,
  onNext,
  partnerId,
  rows,
}: {
  partnerId: BaseKey;
  onBack: () => void;
  onClose: () => void;
  onNext: () => void;
  rows: Record<string, unknown>[];
}) => {
  const { open: notify } = useNotification();
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const errorRef = useRef<HTMLDivElement>(null);
  const tableRef = useGridApiRef();
  useEffect(() => {
    if (Object.keys(errors).length) {
      if (errorRef.current) {
        errorRef.current.focus();
      }
    }
  }, [errors]);
  const columns: GridColDef[] = [
    {
      field: USIOImportColumns.Email_Address,
      headerName: 'Email',
      renderCell: ({ value }) => (
        <Stack direction="row" alignItems="center">
          <span>{value}</span>
          {errors[value] && (
            <Tooltip title={errors[value]} ref={errorRef.current ? null : errorRef}>
              <IconButton>
                <ErrorIcon />
              </IconButton>
            </Tooltip>
          )}
        </Stack>
      ),
      flex: 1,
    },
    {
      field: USIOImportColumns.CH_Name,
      headerName: 'Card Holder Name',
      flex: 1,
    },
    {
      field: USIOImportColumns.Card_Id,
      headerName: 'USIO Card Id',
      flex: 1,
    },
    {
      field: USIOImportColumns.type,
      headerName: 'Type',
      flex: 1,
    },
  ];
  const totalCount = rows.length;
  const batchSize = getBatchSize(rows);
  const [progress, setProgress] = useState(0);

  function importUsers(chunk) {
    return axios.post(
      '/api/platform/payments/createUsioAccounts',
      {
        accounts: chunk,
        partnerId,
      },
      { headers: { 'Content-Type': 'application/json' } },
    );
  }
  const handleImport = async () => {
    setIsLoading(true);
    const newErrorSet = {};
    try {
      const chunks = batchArray({
        batchSize,
        totalCount,
        array: rows,
      });

      for (const [index, chunk] of chunks.entries()) {
        const result = await importUsers(chunk);
        const order = index + 1;

        setProgress(Math.ceil(100 * (order / chunks.length)));
        if (result?.data?.data?.errors?.length) {
          for (const newError of result.data.data.errors) {
            newErrorSet[newError.id] = newError.message;
          }
          setErrors(newErrorSet);
          throw new Error('Error importing cardIds');
        }
      }
      notify?.({
        message: 'successfully imported recipients',
        type: 'success',
      });
      onNext();
    } catch (e) {
      setIsLoading(false);
      tableRef.current.setPage(0);
      const msg = e instanceof AxiosError ? e.response?.data?.error : (e as Error).message;
      notify?.({ message: `Issue: ${msg}`, type: 'error' });
    }
  };

  return (
    <Box sx={{ width: '100%' }}>
      <StyledDataGrid
        rows={sortErrorsToTop({
          rows,
          errors,
          accessor: USIOImportColumns.Email_Address,
        })}
        getRowId={(row) => String(row[USIOImportColumns.Card_Id])}
        getRowClassName={(params) =>
          errors[params.row[USIOImportColumns.Email_Address]] ? 'validation-error' : ''
        }
        columns={columns}
        apiRef={tableRef}
      />
      <ProgressBar isLoading={batchSize < totalCount && isLoading} progress={progress} />
      <Stack direction="row" mt={5}>
        <Button color="inherit" onClick={onBack} sx={{ mr: 1 }}>
          Back
        </Button>
        <Box sx={{ flex: '1 1 auto' }} />
        <Button type="submit" onClick={handleImport} loading={isLoading} variant="contained">
          Import Card Ids
        </Button>
      </Stack>
    </Box>
  );
};
