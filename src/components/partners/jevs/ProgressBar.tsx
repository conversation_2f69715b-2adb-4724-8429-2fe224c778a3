import { Box, LinearProgress, Typography } from '@mui/material';

export default function ProgressBar({
  isLoading,
  progress = 0,
}: { isLoading: boolean; progress: number }) {
  return isLoading ? (
    <Box sx={{ width: '100%', my: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <Box sx={{ width: '100%', mr: 1 }}>
          <LinearProgress variant="determinate" value={progress} />
        </Box>
        <Box sx={{ minWidth: 35 }}>
          <Typography variant="body2" color="text.secondary">{`${Math.round(
            progress,
          )}%`}</Typography>
        </Box>
      </Box>
    </Box>
  ) : null;
}
