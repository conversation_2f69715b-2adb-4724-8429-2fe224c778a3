import {
  <PERSON>ert,
  Button,
  <PERSON><PERSON>,
  <PERSON>alogA<PERSON>,
  <PERSON>alogContent,
  DialogTitle,
  FormControlLabel,
  Switch,
} from '@mui/material';
import { useState } from 'react';

export default function ImportConfirmation({ close, visible, onConfirm }) {
  const [createAccounts, setCreateAccounts] = useState(false);
  return (
    <Dialog open={visible} onClose={close}>
      <DialogTitle>Are you sure you want to proceed with this action?</DialogTitle>
      <DialogContent dividers sx={{ p: 6 }}>
        <Alert severity="error">
          This operation imports users and their related records into the core database. To include
          the creation of individual payment accounts, you must enable the corresponding flag.
        </Alert>
        <FormControlLabel
          control={
            <Switch value={createAccounts} onChange={() => setCreateAccounts(!createAccounts)} />
          }
          label="With card creation"
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={close}>Cancel</Button>
        <Button
          variant="contained"
          onClick={() => {
            onConfirm(createAccounts);
            close();
          }}
        >
          Confirm
        </Button>
      </DialogActions>
    </Dialog>
  );
}
