import CloseIcon from '@mui/icons-material/Close';
import {
  Box,
  Dialog,
  DialogContent,
  DialogTitle,
  Icon<PERSON>utton,
  Stack,
  Step,
  StepButton,
  Stepper,
} from '@mui/material';

import type { BaseKey } from '@refinedev/core';
import { useState } from 'react';
import { RecipientsTable } from './RecipientsTable';
import { ScheduleTable } from './ScheduleTable';
import { UploadForm } from './UploadForm';

export enum PaymentFileSteps {
  UPLOAD = 0,
  IMPORT = 1,
  SCHEDULE = 2,
}

const PaymentFileUploadModal = ({
  onClose,
  partnerId,
  programId,
  adminId,
  fundingDistributorId,
}: {
  onClose: () => void;
  programId: BaseKey;
  adminId: BaseKey;
  fundingDistributorId: string;
  partnerId: string;
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [completed, setCompleted] = useState<{
    [k: number]: boolean;
  }>({});
  const [participants, setParticipants] = useState<Record<string, unknown>[]>([]);

  const handleNext = () => {
    if (!isLastStep()) setActiveStep(activeStep + 1);
    else {
      onClose();
    }
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleStep = (step: number) => () => {
    setActiveStep(step);
  };

  const handleComplete = () => {
    const newCompleted = completed;
    newCompleted[activeStep] = true;
    setCompleted(newCompleted);
    handleNext();
  };
  const steps = {
    [PaymentFileSteps.UPLOAD]: {
      label: 'Upload payment file',
      content: (
        <UploadForm onBack={onClose} onNext={handleComplete} setParticipants={setParticipants} />
      ),
    },
    [PaymentFileSteps.IMPORT]: {
      label: 'Import recipients',
      content: (
        <RecipientsTable
          onBack={handleBack}
          onNext={handleComplete}
          rows={participants}
          partnerId={partnerId}
          programId={programId}
          adminId={adminId}
          fundingDistributorId={fundingDistributorId}
          onClose={onClose}
        />
      ),
    },
    [PaymentFileSteps.SCHEDULE]: {
      label: 'Schedule jobs',
      content: (
        <ScheduleTable
          onBack={handleBack}
          onNext={handleComplete}
          rows={participants}
          programId={programId}
          adminId={adminId}
        />
      ),
    },
  };

  const totalSteps = () => {
    return Object.keys(steps).length;
  };

  const isLastStep = () => {
    return activeStep === totalSteps() - 1;
  };

  return (
    <Dialog open={true} fullScreen onClose={onClose}>
      <Stack direction="row" justifyContent="space-between" mx="1.5rem" alignItems="center">
        <DialogTitle>Bulk Scheduled Payment Importer</DialogTitle>
        <IconButton edge="start" color="inherit" onClick={onClose} aria-label="close">
          <CloseIcon />
        </IconButton>
      </Stack>
      <DialogContent sx={{ pb: 0 }}>
        <Stack>
          <Stepper nonLinear activeStep={activeStep} sx={{ mb: 3 }}>
            {Object.values(steps).map(({ label }, index) => (
              <Step key={label} completed={completed[index]}>
                <StepButton color="inherit" onClick={handleStep(index)}>
                  {label}
                </StepButton>
              </Step>
            ))}
          </Stepper>
          <Box p={3}>{steps[activeStep]?.content}</Box>
        </Stack>
      </DialogContent>
    </Dialog>
  );
};

export default PaymentFileUploadModal;
