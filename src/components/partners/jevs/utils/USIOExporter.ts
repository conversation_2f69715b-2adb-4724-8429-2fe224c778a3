import { centsToDollars } from '@utils/currency';
import { stringify } from 'csv-stringify/sync';
import { JEVSFileMapper, JEVSRecipientColumns, USIOCSVColumns } from 'types/jevs';

export const exportToUSIOWithCSV = (
  recipients: Record<string, unknown>[],
  overrides: Partial<Record<USIOCSVColumns, unknown>>,
) => {
  const mappedRecipients: Record<USIOCSVColumns, unknown>[] = recipients.map((recipient) => {
    return {
      [USIOCSVColumns.cardType]: 'Incentive-Card',
      [USIOCSVColumns.cardCount]: 1,
      [USIOCSVColumns.shippingDestination]: 'distributor',
      [USIOCSVColumns.firstName]: recipient[JEVSFileMapper[JEVSRecipientColumns.FIRST_NAME]],
      [USIOCSVColumns.lastName]: recipient[JEVSFileMapper[JEVSRecipientColumns.LAST_NAME]],
      [USIOCSVColumns.programName]: recipient[JEVSFileMapper[JEVSRecipientColumns.PROGRAM]],
      [USIOCSVColumns.address]: recipient[JEVSFileMapper[JEVSRecipientColumns.PROGRAM_ADDRESS]],
      [USIOCSVColumns.address2]: '',
      [USIOCSVColumns.city]: recipient[JEVSFileMapper[JEVSRecipientColumns.PROGRAM_CITY]],
      [USIOCSVColumns.state]: recipient[JEVSFileMapper[JEVSRecipientColumns.PROGRAM_STATE]],
      [USIOCSVColumns.zipCode]: recipient[JEVSFileMapper[JEVSRecipientColumns.PROGRAM_ZIP_CODE]],
      [USIOCSVColumns.phone]: '',
      [USIOCSVColumns.email]: recipient[JEVSFileMapper[JEVSRecipientColumns.EMAIL]],
      [USIOCSVColumns.loadAmount]: 0,
      [USIOCSVColumns.loadNow]: 'n',
      [USIOCSVColumns.distributorId]: '',
      [USIOCSVColumns.cardDesignId]: '',
      [USIOCSVColumns.giftMessage]: '',
      [USIOCSVColumns.giftSenderFirstName]: '',
      [USIOCSVColumns.giftSenderLastName]: '',
      [USIOCSVColumns.giftVirtualDeliveryMethod]: '',
      [USIOCSVColumns.expiresIn]: '',
      [USIOCSVColumns.reportData1]: '',
      [USIOCSVColumns.reportData2]: '',
      [USIOCSVColumns.reportData3]: '',
      [USIOCSVColumns.source]: '',
      [USIOCSVColumns.shippingMethod]: 5,
      ...overrides,
    };
  });
  return stringify(mappedRecipients, { header: true });
};

export const exportToRecipientsToCSV = (recipients: Record<string, unknown>[]) => {
  const mappedRecipients: Record<string, unknown>[] = recipients.map((recipient) => {
    return {
      [JEVSFileMapper[JEVSRecipientColumns.FIRST_NAME]]:
        recipient[JEVSFileMapper[JEVSRecipientColumns.FIRST_NAME]],
      [JEVSFileMapper[JEVSRecipientColumns.LAST_NAME]]:
        recipient[JEVSFileMapper[JEVSRecipientColumns.LAST_NAME]],
      [JEVSFileMapper[JEVSRecipientColumns.EMAIL]]:
        recipient[JEVSFileMapper[JEVSRecipientColumns.EMAIL]],
      [JEVSFileMapper[JEVSRecipientColumns.SALESFORCE_ID]]:
        recipient[JEVSFileMapper[JEVSRecipientColumns.SALESFORCE_ID]],
    };
  });
  return stringify(mappedRecipients, { header: true });
};
