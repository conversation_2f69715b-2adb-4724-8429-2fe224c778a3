import ErrorIcon from '@mui/icons-material/ErrorOutline';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import {
  Alert,
  AlertTitle,
  Box,
  Button,
  CircularProgress,
  FormControlLabel,
  IconButton,
  Stack,
  Switch,
  Tooltip,
} from '@mui/material';
import { type GridColDef, useGridApiRef } from '@mui/x-data-grid';
import { Paymentmethod } from '@prisma/generated/paymentClient';
import { type BaseKey, useModal, useNotification } from '@refinedev/core';
import { getConfig } from '@utils/config';
import { displayCurrency } from '@utils/currency';
import { sortErrorsToTop } from '@utils/table';
import axios, { AxiosError } from 'axios';
import dayjs from 'dayjs';
import usePaymentAccounts from 'hooks/payments/usePaymentAccounts';
import useUsersByEmail from 'hooks/users/useUsersByEmail';
import JSZ<PERSON> from 'jszip';
import mnemonist from 'mnemonist';
import { batchArray, getBatchSize } from 'pages/api/utils/batch';
import { useEffect, useRef, useState } from 'react';
import { JEVSFileMapper, JEVSRecipientColumns, USIOCSVColumns } from 'types/jevs';
import ImportConfirmation from './ImportConfirmationModal';
import ProgressBar from './ProgressBar';
import { StyledDataGrid } from './StylizedDataGrid';
import { exportToRecipientsToCSV, exportToUSIOWithCSV } from './utils/USIOExporter';

const { MultiMap } = mnemonist;

export const RecipientsTable = ({
  adminId,
  fundingDistributorId,
  onBack,
  onClose,
  onNext,
  programId,
  partnerId,
  rows,
}: {
  adminId: BaseKey;
  fundingDistributorId: string;
  onBack: () => void;
  onClose: () => void;
  onNext: () => void;
  programId: BaseKey;
  partnerId: BaseKey;
  rows: Record<string, unknown>[];
}) => {
  const { open: notify } = useNotification();
  const USIO_DESIGN_ID = getConfig('PUBLIC_USIO_DESIGN_ID');
  const [isLoading, setIsLoading] = useState(false);
  const [useExporter, setUseExporter] = useState(true);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const errorRef = useRef<HTMLDivElement>(null);
  const tableRef = useGridApiRef();
  const [progress, setProgress] = useState(0);
  const importModal = useModal();
  const rowsWithPhysicalCard = rows.filter(
    (row) =>
      row[JEVSFileMapper[JEVSRecipientColumns.PAYMENT_METHOD]] === Paymentmethod.physicalCard,
  );

  const { users = [], isLoading: isUsersLoading } = useUsersByEmail({
    partnerId: partnerId as string,
    emails: rows
      .map((row) => row[JEVSFileMapper[JEVSRecipientColumns.EMAIL]])
      .filter(Boolean) as string[],
  });

  const usersWithPhysicalCards = rowsWithPhysicalCard
    .map((row) => users?.find((user) => user.email === row.email))
    .filter(Boolean);

  const { paymentAccounts = [], isLoading: isAccountsLoading } = usePaymentAccounts({
    filters: [
      {
        field: 'referenceId',
        operator: 'in',
        value: usersWithPhysicalCards?.map((user) => `${user?.id}-${Paymentmethod.physicalCard}`),
      },
    ],
    enabled: !!usersWithPhysicalCards?.length,
  });

  useEffect(() => {
    if (Object.keys(errors).length) {
      if (errorRef.current) {
        errorRef.current.focus();
      }
    }
  }, [errors]);

  const columns: GridColDef[] = [
    {
      field: JEVSFileMapper[JEVSRecipientColumns.SALESFORCE_ID],
      headerName: JEVSRecipientColumns.SALESFORCE_ID,
      renderCell: ({ value }) => (
        <Stack direction="row" alignItems="center">
          <span>{value}</span>
          {errors[value] && (
            <Tooltip title={errors[value]} ref={errorRef.current ? null : errorRef}>
              <IconButton>
                <ErrorIcon />
              </IconButton>
            </Tooltip>
          )}
        </Stack>
      ),
      flex: 1,
    },
    {
      field: JEVSFileMapper[JEVSRecipientColumns.EMAIL],
      headerName: JEVSRecipientColumns.EMAIL,
      flex: 1,
    },
    {
      field: 'Name',
      valueGetter: ({ row }) =>
        `${row[JEVSFileMapper[JEVSRecipientColumns.FIRST_NAME]]} ${
          row[JEVSFileMapper[JEVSRecipientColumns.LAST_NAME]]
        }`,
      flex: 1,
    },
    {
      field: JEVSFileMapper[JEVSRecipientColumns.PAYMENT_AMOUNT],
      headerName: JEVSRecipientColumns.PAYMENT_AMOUNT,
      valueGetter: ({ value }) => displayCurrency(value),
      flex: 1,
    },
    {
      field: JEVSFileMapper[JEVSRecipientColumns.PAYMENT_METHOD],
      headerName: JEVSRecipientColumns.PAYMENT_METHOD,
      flex: 1,
    },
    {
      field: JEVSFileMapper[JEVSRecipientColumns.PAYMENT_DATE],
      headerName: JEVSRecipientColumns.PAYMENT_DATE,
      valueGetter: ({ value }) => dayjs(String(value)).format('MM/DD/YYYY hh:mm A'),
      flex: 1,
    },
    {
      field: JEVSFileMapper[JEVSRecipientColumns.PROGRAM_ADDRESS],
      headerName: JEVSRecipientColumns.PROGRAM_ADDRESS,
      flex: 1,
    },
    {
      field: JEVSFileMapper[JEVSRecipientColumns.PROGRAM_CITY],
      headerName: JEVSRecipientColumns.PROGRAM_CITY,
      flex: 1,
    },
    {
      field: JEVSFileMapper[JEVSRecipientColumns.ORGANIZATION],
      headerName: JEVSRecipientColumns.ORGANIZATION,
      flex: 1,
    },
    {
      field: JEVSFileMapper[JEVSRecipientColumns.PROGRAM],
      headerName: JEVSRecipientColumns.PROGRAM,
      flex: 1,
    },
  ];

  const totalCount = rows.length;
  const batchSize = getBatchSize(rows);

  const importUsers = (chunk, withAccountCreation = false) => {
    return axios.post(
      '/api/platform/payments/scheduleRecurringPayments/import',
      {
        recipients: chunk,
        programId,
        adminId,
        withAccountCreation,
      },
      { headers: { 'Content-Type': 'application/json' } },
    );
  };
  const onImportUsers = async (withAccountCreation = false) => {
    const newErrorSet = {};
    const chunks = batchArray({
      batchSize,
      totalCount,
      array: rows,
    });

    for (const [index, chunk] of chunks.entries()) {
      const result = await importUsers(chunk, withAccountCreation);
      const order = index + 1;

      setProgress(Math.ceil(100 * (order / chunks.length)));
      if (result?.data?.data?.errors?.length) {
        for (const newError of result.data.data.errors) {
          newErrorSet[newError.id] = newError.message;
        }
        setErrors(newErrorSet);
        throw new Error('Error importing recipients');
      }
    }
    notify?.({
      message: 'successfully imported recipients',
      type: 'success',
    });
  };

  const getNewUsioRecords = () => {
    const records = rowsWithPhysicalCard.filter((row) => {
      const user = users?.find((user) => user.email === row.email);
      const paymentAccount = paymentAccounts?.find(
        (account) =>
          account.recipient?.referenceId === user?.id && account.type === 'PHYSICAL_CARD',
      );
      return !paymentAccount;
    });

    const recordsByProgramName = new MultiMap<string, Record<string, unknown>>();
    for (const record of records)
      recordsByProgramName.set(
        record[JEVSFileMapper[JEVSRecipientColumns.PROGRAM]] as string,
        record,
      );

    return recordsByProgramName;
  };

  const onExportFile = async () => {
    const recordsByProgram = getNewUsioRecords();

    if (!recordsByProgram.size) {
      notify?.({
        message: 'No records found to export',
        type: 'error',
      });
      return;
    }
    const zip = new JSZip();

    recordsByProgram.forEachAssociation((records, programName) => {
      const USIOCSV = exportToUSIOWithCSV(records, {
        [USIOCSVColumns.distributorId]: fundingDistributorId,
        [USIOCSVColumns.cardDesignId]: USIO_DESIGN_ID,
      });
      zip.file(`${programName}.csv`, USIOCSV);
    });

    const content = await zip.generateAsync({ type: 'blob' });
    const zipFile = new Blob([content], { type: 'application/zip' });

    const url = URL.createObjectURL(zipFile);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'jevs_usio.zip';
    document.body.appendChild(a);
    a.click();

    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    notify?.({
      message: 'successfully exported USIO bulk csv',
      type: 'success',
    });
  };

  const onImport = async (withAccountCreation = false) => {
    try {
      setIsLoading(true);
      await onImportUsers(withAccountCreation);

      if (useExporter) {
        await onExportFile();
        onClose();
      }

      if (!useExporter) onNext();
    } catch (e) {
      tableRef.current.setPage(0);
      const msg = e instanceof AxiosError ? e.response?.data?.error : (e as Error).message;
      notify?.({ message: `Issue: ${msg}`, type: 'error' });
    } finally {
      setIsLoading(false);
    }
  };

  const recipientsWithoutAccounts = rows.length - (users?.length ?? 0) > 0;
  const recipientsWithoutCardIds = rowsWithPhysicalCard.length - (paymentAccounts?.length ?? 0) > 0;

  const handleMissingAccountExport = async () => {
    const zip = new JSZip();
    const userEmails = users.map((user) => user?.email);
    const missingAccounts = rows.filter(
      (row) => !userEmails.includes(row[JEVSFileMapper[JEVSRecipientColumns.EMAIL]]),
    );
    const usersMissingCardIds = rowsWithPhysicalCard.filter((row) => {
      const user = users?.find((user) => user.email === row.email);
      const paymentAccount = paymentAccounts?.find(
        (account) =>
          account.recipient?.referenceId === user?.id && account.type === 'PHYSICAL_CARD',
      );
      return !paymentAccount;
    });

    if (missingAccounts.length) {
      const CSV = exportToRecipientsToCSV(missingAccounts);
      zip.file('missing_accounts.csv', CSV);
    }

    if (usersMissingCardIds.length) {
      const CSV = exportToRecipientsToCSV(usersMissingCardIds);
      zip.file('missing_card_ids.csv', CSV);
    }

    const content = await zip.generateAsync({ type: 'blob' });
    const zipFile = new Blob([content], { type: 'application/zip' });

    const url = URL.createObjectURL(zipFile);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'missing_accounts.zip';
    document.body.appendChild(a);
    a.click();

    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    notify?.({
      message: 'successfully exported missing accounts',
      type: 'success',
    });
  };

  return (
    <>
      <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%', gap: '8px' }}>
        <Box textAlign="right">
          <FormControlLabel
            control={
              <Switch
                defaultChecked
                value={useExporter}
                onChange={() => setUseExporter(!useExporter)}
              />
            }
            label="Export recipients for USIO bulk card creation"
          />
        </Box>
        <Alert severity="info">
          <AlertTitle>
            Stats{' '}
            {(isUsersLoading || isAccountsLoading) && (
              <CircularProgress color="inherit" size={10} />
            )}
          </AlertTitle>
          <ul>
            <li>
              {isUsersLoading ? 'N/A' : rows.length - (users?.length ?? 0)} recipients need to be
              imported.
            </li>
            <li>
              {isAccountsLoading
                ? 'N/A'
                : rowsWithPhysicalCard.length - (paymentAccounts?.length ?? 0)}{' '}
              physical cards need to be requested.
            </li>
          </ul>
          <Button
            onClick={handleMissingAccountExport}
            startIcon={<FileDownloadIcon />}
            loading={isUsersLoading || isAccountsLoading}
            disabled={!recipientsWithoutAccounts && !recipientsWithoutCardIds}
          >
            Export which accounts are missing
          </Button>
        </Alert>
        <StyledDataGrid
          rows={sortErrorsToTop({
            rows,
            errors,
            accessor: JEVSFileMapper[JEVSRecipientColumns.SALESFORCE_ID],
          })}
          getRowId={(row) => String(row[JEVSFileMapper[JEVSRecipientColumns.SALESFORCE_ID]])}
          getRowClassName={(params) =>
            errors[params.row[JEVSFileMapper[JEVSRecipientColumns.SALESFORCE_ID]]]
              ? 'validation-error'
              : ''
          }
          columns={columns}
          apiRef={tableRef}
        />
        <ProgressBar isLoading={batchSize < totalCount && isLoading} progress={progress} />
        <Stack direction="row" mt={5}>
          <Button color="inherit" onClick={onBack} sx={{ mr: 1 }}>
            Back
          </Button>
          <Box sx={{ flex: '1 1 auto' }} />
          <Button
            type="submit"
            onClick={async () => {
              if (useExporter) return onImport();
              importModal.show();
            }}
            disabled={isUsersLoading || isAccountsLoading}
            loading={isLoading}
            variant="contained"
          >
            {useExporter ? 'Export Bulk CSV and close modal' : 'Import'}
          </Button>
        </Stack>
      </Box>
      <ImportConfirmation {...importModal} onConfirm={onImport} />
    </>
  );
};
