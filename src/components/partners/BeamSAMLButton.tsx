import LockOpen from '@mui/icons-material/LockOpen';
import { Button, Tooltip } from '@mui/material';
import { getConfig } from '@utils/config';

function BeamSAMLButton({ partner, baseUrl }) {
  const beamProvider = getConfig('PUBLIC_BEAM_SAML_PROVIDER_ID');
  const beamTenant = getConfig('PUBLIC_BEAM_TENANT_ID');
  const externalId = partner?.externalId;
  if (!beamProvider || !beamTenant || !externalId) return null;

  const href = new URL(`/partner/${externalId}/auth/signin`, baseUrl);
  href.searchParams.append('beam_provider', beamProvider);
  href.searchParams.append('beam_tenant', beamTenant);

  return (
    <Tooltip
      title={`This link allows for authentication to ${externalId} via your internal Beam Okta account. Follow the links and directions on the page. If you encounter any issues, please let engineering know and we'll fix it up.`}
      placement="bottom"
    >
      <span>
        <Button
          href={href.toString()}
          startIcon={<LockOpen />}
          target="_blank"
          rel="noopener noreferrer"
        >
          Sign In to {externalId}
        </Button>
      </span>
    </Tooltip>
  );
}

export default BeamSAMLButton;
