import { Role_Display } from '@components/admins/CreateAdminDrawer';
import HiddenInput from '@components/forms/HiddenInput';
import SelectInput from '@components/forms/SelectInput';
import TextInput from '@components/forms/TextInput';
import { Add, Delete } from '@mui/icons-material';
import { Button, Stack } from '@mui/material';
import { Role } from 'pages/api/types/identity';
import { FormProvider, useFieldArray } from 'react-hook-form';

export default function AdminAccounts({ partnerId, formProps }): JSX.Element {
  const { fields, append, remove } = useFieldArray({
    control: formProps.control,
    name: 'users',
  });

  const DEFAULT_USER = {
    partnerId,
    name: '',
    email: '',
    validatedEmail: true,
    role: Role.StandardPayment,
  };

  return (
    <FormProvider {...formProps}>
      <Stack>
        {fields.map((user, idx) => (
          <Stack key={user.id} gap={1} direction="row">
            <HiddenInput name={`users.${idx}.partnerId`} value={partnerId} />
            <HiddenInput name={`users.${idx}.validatedEmail`} value={true} />
            <TextInput name={`users.${idx}.name`} label="Name" required />
            <TextInput name={`users.${idx}.email`} label="Email" type="email" required />
            <SelectInput
              name={`users.${idx}.role`}
              label="Role"
              options={Object.keys(Role_Display).map((role) => ({
                id: role,
                name: Role_Display[role].name,
              }))}
            />
            <Button
              size="small"
              variant="text"
              startIcon={<Delete />}
              onClick={() => {
                remove(idx);
              }}
            >
              Remove
            </Button>
          </Stack>
        ))}
        <Button
          variant="outlined"
          startIcon={<Add />}
          onClick={() => append(DEFAULT_USER)}
          sx={{ alignSelf: 'flex-start' }}
        >
          Add Admin
        </Button>
      </Stack>
    </FormProvider>
  );
}
