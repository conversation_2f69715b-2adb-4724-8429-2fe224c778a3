import CreateProgramFields from '@components/programs/CreateProgramFields';
import { Add, Delete } from '@mui/icons-material';
import { Button, Divider, Stack, Typography } from '@mui/material';
import { useEffect } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';

export default function ProgramSetup(): JSX.Element {
  const { getValues, setValue } = useFormContext();
  const {
    externalId,
    parentId,
    applicantTypes: { create: applicantTypes },
    funds: { create: funds },
  } = getValues();

  const { fields, append, remove } = useFieldArray<
    { programs: { create: ({ id?: string; tempId?: string } & typeof DEFAULT_PROGRAM)[] } },
    'programs.create',
    'tempId'
  >({ name: 'programs.create', keyName: 'tempId' });

  const DEFAULT_PROGRAM = {
    name: '',
    heroImage: '',
    programFunds: { create: funds.map(({ id }) => ({ fundId: id })) },
    programFeatures: { create: [] },
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: intentionally only do this on first render
  useEffect(() => {
    if (fields.length === 0) setValue('programs.create', [DEFAULT_PROGRAM]);
  }, []);

  return (
    <>
      <Stack gap={4} divider={<Divider />}>
        {fields.map((program, idx) => (
          <Stack key={program.tempId} gap={2}>
            <Stack direction="row" gap={1}>
              <Typography variant="h5">Program {idx + 1}</Typography>
              <Button
                size="small"
                variant="text"
                startIcon={<Delete />}
                onClick={() => remove(idx)}
              >
                Remove
              </Button>
            </Stack>
            <CreateProgramFields
              id={program.id ?? program.tempId}
              funds={funds}
              partner={{ externalId, parentId }}
              prefix={`programs.create.${idx}`}
              additionalApplicantTypes={applicantTypes}
            />
          </Stack>
        ))}
        <Button
          variant="outlined"
          startIcon={<Add />}
          onClick={() => append(DEFAULT_PROGRAM)}
          sx={{ alignSelf: 'flex-start' }}
        >
          Add Program
        </Button>
      </Stack>
    </>
  );
}
