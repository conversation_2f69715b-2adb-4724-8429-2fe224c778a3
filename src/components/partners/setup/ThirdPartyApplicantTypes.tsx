import HiddenInput from '@components/forms/HiddenInput';
import TextInput from '@components/forms/TextInput';
import { Add, Delete } from '@mui/icons-material';
import { Alert, Button, Stack } from '@mui/material';
import { useMemo } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { v4 as uuidv4 } from 'uuid';

interface CreateApplicantType {
  id?: string;
  tempId?: string;
  name: string;
}

function ApplicantType({
  id,
  idx,
  remove,
}: { id?: string; idx: number; remove: (idx: number) => void }): JSX.Element {
  const typeId = useMemo(() => id ?? uuidv4(), [id]);
  return (
    <Stack gap={1} direction="row">
      <HiddenInput name={`applicantTypes.create.${idx}.id`} value={typeId} />
      <TextInput name={`applicantTypes.create.${idx}.name`} label="Name" />
      <Button size="small" variant="text" startIcon={<Delete />} onClick={() => remove(idx)}>
        Remove
      </Button>
    </Stack>
  );
}

export default function ThirdPartyApplicantTypes(): JSX.Element {
  const { getValues } = useFormContext();
  const { fields, append, remove } = useFieldArray<
    { applicantTypes: { create: CreateApplicantType[] } },
    'applicantTypes.create',
    'tempId'
  >({ name: 'applicantTypes.create', keyName: 'tempId' });

  return (
    <Stack gap={2}>
      {getValues('parentId') && (
        <Alert severity="info">
          This partner will inherit its parent's third party applicant types. They do not need to be
          duplicated. Only create new types that are unique to this child partner.
        </Alert>
      )}

      {fields.map((type, idx) => (
        <ApplicantType key={type.tempId} {...type} idx={idx} remove={remove} />
      ))}
      <Button
        variant="outlined"
        startIcon={<Add />}
        onClick={() => append({ name: '' })}
        sx={{ alignSelf: 'flex-start' }}
      >
        Add
      </Button>
    </Stack>
  );
}
