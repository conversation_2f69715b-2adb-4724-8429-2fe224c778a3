import AddressInput, { MAILING_ADDRESS_FIELDS } from '@components/forms/AddressInput';
import AutocompleteInput from '@components/forms/AutocompleteInput';
import HiddenInput from '@components/forms/HiddenInput';
import TextInput from '@components/forms/TextInput';
import { Checkbox, FormControlLabel, Stack } from '@mui/material';
import { useEffect, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { v4 as uuidv4 } from 'uuid';

export default function PlatformSetup(): JSX.Element {
  const { register, unregister } = useFormContext();

  const [hasMailingAddress, setHasMailingAddress] = useState(false);

  useEffect(() => {
    for (const field of MAILING_ADDRESS_FIELDS) {
      if (hasMailingAddress) register(`address.create.${field}`);
      else unregister(`address.create.${field}`);
    }
  }, [register, unregister, hasMailingAddress]);

  return (
    <Stack gap={2}>
      <HiddenInput name="partnerWhitelabelings.create.platformName" value="Application Portal" />
      <HiddenInput name="id" value={uuidv4()} />
      <TextInput name="name" label="Name" required />
      <TextInput name="externalId" label="URL Component/External ID" required />
      <TextInput name="email" label="Contact Email" type="email" required />
      <TextInput name="phone" label="Contact Phone" type="tel" />
      <TextInput
        name="partnerWhitelabelings.create.senderEmail"
        label="Whitelabeled Email Sender"
        type="email"
      />
      <FormControlLabel
        label="Add Mailing Address"
        control={
          <Checkbox
            checked={hasMailingAddress}
            onChange={() => setHasMailingAddress(!hasMailingAddress)}
          />
        }
      />
      <AutocompleteInput
        name="partnerFeatures.create"
        label="Features"
        resource="features"
        multiple
        transformers={{
          from: (option: { id: string } | { featureId: string; enabled: boolean }) =>
            'featureId' in option ? option : { featureId: option.id, enabled: true },
          id: (value: { featureId: string; enabled: boolean }) => value.featureId,
        }}
      />
      {hasMailingAddress && <AddressInput name="address.create" label="Mailing Address" />}
    </Stack>
  );
}
