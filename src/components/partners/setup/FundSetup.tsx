import FundFields from '@components/funds/FundFields';
import { Add, Delete } from '@mui/icons-material';
import { Button, Divider, Stack, Typography } from '@mui/material';
import type { UseFormReturnType } from '@refinedev/react-hook-form';
import { useEffect } from 'react';
import { useFieldArray } from 'react-hook-form';

export default function FundSetup({
  paymentsFormProps,
}: { paymentsFormProps: UseFormReturnType }): JSX.Element {
  const DEFAULT_FUND = {
    name: '',
    startingBalance: '',
  };
  const { fields, append, remove } = useFieldArray<
    { funds: { create: ({ id?: string; tempId?: string } & typeof DEFAULT_FUND)[] } },
    'funds.create',
    'tempId'
  >({ name: 'funds.create', keyName: 'tempId' });

  const { getValues: getPaymentsValues, unregister: paymentsUnregister } = paymentsFormProps;
  useEffect(() => {
    return () => {
      getPaymentsValues().fundingSources?.create.map((_, idx) =>
        paymentsUnregister(`fundingSources.create.${idx}.clientId`),
      );
    };
  }, [getPaymentsValues, paymentsUnregister]);

  return (
    <Stack gap={4} divider={<Divider />}>
      {fields.map((fund, idx) => (
        <Stack key={fund.tempId} gap={1}>
          <Stack direction="row" gap={1}>
            <Typography variant="h5">Fund {idx + 1}</Typography>
            <Button size="small" variant="text" startIcon={<Delete />} onClick={() => remove(idx)}>
              Remove
            </Button>
          </Stack>
          <FundFields
            prefix={`funds.create.${idx}`}
            paymentsPrefix={`fundingSources.create.${idx}`}
            paymentsFormProps={paymentsFormProps}
            initialData={{ id: fund.id ?? fund.tempId }}
          />
        </Stack>
      ))}
      <Button
        variant="outlined"
        startIcon={<Add />}
        onClick={() => append(DEFAULT_FUND)}
        sx={{ alignSelf: 'flex-start' }}
      >
        Add Fund
      </Button>
    </Stack>
  );
}
