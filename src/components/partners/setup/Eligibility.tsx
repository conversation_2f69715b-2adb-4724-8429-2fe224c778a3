import TextInput from '@components/forms/TextInput';
import { Add, Delete } from '@mui/icons-material';
import { Button, Stack } from '@mui/material';
import { useState } from 'react';

export default function EligibilitySetup(): JSX.Element {
  const [addConfig, setAddConfig] = useState(false);

  return (
    <Stack>
      {addConfig && (
        <Stack>
          <TextInput name={'eligibilityConfigs.create.title'} label="Title" required />
          <TextInput name={'eligibilityConfigs.create.description'} label="Description" required />
          <TextInput name={'eligibilityConfigs.create.copy'} label="Copy" />
          <TextInput name={'eligibilityConfigs.create.message'} label="Message" />
          <Button
            size="small"
            variant="text"
            startIcon={<Delete />}
            onClick={() => setAddConfig(false)}
            sx={{ alignSelf: 'end' }}
          >
            Remove
          </Button>
        </Stack>
      )}
      {!addConfig && (
        <Button
          variant="outlined"
          startIcon={<Add />}
          onClick={() => setAddConfig(true)}
          sx={{ alignSelf: 'flex-start' }}
        >
          Add Eligibility Config
        </Button>
      )}
    </Stack>
  );
}
