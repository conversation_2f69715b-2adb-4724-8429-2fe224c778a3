import ColorPicker from '@components/forms/ColorPicker';
import BrandingImageInput from '@components/forms/images/BrandingImageInput';
import ImageUploadModal from '@components/forms/images/ImageUploadModal';
import { useModal } from '@refinedev/core';
import { useState } from 'react';
import { useFormContext } from 'react-hook-form';

export default function Branding(): JSX.Element {
  const { getValues } = useFormContext();

  const modalProps = useModal();
  const [openImageField, setOpenImageField] = useState('');
  const { externalId } = getValues();

  const openModal = (field: string) => {
    setOpenImageField(field);
    modalProps.show();
  };

  return (
    <>
      <ColorPicker name="partnerWhitelabelings.create.brandColor" />
      <BrandingImageInput
        name="partnerWhitelabelings.create.logo"
        label="Logo"
        openModal={(): void => openModal('logo')}
      />
      <BrandingImageInput
        name="partnerWhitelabelings.create.favicon"
        label="Favicon"
        openModal={(): void => openModal('favicon')}
      />
      <ImageUploadModal
        partnerExternalId={externalId}
        fieldToUpdate={`partnerWhitelabelings.create.${openImageField}`}
        fieldLabel={openImageField}
        imageFilename={openImageField}
        title="Upload"
        modalProps={modalProps}
      />
    </>
  );
}
