import { extractError } from '@components/forms/utils';
import MarkdownEditor from '@components/markdown/Editor';
import { CloseOutlined } from '@mui/icons-material';
import { Box, Drawer, IconButton, Stack, TextField, Typography } from '@mui/material';
import type { BaseKey } from '@refinedev/core';
import { Create, Edit } from '@refinedev/mui';
import { type UseModalFormReturnType, useForm } from '@refinedev/react-hook-form';
import { Controller, FormProvider } from 'react-hook-form';

export const EditBannerDrawer = (props: UseModalFormReturnType & { incidentId?: BaseKey }) => {
  const {
    modal: { visible, close },
    setValue,
    formState: { errors },
    incidentId,
    control,
    refineCore: { formLoading, queryResult },
    saveButtonProps: { disabled, onClick },
    reset,
    handleSubmit,
  } = props;

  const incident = queryResult?.data?.data;

  const onSubmit = async (_, e) => {
    props.refineCore.setId(incidentId);
    const { severity } = props.getValues();
    props.setValue('severity', Number(severity));
    return onClick(e);
  };

  return (
    <Drawer
      open={visible}
      onClose={() => {
        close();
      }}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Edit
        resource="partnerIncidents"
        saveButtonProps={{ ...props, onClick: handleSubmit(onSubmit) }}
        isLoading={false}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <Box
          component="form"
          sx={{ display: 'flex', flexDirection: 'row', flexFlow: 'row', alignItems: 'center' }}
        >
          <FormProvider {...props}>
            <Stack spacing={2}>
              <MarkdownEditor
                name="message"
                label="Message Content"
                instructions={
                  <Typography variant="subtitle2">
                    Additionally, placeholder values for the partner can be inserted using the
                    syntax {'{'}PARTNER_EMAIL{'}'}. PARTNER_EMAIL, PARTNER_PHONE, and PARTNER_NAME
                    are all supported.
                  </Typography>
                }
                required
              />
              <Controller
                control={control}
                name="severity"
                rules={{ min: 0 }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    type="number"
                    label="Display Order"
                    sx={{ width: '100%' }}
                    error={!!extractError(errors, 'incidentMessage.edit.severity')}
                    helperText={
                      extractError(errors, 'incidentMessage.edit.severity')?.message ??
                      'Determines the order of display, from top to bottom, when multiple banners are active. Lower numbers will display at the top.'
                    }
                  />
                )}
              />
            </Stack>
          </FormProvider>
        </Box>
      </Edit>
    </Drawer>
  );
};
