import {
  <PERSON><PERSON>,
  <PERSON>,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
  colors,
} from '@mui/material';
import { type BaseKey, useCreate, type useModalReturnType, useUpdate } from '@refinedev/core';
import { useState } from 'react';
import type { CommunicationChannels, CommunicationChannelsConfig } from 'types/notification';

type EditCommunicationChannelDialogProps = useModalReturnType & {
  close: () => void;
  communicationChannel: CommunicationChannels;
  configId?: BaseKey;
  currentConfig: CommunicationChannelsConfig;
  currentStatus: boolean;
  onSuccess: () => void;
  partnerId: BaseKey;
  visible: boolean;
};

export const EditCommunicationChannelDialog = ({
  close,
  communicationChannel,
  configId,
  currentConfig,
  currentStatus,
  onSuccess,
  partnerId,
  visible,
}: EditCommunicationChannelDialogProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const { mutate } = useUpdate();
  const { mutate: create } = useCreate();
  const enabledOrDisabled = !currentStatus ? 'enabled' : 'disabled';
  async function onSubmit() {
    setIsLoading(true);
    if (configId) {
      await mutate(
        {
          resource: 'partner_communication_channels_configurations',
          dataProviderName: 'config',
          values: {
            config: {
              ...currentConfig,
              channels: { ...currentConfig?.channels, [communicationChannel]: !currentStatus },
            },
          },
          id: configId,
          successNotification: () => {
            return {
              message: `Updated Partner communication channel ${communicationChannel} to ${enabledOrDisabled}`,
              type: 'success',
            };
          },
        },
        {
          onSuccess: () => {
            onSuccess();
            close();
          },
          onSettled: () => {
            setIsLoading(false);
          },
        },
      );
    } else {
      await create(
        {
          resource: 'partner_communication_channels_configurations',
          dataProviderName: 'config',
          values: {
            config: {
              ...currentConfig,
              channels: { ...currentConfig?.channels, [communicationChannel]: !currentStatus },
            },
            partnerId,
          },
          successNotification: () => {
            return {
              message: `Updated Partner communication channel ${communicationChannel} to ${enabledOrDisabled}`,
              type: 'success',
            };
          },
        },
        {
          onSuccess: () => {
            onSuccess();
            close();
          },
          onSettled: () => {
            setIsLoading(false);
          },
        },
      );
    }
  }

  const proceedAtYourOwnRisk =
    'Editing the partner communication channels of a live partner/program carries lots of risk! \
  Please ensure that you understand the impact of these changes.';

  return (
    <Dialog
      sx={{ '& .MuiDialog-paper': { width: '80%', maxHeight: 435 } }}
      maxWidth="xs"
      open={visible}
    >
      <DialogTitle>Are you sure?</DialogTitle>
      <DialogContent dividers>
        <Box gap={'1rem'}>
          <Typography variant="body1">
            Do you want to continue with{' '}
            <span style={{ color: !currentStatus ? colors.green['600'] : colors.red['600'] }}>
              <strong>{!currentStatus ? 'enabling' : 'disabling'}</strong>
            </span>
            : <code>{communicationChannel}</code>? This will{' '}
            {!currentStatus
              ? `turn on all ${communicationChannel}`
              : `turn off all ${communicationChannel}`}{' '}
            for this partner
          </Typography>
          <Alert severity="warning" sx={{ marginTop: '2rem' }}>
            {proceedAtYourOwnRisk}
          </Alert>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button autoFocus onClick={close}>
          Cancel
        </Button>
        <Button onClick={onSubmit} loading={isLoading} variant="contained">
          Set to {enabledOrDisabled}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
