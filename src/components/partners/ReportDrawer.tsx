import CheckboxInput from '@components/forms/CheckboxInput';
import DatePicker from '@components/forms/DatePicker';
import PasswordGenerator from '@components/forms/PasswordGenerator';
import SelectInput from '@components/forms/SelectInput';
import TextInput from '@components/forms/TextInput';
import { CloseOutlined } from '@mui/icons-material';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>er, IconButton, InputAdornment, Stack } from '@mui/material';
import { useGetIdentity, useNotification } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import axios, { AxiosError } from 'axios';
import { type PushResponse, ReportTypes } from 'pages/api/types/report';
import { useEffect, useState } from 'react';
import { FormProvider } from 'react-hook-form';

const ReportCopies = {
  [ReportTypes.Tax_Compliance_1099]: {
    password:
      'By clicking on the Password Link, you are copying the URL that you will share with the partner. This URL takes the partner to the page where they can view the password for the 1099 file.',
    url: 'By clicking on the Report Link, you are copying the URL that you will share with the partner so they can download the 1099 file. The 1099 file will contain the W9 PDFs and the CSV file depending on which checkboxes you selected above. Once the partner downloads the file, they will use the password to access the contents of the file (CSV and W9s).',
  },
};

export default function ReportDrawer({ visible, close, partner }): JSX.Element {
  const [error, setError] = useState<string>();
  const [links, setLinks] = useState<PushResponse[]>();
  const [isLoading, setLoading] = useState<boolean>(false);
  const { open: notify } = useNotification();
  const { data: author } = useGetIdentity();

  const formProps = useForm({ refineCoreProps: { redirect: 'show' } });

  const { handleSubmit, saveButtonProps, watch, getValues, setValue } = formProps;

  const onClose = () => {
    setLinks(undefined);
    setError(undefined);
    setLoading(false);
    formProps.reset();
    close();
  };

  const copyToClipboard = (url) => {
    if (!url) {
      notify?.({ message: 'No data found.', type: 'error' });
      return;
    }
    navigator.clipboard.writeText(url);
  };

  const onSubmit = async (e): Promise<void> => {
    const { type, password, includeW9Files, includeReport, reportYear, awardedAmountThreshold } =
      getValues();
    try {
      if (!includeW9Files && !includeReport) {
        setError('Please select at least one data option.');
        return;
      }
      if (!reportYear) {
        setError('Please select a year.');
        return;
      }
      setLoading(true);
      setError(undefined);
      const response = await axios.post(
        '/api/platform/reports',
        {
          type: type,
          partnerId: partner.id,
          reportYear: new Date(reportYear).getFullYear(),
          password,
          includeW9Files,
          includeReport,
          author,
          ...(awardedAmountThreshold && { awardedAmountThreshold: Number(awardedAmountThreshold) }),
        },
        { headers: { 'Content-Type': 'application/json' } },
      );

      notify?.({ message: 'Report is created successfully.', type: 'success' });
      setLinks(response?.data);
    } catch (e) {
      let message = 'Unknown';
      if (e instanceof AxiosError && e?.response) {
        if (e.response.status > 500) {
          message = 'System timed out';
          setError('System timed out! Please wait 5-10 minutes and check audit logs.');
        } else message = e.response.data?.error;
      }
      notify?.({ message: `Issue with generating ${type} report: ${message}`, type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const reportYear = watch('reportYear');
  const type = watch('type');

  useEffect(() => {
    if (type === ReportTypes.Tax_Compliance_1099 && !reportYear) {
      setValue('reportYear', new Date());
    }
  }, [type, reportYear, setValue]);

  const disabledForm = !!links;

  return (
    <Drawer
      open={visible}
      onClose={onClose}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Edit
        resource="partners"
        title="Generate Report"
        saveButtonProps={{
          disabled: isLoading || saveButtonProps.disabled || disabledForm,
          onClick: handleSubmit(onSubmit),
        }}
        isLoading={isLoading}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton
              onClick={onClose}
              sx={{ width: '30px', height: '30px' }}
              disabled={isLoading}
            >
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <FormProvider {...formProps}>
          <Stack component="form" gap={1} sx={{ mt: 2 }}>
            <SelectInput
              name="type"
              label="Report Type"
              options={[{ id: ReportTypes.Tax_Compliance_1099, name: '1099 Tax Report' }]}
              disabled={disabledForm}
            />
            {watch('type') === ReportTypes.Tax_Compliance_1099 && (
              <>
                <TextInput
                  name="password"
                  label="File Password"
                  required
                  type="password"
                  disabled
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <PasswordGenerator
                          onChange={(value) => setValue('password', value)}
                          disabled={disabledForm}
                        />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ '.MuiInputBase-root': { paddingRight: '0px' } }}
                />
                <DatePicker
                  label="Report Year"
                  onChange={(value) => setValue('reportYear', value)}
                  value={watch('reportYear')}
                  disablePast={false}
                  disableFuture
                  views={['year']}
                  disabled={disabledForm}
                />
                <TextInput
                  name="awardedAmountThreshold"
                  label="Awarded Amount Threshold (cents)"
                  type="number"
                />
                <CheckboxInput
                  name="includeW9Files"
                  label="Attach W9 Files"
                  disabled={disabledForm}
                />
                <CheckboxInput
                  name="includeReport"
                  label="Query Tax Data"
                  disabled={disabledForm}
                />
              </>
            )}
            {isLoading && (
              <Alert severity="warning">This may take a while. Please be patient!</Alert>
            )}
            {error && <Alert severity="error">{error}</Alert>}
            {!!links && (
              <>
                {links?.map(({ previewURL, type }, idx) => (
                  <Stack
                    component="div"
                    key={`${type}-${
                      // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
                      idx
                    }`}
                  >
                    {!!ReportCopies[watch('type')][type] && (
                      <Alert security="info">{ReportCopies[watch('type')][type]}</Alert>
                    )}
                    <Button onClick={() => copyToClipboard(previewURL)} sx={{ mt: 1 }}>
                      {type === 'password'
                        ? 'Copy this PASSWORD link to share.'
                        : 'Copy this REPORT link to share.'}
                    </Button>
                  </Stack>
                ))}
              </>
            )}
          </Stack>
        </FormProvider>
      </Edit>
    </Drawer>
  );
}
