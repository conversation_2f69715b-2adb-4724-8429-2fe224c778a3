import {
  <PERSON><PERSON>,
  <PERSON>Item,
  <PERSON>ItemAvatar,
  ListItemText,
  ListSubheader,
  Switch,
  Tooltip,
} from '@mui/material';
import { type BaseKey, useModal } from '@refinedev/core';
import { List } from '@refinedev/mui';
import useCommunicationConfiguration from 'hooks/communicationConfigurations/useCommunicationConfiguration';
import { Fragment, useState } from 'react';
import {
  CommunicationChannels,
  NotificationType,
  type PlatformNotificationType,
} from 'types/notification';
import { AddPartnerNotificationOverrideDrawer } from './AddPartnerNotificationOverrideDrawer';
import { EditPartnerNotificationConfirmationDialog } from './EditPartnerNotificationConfirmationDialog';

const applicantMessageNotificationOptions = [
  {
    value: NotificationType.ParticipantCaseCommentAssignee,
    label: `Current assignee's email (if one exists)`,
  },
  {
    value: NotificationType.ParticipantCaseCommentSupport,
    label: 'Program support email',
  },
];
export const PartnerNotifications = ({ partnerId }: { partnerId: BaseKey }) => {
  const createPartnerNotificationDrawerFormProps = useModal();
  const editPartnerNotificationModal = useModal();
  const { configuration, onEnableNotification } = useCommunicationConfiguration(partnerId);
  const [isLoading, setLoading] = useState<boolean>(false);

  const [selectedNotificationType, setSelectedNotificationType] = useState<{
    type: PlatformNotificationType;
    channel: CommunicationChannels;
  }>();

  return (
    <>
      <List
        title="Partner notifications"
        resource="partner_communication_channels_configurations"
        breadcrumb={false}
        createButtonProps={{
          onClick: () => createPartnerNotificationDrawerFormProps.show(),
          children: 'Add Notification Override',
        }}
      >
        {Object.values(CommunicationChannels)
          .filter((channelType) => !!configuration?.config?.[channelType])
          .map((channelType) => (
            <Fragment key={channelType}>
              <ListSubheader>{channelType}</ListSubheader>
              {Object.keys(configuration?.config?.[channelType] ?? {}).map((notificationType) => (
                <Tooltip
                  title={`when this is toggled on ${channelType} WILL be sent for this event type`}
                  placement="right"
                  key={notificationType}
                >
                  <ListItem sx={{ width: 'fit-content' }}>
                    <ListItemAvatar>
                      <Switch
                        checked={configuration?.config?.[channelType][notificationType]}
                        onChange={() => {
                          setSelectedNotificationType({
                            type: notificationType as PlatformNotificationType,
                            channel: channelType as CommunicationChannels,
                          });
                          editPartnerNotificationModal.show();
                        }}
                      />
                    </ListItemAvatar>
                    <ListItemText sx={{ whiteSpace: 'pre-line' }} primary={notificationType} />
                  </ListItem>
                </Tooltip>
              ))}
            </Fragment>
          ))}
        <Fragment>
          <ListSubheader>Applicant message notifications</ListSubheader>
          <ListItem sx={{ width: 'fit-content' }}>
            <ListItemText sx={{ whiteSpace: 'pre-line' }}>
              Select where notifications about new messages from applicants will be sent.
            </ListItemText>
          </ListItem>
          {applicantMessageNotificationOptions.map((option) => (
            <ListItem key={option.value} sx={{ width: 'fit-content' }}>
              <ListItemAvatar>
                <Switch
                  checked={configuration?.config?.[CommunicationChannels.EMAIL]?.[option.value]}
                  onChange={() => {
                    onEnableNotification({
                      notificationType: option.value as PlatformNotificationType,
                      communicationChannel: CommunicationChannels.EMAIL,
                      enabled:
                        !configuration?.config?.[CommunicationChannels.EMAIL]?.[option.value],
                    });
                  }}
                />
              </ListItemAvatar>
              <ListItemText sx={{ whiteSpace: 'pre-line' }} primary={option.label} />
            </ListItem>
          ))}
        </Fragment>
      </List>

      <AddPartnerNotificationOverrideDrawer
        {...createPartnerNotificationDrawerFormProps}
        close={createPartnerNotificationDrawerFormProps.close}
        partnerId={partnerId}
      />
      {selectedNotificationType ? (
        <EditPartnerNotificationConfirmationDialog
          {...editPartnerNotificationModal}
          currentConfig={configuration?.config}
          notificationType={selectedNotificationType.type}
          currentChannel={selectedNotificationType.channel}
          currentStatus={
            configuration?.config?.[selectedNotificationType.channel]?.[
              selectedNotificationType.type
            ]
          }
          onConfirm={async () => {
            setLoading(true);
            await onEnableNotification({
              notificationType: selectedNotificationType.type,
              communicationChannel: selectedNotificationType.channel,
              enabled:
                !configuration?.config?.[selectedNotificationType.channel]?.[
                  selectedNotificationType.type
                ],
              onSuccess() {
                editPartnerNotificationModal.close();
              },
              onSettled() {
                setLoading(false);
              },
            });
          }}
          isLoading={isLoading}
          close={editPartnerNotificationModal.close}
        />
      ) : null}
    </>
  );
};
