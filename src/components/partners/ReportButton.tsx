import SummarizeIcon from '@mui/icons-material/Summarize';
import { Button } from '@mui/material';
import { CanAccess } from '@refinedev/core';

export default function ReportButton({ partner, openDrawer }): JSX.Element {
  if (partner?.childPartners?.length) return <></>;

  return (
    <CanAccess resource="partners" action="reports">
      <Button onClick={() => openDrawer()} startIcon={<SummarizeIcon />}>
        Generate Report
      </Button>
    </CanAccess>
  );
}
