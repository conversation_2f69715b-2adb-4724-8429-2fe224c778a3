import EditIcon from '@mui/icons-material/Edit';
import { IconButton, ListItem, ListItemIcon, ListItemText } from '@mui/material';
import { CanAccess, useModal } from '@refinedev/core';
import { List } from '@refinedev/mui';
import { useState } from 'react';
import EditTenantDrawer from './EditTenantDrawer';
import type { TenantRole } from './EditTenantDrawer';
import HealthCheckButton from './HealthCheckButton';
import MigrationButton from './MigrationButton';

export default function PartnerIdentityConfig({ partner, refetch }): JSX.Element {
  const [selectedRole, setSelectedRole] = useState<string>();
  const getTenantId = (role: string): string | undefined => {
    const identityConfig = partner?.config?.identity;
    if (identityConfig?.[role]?.tenantId) return identityConfig[role].tenantId;
  };
  const editDrawer = useModal();

  return (
    <CanAccess resource="partners" action="identity">
      <List
        resource="partner"
        title="Identity Config"
        breadcrumb={false}
        headerButtons={() => (
          <>
            <MigrationButton partner={partner} objectTypes={[]} />
            <HealthCheckButton />
          </>
        )}
      >
        {['applicant', 'advocate'].map((role) => (
          <ListItem>
            <ListItemText primary={role} secondary={getTenantId(role) ?? 'no custom tenant'} />
            <ListItemIcon>
              <IconButton
                onClick={() => {
                  setSelectedRole(role);
                  editDrawer.show();
                }}
              >
                <EditIcon fontSize="small" />
              </IconButton>
            </ListItemIcon>
          </ListItem>
        ))}
      </List>
      <EditTenantDrawer
        {...editDrawer}
        close={() => {
          editDrawer.close();
          setSelectedRole(undefined);
        }}
        tenantRole={selectedRole as TenantRole}
        partnerId={partner?.id}
        tenantId={getTenantId(selectedRole as string)}
        onSuccess={() => refetch()}
      />
    </CanAccess>
  );
}
