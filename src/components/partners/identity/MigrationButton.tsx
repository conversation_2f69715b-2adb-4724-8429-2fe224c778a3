import { PermIdentity } from '@mui/icons-material';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>le,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
} from '@mui/material';
import { useModal, useNotification } from '@refinedev/core';
import axios, { AxiosError } from 'axios';
import { useState } from 'react';

export default function MigrationButton({ partner, objectTypes }): JSX.Element {
  if (!partner) return <></>;
  const [isLoading, setLoading] = useState<boolean>(false);
  const { open: notify } = useNotification();
  const confirmationDialog = useModal();

  const onSubmit = async () => {
    setLoading(true);
    axios
      .post(
        '/api/platform/identity/migratePermissions',
        {
          partnerId: partner.id,
          objectTypes,
        },
        { headers: { 'Content-Type': 'application/json' } },
      )
      .then(() => {
        notify?.({
          message: 'Permissions and relations successfully migrated.',
          type: 'success',
        });
      })
      .catch((e) => {
        const msg = e instanceof AxiosError ? e.response?.data?.error : (e as Error).message;
        notify?.({ message: `Migration encountered an error: ${msg}`, type: 'error' });
      })
      .finally(() => setLoading(false));
  };

  return (
    <>
      <Button
        onClick={() => confirmationDialog.show()}
        startIcon={<PermIdentity />}
        loading={!!isLoading}
        loadingPosition="start"
        variant="contained"
      >
        Migrate Permissions
      </Button>
      <Dialog
        open={confirmationDialog.visible}
        onClose={confirmationDialog.close}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle>Are you sure?</DialogTitle>
        <DialogContent sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Alert severity="warning">
            <AlertTitle>For Special Circumstances Only</AlertTitle>
            <ul style={{ display: 'flex', flexDirection: 'column', gap: 10 }}>
              <li>
                This tool is only intended for use in special cases. This action will migrate
                existing entity relationships into SpiceDB.
              </li>

              {['applicant', 'advocate'].map((role) => (
                <li>
                  Partner uses{' '}
                  {partner.config?.identity?.[role]?.tenantId
                    ? `custom tenant ID (${partner.config.identity[role].tenantId})`
                    : 'Beam default tenant ID'}{' '}
                  for {role} role.
                </li>
              ))}
              <li>The migration might take time, please do not close the window.</li>
            </ul>
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              confirmationDialog.close();
            }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              confirmationDialog.close();
              onSubmit();
            }}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
