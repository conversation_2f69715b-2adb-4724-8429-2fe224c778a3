import JsonForm from '@components/forms/JsonForm';
import { CloseOutlined } from '@mui/icons-material';
import { Drawer, IconButton, List, ListItem, ListItemText, Stack, Typography } from '@mui/material';
import { useNotification } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import axios, { AxiosError } from 'axios';
import { useEffect, useState } from 'react';
import { FormProvider } from 'react-hook-form';
import configSchema from '../../../schemas/identityConfig/config.json';
import uiSchema from '../../../schemas/identityConfig/ui.json';

export type TenantRole = 'applicant' | 'advocate';

interface EditTenantDrawerProps {
  visible: boolean;
  close: () => void;
  tenantRole: TenantRole;
  partnerId: string;
  tenantId?: string;
  onSuccess: () => void;
}

export default function EditTenantDrawer({
  visible,
  close,
  tenantRole,
  partnerId,
  tenantId,
  onSuccess,
}: EditTenantDrawerProps) {
  const [tenant, setTenant] = useState<{ displayName: string }>();
  const [isLoading, setLoading] = useState<boolean>(false);
  const { open: notify } = useNotification();
  const formProps = useForm();

  const { saveButtonProps, reset, getValues, setValue } = formProps;

  const onClose = () => {
    reset();
    setTenant(undefined);
    setLoading(false);
    close();
  };

  const retrieveTenant = async () => {
    try {
      const response = await axios.post(
        '/api/platform/identity/retrieveTenant',
        {
          tenantId,
        },
        { headers: { 'Content-Type': 'application/json' } },
      );
      return response.data;
    } catch (e) {
      return undefined;
    }
  };

  const onSubmit = async (): Promise<void> => {
    setLoading(true);
    try {
      await axios.post(
        '/api/platform/identity/upsertTenant',
        {
          partnerId: partnerId,
          role: tenantRole,
          options: getValues('options'),
        },
        { headers: { 'Content-Type': 'application/json' } },
      );
      notify?.({
        message: 'custom tenant configured successfully.',
        type: 'success',
      });
      onSuccess();
      onClose();
    } catch (e) {
      const msg = e instanceof AxiosError ? e.response?.data?.error : (e as Error).message;
      notify?.({
        message: `Identity tenant configuration encountered an error: ${msg}`,
        type: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (tenantId && !tenant) {
      retrieveTenant().then((tenant) => {
        setTenant(tenant);
        if (tenant?.options) setValue('options', tenant.options);
      });
    }
  }, [tenantId, retrieveTenant, setValue, tenant]);

  return (
    <Drawer
      open={visible}
      anchor="right"
      onClose={onClose}
      PaperProps={{
        sx: {
          minWidth: '50%',
          padding: 2,
          '& .MuiCardContent-root': { overflowY: 'auto' },
        },
      }}
    >
      <Edit
        resource="partners"
        title={tenantId ? 'Edit Tenant' : 'Create Tenant'}
        saveButtonProps={{
          disabled: isLoading || saveButtonProps.disabled,
          onClick: onSubmit,
        }}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={onClose} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <Stack component="form" gap={2} sx={{ mt: 2 }}>
          <FormProvider {...formProps}>
            <List>
              <ListItem>
                <ListItemText primary="Tenant ID" secondary={tenantId ?? 'N/A'} />
              </ListItem>
              <ListItem>
                <ListItemText primary="Display Name" secondary={tenant?.displayName ?? 'N/A'} />
              </ListItem>
            </List>

            <Typography variant="h6">Options</Typography>
            <JsonForm name="options" schema={configSchema} uiSchema={uiSchema} />
          </FormProvider>
        </Stack>
      </Edit>
    </Drawer>
  );
}
