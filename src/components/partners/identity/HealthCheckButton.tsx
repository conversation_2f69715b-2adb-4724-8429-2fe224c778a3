import { Button } from '@mui/material';
import { useNotification } from '@refinedev/core';
import axios, { AxiosError } from 'axios';
import { useState } from 'react';

export default function HealthCheckButton(): JSX.Element {
  const [isLoading, setLoading] = useState<boolean>(false);
  const { open: notify } = useNotification();

  const onSubmit = async () => {
    setLoading(true);
    axios
      .get('/api/platform/identity/healthCheck')
      .then(() => {
        notify?.({
          message: 'Connected to Identity Server successfully.',
          type: 'success',
        });
      })
      .catch((e) => {
        const msg = e instanceof AxiosError ? e.response?.data?.error : (e as Error).message;
        notify?.({ message: `Can not connect to Identity Server: ${msg}`, type: 'error' });
      })
      .finally(() => setLoading(false));
  };

  return (
    <Button
      onClick={() => onSubmit()}
      loading={!!isLoading}
      loadingPosition="start"
      variant="contained"
    >
      Health Check
    </Button>
  );
}
