import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>temAvatar,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ListSubheader,
  Switch,
  Tooltip,
} from '@mui/material';
import { type BaseKey, useModal } from '@refinedev/core';
import { List } from '@refinedev/mui';
import useCommunicationConfiguration from 'hooks/communicationConfigurations/useCommunicationConfiguration';
import { useState } from 'react';
import { CommunicationChannels, type CommunicationChannelsConfig } from 'types/notification';
import { EditCommunicationChannelDialog } from './EditCommunicationChannelDialog';

export const PartnerCommunicationChannels = ({
  partnerId,
  refetch,
}: { partnerId: BaseKey; refetch: () => void }) => {
  const { configuration } = useCommunicationConfiguration(partnerId);
  const editCommunicationChannelModal = useModal();
  const [selectedCommunicationChannel, setSelectedCommunicationChannel] =
    useState<CommunicationChannels>();
  const config = configuration?.config as CommunicationChannelsConfig;

  const isChecked = (channel) => {
    if (channel === CommunicationChannels.EMAIL) {
      return configuration?.config?.channels?.[channel] !== false;
    }
    return configuration?.config?.channels?.[channel] === true;
  };

  return (
    <>
      <List
        title="Partner communication channels"
        resource="partner_communication_channels_configurations"
        breadcrumb={false}
      >
        {Object.values(CommunicationChannels).map((channel) => (
          <Tooltip
            title={`when this is toggled off, all ${channel} will NOT be sent for this partner (excluding payments and auth)`}
            placement="right"
            key={channel}
          >
            <ListItem sx={{ width: 'fit-content' }}>
              <ListItemAvatar>
                <Switch
                  checked={isChecked(channel)}
                  onChange={() => {
                    setSelectedCommunicationChannel(channel);
                    editCommunicationChannelModal.show();
                  }}
                />
              </ListItemAvatar>
              <ListItemText sx={{ whiteSpace: 'pre-line' }} primary={channel.toUpperCase()} />
            </ListItem>
          </Tooltip>
        ))}
      </List>
      {selectedCommunicationChannel ? (
        <EditCommunicationChannelDialog
          {...editCommunicationChannelModal}
          currentConfig={config}
          communicationChannel={selectedCommunicationChannel}
          currentStatus={isChecked(selectedCommunicationChannel)}
          configId={configuration?.id}
          onSuccess={refetch}
          close={editCommunicationChannelModal.close}
          partnerId={partnerId}
        />
      ) : null}
    </>
  );
};
