import TextInput from '@components/forms/TextInput';
import { CloseOutlined } from '@mui/icons-material';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Stack } from '@mui/material';
import { useNotification } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import axios, { AxiosError } from 'axios';
import { useUser } from 'hooks/users/useUser';
import { useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

export default function SendSmsDrawer({ visible, close, userId }) {
  const [isLoading, setLoading] = useState<boolean>(false);
  const formProps = useForm();
  const { handleSubmit, getValues } = formProps;
  const { open: notify } = useNotification();
  const { user, isLoading: isUserLoading } = useUser(userId);

  const onClose = (refetch = false) => {
    setLoading(false);
    formProps.reset();
    close(refetch);
  };

  const onSubmit = async () => {
    const { body } = getValues();
    if (!user) notify?.({ message: 'User not found', type: 'error' });
    setLoading(true);
    axios
      .post(
        '/api/platform/notifications/sendSms',
        {
          recipients: [{ name: user?.name, phone: user?.phone }],
          providerParameters: { body },
        },
        { headers: { 'Content-Type': 'application/json' } },
      )
      .then(() => {
        notify?.({
          message: 'Successfully sent sms',
          type: 'success',
        });
        onClose();
      })
      .catch((e) => {
        const msg = e instanceof AxiosError ? e.response?.data?.error : (e as Error).message;
        notify?.({ message: `Can not connect to Notification Server: ${msg}`, type: 'error' });
      })
      .finally(() => setLoading(false));
  };

  return (
    <Drawer
      open={visible}
      onClose={() => onClose()}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Edit
        title={'Send Test SMS'}
        footerButtons={() => (
          <Button
            type="submit"
            onClick={handleSubmit(onSubmit)}
            variant="contained"
            loading={isLoading}
          >
            Send
          </Button>
        )}
        isLoading={isLoading || isUserLoading}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => onClose()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <FormProvider {...formProps}>
          <Stack component="form" autoComplete="off" gap={3}>
            <TextInput
              name="body"
              type="text"
              label="Body"
              InputProps={{ multiline: true }}
              required
            />
          </Stack>
        </FormProvider>
      </Edit>
    </Drawer>
  );
}
