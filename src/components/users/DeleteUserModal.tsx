import {
  Alert,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
} from '@mui/material';
import type { useModalReturnType } from '@refinedev/core';
import { isProduction } from '@utils/env';

interface DeleteUserModalProps {
  doDelete: () => void;
  modal: useModalReturnType;
  user?: { id: string; name: string };
}

export default function DeleteUserModal({
  doDelete,
  modal: { close, visible },
  user,
}: DeleteUserModalProps): JSX.Element {
  const onAcknowledgeModal = () => {
    close();
    doDelete();
  };

  return (
    <Dialog
      open={visible}
      onClose={close}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <DialogTitle>Delete User</DialogTitle>
      <DialogContent>
        {isProduction() && (
          <Alert severity="warning" sx={{ marginBottom: '16px' }}>
            You are in the Production environment.
          </Alert>
        )}
        <Typography>Are you sure you want to delete the user ({user?.name})?</Typography>
      </DialogContent>
      <DialogActions>
        <Button onClick={close}>Cancel</Button>
        <Button variant="contained" onClick={onAcknowledgeModal}>
          Delete
        </Button>
      </DialogActions>
    </Dialog>
  );
}
