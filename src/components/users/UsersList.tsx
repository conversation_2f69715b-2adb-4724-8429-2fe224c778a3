import { DataGrid } from '@components/data-grid/DataGrid';
import { CheckCircleOutline, WarningAmber } from '@mui/icons-material';
import type { GridColDef } from '@mui/x-data-grid';
import { EmailField, List, ShowButton, useDataGrid } from '@refinedev/mui';
import { useMemo } from 'react';

export default function UsersList({
  ids,
}: {
  ids?: string[];
}): JSX.Element {
  const { dataGridProps } = useDataGrid({
    resource: 'users',
    dataProviderName: 'identity',
    sorters: {
      initial: [{ field: 'createdAt', order: 'desc' }],
    },
    ...(ids && {
      syncWithLocation: false,
      filters: {
        defaultBehavior: 'replace',
        permanent: [{ field: 'id', operator: 'in' as const, value: ids }],
      },
    }),
    meta: {
      fields: [
        {
          edges: [
            {
              node: [
                'id',
                'name',
                'email',
                'createdAt',
                'deactivatedAt',
                'verifiedEmail',
                { applicantsList: ['id', 'partnerId'] },
                { advocatesList: ['id', 'partnerId'] },
              ],
            },
          ],
        },
      ],
    },
  });

  const columns = useMemo<GridColDef[]>(
    () =>
      [
        {
          field: 'id',
          flex: 1,
          headerName: 'ID',
          minWidth: 200,
        },
        {
          field: 'name',
          flex: 1,
          headerName: 'Name',
          minWidth: 200,
        },
        {
          field: 'email',
          flex: 1,
          headerName: 'Email',
          minWidth: 250,
          renderCell: function render({ value }) {
            return <EmailField value={value} />;
          },
        },

        {
          field: 'verifiedEmail',
          headerName: 'Verified Email',
          minWidth: 100,
          renderCell: function render({ value }) {
            return (
              <>
                {value ? <CheckCircleOutline color="success" /> : <WarningAmber color="warning" />}
              </>
            );
          },
        },
        {
          field: 'advocatesList',
          headerName: 'Is Advocate',
          sortable: false,
          filterable: false,
          minWidth: 100,
          renderCell: function render({ value }) {
            return <>{value.length > 0 && <CheckCircleOutline color="success" />}</>;
          },
        },
        {
          field: 'applicantsList',
          headerName: 'Is Applicant',
          sortable: false,
          filterable: false,
          minWidth: 100,
          renderCell: function render({ value }) {
            return <>{value.length > 0 && <CheckCircleOutline color="success" />}</>;
          },
        },
        {
          field: 'createdAt',
          headerName: 'Created',
          minWidth: 150,
        },
        {
          field: 'deactivatedAt',
          headerName: 'Deactivated',
          minWidth: 150,
        },
        {
          field: 'actions',
          headerName: 'Actions',
          sortable: false,
          filterable: false,
          renderCell: function render({ row }) {
            return (
              <>
                <ShowButton resource="users" hideText recordItemId={row.id} />
              </>
            );
          },
          align: 'center',
          headerAlign: 'center',
          minWidth: 80,
        },
      ] as GridColDef[],
    [],
  );
  return (
    <>
      <List title="Users" {...(!!ids?.length && { breadcrumb: false })}>
        <DataGrid {...dataGridProps} columns={columns} />
      </List>
    </>
  );
}
