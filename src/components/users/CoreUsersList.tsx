import { DataGrid } from '@components/data-grid/DataGrid';
import Link from '@components/navigation/Link';
import { Visibility } from '@mui/icons-material';
import type { GridColDef } from '@mui/x-data-grid';
import type { BaseKey } from '@refinedev/core';
import { EditButton, EmailField, List, ShowButton, useDataGrid } from '@refinedev/mui';
import { formatDisplayId } from '@utils/displayId';
import { getAccountValue } from '@utils/payments';
import usePaymentAccounts from 'hooks/payments/usePaymentAccounts';
import { useMemo } from 'react';

export default function CoreUsersList({
  partnerId,
  userId,
  ids,
  columns: additionalColumns = [],
  withPaymentAccounts,
}: {
  partnerId?: BaseKey;
  userId?: string;
  ids?: string[];
  columns?: GridColDef[];
  withPaymentAccounts?: boolean;
}): JSX.Element {
  const { dataGridProps } = useDataGrid({
    dataProviderName: 'default',
    resource: 'users',
    sorters: {
      initial: [{ field: 'createdAt', order: 'desc' }],
    },
    filters: {
      defaultBehavior: 'replace',
      permanent: [
        ...(partnerId ? [{ field: 'partnerId', operator: 'eq' as const, value: partnerId }] : []),
        ...(userId ? [{ field: 'id', operator: 'eq' as const, value: userId }] : []),
        ...(ids ? [{ field: 'id', operator: 'in' as const, value: ids }] : []),
      ],
    },
    meta: {
      fields: [
        {
          edges: [
            {
              node: [
                'id',
                'displayId',
                'name',
                'email',
                'createdAt',
                'deactivatedAt',
                'validatedEmail',
                { partner: ['name'] },
                ...(additionalColumns.map((col) => col.field) as string[]),
              ],
            },
          ],
        },
      ],
    },
    ...(!!userId && { syncWithLocation: false }),
  });

  const { paymentAccounts } = usePaymentAccounts({
    filters: [
      {
        field: 'recipient.referenceId',
        operator: 'in',
        value: dataGridProps.rows.map(({ id }) => id),
      },
    ],
    enabled: !!withPaymentAccounts,
  });

  const paymentColumns = withPaymentAccounts && [
    {
      field: 'account',
      headerName: 'Account',
      flex: 1,
      sortable: false,
      filterable: false,
      valueGetter: ({ row }) =>
        paymentAccounts?.filter((account) => account.recipient.referenceId === row.id),
      renderCell: function render({ value }) {
        return value?.map((account) => getAccountValue(account))?.join(',');
      },
    },
  ];

  const columns = useMemo<GridColDef[]>(
    () =>
      [
        {
          field: 'id',
          flex: 1,
          headerName: 'ID',
          minWidth: 200,
        },
        {
          field: 'partner.name',
          flex: 1,
          headerName: 'Partner',
          minWidth: 100,
          valueGetter: ({ row }) => row.partner.name,
        },
        {
          field: 'displayId',
          headerName: 'Display Id',
          minWidth: 150,
          renderCell: function render({ value }) {
            return formatDisplayId(value, 'U');
          },
        },
        {
          field: 'name',
          flex: 1,
          headerName: 'Name',
          minWidth: 200,
        },
        {
          field: 'email',
          flex: 1,
          headerName: 'Email',
          minWidth: 250,
          renderCell: function render({ value }) {
            return <EmailField value={value} />;
          },
        },
        {
          field: 'createdAt',
          headerName: 'Created',
          minWidth: 150,
        },
        {
          field: 'deactivatedAt',
          headerName: 'Deactivated',
          minWidth: 150,
        },
        ...additionalColumns,
        ...(paymentColumns || []),
        {
          field: 'actions',
          headerName: 'Actions',
          sortable: false,
          renderCell: function render({ row }) {
            return (
              <>
                <Link to={`/users/coreUsers/show/${row?.id}`}>
                  <Visibility />
                </Link>
              </>
            );
          },
          align: 'center',
          headerAlign: 'center',
          minWidth: 80,
        },
      ] as GridColDef[],
    [additionalColumns, paymentColumns],
  );

  return (
    <>
      <List title="Users" breadcrumb={false}>
        <DataGrid {...dataGridProps} columns={columns} />
      </List>
    </>
  );
}
