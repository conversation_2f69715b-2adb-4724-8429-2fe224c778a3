import { DataGrid } from '@components/data-grid/DataGrid';
import Link from '@components/navigation/Link';
import type { GridColDef } from '@mui/x-data-grid';
import { List, useDataGrid } from '@refinedev/mui';
import { formatDisplayId } from '@utils/displayId';
import usePartners from 'hooks/partners/usePartners';
import useCoreUsers from 'hooks/users/useCoreUsers';
import { useMemo } from 'react';

export default function ApplicantsList({
  userId,
}: {
  userId: string;
}): JSX.Element {
  const { dataGridProps } = useDataGrid({
    dataProviderName: 'identity',
    resource: 'applicants',
    ...(userId && {
      syncWithLocation: false,
      filters: {
        defaultBehavior: 'replace',
        permanent: [{ field: 'userId', operator: 'eq' as const, value: userId }],
      },
    }),
    meta: {
      fields: [
        {
          edges: [
            {
              node: ['id', 'userId', 'partnerId'],
            },
          ],
        },
      ],
    },
  });
  const { partners, isLoading: isPartnerLoading } = usePartners({
    filters: [
      {
        field: 'id',
        operator: 'in',
        value: dataGridProps?.rows?.map(({ partnerId }) => partnerId),
      },
    ],
    enabled: !!dataGridProps?.rowCount,
  });

  const { coreUsers, isLoading: isUsersLoading } = useCoreUsers({
    filters: [
      {
        field: 'id',
        operator: 'in',
        value: dataGridProps?.rows?.map(({ id }) => id),
      },
    ],
    enabled: !!dataGridProps?.rowCount,
  });

  const columns = useMemo<GridColDef[]>(
    () =>
      [
        {
          field: 'id',
          flex: 1,
          headerName: 'UUID',
          minWidth: 200,
        },
        {
          field: 'partnerId',
          flex: 1,
          headerName: 'Partner',
          minWidth: 200,
          renderCell: (param) => {
            if (isPartnerLoading) return 'loading...';
            const partnerId = param.row.partnerId;
            const partner = partners?.find((partner) => partnerId === partner.id);
            return (
              <Link to={`/partners/show/${partnerId}`}>{partner ? partner.name : partnerId}</Link>
            );
          },
        },
        {
          field: 'coreUserId',
          flex: 1,
          headerName: 'Core User',
          minWidth: 200,
          renderCell: (param) => {
            if (isUsersLoading) return 'loading...';
            const coreUserId = param.row.id;
            const coreUser = coreUsers?.find((user) => coreUserId === user.id);
            return coreUser ? (
              <Link to={`/users/coreUsers/show/${coreUserId}`}>
                {formatDisplayId(coreUser.displayId, 'U')}
              </Link>
            ) : (
              coreUserId
            );
          },
        },
      ] as GridColDef[],
    [partners, coreUsers, isPartnerLoading, isUsersLoading],
  );
  return (
    <>
      <List title="Applicants" {...(!!userId && { breadcrumb: false })}>
        <DataGrid
          {...dataGridProps}
          columns={columns}
          initialState={{
            columns: {
              columnVisibilityModel: {
                id: true,
              },
            },
            syncWithLocation: false,
          }}
        />
      </List>
    </>
  );
}
