import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import { Button } from '@mui/material';
import { CanAccess } from '@refinedev/core';

export default function DeleteUserButton({ openDrawer, user }): JSX.Element {
  const disabled = user?.adminsList?.length !== 0;
  return (
    <CanAccess resource="core_users" action="deactivate">
      <Button onClick={openDrawer} disabled={disabled}>
        <DeleteOutlineIcon fontSize="small" color={disabled ? 'disabled' : 'error'} />
        DELETE
      </Button>
    </CanAccess>
  );
}
