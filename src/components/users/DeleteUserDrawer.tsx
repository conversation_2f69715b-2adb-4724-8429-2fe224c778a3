import SelectInput from '@components/forms/SelectInput';
import { CloseOutlined } from '@mui/icons-material';
import { Drawer, IconButton, List, ListItem, ListItemText, Stack } from '@mui/material';
import { useCan, useGetIdentity, useModal, useNotification } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import axios from 'axios';
import { useUser } from 'hooks/users/useUser';
import { useState } from 'react';
import { FormProvider } from 'react-hook-form';
import { DeleteTypes } from 'types/requests';
import DeleteUserModal from './DeleteUserModal';

export default function DeleteUserDrawer({ visible, close, userId }) {
  const [isLoading, setLoading] = useState<boolean>(false);
  const formProps = useForm({
    refineCoreProps: {
      dataProviderName: 'default',
      resource: 'users',
      redirect: false,
      id: userId,
      meta: { fields: ['id'] },
    },
  });
  const { handleSubmit, saveButtonProps, getValues } = formProps;
  const { user, isLoading: isUserLoading } = useUser(userId);
  const { data: author } = useGetIdentity();
  const { open: notify } = useNotification();
  const { data: canDelete } = useCan({ resource: 'core_users', action: 'delete' });

  const onClose = (success = false) => {
    setLoading(false);
    close(success);
  };

  const onSubmit = async () => {
    try {
      setLoading(true);
      if (!user) throw new Error('User not found.');
      if (user?.adminsList?.length) throw new Error('Cannot delete an admin.');

      const { type } = getValues();
      //TODO: Add dropdown for request type here
      await axios.delete(`/api/platform/users/${user?.id}`, {
        data: {
          type,
          author,
          entityType: 'USER',
          partnerId: user?.partner?.id,
          requestType: 'PARTNER_SCHEDULED',
        },
      });
      notify?.({
        message: 'User successfully deleted.',
        type: 'success',
      });
      onClose(true);
    } catch (error) {
      setLoading(false);
      notify?.({
        message: `Transaction failed. Error: ${(error as Error)?.message ?? 'Unknown'}`,
        type: 'error',
      });
    }
  };

  const confirmationModal = useModal();

  return (
    <Drawer
      open={visible}
      onClose={() => onClose()}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Edit
        dataProviderName="default"
        resource="users"
        title="Delete User"
        saveButtonProps={{
          disabled: saveButtonProps.disabled || isLoading || isUserLoading,
          onClick: handleSubmit(confirmationModal.show),
        }}
        isLoading={isLoading || isUserLoading}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => onClose()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <FormProvider {...formProps}>
          <Stack component="form" autoComplete="off" gap={3}>
            <List
              sx={{
                width: '100%',
                bgcolor: 'background.paper',
                display: 'grid',
                gridTemplateColumns: '1fr 1fr',
              }}
            >
              <ListItem>
                <ListItemText primary={user?.name} secondary="Name" />
              </ListItem>
              <ListItem>
                <ListItemText primary={user?.email} secondary="Email" />
              </ListItem>
              <ListItem>
                <ListItemText primary={user?.partner?.name} secondary="Partner" />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary={user?.applicationsBySubmitterIdList?.length ?? 0}
                  secondary="Number of Applications"
                />
              </ListItem>
            </List>
            <SelectInput
              name="type"
              label="Method"
              options={[
                { id: DeleteTypes.SOFT, name: 'Deactivate' },
                {
                  id: DeleteTypes.HARD,
                  name: 'Permanent (You Cannot Undo This Action)',
                  disabled: !canDelete?.can,
                },
              ]}
            />
          </Stack>
        </FormProvider>
        <DeleteUserModal
          modal={confirmationModal}
          user={user as { id: string; name: string }}
          doDelete={onSubmit}
        />
      </Edit>
    </Drawer>
  );
}
