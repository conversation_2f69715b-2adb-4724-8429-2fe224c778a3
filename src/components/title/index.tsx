import { useLink } from '@refinedev/core';
import type { RefineLayoutThemedTitleProps } from '@refinedev/mui';
import type React from 'react';

export const Title: React.FC<RefineLayoutThemedTitleProps> = ({ collapsed, wrapperStyles }) => {
  const Link = useLink();

  return (
    <Link to="/dashboard" style={{ height: '25px' }}>
      {collapsed && <img src="/logo-collapsed.svg" alt="sorcery" width="28px" />}
      {!collapsed && <img src="/logo.svg" alt="sorcery" width="140px" />}
    </Link>
  );
};
