import { DataGrid } from '@components/data-grid/DataGrid';
import Preview from '@components/markdown/Preview';
import Link from '@components/navigation/Link';
import { Typography } from '@mui/material';
import { useList } from '@refinedev/core';
import { List, useDataGrid } from '@refinedev/mui';

export const BannersList = () => {
  const { dataGridProps } = useDataGrid({
    resource: 'incident_messages',

    meta: {
      fields: [
        {
          nodes: [
            'id',
            'message',
            {
              partnerIncidentsByIncidentId: [
                {
                  nodes: [
                    {
                      partner: ['id', 'name'],
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
  });

  const MarkdownCellRenderer = (props) => {
    const { value } = props;

    return <Preview>{value}</Preview>;
  };

  const columns = [
    { field: 'id', headerName: 'Id', flex: 1 },
    {
      field: 'message',
      headerName: 'Message',
      minWidth: 400,
      renderCell: (params) => <MarkdownCellRenderer value={params.value} />,
    },
    {
      field: 'partner.name',
      valueGetter: (params) => {
        const partnerIncidents = params.row.partnerIncidentsByIncidentId;
        if (partnerIncidents && partnerIncidents?.length > 0) {
          return partnerIncidents[0]?.partner;
        }
        return '';
      },
      renderCell: (params) => {
        const partner = params.value;
        if (partner) {
          return (
            <Link to={`/partners/show/${partner.id}`}>
              <Typography>{partner.name}</Typography>
            </Link>
          );
        }
        return null;
      },
      headerName: 'Partner',
      minWidth: 300,
      sortable: false,
    },
  ];

  return (
    <List resource="incident_messages" title="Banners" {...{ breadcrumb: false }}>
      <DataGrid getRowHeight={() => 'auto'} {...dataGridProps} columns={columns} />
    </List>
  );
};
