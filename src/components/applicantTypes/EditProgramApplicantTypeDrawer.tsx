import AutocompleteInput from '@components/forms/AutocompleteInput';
import HiddenInput from '@components/forms/HiddenInput';
import TextInput from '@components/forms/TextInput';
import CloseOutlined from '@mui/icons-material/CloseOutlined';
import { <PERSON><PERSON>, Drawer, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ack, TextField } from '@mui/material';
import { useList } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import { type UseModalFormReturnType, useForm } from '@refinedev/react-hook-form';
import { useEffect } from 'react';
import { FormProvider } from 'react-hook-form';

export default function EditProgramApplicantTypeDrawer({
  modal: { visible, close },
  program: {
    id: programId,
    partner: { id: partnerId, parentId },
  },
  ...formProps
}: UseModalFormReturnType & {
  program: { id: string; partner: { id: string; parentId?: string } };
}) {
  const {
    handleSubmit,
    refineCore: { formLoading, queryResult },
    saveButtonProps: { disabled, onClick },
  } = formProps;

  const type = queryResult?.data?.data.applicantType;

  const { data: configData } = useList({
    dataProviderName: 'config',
    resource: 'program_application_configurations',
    queryOptions: { enabled: !!type },
    filters: [
      { field: 'programId', operator: 'eq', value: programId },
      { field: 'applicantTypeId', operator: 'eq', value: type?.id },
    ],
    meta: { fields: [{ nodes: ['id', 'configurationId'] }] },
  });
  const existingConfig = configData?.data?.[0];

  const configFormProps = useForm({
    refineCoreProps: {
      dataProviderName: 'config',
      resource: 'program_application_configurations',
      redirect: false,
      action: existingConfig ? 'edit' : 'create',
      meta: { fields: ['id'] },
    },
  });

  useEffect(() => {
    if (!existingConfig) return;

    configFormProps.setValue('configurationId', existingConfig.configurationId);
    configFormProps.refineCore.setId(existingConfig.id);
  }, [existingConfig, configFormProps.setValue, configFormProps.refineCore.setId]);

  const onSubmit = async (_, e) => {
    configFormProps.saveButtonProps.onClick(e);
    return onClick(e);
  };

  const onClose = () => {
    configFormProps.reset();
    formProps.reset();
    close();
  };

  return (
    <Drawer
      open={visible}
      onClose={onClose}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Edit
        resource="program_applicant_types"
        saveButtonProps={{ disabled, onClick: handleSubmit(onSubmit) }}
        isLoading={formLoading}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={onClose} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <Stack component="form" autoComplete="off" gap={2}>
          <TextField
            label="Applicant Type"
            value={type?.name}
            InputLabelProps={{ shrink: true }}
            fullWidth
            disabled
          />
          <FormProvider {...formProps}>
            <TextInput name="nameOverride" label="Name Override" />
          </FormProvider>
          <FormProvider {...configFormProps}>
            <HiddenInput name="programId" value={programId} />
            <HiddenInput name="applicantTypeId" value={type?.id} />
            <Alert severity="warning">
              Please note that changing the application configuration could have consequences for
              any existing applications that have been submitted by users with this Applicant Type,
              if the new and existing configurations are not compatible.
            </Alert>
            <AutocompleteInput
              name={'configurationId'}
              label="Application Configuration"
              dataProviderName="config"
              resource="application_configurations"
              filters={[
                {
                  operator: 'or',
                  value: [
                    { field: 'partnerId', operator: 'null', value: true },
                    {
                      field: 'partnerId',
                      operator: 'in',
                      value: [partnerId, parentId].filter(Boolean),
                    },
                  ],
                },
              ]}
              required
            />
          </FormProvider>
        </Stack>
      </Edit>
    </Drawer>
  );
}
