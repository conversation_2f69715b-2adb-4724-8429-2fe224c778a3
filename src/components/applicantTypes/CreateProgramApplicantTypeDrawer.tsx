import AutocompleteInput from '@components/forms/AutocompleteInput';
import HiddenInput from '@components/forms/HiddenInput';
import TextInput from '@components/forms/TextInput';
import CloseOutlined from '@mui/icons-material/CloseOutlined';
import { Drawer, IconButton, Stack } from '@mui/material';
import { Create } from '@refinedev/mui';
import { type UseModalFormReturnType, useForm } from '@refinedev/react-hook-form';
import { FormProvider } from 'react-hook-form';

type CreateProgramApplicantTypeDrawerProps = UseModalFormReturnType & {
  program: { id: string; partner: { id: string; parentId?: string } };
  existingTypes: string[];
};

export default function CreateProgramApplicantTypeDrawer({
  modal: { visible, close },
  program: {
    id: programId,
    partner: { id: partnerId, parentId },
  },
  existingTypes,
  ...formProps
}: CreateProgramApplicantTypeDrawerProps) {
  const {
    handleSubmit,
    refineCore: { formLoading },
    saveButtonProps: { disabled, onClick },
    watch,
  } = formProps;

  const configFormProps = useForm({
    refineCoreProps: {
      dataProviderName: 'config',
      resource: 'program_application_configurations',
      redirect: false,
      action: 'create',
    },
  });

  const onSubmit = async (_, e) => {
    configFormProps.saveButtonProps.onClick(e);
    return onClick(e);
  };

  const onClose = () => {
    configFormProps.reset();
    formProps.reset();
    close();
  };

  return (
    <Drawer
      open={visible}
      onClose={onClose}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Create
        resource="program_applicant_types"
        saveButtonProps={{ disabled, onClick: handleSubmit(onSubmit) }}
        isLoading={formLoading}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={onClose} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <Stack component="form" autoComplete="off" gap={2}>
          <FormProvider {...formProps}>
            <HiddenInput name="programId" value={programId} />
            <AutocompleteInput
              name="applicantTypeId"
              label="Applicant Type"
              resource="applicant_types"
              filters={[
                {
                  operator: 'or',
                  value: [
                    { field: 'partnerId', operator: 'null', value: true },
                    {
                      field: 'partnerId',
                      operator: 'in',
                      value: [partnerId, parentId].filter(Boolean),
                    },
                  ],
                },
              ]}
              excludedItems={existingTypes}
              required
            />
            <TextInput name="nameOverride" label="Name Override" />
          </FormProvider>
          <FormProvider {...configFormProps}>
            <HiddenInput name="programId" value={programId} />
            <HiddenInput name="applicantTypeId" value={watch('applicantTypeId')} />
            <AutocompleteInput
              name={'configurationId'}
              label="Application Configuration"
              dataProviderName="config"
              resource="application_configurations"
              filters={[
                {
                  operator: 'or',
                  value: [
                    { field: 'partnerId', operator: 'null', value: true },
                    {
                      field: 'partnerId',
                      operator: 'in',
                      value: [partnerId, parentId].filter(Boolean),
                    },
                  ],
                },
              ]}
              required
            />
          </FormProvider>
        </Stack>
      </Create>
    </Drawer>
  );
}
