import HiddenInput from '@components/forms/HiddenInput';
import CloseOutlined from '@mui/icons-material/CloseOutlined';
import { Drawer, IconButton, Stack } from '@mui/material';
import { Create } from '@refinedev/mui';
import { type UseModalFormReturnType, useForm } from '@refinedev/react-hook-form';
import { FormProvider } from 'react-hook-form';
import ApplicantTypeFields from './ApplicantTypeFields';

type CreateApplicantTypeDrawerProps = UseModalFormReturnType & {
  partner: { id: string; parent?: { id: string } };
};

export default function CreateApplicantTypeDrawer({
  partner,
  ...props
}: CreateApplicantTypeDrawerProps) {
  const {
    handleSubmit,
    modal: { visible, close },
    refineCore: { formLoading },
    saveButtonProps: { disabled, onClick },
  } = props;

  const configFormProps = useForm({
    refineCoreProps: {
      dataProviderName: 'config',
      resource: 'applicant_profile_configurations',
      redirect: false,
      action: 'create',
    },
  });

  const onSubmit = async (_, e) => {
    configFormProps.saveButtonProps.onClick(e);
    return onClick(e);
  };

  return (
    <Drawer
      open={visible}
      onClose={close}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 700 } } }}
    >
      <Create
        resource="applicant_types"
        saveButtonProps={{ disabled, onClick: handleSubmit(onSubmit) }}
        isLoading={formLoading}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <Stack component="form" autoComplete="off" gap={2}>
          <FormProvider {...props}>
            <HiddenInput name="partnerId" />
            <ApplicantTypeFields partner={partner} configFormProps={configFormProps} />
          </FormProvider>
        </Stack>
      </Create>
    </Drawer>
  );
}
