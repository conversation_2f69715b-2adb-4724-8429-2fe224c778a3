import { DataGrid } from '@components/data-grid/DataGrid';
import { useList } from '@refinedev/core';
import { DeleteButton, EditButton, List, useDataGrid } from '@refinedev/mui';
import { useModalForm } from '@refinedev/react-hook-form';
import type { FieldValues } from 'react-hook-form';
import CreateApplicantTypeDrawer from './CreateApplicantTypeDrawer';
import EditApplicantTypeDrawer from './EditApplicantTypeDrawer';

export default function ApplicantTypesList({ partner }) {
  const { data: configData } = useList({
    dataProviderName: 'config',
    resource: 'applicant_profile_configurations',
    meta: { fields: [{ nodes: ['id', 'applicantTypeId', 'config'] }] },
    filters: [{ field: 'partnerId', operator: 'eq', value: partner.id }],
  });

  const { dataGridProps } = useDataGrid({
    resource: 'applicant_types',
    meta: { fields: [{ nodes: ['id', 'name', { partner: ['id', 'name'] }] }] },
    filters: {
      defaultBehavior: 'replace',
      permanent: [
        {
          operator: 'or',
          value: [
            { field: 'partnerId', operator: 'null', value: true },
            {
              field: 'partnerId',
              operator: 'in',
              value: [partner.id, ...(partner.parent ? [partner.parent.id] : [])],
            },
          ],
        },
      ],
    },
    syncWithLocation: false,
  });

  const createDrawer = useModalForm({
    syncWithLocation: false,
    refineCoreProps: { resource: 'applicant_types', action: 'create', redirect: false },
    defaultValues: { partnerId: partner.id } as FieldValues,
  });

  const editDrawer = useModalForm({
    refineCoreProps: {
      resource: 'applicant_types',
      action: 'edit',
      redirect: false,
      meta: { fields: ['id', 'name', 'partnerId'] },
    },
  });

  const columns = [
    { field: 'name', headerName: 'Name', flex: 1 },
    {
      field: 'partner.name',
      headerName: 'Owner',
      flex: 1,
      sortable: false,
      valueGetter: ({ row }) => row.partner?.name ?? 'Beam',
    },
    {
      field: 'profileConfig',
      headerName: 'Applicant Profile Keys',
      flex: 1,
      sortable: false,
      valueGetter: ({ row }) => {
        const profileConfig = configData?.data?.find(
          ({ applicantTypeId }) => applicantTypeId === row.id,
        );
        return (profileConfig?.config.profileKeys ?? []).map(({ label }) => label).join(', ');
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 1,
      sortable: false,
      renderCell: ({ row }) => (
        <>
          <EditButton
            key={row.id}
            resource="applicant_types"
            recordItemId={row.id}
            onClick={() => {
              editDrawer.refineCore.setId(row.id);
              editDrawer.modal.show(row.id);
            }}
            hideText
          />
          <DeleteButton
            hideText
            resource="applicant_types"
            recordItemId={row.id}
            disabled={!row.partner}
            confirmTitle="Are you sure? (This action will fail if any program is using this applicant type, or if any user has it.)"
          />
        </>
      ),
    },
  ];

  return (
    <>
      <List
        resource="applicant_types"
        breadcrumb={false}
        createButtonProps={{ onClick: () => createDrawer.modal.show() }}
      >
        <DataGrid {...dataGridProps} columns={columns} />
      </List>
      <CreateApplicantTypeDrawer {...createDrawer} partner={partner} />
      <EditApplicantTypeDrawer {...editDrawer} partner={partner} />
    </>
  );
}
