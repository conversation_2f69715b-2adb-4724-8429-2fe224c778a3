import HiddenInput from '@components/forms/HiddenInput';
import JsonForm from '@components/forms/JsonForm';
import TextInput from '@components/forms/TextInput';
import { Alert, Typography } from '@mui/material';
import type { UseFormReturnType } from '@refinedev/react-hook-form';
import { useMemo } from 'react';
import { FormProvider } from 'react-hook-form';
import profileConfigSchema from 'schemas/profileConfigs/config.json';
import profileUiSchema from 'schemas/profileConfigs/ui.json';
import { v4 as uuidv4 } from 'uuid';

interface ApplicantTypeFieldsProps {
  configFormProps: UseFormReturnType;
  initialData?: { id: string; partnerId?: string };
  partner: { id: string; parent?: { id: string } };
}

export default function ApplicantTypeFields({
  configFormProps,
  initialData,
  partner,
}: ApplicantTypeFieldsProps): JSX.Element {
  const typeId = useMemo(() => initialData?.id ?? uuidv4(), [initialData]);

  return (
    <>
      <HiddenInput name="id" value={typeId} />
      {initialData?.id && initialData.partnerId && initialData.partnerId === partner.parent?.id && (
        <Alert severity="warning">
          Editing this applicant type will affect <strong>all</strong> other child partners who also
          use this third party type.
        </Alert>
      )}
      <TextInput
        name="name"
        label="Name"
        disabled={initialData && !initialData.partnerId}
        required
      />
      <FormProvider {...configFormProps}>
        <HiddenInput name="partnerId" value={partner.id} />
        <HiddenInput name="applicantTypeId" value={typeId} />
        <Typography variant="h6">Applicant Profile Keys</Typography>
        <JsonForm name="config" schema={profileConfigSchema} uiSchema={profileUiSchema} />
      </FormProvider>
    </>
  );
}
