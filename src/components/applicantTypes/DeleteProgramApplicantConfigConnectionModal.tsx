import {
  <PERSON><PERSON>,
  <PERSON>ton,
  Dialog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  List,
  ListItem,
  ListItemText,
  Typography,
} from '@mui/material';
import { type BaseKey, useDelete, useList } from '@refinedev/core';
import type { UseModalFormReturnType } from '@refinedev/react-hook-form';
import { isProduction } from '@utils/env';

export default function DeleteProgramApplicantConfigConnectionModal({
  modal: { visible, close },
  program: { id: programId },
  ...formProps
}: UseModalFormReturnType & {
  program: { id: string; partner: { id: string; parentId?: string } };
}): JSX.Element {
  const {
    refineCore: { queryResult },
  } = formProps;

  const programApplicantType = queryResult?.data?.data;
  const type = programApplicantType?.applicantType;
  const { mutate } = useDelete();

  const { data } = useList({
    dataProviderName: 'config',
    resource: 'program_application_configurations',
    queryOptions: { enabled: !!type },
    filters: [
      { field: 'programId', operator: 'eq', value: programId },
      { field: 'applicantTypeId', operator: 'eq', value: type?.id },
    ],
    meta: {
      fields: [{ nodes: ['id', { configuration: ['id', 'name'] }] }],
    },
  });
  const programApplicationConfig = data?.data?.[0];

  const onAcknowledge = async () => {
    if (programApplicationConfig?.id) {
      await mutate({
        dataProviderName: 'config',
        resource: 'program_application_configurations',
        id: programApplicationConfig.id,
      });
    }
    await mutate(
      {
        resource: 'program_applicant_types',
        id: programApplicantType?.id as BaseKey,
      },
      {
        onSuccess: () => {
          close();
        },
      },
    );
  };

  return (
    <Dialog open={visible} onClose={close}>
      <DialogTitle>Remove the application configuration from applicant type</DialogTitle>
      <DialogContent>
        {isProduction() && (
          <Alert severity="warning" sx={{ marginBottom: '16px' }}>
            You are in the Production environment.
          </Alert>
        )}
        <Typography>
          Are you sure you want to Remove this applicant type and associated application
          configuration?
        </Typography>
        <List
          sx={{
            width: '100%',
            bgcolor: 'background.paper',
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
          }}
        >
          <ListItem>
            <ListItemText primary={type?.name} secondary="Applicant Type" />
          </ListItem>
          <ListItem>
            <ListItemText
              primary={programApplicationConfig?.configuration?.name ?? 'N/A'}
              secondary="Application Config"
            />
          </ListItem>
        </List>
        <Alert severity="warning">
          Remove an application configuration could affect previous applications with that program.
        </Alert>
      </DialogContent>
      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Button onClick={close}>Cancel</Button>
        <Button disabled={!programApplicantType?.id} variant="contained" onClick={onAcknowledge}>
          Remove
        </Button>
      </DialogActions>
    </Dialog>
  );
}
