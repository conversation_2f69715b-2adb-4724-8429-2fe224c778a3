import { DataGrid } from '@components/data-grid/DataGrid';
import Link from '@components/navigation/Link';
import { useList } from '@refinedev/core';
import { DeleteButton, EditButton, List, useDataGrid } from '@refinedev/mui';
import { useModalForm } from '@refinedev/react-hook-form';
import CreateProgramApplicantTypeDrawer from './CreateProgramApplicantTypeDrawer';
import DeleteProgramApplicantConfigConnectionModal from './DeleteProgramApplicantConfigConnectionModal';
import EditProgramApplicantTypeDrawer from './EditProgramApplicantTypeDrawer';

export default function ProgramApplicantTypesList({ program }) {
  const { id: programId } = program;
  const { data: configData } = useList({
    dataProviderName: 'config',
    resource: 'program_application_configurations',
    meta: {
      fields: [{ nodes: ['id', 'applicantTypeId', { configuration: ['id', 'name'] }] }],
    },
    filters: [{ field: 'programId', operator: 'eq', value: programId }],
  });

  const { dataGridProps, tableQueryResult } = useDataGrid({
    resource: 'program_applicant_types',
    meta: {
      fields: [
        {
          nodes: ['id', 'nameOverride', { applicantType: ['id', 'name'] }, {}],
        },
      ],
    },
    filters: {
      defaultBehavior: 'replace',
      permanent: [{ field: 'programId', operator: 'eq', value: programId }],
    },
    syncWithLocation: false,
  });

  const createDrawer = useModalForm({
    syncWithLocation: false,
    refineCoreProps: {
      resource: 'program_applicant_types',
      action: 'create',
      redirect: false,
      onMutationSuccess: () => tableQueryResult.refetch(),
    },
  });

  const editDrawer = useModalForm({
    refineCoreProps: {
      resource: 'program_applicant_types',
      action: 'edit',
      redirect: false,
      meta: {
        fields: ['id', 'nameOverride', { applicantType: ['id', 'name'] }],
      },
      onMutationSuccess: () => tableQueryResult.refetch(),
    },
  });

  const deleteModal = useModalForm({
    refineCoreProps: {
      resource: 'program_applicant_types',
      action: 'edit',
      redirect: false,
      meta: {
        fields: ['id', 'nameOverride', { applicantType: ['id', 'name'] }],
      },
      onMutationSuccess: () => tableQueryResult.refetch(),
    },
  });

  const columns = [
    {
      field: 'applicantType.name',
      headerName: 'Type',
      flex: 1,
      valueGetter: ({ row }) => row.applicantType.name,
    },
    { field: 'nameOverride', headerName: 'Name override', flex: 1 },
    {
      field: 'appConfig',
      headerName: 'App Config',
      flex: 1,
      sortable: false,
      renderCell: ({ row }) => {
        const config = configData?.data.find(
          ({ applicantTypeId }) => applicantTypeId === row.applicantType.id,
        );
        if (!config) return null;
        return (
          <Link to={`/appConfigs/show/${config.configuration.id}`}>
            {config.configuration.name}
          </Link>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 1,
      sortable: false,
      renderCell: ({ row }) => {
        const config = configData?.data.find(
          ({ applicantTypeId }) => applicantTypeId === row.applicantType.id,
        );
        return (
          <>
            <EditButton
              key={row.id}
              resource="program_applicant_types"
              recordItemId={row.id}
              onClick={() => {
                editDrawer.refineCore.setId(row.id);
                editDrawer.modal.show(row.id);
              }}
              hideText
            />
            <DeleteButton
              key={`${row.id}-delete`}
              resource="program_applicant_types"
              recordItemId={row.id}
              onClick={() => {
                deleteModal.refineCore.setId(row.id);
                deleteModal.modal.show(row.id);
              }}
              hideText
            />
            {/* 
          TODO: May need/want to support program applicant type delete, but will need to be very careful
          and prevent deleting any type that a user has already applied with, otherwise
          lots could break. Just going to leave it off for now, and we can add it if
          needed down the line.
          */}
          </>
        );
      },
    },
  ];

  const existingTypes = dataGridProps?.rows?.map((type) => type.applicantType.id) ?? [];

  return (
    <>
      <List
        resource="program_applicant_types"
        breadcrumb={false}
        createButtonProps={{ onClick: () => createDrawer.modal.show() }}
      >
        <DataGrid {...dataGridProps} columns={columns} />
      </List>
      <CreateProgramApplicantTypeDrawer
        {...createDrawer}
        program={program}
        existingTypes={existingTypes}
      />
      <EditProgramApplicantTypeDrawer {...editDrawer} program={program} />
      <DeleteProgramApplicantConfigConnectionModal {...deleteModal} program={program} />
    </>
  );
}
