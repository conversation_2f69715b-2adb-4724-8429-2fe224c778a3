import CloseOutlined from '@mui/icons-material/CloseOutlined';
import { Drawer, IconButton, Stack } from '@mui/material';
import { useList } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import { type UseModalFormReturnType, useForm } from '@refinedev/react-hook-form';
import { useEffect } from 'react';
import { FormProvider } from 'react-hook-form';
import ApplicantTypeFields from './ApplicantTypeFields';

type EditApplicantTypeDrawerProps = UseModalFormReturnType & {
  partner: { id: string; parent?: { id: string } };
};

export default function EditApplicantTypeDrawer({
  partner,
  ...props
}: EditApplicantTypeDrawerProps) {
  const {
    handleSubmit,
    modal: { visible, close },
    refineCore: { formLoading, queryResult },
    saveButtonProps: { disabled, onClick },
  } = props;
  const type = queryResult?.data?.data;

  const { data: configData } = useList({
    dataProviderName: 'config',
    resource: 'applicant_profile_configurations',
    filters: [
      { field: 'partnerId', operator: 'eq', value: partner.id },
      { field: 'applicantTypeId', operator: 'eq', value: type?.id },
    ],
    queryOptions: { enabled: !!type?.id },
    meta: { fields: [{ nodes: ['id'] }] },
  });
  const existingConfig = configData?.data?.[0];

  const configFormProps = useForm({
    refineCoreProps: {
      dataProviderName: 'config',
      resource: 'applicant_profile_configurations',
      redirect: false,
      action: existingConfig ? 'edit' : 'create',
      meta: { fields: ['id', 'config'] },
    },
  });

  useEffect(() => {
    if (existingConfig) configFormProps.refineCore.setId(existingConfig.id);
  }, [existingConfig, configFormProps.refineCore.setId]);

  const onSubmit = async (_, e) => {
    configFormProps.saveButtonProps.onClick(e);
    return onClick(e);
  };

  return (
    <Drawer
      open={visible}
      onClose={close}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 700 } } }}
    >
      <Edit
        resource="applicant_types"
        saveButtonProps={{ disabled, onClick: handleSubmit(onSubmit) }}
        isLoading={formLoading}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <Stack component="form" autoComplete="off" gap={2}>
          <FormProvider {...props}>
            <ApplicantTypeFields
              partner={partner}
              configFormProps={configFormProps}
              initialData={{ id: type?.id as string, partnerId: type?.partnerId }}
            />
          </FormProvider>
        </Stack>
      </Edit>
    </Drawer>
  );
}
