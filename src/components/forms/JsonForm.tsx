import type { JsonSchema, UISchemaElement } from '@jsonforms/core';
import { materialCells, materialRenderers } from '@jsonforms/material-renderers';
import { JsonForms } from '@jsonforms/react';
import { useMemo, useState } from 'react';
import { Controller, useFormContext } from 'react-hook-form';

interface JsonFormProps {
  name: string;
  schema: JsonSchema;
  uiSchema?: UISchemaElement;
  defaultValue?: object | Array<object>;
  readonly?: boolean;
}

const DEFAULT_VALUE = {};

export default function JsonForm({
  name,
  readonly = false,
  schema,
  uiSchema,
  defaultValue = DEFAULT_VALUE,
}: JsonFormProps): JSX.Element {
  const [errors, setErrors] = useState([]);
  const { control } = useFormContext() ?? {};

  return (
    <Controller
      control={control}
      name={name}
      rules={{ validate: () => errors.length === 0 }}
      defaultValue={defaultValue}
      render={({ field: { onChange, value } }) => {
        const parsedConfig = useMemo(
          () => (typeof value === 'string' ? JSON.parse(value) : value),
          [value],
        );
        const handleChange = ({ errors, data }) => {
          if (data !== value && data !== defaultValue) {
            setErrors(errors);
            onChange(data);
          }
        };
        return (
          <JsonForms
            schema={schema}
            uischema={uiSchema}
            data={parsedConfig}
            renderers={materialRenderers}
            cells={materialCells}
            onChange={handleChange}
            readonly={readonly}
          />
        );
      }}
    />
  );
}
