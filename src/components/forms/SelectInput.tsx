import {
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  Typography,
} from '@mui/material';
import { type ReactNode, useEffect } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { extractError } from './utils';

export interface Option {
  id: string;
  name: string;
  disabled?: boolean;
  secondaryText?: ReactNode;
}

interface SelectInputProps<T = string> {
  name: string;
  label: string;
  options: Option[];
  multiple?: boolean;
  transformers?: {
    from: (option: string) => T;
    id: (value: T) => string;
  };
  disabled?: boolean;
  required?: boolean;
  value?: boolean | number | string;
}

export default function SelectInput<T>({
  name,
  value,
  label,
  options,
  multiple = false,
  disabled = false,
  required = false,
  transformers = { from: (option) => option as T, id: (value: T) => value as string },
}: SelectInputProps<T>): JSX.Element {
  const {
    control,
    setValue,
    formState: { errors },
  } = useFormContext();
  const error = extractError(errors, name);

  useEffect(() => {
    if (value !== undefined) setValue(name, value);
  }, [name, value, setValue]);

  return (
    <Controller
      control={control}
      name={name}
      defaultValue={multiple ? [] : ''}
      rules={{
        ...(required && { required: 'This field is required' }),
      }}
      render={({ field }) => (
        <FormControl required={required} fullWidth>
          <InputLabel shrink>{label}</InputLabel>
          <Select
            {...field}
            multiple={multiple}
            label={label}
            value={
              Array.isArray(field.value)
                ? field.value.map(transformers.id)
                : transformers.id(field.value)
            }
            onChange={(e) =>
              field.onChange(
                Array.isArray(e.target.value)
                  ? e.target.value.map(transformers.from)
                  : transformers.from(e.target.value),
              )
            }
            disabled={disabled}
            error={!!error}
          >
            {options.map(({ id, name, disabled, secondaryText }) => (
              <MenuItem key={id} value={id} disabled={disabled}>
                <Stack alignItems="flex-start">
                  <Typography>{name}</Typography>
                  {secondaryText && (
                    <Typography
                      variant="body1"
                      color="grey"
                      sx={{ wordBreak: 'break-word', whiteSpace: 'normal', maxWidth: '400px' }}
                    >
                      {secondaryText}
                    </Typography>
                  )}
                </Stack>
              </MenuItem>
            ))}
          </Select>
          {error && <FormHelperText error>{error.message}</FormHelperText>}
        </FormControl>
      )}
    />
  );
}
