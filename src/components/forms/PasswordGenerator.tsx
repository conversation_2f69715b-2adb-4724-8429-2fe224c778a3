import LockResetIcon from '@mui/icons-material/LockReset';
import { IconButton } from '@mui/material';

export default function PasswordGenerator({
  onChange,
  passwordLength = 12,
  useSymbols = true,
  useNumbers = true,
  useLowerCase = true,
  useUpperCase = true,
  disabled = false,
}: {
  onChange: (input: string) => void;
  disabled?: boolean;
  passwordLength?: number;
  useSymbols?: boolean;
  useLowerCase?: boolean;
  useUpperCase?: boolean;
  useNumbers?: boolean;
}) {
  const generatePassword = () => {
    let charset = '';
    let newPassword = '';

    if (useSymbols) charset += '!@#$%^&*()';
    if (useNumbers) charset += '0123456789';
    if (useLowerCase) charset += 'abcdefghijklmnopqrstuvwxyz';
    if (useUpperCase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';

    for (let i = 0; i < passwordLength; i++) {
      newPassword += charset.charAt(Math.floor(Math.random() * charset.length));
    }

    onChange(newPassword);
  };

  return (
    <IconButton onClick={() => generatePassword()} disabled={disabled}>
      <LockResetIcon />
    </IconButton>
  );
}
