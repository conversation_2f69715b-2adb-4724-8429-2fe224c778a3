import { useEffect } from 'react';
import { useFormContext } from 'react-hook-form';

interface HiddenInputProps {
  name: string;
  value?: boolean | number | string;
}

export default function HiddenInput({ name, value }: HiddenInputProps): JSX.Element {
  const { register, setValue } = useFormContext();
  useEffect(() => {
    if (value !== undefined) setValue(name, value);
  }, [name, value, setValue]);

  if (typeof value === 'boolean')
    return (
      <input
        {...register(name)}
        checked={value}
        type="checkbox"
        style={{ display: 'none' }}
        readOnly
      />
    );

  return (
    <input {...register(name)} {...(value !== undefined && { value })} type="hidden" readOnly />
  );
}
