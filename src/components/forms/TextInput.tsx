import { type InputProps, type SxProps, TextField } from '@mui/material';
import { isPossiblePhoneNumber, parsePhoneNumber } from 'libphonenumber-js';
import { useFormContext } from 'react-hook-form';
import isEmail from 'validator/lib/isEmail';
import { extractError } from './utils';

interface TextInputProps {
  defaultValue?: string | undefined | null;
  disabled?: boolean;
  helperText?: string;
  InputProps?: InputProps;
  label: string;
  name: string;
  pattern?: RegExp;
  required?: boolean;
  sx?: SxProps;
  type?: 'text' | 'tel' | 'email' | 'number' | 'password';
}

export default function TextInput({
  defaultValue,
  disabled,
  helperText,
  InputProps,
  label,
  name,
  pattern,
  required,
  sx,
  type = 'text',
}: TextInputProps): JSX.Element {
  const {
    register,
    formState: { errors },
  } = useFormContext();

  return (
    <TextField
      {...register(name, {
        ...(required && { required: 'This field is required' }),
        ...(type === 'email' && {
          validate: (val) => !val || isEmail(val) || 'Please enter a valid email address',
        }),
        ...(type === 'tel' && {
          validate: (val) =>
            !val || isPossiblePhoneNumber(val, 'US') || 'Please enter a valid phone number',
        }),
        setValueAs: (val: string) => {
          if (val === '') return defaultValue;
          if (type === 'email') return val?.toLowerCase();
          if (type === 'tel' && val && isPossiblePhoneNumber(val, 'US'))
            return parsePhoneNumber(val, 'US').number;
          return val;
        },
        ...(pattern && { pattern }),
      })}
      error={!!extractError(errors, name)}
      helperText={extractError(errors, name)?.message ?? helperText}
      fullWidth
      InputLabelProps={{ shrink: true }}
      type={type}
      label={label}
      disabled={disabled}
      InputProps={InputProps}
      sx={sx}
      required={required}
    />
  );
}
