import { Checkbox, FormControlLabel } from '@mui/material';
import { Controller, useFormContext } from 'react-hook-form';

interface DeactivatedDateInputProps {
  name?: string;
  label?: string;
}

export default function DeactivatedDateInput({
  name = 'deactivatedAt',
  label = 'Deactivated',
}: DeactivatedDateInputProps): JSX.Element {
  const { control } = useFormContext();
  return (
    <Controller
      control={control}
      name={name}
      defaultValue={null}
      render={({ field: { onChange, value, name } }) => (
        <FormControlLabel
          control={
            <Checkbox
              color="warning"
              onChange={(_, value) => {
                if (value === false) {
                  onChange(null);
                } else {
                  onChange(new Date().toISOString());
                }
              }}
              name={name}
              checked={!!value}
            />
          }
          label={label}
        />
      )}
    />
  );
}
