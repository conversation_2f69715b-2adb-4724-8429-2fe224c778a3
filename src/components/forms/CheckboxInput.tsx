import { Checkbox, FormControlLabel } from '@mui/material';
import { Controller, useFormContext } from 'react-hook-form';

interface CheckboxInputProps {
  name: string;
  label: string;
  disabled?: boolean;
  defaultValue?: boolean;
}

export default function CheckboxInput({
  name,
  label,
  disabled = false,
  defaultValue,
}: CheckboxInputProps): JSX.Element {
  const { control } = useFormContext();
  return (
    <Controller
      control={control}
      name={name}
      defaultValue={!!defaultValue}
      render={({ field: { value, name, ...field } }) => (
        <FormControlLabel
          {...field}
          checked={value}
          control={<Checkbox disabled={disabled} />}
          label={label}
        />
      )}
    />
  );
}
