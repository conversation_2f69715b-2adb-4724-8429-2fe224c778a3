import { Popover, TextField } from '@mui/material';
import { useRef, useState } from 'react';
import { SketchPicker } from 'react-color';
import { Controller, useFormContext } from 'react-hook-form';
import { extractError } from './utils';

export default function ColorPicker({ name }: { name: string }): JSX.Element {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  const [showPicker, setShowPicker] = useState(false);
  const anchorEl = useRef<HTMLInputElement>(null);

  const openPicker = () => {
    setShowPicker(!showPicker);
    anchorEl.current?.blur();
  };
  return (
    <Controller
      control={control}
      rules={{ required: 'This field is required' }}
      name={name}
      defaultValue=""
      render={({ field }) => (
        <>
          <TextField
            {...field}
            onChange={() => {}}
            onFocus={openPicker}
            inputRef={anchorEl}
            InputProps={{
              sx: {
                alignSelf: 'flex-start',
                ...(field.value && { color: field.value }),
              },
            }}
            InputLabelProps={{ shrink: true }}
            margin="normal"
            label="Brand Color"
            error={!!extractError(errors, name)}
            helperText={extractError(errors, name)?.message}
          />
          <Popover
            id="color-picker-popover"
            open={showPicker}
            anchorEl={anchorEl.current}
            onClose={(): void => setShowPicker(false)}
          >
            <SketchPicker
              color={field.value}
              onChangeComplete={(color) => field.onChange(color.hex)}
              disableAlpha
            />
          </Popover>
        </>
      )}
    />
  );
}
