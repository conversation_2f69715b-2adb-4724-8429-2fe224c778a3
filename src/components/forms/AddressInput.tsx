import { Box, Typography } from '@mui/material';
import TextInput from './TextInput';

export const MAILING_ADDRESS_FIELDS = ['addressLine1', 'addressLine2', 'city', 'state', 'zip'];

export default function AddressInput({
  name,
  label,
}: {
  name: string;
  label: string;
}): JSX.Element {
  return (
    <Box>
      {/* TODO: Field set with label instead? */}
      <Typography variant="h5">{label}</Typography>
      <TextInput name={`${name}.addressLine1`} label="Address Line 1" required />
      <TextInput name={`${name}.addressLine2`} label="Address Line 2" />
      <TextInput name={`${name}.city`} label="City" required />
      {/* TODO: Select/dropdown of state abbrevs */}
      <TextInput name={`${name}.state`} label="State" required />
      <TextInput name={`${name}.zip`} label="Zip" required />
    </Box>
  );
}
