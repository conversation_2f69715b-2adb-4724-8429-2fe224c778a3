import type { FieldErrors } from 'react-hook-form';

export function extractError(errors: FieldErrors, name: string): { message: string } | undefined {
  // @ts-expect-error
  return name.split('.').reduce((prev, curr) => prev?.[curr], errors);
}

export const centsToDollars = (cents: number): number =>
  Number.parseFloat((cents / 100).toFixed(2));

export const dollarsToCents = (dollars: number): number => Math.round(dollars * 100);
