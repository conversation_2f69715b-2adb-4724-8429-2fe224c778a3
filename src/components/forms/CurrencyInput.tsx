import { InputAdornment, TextField } from '@mui/material';
import { Controller, type UseControllerProps, useFormContext } from 'react-hook-form';
import { centsToDollars, dollarsToCents, extractError } from './utils';

interface CurrencyInputProps {
  name: string;
  label: string;
  required?: boolean;
  rules?: UseControllerProps['rules'];
}

export default function CurrencyInput({
  name,
  label,
  required,
  rules,
}: CurrencyInputProps): JSX.Element {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  return (
    <Controller
      control={control}
      name={name}
      rules={{
        ...(required && { required: 'This field is required' }),
        min: 0,
        ...rules,
      }}
      render={({ field }) => (
        <TextField
          {...field}
          value={Number(field.value) ? centsToDollars(field.value) : field.value}
          onChange={(e) =>
            field.onChange(e.target.value ? dollarsToCents(Number(e.target.value)) : e.target.value)
          }
          autoComplete="off"
          error={!!extractError(errors, name)}
          helperText={extractError(errors, name)?.message as string}
          margin="normal"
          InputLabelProps={{ shrink: true }}
          label={label}
          type="number"
          inputProps={{ step: 0.01 }}
          InputProps={{
            startAdornment: <InputAdornment position="start">$</InputAdornment>,
          }}
        />
      )}
    />
  );
}
