import { CheckBox, CheckBoxOutlineBlank } from '@mui/icons-material';
import { Autocomplete, Checkbox, TextField } from '@mui/material';
import type { CrudFilters } from '@refinedev/core';
import { useAutocomplete } from '@refinedev/mui';
import { Controller, useFormContext } from 'react-hook-form';
import { extractError } from './utils';

interface Option {
  id: string;
  name: string;
}

interface AutocompleteInputProps<T = Option> {
  name: string;
  label: string;
  resource: string;
  dataProviderName?: string;
  excludedItems?: string[];
  filters?: CrudFilters;
  multiple?: boolean;
  nameField?: string;
  dataField?: string;
  noOptionsText?: string;
  required?: boolean;
  transformers?: {
    from: (option: T | Option) => T;
    id: (value: T) => string;
  };
}

export default function AutocompleteInput<T>({
  name,
  label,
  resource,
  nameField = 'name',
  dataField = '',
  dataProviderName = 'default',
  filters,
  excludedItems,
  multiple = false,
  noOptionsText = 'There are no options available',
  required = false,
  transformers = {
    from: (option) => (option as Option).id as T,
    id: (value: T) => {
      console.log(value);
      return (value as Option).id;
    },
  },
}: AutocompleteInputProps<T>): JSX.Element {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const { autocompleteProps } = useAutocomplete({
    dataProviderName,
    resource,
    meta: { fields: [{ nodes: ['id', nameField, dataField] }] },
    pagination: { mode: 'off' },
    filters,
    onSearch: (value) => [{ field: nameField, operator: 'contains', value }],
  });

  return (
    <Controller
      control={control}
      rules={{
        ...(required && { required: 'This field is required' }),
      }}
      name={name}
      defaultValue={multiple ? [] : null}
      render={({ field }) => (
        <Autocomplete<Option, typeof multiple>
          {...autocompleteProps}
          {...field}
          onChange={(_, value: Option | Option[] | null) => {
            if (value === null) field.onChange(multiple ? [] : null);
            else if (!Array.isArray(value)) field.onChange(transformers.from(value));
            else field.onChange(value.map(transformers.from));
          }}
          getOptionLabel={(item) =>
            autocompleteProps.options?.find(
              (p) =>
                p?.id?.toString() === item ||
                p?.id?.toString() === transformers.id(item as T)?.toString(),
            )?.[nameField] ?? ''
          }
          getOptionDisabled={(option) => !!excludedItems?.includes(option.id)}
          isOptionEqualToValue={(option, value) =>
            value === undefined ||
            option?.id?.toString() === transformers.id(value as T)?.toString()
          }
          noOptionsText={noOptionsText}
          renderInput={(params) => (
            <TextField
              {...params}
              label={label}
              error={!!extractError(errors, name)}
              helperText={extractError(errors, name)?.message as string}
              required={required}
              fullWidth
            />
          )}
          {...(multiple && {
            multiple,
            disableCloseOnSelect: true,
            renderOption: (props, option, { selected }) => (
              <li {...props}>
                <Checkbox
                  icon={<CheckBoxOutlineBlank fontSize="small" />}
                  checkedIcon={<CheckBox fontSize="small" />}
                  style={{ marginRight: 8 }}
                  checked={selected}
                />
                {option.name}
              </li>
            ),
          })}
          fullWidth
        />
      )}
    />
  );
}
