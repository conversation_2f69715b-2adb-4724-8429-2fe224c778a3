import { CircularProgressWithLabel } from '@components/CircularProgressWithLabel';
import { UploadFileSharp } from '@mui/icons-material';
import {
  Alert,
  AlertTitle,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
} from '@mui/material';
import type { useModalReturnType } from '@refinedev/core';
import { getConfig } from '@utils/config';
import axios from 'axios';
import { IKContext, IKUpload } from 'imagekitio-react';
import { useEffect, useRef, useState } from 'react';
import { useFormContext } from 'react-hook-form';

export interface ImageUploadProps {
  /** partnerExternalId is used to construct the base folder for all of this partner's branding images */
  partnerExternalId: string;
  /** The refine field to update */
  fieldToUpdate: string;
  /** The human-readable field name */
  fieldLabel: string;
  /** The image filename to create/replace */
  imageFilename: string;

  title: string;
  modalProps: useModalReturnType;
  onUploadSuccess?: () => void;
}

export default function ImageUploadModal({
  partnerExternalId,
  fieldToUpdate,
  fieldLabel,
  imageFilename,
  title,
  modalProps: { visible, close },
  onUploadSuccess,
}: ImageUploadProps): JSX.Element {
  const { setValue } = useFormContext();
  const [fileTypeError, setFileTypeError] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const inputRef = useRef<HTMLLabelElement>(null);

  const IMAGEKIT_PUBLIC_KEY = getConfig('PUBLIC_IMAGEKIT_PUBLIC_KEY');
  const IMAGEKIT_URL_ENDPOINT = getConfig('PUBLIC_IMAGEKIT_URL_ENDPOINT');
  const IMAGEKIT_BRANDING_UPLOAD_FOLDER = getConfig('PUBLIC_BRANDING_UPLOAD_FOLDER');

  const uploadFolderBase = IMAGEKIT_BRANDING_UPLOAD_FOLDER ?? 'branding-dev';
  const uploadFolder = `/${uploadFolderBase}/${partnerExternalId}`;

  const onError = (err) => {
    throw err;
  };

  useEffect(() => {
    if (visible) {
      setUploadProgress(0);
      setFileTypeError(false);
    }
  }, [visible]);

  const onUploadStart = () => {
    setUploadProgress(1);
  };

  const onUploadProgress = (progress) => {
    setUploadProgress(Math.trunc((progress.loaded / progress.total) * 100));
  };

  /** This is called by imagekit on successful upload */
  const onSuccess = (res) => {
    // set the db column to the new filename, using the version in a query param ?vX for cache-busting
    setValue(fieldToUpdate, `${res.url}?v${res.versionInfo.name.replace('Version ', '')}`);
    onUploadSuccess?.();
    close();
  };

  const validateFile = (file) => {
    if (file.type === 'image/jpeg' || file.type === 'image/png') {
      setFileTypeError(false);
      return true;
    }
    setFileTypeError(true);
    return false;
  };

  return (
    <Dialog open={visible} onClose={close}>
      <DialogTitle>
        {title} {fieldLabel}
      </DialogTitle>
      <DialogContent dividers sx={{ p: 6 }}>
        {uploadProgress > 0 && <CircularProgressWithLabel value={uploadProgress} />}
        {fileTypeError === true && (
          <Alert severity="error">
            <AlertTitle>File Upload Error</AlertTitle>
            The file could not be uploaded. Only JPG or PNG files supported at this time.
          </Alert>
        )}
        <IKContext
          publicKey={IMAGEKIT_PUBLIC_KEY}
          urlEndpoint={IMAGEKIT_URL_ENDPOINT}
          transformationPosition="path"
          authenticator={async () => {
            const res = await axios.get(
              `${window.location.protocol}//${window.location.host}/api/imagekitio-auth`,
            );
            return res.data;
          }}
        >
          <label ref={inputRef}>
            <IKUpload
              fileName={imageFilename}
              folder={uploadFolder}
              overwriteFile={true}
              useUniqueFileName={false}
              onUploadStart={onUploadStart}
              onUploadProgress={onUploadProgress}
              validateFile={validateFile}
              onError={onError}
              onSuccess={onSuccess}
              style={{ display: 'none' }}
            />
          </label>
          {inputRef && !uploadProgress && (
            <Button
              onClick={() => inputRef.current?.click()}
              variant="contained"
              color="primary"
              startIcon={<UploadFileSharp />}
            >
              Upload New File and Replace {fieldLabel}
            </Button>
          )}
        </IKContext>
        <Typography variant="overline" display="block" gutterBottom>
          Only jpg or png files currently supported.
        </Typography>
      </DialogContent>
      <DialogActions>
        <Button onClick={close} disabled={uploadProgress > 0}>
          Cancel
        </Button>
      </DialogActions>
    </Dialog>
  );
}
