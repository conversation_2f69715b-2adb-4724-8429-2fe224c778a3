import type { UseModalFormReturnType } from '@refinedev/react-hook-form';
import { FormProvider } from 'react-hook-form';
import ImageUploadModal, { type ImageUploadProps } from './ImageUploadModal';

export type EditBrandImagesCustomModalProps = Omit<ImageUploadProps, 'title' | 'modalProps'>;

export const EditBrandImagesModal = ({
  modal,
  ...props
}: EditBrandImagesCustomModalProps & UseModalFormReturnType) => {
  const {
    refineCore: { onFinish },
    handleSubmit,
  } = props;

  const handleUploadSuccess = async () => {
    modal.close();
    await handleSubmit(onFinish)();
  };

  return (
    <FormProvider {...props}>
      <ImageUploadModal
        {...props}
        title={modal.title}
        modalProps={modal}
        onUploadSuccess={handleUploadSuccess}
      />
    </FormProvider>
  );
};
