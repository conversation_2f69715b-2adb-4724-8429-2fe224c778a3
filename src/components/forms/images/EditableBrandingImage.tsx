import { EditOutlined } from '@mui/icons-material';
import { <PERSON><PERSON>, Card, CardActions, CardContent, CardHeader } from '@mui/material';

interface EditableBrandingImageProps {
  label: string;
  imageUrl: string;
  openEditModal: () => void;
}

export default function EditableBrandingImage({
  label,
  imageUrl,
  openEditModal,
}: EditableBrandingImageProps): JSX.Element {
  return (
    <Card sx={{ width: 275 }}>
      <CardHeader title={label} />
      <CardContent>{imageUrl && <img src={imageUrl} width={250} alt={label} />}</CardContent>
      <CardActions
        sx={{
          justifyContent: 'flex-end',
        }}
      >
        <Button
          size="small"
          variant="outlined"
          startIcon={<EditOutlined />}
          onClick={openEditModal}
        >
          Update {label}
        </Button>
      </CardActions>
    </Card>
  );
}
