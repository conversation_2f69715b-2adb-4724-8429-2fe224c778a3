import { FormHelperText } from '@mui/material';
import { Controller, useFormContext } from 'react-hook-form';
import { extractError } from '../utils';
import EditableBrandingImage from './EditableBrandingImage';

interface BrandingImageInputProps {
  name: string;
  label: string;
  openModal: () => void;
}

export default function BrandingImageInput({
  name,
  label,
  openModal,
}: BrandingImageInputProps): JSX.Element {
  const {
    control,
    formState: { errors },
  } = useFormContext();
  return (
    <Controller
      control={control}
      rules={{ required: 'This field is required' }}
      name={name}
      defaultValue={null}
      render={({ field: { value } }) => (
        <>
          <EditableBrandingImage label={label} imageUrl={value} openEditModal={openModal} />
          {extractError(errors, name) && (
            <FormHelperText error>{extractError(errors, name)?.message}</FormHelperText>
          )}
        </>
      )}
    />
  );
}
