import {
  type DatePickerProps,
  LocalizationProvider,
  DatePicker as MUIDatePicker,
} from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import dayjs from '@utils/dayJsConfig';

export default function DatePicker({
  label,
  value,
  onChange,
  disablePast = true,
  ...params
}: DatePickerProps<Date>): JSX.Element {
  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <MUIDatePicker
        label={label}
        value={value ? dayjs(value).toDate() : value}
        onChange={onChange}
        disablePast={disablePast}
        {...params}
      />
    </LocalizationProvider>
  );
}
