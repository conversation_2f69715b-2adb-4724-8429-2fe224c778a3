import HiddenInput from '@components/forms/HiddenInput';
import SelectInput from '@components/forms/SelectInput';
import CloseOutlined from '@mui/icons-material/CloseOutlined';
import { Drawer, Hidden, IconButton, Stack } from '@mui/material';
import { Create } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import { FormProvider } from 'react-hook-form';
import DataLookupFields from './DataLookupFields';

export default function CreateDataLookupConfigurationDrawer({
  modal: { visible, close },
  program,
}): JSX.Element {
  const { id: programId, programApplicantTypes } = program;
  const formProps = useForm({
    refineCoreProps: {
      action: 'create',
      resource: 'configs',
      dataProviderName: 'verification',
    },
  });

  const onSubmit = async (_, e) => {
    close();
    return formProps.saveButtonProps.onClick(e);
  };

  return (
    <Drawer
      open={visible}
      onClose={close}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 700 } } }}
    >
      <Create
        resource="verification_configurations"
        saveButtonProps={{
          ...formProps.saveButtonProps,
          onClick: formProps.handleSubmit(onSubmit),
        }}
        isLoading={formProps.refineCore.formLoading}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
          title: 'Create Data Lookup Configuration',
        }}
      >
        <FormProvider {...formProps}>
          <Stack component="form" autoComplete="off" gap={2}>
            <HiddenInput name="programId" value={programId} />
            <HiddenInput name="service" value="DATA_LOOKUP" />
            {programApplicantTypes.length === 1 ? (
              <HiddenInput
                name="applicantTypeId"
                value={programApplicantTypes[0].applicantType.id}
              />
            ) : (
              <SelectInput
                name="applicantTypeId"
                label="Applicant Type"
                options={programApplicantTypes.map(({ nameOverride, applicantType }) => ({
                  id: applicantType.id,
                  name: nameOverride ?? applicantType.name,
                }))}
                required
              />
            )}
            <DataLookupFields name="lookupConfigs.create.fields" />
          </Stack>
        </FormProvider>
      </Create>
    </Drawer>
  );
}
