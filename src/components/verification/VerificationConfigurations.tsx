import { List, ListItem, ListItemText, Typography } from '@mui/material';
import { type BaseRecord, useList, useModal } from '@refinedev/core';
import { CreateButton, EditButton } from '@refinedev/mui';
import { Fragment, useState } from 'react';
import CreateDataLookupConfigurationDrawer from './CreateDataLookupConfigurationDrawer';
import DataLookupConfiguration from './DataLookupConfiguration';
import EditDataLookupConfigurationDrawer from './EditDataLookupConfigurationDrawer';

export default function VerificationConfigurations({ program }): JSX.Element {
  const { id: programId, programApplicantTypes } = program;
  const { data } = useList({
    resource: 'configs',
    dataProviderName: 'verification',
    meta: {
      fields: [{ nodes: ['id', 'applicantTypeId', 'service'] }],
    },
    filters: [
      { field: 'programId', operator: 'eq', value: programId },
      { field: 'deactivatedAt', operator: 'null', value: true },
    ],
    // TODO: when complete multiparty support is added for verification
    // (i.e., verification for multiple applicant types supported), this should be removed
    pagination: { pageSize: 1 },
    sorters: [{ field: 'createdAt', order: 'desc' }],
  });

  const [editing, setEditing] = useState<BaseRecord>();
  const createModal = useModal();

  return (
    <>
      <Typography variant="h5">Verification</Typography>
      <List
        sx={{
          width: '100%',
          display: 'grid',
          gridTemplateColumns: '1fr 1fr 1fr',
        }}
      >
        {data?.data.length === 0 && (
          <>
            <ListItem>
              <ListItemText primary="Program has no verification configuration" />
            </ListItem>
            <ListItem>
              <CreateButton
                resource="verification_configurations"
                onClick={() => createModal.show()}
              />
            </ListItem>
          </>
        )}
        {data?.data.map((config) => {
          const applicantType = programApplicantTypes.find(
            ({ applicantType }) => applicantType.id === config.applicantTypeId,
          );
          return (
            <Fragment key={config.id}>
              <ListItem>
                <ListItemText primary={config.service} secondary="Service" />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary={applicantType?.nameOverride ?? applicantType?.applicantType.name}
                  secondary="Applicant Type"
                />
              </ListItem>
              <ListItem>
                <EditButton
                  resource="verification_configurations"
                  id={config.id as string}
                  onClick={() => setEditing(config)}
                />
              </ListItem>
              <ListItem sx={{ gridColumn: '1 / span 3' }}>
                <ServiceConfig {...(config as { id: string; service: string })} />
              </ListItem>
            </Fragment>
          );
        })}
      </List>
      <CreateDataLookupConfigurationDrawer modal={createModal} program={program} />
      {editing && (
        <EditDataLookupConfigurationDrawer
          modal={{ visible: !!editing, close: () => setEditing(undefined) }}
          config={editing}
          program={program}
        />
      )}
    </>
  );
}

export function ServiceConfig({ id, service }): JSX.Element {
  switch (service) {
    case 'DATA_LOOKUP':
      return <DataLookupConfiguration configId={id} />;
  }
  return <></>;
}
