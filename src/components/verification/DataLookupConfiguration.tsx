import { materialCells, materialRenderers } from '@jsonforms/material-renderers';
import { JsonForms } from '@jsonforms/react';
import useLatestDataLookupConfig from 'hooks/verification/useLatestDataLookupConfig';
import configSchema from 'schemas/verification/dataLookup.json';
import uiSchema from 'schemas/verification/dataLookupUI.json';

const DEFAULT = [];

export default function DataLookupConfiguration({ configId }: { configId: string }): JSX.Element {
  const { config } = useLatestDataLookupConfig(configId);

  return (
    <JsonForms
      schema={configSchema}
      uischema={uiSchema}
      data={config?.fields ?? DEFAULT}
      renderers={materialRenderers}
      cells={materialCells}
      readonly
    />
  );
}
