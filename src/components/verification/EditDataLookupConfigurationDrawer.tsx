import HiddenInput from '@components/forms/HiddenInput';
import SelectInput from '@components/forms/SelectInput';
import CloseOutlined from '@mui/icons-material/CloseOutlined';
import { Drawer, IconButton, Stack } from '@mui/material';
import { Create } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import useLatestDataLookupConfig from 'hooks/verification/useLatestDataLookupConfig';
import { useEffect } from 'react';
import { FormProvider } from 'react-hook-form';
import DataLookupFields from './DataLookupFields';

export default function EditDataLookupConfigurationDrawer({
  modal: { visible, close },
  program,
  config,
}): JSX.Element {
  const formProps = useForm({
    refineCoreProps: {
      action: 'create',
      resource: 'lookupConfigs',
      dataProviderName: 'verification',
    },
    defaultValues: {
      configId: config.id,
      config: {
        updateById: {
          id: config.id,
          patch: {
            applicantTypeId: config.applicantTypeId,
          },
        } as object | undefined,
      },
      fields: [],
    },
  });

  const { config: dataLookupConfig } = useLatestDataLookupConfig(config.id);
  useEffect(() => {
    if (dataLookupConfig) formProps.setValue('fields', dataLookupConfig.fields);
  }, [dataLookupConfig, formProps.setValue]);

  const onSubmit = async (data, e) => {
    if (data.config.updateById.patch.applicantTypeId === config.applicantTypeId)
      formProps.setValue('config.updateById', undefined);

    close();
    return formProps.saveButtonProps.onClick(e);
  };

  return (
    <Drawer
      open={visible}
      onClose={close}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 700 } } }}
    >
      <Create
        resource="verification_configurations"
        saveButtonProps={{
          ...formProps.saveButtonProps,
          onClick: formProps.handleSubmit(onSubmit),
        }}
        isLoading={formProps.refineCore.formLoading}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
          title: 'Edit Data Lookup Configuration',
        }}
      >
        <FormProvider {...formProps}>
          <Stack component="form" autoComplete="off" gap={2}>
            <HiddenInput name="configId" value={config.id ?? ''} />
            {program.programApplicantTypes.length > 1 && (
              <SelectInput
                name="config.updateById.patch.applicantTypeId"
                label="Applicant Type"
                options={program.programApplicantTypes.map(({ nameOverride, applicantType }) => ({
                  id: applicantType.id,
                  name: nameOverride ?? applicantType.name,
                }))}
                required
              />
            )}
            <DataLookupFields name="fields" />
          </Stack>
        </FormProvider>
      </Create>
    </Drawer>
  );
}
