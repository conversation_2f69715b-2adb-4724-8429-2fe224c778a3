import { List, ShowButton, useDataGrid } from '@refinedev/mui';
import { useModalForm } from '@refinedev/react-hook-form';
import { displayCurrency } from 'utils/currency';
import { aggregateFulfillments } from 'utils/operations';
import { DataGrid } from '../data-grid/DataGrid';
import { CreateFundDrawer } from './CreateFundDrawer';

const columns = [
  {
    field: 'id',
    headerName: 'ID',
    minWidth: 150,
  },
  {
    field: 'name',
    headerName: 'Name',
    minWidth: 500,
  },
  {
    field: 'startingBalance',
    headerName: 'Starting Balance',
    minWidth: 200,
    valueGetter: (params) => displayCurrency(params.row?.startingBalance),
  },
  {
    field: 'remainingBalance',
    headerName: 'Remaining Balance',
    minWidth: 200,
    valueGetter: (params) =>
      displayCurrency(
        params.row?.startingBalance - params.row?.fulfillments?.aggregates?.sum?.approvedAmount,
      ),
  },
  {
    field: 'actions',
    headerName: 'Actions',
    renderCell: ({ row }) => <ShowButton resource="funds" recordItemId={row.id} />,
    align: 'center',
    headerAlign: 'center',
    minWidth: 80,
    filterable: false,
  },
];

export const PartnerFundList = ({ partnerId }) => {
  const { dataGridProps } = useDataGrid({
    resource: 'funds',
    meta: {
      fields: [
        {
          nodes: [
            'id',
            'name',
            'startingBalance',
            aggregateFulfillments(['SUCCESS', 'INITIATED', 'FAILED', 'AUTHORIZED']),
          ],
        },
      ],
    },
    filters: {
      defaultBehavior: 'replace',
      permanent: [{ field: 'partnerId', operator: 'eq', value: partnerId }],
    },
    syncWithLocation: false,
  });

  const createFundProps = useModalForm({
    syncWithLocation: false,
    refineCoreProps: { resource: 'funds', action: 'create', redirect: false },
  });

  return (
    <>
      <List
        resource={'funds'}
        breadcrumb={false}
        createButtonProps={{
          onClick: () => createFundProps.modal.show(),
        }}
      >
        <DataGrid {...dataGridProps} columns={columns} autoHeight syncWithLocation={false} />
      </List>
      <CreateFundDrawer {...createFundProps} partnerId={partnerId} />
    </>
  );
};
