import AutocompleteInput from '@components/forms/AutocompleteInput';
import HiddenInput from '@components/forms/HiddenInput';
import { CloseOutlined } from '@mui/icons-material';
import { Drawer, IconButton } from '@mui/material';
import { Create } from '@refinedev/mui';
import { FormProvider } from 'react-hook-form';

export const CreateProgramFundDrawer = (props) => {
  const {
    saveButtonProps,
    modal: { visible, close },
    programId,
    partnerId,
    programFunds,
  } = props;

  return (
    <Drawer
      open={visible}
      onClose={close}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Create
        resource="programFunds"
        saveButtonProps={saveButtonProps}
        isLoading={false}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <FormProvider {...props}>
          <HiddenInput name="programId" value={programId} />
          <AutocompleteInput
            name="fundId"
            label="Fund"
            resource="funds"
            filters={[{ field: 'partnerId', operator: 'eq', value: partnerId }]}
            excludedItems={programFunds}
            required
          />
        </FormProvider>
      </Create>
    </Drawer>
  );
};
