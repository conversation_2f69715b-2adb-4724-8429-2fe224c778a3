import { List, ShowButton, useDataGrid } from '@refinedev/mui';
import { useModalForm } from '@refinedev/react-hook-form';
import { displayCurrency } from 'utils/currency';
import { aggregateFulfillments } from 'utils/operations';
import { DataGrid } from '../data-grid/DataGrid';
import { CreateProgramFundDrawer } from './CreateProgramFundDrawer';

const columns = [
  {
    field: 'id',
    headerName: 'ID',
    minWidth: 150,
  },
  {
    field: 'fundId',
    headerName: 'Fund ID',
    minWidth: 500,
    valueGetter: (params) => params.row?.fund?.id,
  },
  {
    field: 'name',
    headerName: 'Name',
    minWidth: 500,
    valueGetter: (params) => params.row?.fund?.name,
  },
  {
    field: 'startingBalance',
    headerName: 'Starting Balance',
    minWidth: 200,
    valueGetter: (params) => displayCurrency(params.row?.fund?.startingBalance),
  },
  {
    field: 'remainingBalance',
    headerName: 'Remaining Balance',
    minWidth: 200,
    valueGetter: (params) =>
      displayCurrency(
        params.row?.fund?.startingBalance -
          params.row?.fund?.fulfillments?.aggregates?.sum?.approvedAmount,
      ),
  },
  {
    field: 'actions',
    headerName: 'Actions',
    renderCell: ({ row }) => <ShowButton resource="funds" recordItemId={row.fund?.id} />,
    align: 'center',
    headerAlign: 'center',
    minWidth: 250,
    filterable: false,
  },
];

export const ProgramFundList = ({ programId, partnerId }) => {
  const { dataGridProps } = useDataGrid({
    resource: 'programFunds',
    meta: {
      fields: [
        {
          nodes: [
            'id',
            'programId',
            {
              fund: [
                'id',
                'name',
                'startingBalance',
                aggregateFulfillments(['SUCCESS', 'INITIATED', 'FAILED', 'AUTHORIZED']),
              ],
            },
          ],
        },
      ],
    },
    filters: {
      defaultBehavior: 'replace',
      permanent: [{ field: 'programId', operator: 'eq', value: programId }],
    },
    syncWithLocation: false,
  });

  const createFundProps = useModalForm({
    syncWithLocation: false,
    refineCoreProps: { resource: 'programFunds', action: 'create', redirect: false },
  });

  const programFunds = dataGridProps?.rows?.map((programFund) => programFund.fund.id);

  return (
    <>
      <List
        resource={'program_funds'}
        breadcrumb={false}
        createButtonProps={{
          onClick: () => createFundProps.modal.show(),
        }}
      >
        <DataGrid {...dataGridProps} columns={columns} autoHeight syncWithLocation={false} />
      </List>
      <CreateProgramFundDrawer
        {...createFundProps}
        partnerId={partnerId}
        programId={programId}
        programFunds={programFunds}
      />
    </>
  );
};
