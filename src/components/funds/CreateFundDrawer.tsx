import HiddenInput from '@components/forms/HiddenInput';
import { CloseOutlined } from '@mui/icons-material';
import { Box, Drawer, IconButton } from '@mui/material';
import { Create } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import useAuthorization from 'hooks/identity/useAuthorization';
import { Relation } from 'pages/api/types/identity';
import { FormProvider } from 'react-hook-form';
import FundFields from './FundFields';

export const CreateFundDrawer = (props) => {
  const {
    saveButtonProps: { disabled, onClick },
    modal: { visible, close },
    partnerId,
    handleSubmit,
  } = props;

  const authorization = useAuthorization();
  const fundingSourceformProps = useForm({
    refineCoreProps: {
      dataProviderName: 'payment',
      resource: 'fundingSources',
      redirect: false,
    },
  });

  const {
    saveButtonProps: { onClick: onClickFundingSource },
  } = fundingSourceformProps;

  const onSubmit = (data, e) => {
    const { id: fundId } = data;
    onClickFundingSource(e);
    onClick(e);
    if (fundId && partnerId)
      authorization.createRelationships({
        partnerId,
        relationships: [
          {
            relation: 'org',
            object: { objectId: fundId, objectType: Relation.FUND },
            subject: { objectId: partnerId, objectType: Relation.ORGANIZATION },
          },
        ],
      });
  };

  return (
    <Drawer
      open={visible}
      onClose={close}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Create
        resource="funds"
        saveButtonProps={{ disabled, onClick: handleSubmit(onSubmit) }}
        isLoading={false}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <FormProvider {...props}>
          <Box component="form" sx={{ display: 'flex', flexDirection: 'column' }}>
            <HiddenInput name="partnerId" value={partnerId} />
            <FundFields paymentsFormProps={fundingSourceformProps} />
          </Box>
        </FormProvider>
      </Create>
    </Drawer>
  );
};
