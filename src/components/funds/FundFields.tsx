import CurrencyInput from '@components/forms/CurrencyInput';
import HiddenInput from '@components/forms/HiddenInput';
import JsonForm from '@components/forms/JsonForm';
import TextInput from '@components/forms/TextInput';
import type { UseFormReturnType } from '@refinedev/react-hook-form';
import { getConfig } from '@utils/config';
import { useMemo } from 'react';
import { FormProvider } from 'react-hook-form';
import configSchema from 'schemas/fundingSources/config.json';
import uiSchema from 'schemas/fundingSources/ui.json';
import { v4 as uuidv4 } from 'uuid';

interface FundFieldsProps {
  paymentsFormProps: UseFormReturnType;
  initialData?: { id: string; awardedBalance?: number };
  prefix?: string;
  paymentsPrefix?: string;
}

// This should be in capital
enum provider {
  JPMC = 'JPMC',
}

export default function FundFields({
  paymentsFormProps,
  initialData,
  prefix: platformPrefix,
  paymentsPrefix,
}: FundFieldsProps): JSX.Element {
  const fundId = useMemo(() => initialData?.id ?? uuidv4(), [initialData?.id]);
  const fieldName = (name: string, prefix = platformPrefix): string =>
    [prefix, name].filter(Boolean).join('.');

  return (
    <>
      <HiddenInput name={fieldName('id')} value={fundId} />
      <TextInput name={fieldName('name')} label="Name" required />
      <CurrencyInput
        name={fieldName('startingBalance')}
        label="StartingBalance"
        required
        rules={{
          validate: (val) =>
            val >= (initialData?.awardedBalance ?? 0) ||
            'Cannot be below sum of awarded fulfillments',
        }}
      />
      <TextInput name={fieldName('defaultPaymentFieldKey')} label="Default Payment Field Key" />
      {/* TODO: What if it's an External Payments partner? Shouldn't create funding source in that case */}
      <FormProvider {...paymentsFormProps}>
        <HiddenInput name={fieldName('referenceId', paymentsPrefix)} value={fundId} />
        <HiddenInput name={fieldName('provider', paymentsPrefix)} value={provider.JPMC} />
        <HiddenInput
          name={fieldName('clientId', paymentsPrefix)}
          value={getConfig('PUBLIC_PAYMENT_CLIENT_ID')}
        />
        {/* TODO: Sync name as Partner Name - Fund Name? */}
        <JsonForm
          name={fieldName('keys', paymentsPrefix)}
          schema={configSchema}
          uiSchema={uiSchema}
        />
      </FormProvider>
    </>
  );
}
