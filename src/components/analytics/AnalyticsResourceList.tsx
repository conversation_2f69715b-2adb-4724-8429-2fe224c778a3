import { DataGrid } from '@components/data-grid/DataGrid';
import { <PERSON><PERSON>, <PERSON> } from '@mui/material';
import { DeleteButton, EditButton, List, useDataGrid } from '@refinedev/mui';
import { useModalForm } from '@refinedev/react-hook-form';
import AnalyticsResourceDrawer from './AnalyticsResourceDrawer';
import EnableAnalyticsDrawer from './EnableAnalyticsDrawer';

export default function AnalyticsResourceList({ partner }) {
  const partnerId = partner.id;
  const { dataGridProps, tableQueryResult } = useDataGrid({
    resource: 'analytics_resources',
    meta: { fields: [{ nodes: ['id', 'name', 'url', 'dashboardId', 'workspaceId'] }] },
    filters: {
      defaultBehavior: 'replace',
      permanent: [{ field: 'partnerId', operator: 'eq', value: partnerId }],
    },
    syncWithLocation: false,
  });

  const enableAnalyticsDrawer = useModalForm({
    refineCoreProps: {
      resource: 'partners',
      action: 'edit',
      id: partner.id,
      redirect: false,
      meta: { fields: ['id', 'gleanInviteToken'] },
    },
  });

  const createDrawer = useModalForm({
    syncWithLocation: false,
    refineCoreProps: {
      resource: 'analytics_resources',
      action: 'create',
      redirect: false,
      onMutationSuccess: () => {
        tableQueryResult.refetch();
      },
    },
  });

  const editDrawer = useModalForm({
    warnWhenUnsavedChanges: false,
    syncWithLocation: false,
    refineCoreProps: {
      resource: 'analytics_resources',
      action: 'edit',
      redirect: false,
      meta: { fields: ['id', 'name', 'url', 'partnerId', 'dashboardId', 'workspaceId'] },
      onMutationSuccess: () => {
        tableQueryResult.refetch();
      },
    },
  });

  const columns = [
    { field: 'id', headerName: 'ID', flex: 1 },
    { field: 'name', headerName: 'Name', flex: 1 },
    {
      field: 'url',
      headerName: 'URL',
      flex: 1,
      renderCell: ({ value }) => (
        <Link href={value} target="_blank">
          {value}
        </Link>
      ),
    },
    { field: 'dashboardId', headerName: 'Dashboard Id', flex: 1 },
    { field: 'workspaceId', headerName: 'Workspace Id', flex: 1 },
    {
      field: 'actions',
      headerName: 'Actions',
      flex: 1,
      sortable: false,
      renderCell: ({ row }) => (
        <>
          <EditButton
            key={`${row.id}-edit`}
            resource="analytics_resources"
            recordItemId={row.id}
            onClick={() => {
              editDrawer.refineCore.setId(row.id);
              editDrawer.modal.show(row.id);
            }}
            hideText
          />
          <DeleteButton
            key={`${row.id}-delete`}
            resource="analytics_resources"
            recordItemId={row.id}
            confirmTitle={`This action cannot be undone. Are you sure you want to delete Analytic Resource (${row.name})?`}
            hideText
          />
        </>
      ),
    },
  ];

  return (
    <>
      <List
        resource="analytics_resources"
        breadcrumb={false}
        createButtonProps={{
          onClick: () => {
            createDrawer.reset();
            createDrawer.modal.show();
          },
        }}
      >
        {partner.gleanInviteToken ? (
          <DataGrid {...dataGridProps} columns={columns} syncWithLocation={false} />
        ) : (
          <Button variant="contained" onClick={() => enableAnalyticsDrawer.modal.show()}>
            Enable
          </Button>
        )}
      </List>
      <EnableAnalyticsDrawer {...enableAnalyticsDrawer} />
      <AnalyticsResourceDrawer {...createDrawer} mode="ADD" partnerId={partner.id} />
      <AnalyticsResourceDrawer {...editDrawer} mode="EDIT" partnerId={partner.id} />
    </>
  );
}
