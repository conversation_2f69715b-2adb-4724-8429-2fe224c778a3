import HiddenInput from '@components/forms/HiddenInput';
import TextInput from '@components/forms/TextInput';
import CloseOutlined from '@mui/icons-material/CloseOutlined';
import { Drawer, IconButton, Stack } from '@mui/material';
import { Create, Edit } from '@refinedev/mui';
import type { UseModalFormReturnType } from '@refinedev/react-hook-form';
import { FormProvider } from 'react-hook-form';

type AnalyticsResourceDrawerProps = UseModalFormReturnType & {
  mode: 'ADD' | 'EDIT';
  partnerId: string;
};

export default function AnalyticsResourceDrawer({ mode, ...props }: AnalyticsResourceDrawerProps) {
  const {
    modal: { visible, close },
    refineCore: { formLoading },
    saveButtonProps,
    reset,
  } = props;

  const Component = mode === 'ADD' ? Create : Edit;

  return (
    <Drawer
      open={visible}
      onClose={() => {
        reset();
        close();
      }}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Component
        resource="analytics_resources"
        saveButtonProps={saveButtonProps}
        isLoading={formLoading}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <FormProvider {...props}>
          <Stack component="form" autoComplete="off" gap={1}>
            <HiddenInput name="partnerId" value={props.partnerId} />
            <TextInput name="name" label="Name" defaultValue="Dashboard" required />
            <TextInput
              name="url"
              label="URL"
              helperText="e.g., https://glean.io/app/dsb/1kdsj84osdfjskd03 (be sure to strip out any query parameters) for Preset use the Superset Domain"
              required
              sx={{ paddingBottom: '1rem' }}
            />
            <TextInput name="dashboardId" label="Dashboard Id" />
            <TextInput name="workspaceId" label="Workspace Id" />
          </Stack>
        </FormProvider>
      </Component>
    </Drawer>
  );
}
