import TextInput from '@components/forms/TextInput';
import CloseOutlined from '@mui/icons-material/CloseOutlined';
import { Alert, AlertTitle, Drawer, IconButton, Stack } from '@mui/material';
import { Edit } from '@refinedev/mui';
import type { UseModalFormReturnType } from '@refinedev/react-hook-form';
import { FormProvider } from 'react-hook-form';

export default function EnableAnalyticsDrawer(props: UseModalFormReturnType) {
  const {
    modal: { visible, close },
    refineCore: { formLoading },
    saveButtonProps,
  } = props;

  return (
    <Drawer
      open={visible}
      onClose={close}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Edit
        resource="partners"
        saveButtonProps={saveButtonProps}
        isLoading={formLoading}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <FormProvider {...props}>
          <Stack component="form" autoComplete="off" gap={1}>
            <Alert severity="info">
              <AlertTitle>Enable Analytics: Embed Feature</AlertTitle>
              After entering the invite token and adding the analytics resource(s), be sure to also
              enable the Analytics: Embed feature for at least one of the partner's programs.
            </Alert>
            <TextInput
              name="gleanInviteToken"
              label="Glean Invite Token"
              helperText="This should *not* include the complete url, just the token value"
              required
            />
          </Stack>
        </FormProvider>
      </Edit>
    </Drawer>
  );
}
