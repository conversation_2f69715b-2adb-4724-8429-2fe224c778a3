import CaseList from '@components/cases/CaseList';
import UsersList from '@components/users/UsersList';
import { Alert, Box, FormControl, InputLabel, MenuItem, Select } from '@mui/material';
import { useModal } from '@refinedev/core';
import { List } from '@refinedev/mui';
import useAuthorization from 'hooks/identity/useAuthorization';
import type { ObjectReference } from 'pages/api/types/identity';
import { Relation } from 'pages/api/types/identity';
import PartnerList from 'pages/partners';
import ProgramsList from 'pages/programs';
import { useEffect, useState } from 'react';
import CreatePermissionDrawer from './CreatePermissionDrawer';

interface PermissionListProps {
  resource?: ObjectReference;
  subject?: ObjectReference;
}

export default ({ resource, subject }: PermissionListProps) => {
  const { lookupPermissions } = useAuthorization();
  const [permission, setPermission] = useState<'edit' | 'view'>('edit');
  const [orgs, setOrgs] = useState<ObjectReference[]>([]);
  const [programs, setPrograms] = useState<ObjectReference[]>([]);
  const [cases, setCases] = useState<ObjectReference[]>([]);
  const [users, setUsers] = useState<ObjectReference[]>([]);

  const source = resource?.objectId ? 'resource' : 'subject';

  const fetchRelationships = () => {
    if (!subject?.objectId && !resource?.objectId) return;
    switch (source) {
      case 'resource': {
        lookupPermissions({
          resource,
          permission,
          source: 'resource',
          objectTypeFilter: Relation.USER,
        }).then((result) =>
          result
            ? setUsers(result)
            : console.warn(
                `issue retrieving ${Relation.USER}:${permission} permissions for ${resource?.objectType}:${resource?.objectId}`,
              ),
        );
        break;
      }
      case 'subject': {
        lookupPermissions({
          subject,
          permission,
          source: 'subject',
          objectTypeFilter: Relation.ORGANIZATION,
        }).then((result) =>
          result
            ? setOrgs(result)
            : console.warn(
                `issue retrieving ${Relation.ORGANIZATION}:${permission} permissions for ${subject?.objectType}:${subject?.objectId}`,
              ),
        );
        lookupPermissions({
          subject,
          permission,
          source: 'subject',
          objectTypeFilter: Relation.PROGRAM,
        }).then((result) =>
          result
            ? setPrograms(result)
            : console.warn(
                `issue retrieving ${Relation.PROGRAM}:${permission} permissions for ${subject?.objectType}:${subject?.objectId}`,
              ),
        );
        lookupPermissions({
          subject,
          permission: 'edit',
          source: 'subject',
          objectTypeFilter: Relation.CASE,
        }).then((result) =>
          result
            ? setCases(result)
            : console.warn(
                `issue retrieving ${Relation.CASE}:${permission} permissions for ${subject?.objectType}:${subject?.objectId}`,
              ),
        );
        break;
      }
    }
  };
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    fetchRelationships();
  }, [permission, subject, resource]);

  const handlePermissionChange = (e) => setPermission(e.target.value);

  const createPermissionDrawer = useModal();

  return (
    <>
      <List
        title="Permissions"
        breadcrumb={false}
        createButtonProps={{ onClick: () => createPermissionDrawer.show() }}
      >
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: '8px', padding: '8px' }}>
          <Alert severity="info">
            This sections shows applied permissions for the given resource. Change the filter toggle
            to view lists of different types of permissions.
          </Alert>
          <FormControl sx={{ m: 1, minWidth: 120, maxWidth: 10 }}>
            <InputLabel id="permission-select-label">Filter</InputLabel>
            <Select
              labelId="ermission-select-label"
              id="ermission-select"
              value={permission}
              label="Permission"
              onChange={handlePermissionChange}
            >
              <MenuItem value={'edit'}>Edit</MenuItem>
              <MenuItem value={'view'}>View</MenuItem>
            </Select>
          </FormControl>

          {source === 'resource' ? (
            <>
              <UsersList ids={users?.map((permission) => permission.objectId)} />
            </>
          ) : null}
          {source === 'subject' ? (
            <>
              <PartnerList filter={{ ids: orgs?.map((permission) => permission.objectId) }} />
              <ProgramsList filter={{ ids: programs?.map((permission) => permission.objectId) }} />
              <CaseList filter={{ ids: cases?.map((permission) => permission.objectId) }} />
            </>
          ) : null}
        </Box>
      </List>
      <CreatePermissionDrawer
        {...createPermissionDrawer}
        resource={resource}
        subject={subject}
        onSuccess={() => fetchRelationships()}
      />
    </>
  );
};
