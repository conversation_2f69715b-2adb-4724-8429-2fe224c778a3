import SelectInput from '@components/forms/SelectInput';
import TextInput from '@components/forms/TextInput';
import CloseOutlined from '@mui/icons-material/CloseOutlined';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>le, Drawer, <PERSON><PERSON><PERSON><PERSON><PERSON>, Stack } from '@mui/material';
import { Create } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import useAuthorization from 'hooks/identity/useAuthorization';
import { type ObjectReference, Relation, RelationType } from 'pages/api/types/identity';
import { useEffect } from 'react';
import { FormProvider } from 'react-hook-form';

type CreatePermissionProps = {
  resource?: ObjectReference;
  subject?: ObjectReference;
  visible: boolean;
  close: () => void;
  onSuccess?: () => void;
};

export default function CreatePermissionDrawer({
  resource,
  subject,
  visible,
  close,
  onSuccess,
}: CreatePermissionProps) {
  const formProps = useForm();
  const { createRelationships, isLoading } = useAuthorization();

  const onSubmit = async () => {
    const { resource, subject, relation } = formProps.getValues();
    const success = await createRelationships({
      relationships: [
        {
          object: resource,
          subject,
          relation,
        },
      ],
    });
    if (success) {
      if (onSuccess) onSuccess();
      formProps.reset();
      close();
    }
  };

  useEffect(() => {
    if (!visible) return;
    if (resource?.objectId) {
      formProps.setValue('resource.objectId', resource.objectId);
      formProps.setValue('resource.objectType', resource.objectType);
    }
    if (subject?.objectId) {
      formProps.setValue('subject.objectId', subject.objectId);
      formProps.setValue('subject.objectType', subject.objectType);
    }
  }, [resource, subject, formProps.setValue, visible]);

  const types = Object.values(Relation)
    .filter((relation) => relation !== 'UNKNOWN')
    .map((relation) => ({
      id: relation,
      name: relation,
    }));

  return (
    <Drawer
      open={visible}
      onClose={close}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 700 } } }}
    >
      <Create
        title="Create Permission"
        saveButtonProps={{
          disabled: isLoading,
          onClick: formProps.handleSubmit(onSubmit),
        }}
        isLoading={isLoading}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton
              onClick={() => {
                formProps.reset();
                close();
              }}
              sx={{ width: '30px', height: '30px' }}
            >
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <Stack component="form" autoComplete="off" gap={2}>
          <Alert severity="warning">
            <AlertTitle>For Special Circumstances Only</AlertTitle>
            This tool is only intended for use in special cases that are not supported according to
            normal rules. Please use identity user ID if USER is selected as resource/subject type.
          </Alert>
          <FormProvider {...formProps}>
            <SelectInput
              name="resource.objectType"
              label="Resource Type"
              required
              options={types}
            />
            <TextInput name="resource.objectId" label="Resource ID" required />
            <SelectInput
              name="relation"
              label="Relation"
              required
              options={Object.values(RelationType).map((relation) => ({
                id: relation,
                name: relation,
              }))}
            />
            <SelectInput name="subject.objectType" label="Subject Type" required options={types} />
            <TextInput name="subject.objectId" label="Subject ID" required />
          </FormProvider>
        </Stack>
      </Create>
    </Drawer>
  );
}
