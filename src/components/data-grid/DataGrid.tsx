import { GridToolbar, DataGrid as MUIDataGrid } from '@mui/x-data-grid';

const DataGrid = (props) => {
  const rows =
    props?.rows?.edges !== undefined ? props.rows.edges.flatMap((edge) => edge.node) : props.rows;

  return (
    <MUIDataGrid
      {...props}
      rows={rows}
      autoHeight
      filterDebounceMs={1000}
      slots={{ toolbar: props?.hideToolbar ? undefined : GridToolbar }}
      density="compact"
      initialState={{
        columns: {
          columnVisibilityModel: {
            // Hide columns by default
            id: false,
          },
        },
        syncWithLocation: true,
        ...(props.initialState ?? {}),
      }}
    />
  );
};

export { DataGrid };
