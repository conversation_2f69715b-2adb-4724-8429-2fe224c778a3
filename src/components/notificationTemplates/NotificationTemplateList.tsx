import { DataGrid } from '@components/data-grid/DataGrid';
import { Typography } from '@mui/material';
import { type CrudFilters, useGo } from '@refinedev/core';
import { DeleteButton, ShowButton } from '@refinedev/mui';
import { EditButton, List, useDataGrid } from '@refinedev/mui';
import { NotificationChannel } from '@utils/notificationTemplate';

interface TemplateFilter {
  channel: string;
  partnerId?: string;
  programId?: string;
}

const columns = [
  { field: 'id', headerName: 'ID', flex: 1 },
  {
    field: 'type',
    headerName: 'Notification Type',
    flex: 1,
    valueGetter: (params) => params.row?.type,
  },
  {
    field: 'channel',
    headerName: 'Notification Channel',
    flex: 1,
    valueGetter: (params) => params.row?.channel,
  },
  {
    field: 'actions',
    headerName: 'Actions',
    sortable: 'false',
    flex: 1,
    renderCell: function render({ row }) {
      return (
        <>
          <EditButton hideText resource="notification_templates" recordItemId={row.id} />
          <ShowButton hideText resource="notification_templates" recordItemId={row.id} />
          {/* 
            To do: Needs to add timestamp fields
            {row.partnerId && (
            <DeleteButton hideText resource="notification_templates" recordItemId={row.id} />
          )} */}
        </>
      );
    },
  },
];

function ChannelTemplateList({ filter }: { filter: TemplateFilter }) {
  const filters = [
    { field: 'channel', operator: 'eq', value: filter.channel },
    ...(filter.partnerId
      ? [{ field: 'partnerId', operator: 'eq', value: filter.partnerId }]
      : [{ field: 'partnerId', operator: 'null', value: true }]),
    ...(filter.programId
      ? [{ field: 'programId', operator: 'eq' as const, value: filter.programId }]
      : [{ field: 'programId', operator: 'null' as const, value: true }]),
  ];

  const { dataGridProps } = useDataGrid({
    resource: 'notification_templates',
    meta: { fields: [{ nodes: ['id', 'type', 'channel', 'content', 'partnerId'] }] },
    filters: {
      defaultBehavior: 'replace',
      permanent: filters as CrudFilters,
    },
    ...(filter && { syncWithLocation: false }),
  });

  return (
    <>
      <Typography variant="subtitle1" fontWeight="bold">
        {filter.channel?.toUpperCase()} Templates:
      </Typography>
      <DataGrid {...dataGridProps} columns={columns} />
    </>
  );
}

export default function NotificationTemplateList({
  filter,
}: { filter?: Omit<TemplateFilter, 'channel'> }): JSX.Element {
  const go = useGo();

  return (
    <List
      resource="notification_templates"
      createButtonProps={{
        onClick: () => go({ to: '/notificationTemplates/create', query: filter }),
      }}
      {...(filter && { breadcrumb: false })}
    >
      {Object.values(NotificationChannel).map((channel) => (
        <ChannelTemplateList filter={{ channel, ...(filter || {}) }} />
      ))}
    </List>
  );
}
