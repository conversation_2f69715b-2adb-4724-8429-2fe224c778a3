import JsonForm from '@components/forms/JsonForm';
import TextInput from '@components/forms/TextInput';
import MarkdownEditor from '@components/markdown/Editor';
import { Button, Link, Typography } from '@mui/material';
import { NotificationChannel, TEMPLATE_VARIABLES } from '@utils/notificationTemplate';
import { useFormContext } from 'react-hook-form';
import actionConfig from 'schemas/notificationTemplates/actionConfig.json';
import actionUI from 'schemas/notificationTemplates/actionUI.json';
import type { NotificationType } from 'types/notification';

const DEFAULT_ACTION = { url: 'login', text: 'Log In' };

export default function TemplateContentFields({
  partnerId,
  notificationType,
  notificationChannel,
}: {
  partnerId?: string;
  notificationType: NotificationType;
  notificationChannel: NotificationChannel;
}): JSX.Element {
  const { setValue, watch } = useFormContext();
  const variables = TEMPLATE_VARIABLES[notificationType];
  const isDefaultTemplate = !partnerId;

  const addAction = (field: string) => setValue(field, DEFAULT_ACTION);
  const removeAction = (field: string) => setValue(field, undefined);

  return (
    <>
      <Typography variant="subtitle2">
        You may include dynamic variables in any of these fields using{' '}
        <Link target="_blank" href="https://handlebarsjs.com/guide/">
          handlebars syntax
        </Link>
        .
      </Typography>
      {variables && (
        <Typography variant="subtitle2">
          Available variables for this template are: {variables.join(', ')}.
        </Typography>
      )}
      {notificationChannel === NotificationChannel.EMAIL && (
        <TextInput name="subject" label="Subject" required={isDefaultTemplate} />
      )}
      <MarkdownEditor
        name="preText"
        label="Pre Text"
        required={isDefaultTemplate}
        preview={false}
      />
      {notificationChannel === NotificationChannel.EMAIL && (
        <>
          {watch('button') ? (
            <>
              <Typography variant="body1">CTA Button</Typography>
              <JsonForm name="button" schema={actionConfig} uiSchema={actionUI} />
              <Button
                size="small"
                variant="contained"
                onClick={() => removeAction('button')}
                sx={{ alignSelf: 'flex-start' }}
              >
                Remove CTA
              </Button>
            </>
          ) : (
            <Button
              size="small"
              variant="contained"
              onClick={() => addAction('button')}
              sx={{ alignSelf: 'flex-start' }}
            >
              Add CTA
            </Button>
          )}
        </>
      )}
      {notificationChannel === NotificationChannel.SMS && (
        <>
          {watch('link') ? (
            <>
              <Typography variant="body1">CTA Link</Typography>
              <JsonForm name="link" schema={actionConfig} uiSchema={actionUI} />
              <Button
                size="small"
                variant="contained"
                onClick={() => removeAction('link')}
                sx={{ alignSelf: 'flex-start' }}
              >
                Remove CTA
              </Button>
            </>
          ) : (
            <Button
              size="small"
              variant="contained"
              onClick={() => addAction('link')}
              sx={{ alignSelf: 'flex-start' }}
            >
              Add CTA
            </Button>
          )}
        </>
      )}
      <MarkdownEditor name="postText" label="Post Text" preview={false} />
    </>
  );
}
