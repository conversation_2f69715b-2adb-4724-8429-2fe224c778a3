import TextInput from '@components/forms/TextInput';
import Markdown from '@components/markdown/Markdown';
import { Box, Button, Grid, Stack, Typography } from '@mui/material';
import {
  COMMON_VARIABLES,
  type EmailTemplateContent,
  NotificationChannel,
  type NotificationTemplate,
  type SmsTemplateContent,
  TEMPLATE_VARIABLES,
} from '@utils/notificationTemplate';
import Handlebars from 'handlebars';
import { FormProvider, useForm } from 'react-hook-form';

Handlebars.registerHelper('eq', (a, b) => a === b);
Handlebars.registerHelper('not', (a) => !a);

const render = (text: string, variables: Record<string, string>) => {
  try {
    return Handlebars.compile(text ?? ' ')(variables);
  } catch {
    return 'INVALID TEMPLATE - Please check and make sure all brackets are closed';
  }
};

function EmailPreview({
  content: { subject, button, preText, postText },
  variables,
}: { content: EmailTemplateContent; variables: Record<string, string> }) {
  const renderContent = (text: string) => render(text, variables);
  return (
    <>
      <Box sx={{ p: 1, m: 1, border: '1px solid', borderRadius: 2 }}>{renderContent(subject)}</Box>
      <Stack
        gap={2}
        alignItems="flex-start"
        sx={{ p: 1, m: 1, border: '1px solid', borderRadius: 2 }}
      >
        <Typography>
          Hi {'{'}recipient name{'}'}
        </Typography>
        <Markdown>{renderContent(preText)}</Markdown>
        {button && <Button variant="contained">{renderContent(button.text)}</Button>}
        {postText && <Markdown>{renderContent(postText)}</Markdown>}
        <Typography>
          - {'{'}partner name{'}'}
        </Typography>
      </Stack>
    </>
  );
}

function SMSPreview({
  content: { link, preText, postText },
  variables,
}: { content: SmsTemplateContent; variables: Record<string, string> }) {
  const renderContent = (text: string) => render(text, variables);
  return (
    <Stack
      gap={2}
      alignItems="flex-start"
      sx={{ p: 1, m: 1, border: '1px solid', borderRadius: 2 }}
    >
      <Typography>
        Hi {'{'}recipient name{'}'}
      </Typography>
      <Markdown>{renderContent(preText)}</Markdown>
      {link && (
        <>
          {renderContent(link.text)}: {link.url}
        </>
      )}
      {postText && <Markdown>{renderContent(postText)}</Markdown>}
      <Typography>
        - {'{'}partner name{'}'}
      </Typography>
    </Stack>
  );
}

export default function NotificationTemplatePreview({
  template: { type, channel, content },
}: { template: NotificationTemplate }): JSX.Element {
  const formProps = useForm();
  const availableVariables = [...(TEMPLATE_VARIABLES[type] ?? []), ...COMMON_VARIABLES];

  const variables = formProps.watch();

  return (
    <Stack gap={2}>
      {channel === NotificationChannel.EMAIL && (
        <EmailPreview content={content as EmailTemplateContent} variables={variables} />
      )}
      {channel === NotificationChannel.SMS && (
        <SMSPreview content={content as SmsTemplateContent} variables={variables} />
      )}
      {availableVariables?.length && (
        <FormProvider {...formProps}>
          <Typography variant="subtitle1">Variables</Typography>
          <Grid container spacing={2}>
            {availableVariables.map((v) => (
              <Grid item>
                <TextInput name={v} label={v} />
              </Grid>
            ))}
          </Grid>
        </FormProvider>
      )}
    </Stack>
  );
}
