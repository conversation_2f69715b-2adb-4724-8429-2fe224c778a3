import CloseOutlined from '@mui/icons-material/CloseOutlined';
import DeleteIcon from '@mui/icons-material/Delete';
import { <PERSON><PERSON>, Drawer, IconButton, Stack } from '@mui/material';
import { useList } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import { displayCurrency } from '@utils/currency';
import { getRemainingPayments } from '@utils/payments';
import dayjs from 'dayjs';
import { useSchedule } from 'hooks/schedules/useSchedule';
import { useEffect, useState } from 'react';
import { FormProvider } from 'react-hook-form';

export default function CancelPaymentsDrawer({
  visible,
  close,
  fulfillment,
  singlePayment = false,
  refetch = () => {},
}): JSX.Element {
  const remainingPayments = getRemainingPayments(fulfillment);
  const totalRemaining = remainingPayments.reduce((sum, payment) => sum + payment.amount, 0);

  const platformFormProps = useForm({
    refineCoreProps: {
      action: 'edit',
      resource: 'fulfillments',
      redirect: 'show',
      invalidates: ['list', 'many'],
      id: fulfillment.id,
      meta: { fields: ['id'] },
    },
  });
  const paymentsFormProps = useForm({
    refineCoreProps: {
      action: 'edit',
      resource: 'schedules',
      dataProviderName: 'payment',
      redirect: false,
      meta: { fields: ['id'] },
    },
  });
  const schedulerFormProps = useForm({
    refineCoreProps: {
      action: 'edit',
      resource: 'schedules',
      dataProviderName: 'scheduler',
      redirect: false,
      meta: { fields: ['id'] },
      onMutationSuccess: () => {
        if (refetch) refetch();
        close();
      },
    },
  });

  const { data: paymentScheduleData } = useList({
    resource: 'schedules',
    dataProviderName: 'payment',
    meta: {
      fields: [{ nodes: ['id', 'referenceId', 'paymentMethod'] }],
    },
    filters: [
      { field: 'deactivatedAt', operator: 'null', value: true },
      { field: 'referenceId', operator: 'eq', value: fulfillment.id },
    ],
  });

  const { schedule } = useSchedule(fulfillment.id);

  useEffect(() => {
    if (paymentScheduleData) paymentsFormProps.refineCore.setId(paymentScheduleData?.data[0]?.id);
  }, [paymentScheduleData, paymentsFormProps.refineCore.setId]);

  useEffect(() => {
    if (!schedule) return;
    schedulerFormProps.refineCore.setId(schedule.id);
  }, [schedule, schedulerFormProps.refineCore.setId]);

  const [error, setError] = useState<string>();

  const onSubmit = async (e): Promise<void> => {
    setError(undefined);
    const now = dayjs.utc();

    const paymentsSchedule = paymentScheduleData?.data[0];
    if (!schedule || !paymentsSchedule) {
      setError('Could not find relational data for schedule - please report to engineering');
      return;
    }

    for (const [idx, payment] of remainingPayments.entries()) {
      platformFormProps.setValue(`payments.${idx}.id`, payment.id);
      platformFormProps.setValue(`payments.${idx}.status`, 'CANCELED');
      schedulerFormProps.setValue(`jobs.${idx}.id`, payment.id);
      schedulerFormProps.setValue(`jobs.${idx}.deactivatedAt`, now);
    }

    paymentsFormProps.saveButtonProps.onClick(e);
    platformFormProps.saveButtonProps.onClick(e);
    schedulerFormProps.saveButtonProps.onClick(e);
  };

  return (
    <Drawer
      open={visible}
      onClose={close}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Edit
        resource="fulfillments"
        title={singlePayment ? 'Cancel scheduled Payment' : 'Cancel Remaining Payments'}
        saveButtonProps={{
          disabled: platformFormProps.saveButtonProps.disabled,
          onClick: platformFormProps.handleSubmit(onSubmit),
          children: 'Cancel Payments',
          startIcon: undefined,
          endIcon: <DeleteIcon />,
          color: 'error',
        }}
        isLoading={false}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <FormProvider {...platformFormProps}>
          <Stack component="form" autoComplete="off" gap={1}>
            <Alert severity="warning">
              Are you sure you wish to cancel the payment(s) on this schedule? This cannot be
              undone.
              <br />
              <br />
              There is {displayCurrency(totalRemaining)} remaining to be paid out.
            </Alert>
            {error && <Alert severity="error">{error}</Alert>}
          </Stack>
        </FormProvider>
      </Edit>
    </Drawer>
  );
}
