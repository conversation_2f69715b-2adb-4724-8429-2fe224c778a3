import HiddenInput from '@components/forms/HiddenInput';
import { CloseOutlined } from '@mui/icons-material';
import { <PERSON><PERSON>, Drawer, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ack, TextField } from '@mui/material';
import { type BaseRecord, useNotification, useUpdate } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import promisify from '@utils/promisify';
import { useState } from 'react';
import { FormProvider } from 'react-hook-form';

export default function EditSchedulerSettingsDrawer({
  visible,
  close,
  setting,
  onDrawerClose,
}: {
  visible: boolean;
  close: () => void;
  setting?: BaseRecord;
  onDrawerClose?: () => void;
}): JSX.Element {
  const formProps = useForm({ values: setting });
  const [error, setError] = useState<string>();
  const [isLoading, setIsLoading] = useState(false);
  const { mutate: doUpdate } = useUpdate();

  const { handleSubmit, register, saveButtonProps, getValues } = formProps;

  const onClose = () => {
    setError(undefined);
    setIsLoading(false);
    formProps.reset();
    onDrawerClose?.();
    close();
  };

  const onSubmit = async (): Promise<void> => {
    setError(undefined);
    setIsLoading(true);

    const { id, value, description } = getValues();
    const result = await promisify(doUpdate, {
      resource: 'settings',
      dataProviderName: 'scheduler',
      id: id as string,
      values: { value, description },
    });

    const { data } = result as { data: { id: string } };

    if (data?.id) {
      onClose();
    } else {
      setError('Issue updating setting - please report to engineering');
      setIsLoading(false);
    }
  };

  return (
    <Drawer
      open={visible}
      onClose={onClose}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Edit
        resource="payments-settings"
        title="Edit Scheduler Setting"
        saveButtonProps={{
          disabled: saveButtonProps.disabled,
          onClick: handleSubmit(onSubmit),
          loading: isLoading,
        }}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={onClose} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        {setting && (
          <FormProvider {...formProps}>
            <Stack component="form" autoComplete="off" gap={2}>
              <HiddenInput name="setting.id" value={setting.id} />
              <TextField {...register('key')} label="Key" disabled />
              <TextField {...register('value')} label="value" />
              <TextField
                {...register('description')}
                label="Description"
                type="text"
                multiline
                rows={4}
              />
              {error && <Alert severity="error">{error}</Alert>}
            </Stack>
          </FormProvider>
        )}
      </Edit>
    </Drawer>
  );
}
