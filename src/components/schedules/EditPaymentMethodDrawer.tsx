import SelectInput from '@components/forms/SelectInput';
import TextInput from '@components/forms/TextInput';
import CloseOutlined from '@mui/icons-material/CloseOutlined';
import { Alert, Drawer, IconButton, Stack } from '@mui/material';
import { useCreate, useUpdate } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import { getRemainingPayments } from '@utils/payments';
import promisify from '@utils/promisify';
import dayjs from 'dayjs';
import { usePaymentSchedule } from 'hooks/payments/usePaymentSchedule';
import { useRecipient } from 'hooks/payments/useRecipient';
import { useEffect, useState } from 'react';
import { FormProvider } from 'react-hook-form';

export default function EditPaymentMethodDrawer({ visible, close, fulfillment }): JSX.Element {
  const remainingPayments = getRemainingPayments(fulfillment);
  const nextPayment = [...remainingPayments].sort(
    (p1, p2) => dayjs(p1.scheduledFor).unix() - dayjs(p2.scheduledFor).unix(),
  )[0];

  const platformFormProps = useForm({
    refineCoreProps: {
      action: 'edit',
      resource: 'fulfillments',
      redirect: 'show',
      id: fulfillment.id,
      meta: { fields: ['id'] },
    },
  });
  const { getValues, handleSubmit, saveButtonProps, setValue, watch } = platformFormProps;
  useEffect(() => setValue('method', nextPayment.method), [setValue, nextPayment]);

  const { paymentSchedule } = usePaymentSchedule(fulfillment.id);
  const { recipient } = useRecipient(nextPayment.payeeId);

  useEffect(() => {
    if (!paymentSchedule) return;

    const cardId = paymentSchedule.account?.keys?.cardId;
    if (cardId && !getValues().cardId) setValue('cardId', cardId);
  }, [paymentSchedule, getValues, setValue]);

  const isPrepaidCard = (method) => ['PHYSICAL_CARD', 'VIRTUAL_CARD'].includes(method);

  const [error, setError] = useState<string>();

  const { mutate: doCreate } = useCreate();
  const { mutate: doUpdate } = useUpdate();

  const prepareAccount = async (): Promise<string | undefined> => {
    const { method, cardId: strCardId } = getValues();
    const cardId = Number(strCardId);

    const existingAccount = paymentSchedule?.account;
    const deactivatePrepaidAccount = async () => {
      if (!existingAccount || !isPrepaidCard(existingAccount.type)) return;
      await promisify(doUpdate, {
        resource: 'accounts',
        dataProviderName: 'payment',
        id: existingAccount.id,
        values: { deactivatedAt: dayjs().utc().toDate() },
      });
    };

    if (method === 'ACH') {
      const account = recipient?.accountsList?.find(
        (account) => !account.deactivatedAt && account.type === 'ACH',
      );
      if (!account) {
        setError(
          'Could not find bank account for recipient. Please ensure the recipient has bank account details saved',
        );
        return;
      }
      await deactivatePrepaidAccount();
      return account.id;
    }

    if (cardId === existingAccount?.keys?.cardId) return existingAccount.id;

    await deactivatePrepaidAccount();
    const newAccount = await promisify(doCreate, {
      resource: 'accounts',
      dataProviderName: 'payment',
      values: {
        referenceId: fulfillment.id,
        recipientId: recipient?.id,
        type: method,
        keys: { cardId },
      },
    });
    const { data } = newAccount as { data: { id: string } };
    return data.id;
  };

  const onSubmit = async (): Promise<void> => {
    setError(undefined);

    if (!paymentSchedule || !recipient) {
      setError('Could not find relational data for schedule - please report to engineering');
      return;
    }

    const { method, cardId } = getValues();
    if (method === nextPayment.method && cardId === paymentSchedule.account?.keys?.cardId)
      return close();

    const accountId = await prepareAccount();
    if (!accountId) return;

    await promisify(doUpdate, {
      resource: 'schedules',
      dataProviderName: 'payment',
      id: paymentSchedule.id as string,
      values: {
        accountId,
        paymentMethod: method,
      },
    });

    await promisify(doUpdate, {
      resource: 'fulfillments',
      id: fulfillment.id,
      values: {
        payments: remainingPayments.map(({ id }) => ({ id, method })),
      },
    });

    close();
  };

  return (
    <Drawer
      open={visible}
      onClose={close}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Edit
        resource="fulfillments"
        title="Change Payment Method"
        saveButtonProps={{ disabled: saveButtonProps.disabled, onClick: handleSubmit(onSubmit) }}
        isLoading={false}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <FormProvider {...platformFormProps}>
          <Stack component="form" autoComplete="off" gap={1}>
            <Alert severity="info">
              You are changing the payment method for all {remainingPayments.length} remaining
              payments.
            </Alert>
            <SelectInput
              name="method"
              label="Payment Method"
              options={[
                { id: 'ACH', name: 'Direct Deposit' },
                { id: 'PHYSICAL_CARD', name: 'Physical Prepaid Card' },
                { id: 'VIRTUAL_CARD', name: 'Virtual Prepaid Card' },
              ]}
            />
            {isPrepaidCard(watch('method')) && (
              <TextInput name="cardId" label="Prepaid Card ID" type="number" required />
            )}
            {error && <Alert severity="error">{error}</Alert>}
          </Stack>
        </FormProvider>
      </Edit>
    </Drawer>
  );
}
