import { DataGrid } from '@components/data-grid/DataGrid';
import { type BaseRecord, useModal } from '@refinedev/core';
import { EditButton, useDataGrid } from '@refinedev/mui';
import { useMemo, useState } from 'react';
import EditSchedulerSettingsDrawer from './EditSchedulerSettingsDrawer';

export default function SchedulerSettingsList(): JSX.Element {
  const { dataGridProps } = useDataGrid({
    resource: 'settings',
    dataProviderName: 'scheduler',
    meta: {
      fields: [{ nodes: ['id', 'key', 'value', 'description'] }],
    },
  });
  const [selectedSetting, setSelectedSetting] = useState<BaseRecord>();
  const editSettingDrawer = useModal();
  const onEdit = (row: BaseRecord) => {
    setSelectedSetting(row);
    editSettingDrawer.show();
  };
  const columns = useMemo(
    () => [
      {
        field: 'key',
        headerName: 'Key',
        flex: 1,
        sortable: true,
      },
      {
        field: 'value',
        headerName: 'Value',
        flex: 1,
        sortable: true,
      },
      {
        field: 'description',
        headerName: 'Description',
        flex: 1,
        sortable: true,
      },
      {
        field: 'actions',
        headerName: 'Actions',
        renderCell: ({ row }) => <EditButton size="small" onClick={() => onEdit(row)} />,
        align: 'center',
        headerAlign: 'center',
        minWidth: 80,
      },
    ],
    [onEdit],
  );

  return (
    <>
      <EditSchedulerSettingsDrawer {...editSettingDrawer} setting={selectedSetting} />
      <DataGrid {...dataGridProps} hideToolbar columns={columns} />
    </>
  );
}
