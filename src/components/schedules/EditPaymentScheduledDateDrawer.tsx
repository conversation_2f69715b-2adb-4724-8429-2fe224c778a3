import DatePicker from '@components/forms/DatePicker';
import { CloseOutlined } from '@mui/icons-material';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemText,
  Stack,
  Typography,
} from '@mui/material';
import { type BaseRecord, useNotification } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import { displayDate, standardizeScheduleTime } from '@utils/date';
import axios from 'axios';
import dayjs from 'dayjs';
import { useJobs } from 'hooks/schedules/useJob';
import { useEffect, useState } from 'react';
import { FormProvider } from 'react-hook-form';

export default function EditPaymentScheduledDateDrawer({
  visible,
  close,
  payments = [],
  onDrawerClose,
}: {
  visible: boolean;
  close: () => void;
  payments: BaseRecord[];
  onDrawerClose?: () => void;
}): JSX.Element {
  const [error, setError] = useState<string>();
  const [open, setOpen] = useState(true);
  const { open: notify } = useNotification();
  const [isLoading, setIsLoading] = useState(false);
  const formProps = useForm();
  const paymentIds = payments.flatMap((payment) => {
    if (!payment?.id) return [];
    return [payment.id];
  });
  const { jobs: scheduledJobs } = useJobs(paymentIds, { status: 'SCHEDULED', operator: 'not' });

  const { handleSubmit, saveButtonProps, setValue, getValues, watch } = formProps;

  useEffect(() => {
    if (payments[0]?.scheduledFor) {
      /// since all of the payments will have the same date we just use the first one to set the default form value
      setValue('scheduledFor', payments[0].scheduledFor);
    }
  }, [payments, setValue]);

  const onClose = () => {
    setError(undefined);
    setIsLoading(false);
    formProps.reset();
    onDrawerClose?.();
    close();
  };

  const onSubmit = async (): Promise<void> => {
    const errorMessage = 'Could not find relational data for job - please report to engineering';
    setError(undefined);
    setIsLoading(true);
    const { scheduledFor } = getValues();

    if (!scheduledJobs?.length) {
      setError(errorMessage);
      setIsLoading(false);
      return;
    }
    const jobIds = scheduledJobs.flatMap((job) => {
      if (!job?.id) return [];
      return [job.id];
    });

    if (jobIds.length !== paymentIds.length) {
      setError(errorMessage);
      setIsLoading(false);
      return;
    }

    const response = await axios.post(
      '/api/platform/scheduledPayments/changeScheduledDate',
      {
        jobIds,
        paymentIds,
        scheduledFor: standardizeScheduleTime(scheduledFor),
      },
      { headers: { 'Content-Type': 'application/json' } },
    );
    if (response.status === 200) {
      notify?.({
        message: `Successfully updated scheduled payments to ${dayjs(scheduledFor).format(
          'MM/DD/YYYY',
        )}`,
        type: 'success',
      });
      onClose();
    } else {
      setError('Issue updating payments - please report to engineering');
      setIsLoading(false);
    }
  };

  return (
    <Drawer
      open={visible}
      onClose={onClose}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Edit
        resource="payments"
        title={`Edit Schedule Date${payments.length > 1 ? 's' : ''}`}
        saveButtonProps={{
          disabled: saveButtonProps.disabled,
          onClick: handleSubmit(onSubmit),
          loading: isLoading,
        }}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={onClose} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <FormProvider {...formProps}>
          <Stack component="form" autoComplete="off" gap={1}>
            {payments.length > 1 && (
              <Button onClick={() => setOpen(!open)}>
                {open ? 'Collapse' : 'View'} Affected Payments
              </Button>
            )}
            <Collapse in={open} timeout="auto" unmountOnExit>
              <List
                sx={{
                  width: '100%',
                  bgcolor: 'background.paper',
                }}
              >
                {payments.map((payment, index) => (
                  <Stack key={payment.displayId} mb={1}>
                    {payments.length > 1 && <Typography>Payment {index + 1}</Typography>}
                    <Stack direction="row">
                      <ListItem>
                        <ListItemText primary={payment.displayId} secondary="Display ID" />
                      </ListItem>
                      <ListItem>
                        <ListItemText
                          primary={displayDate(payment?.scheduledFor)}
                          secondary="Past Scheduled Date"
                        />
                      </ListItem>
                    </Stack>
                  </Stack>
                ))}
              </List>
            </Collapse>
            <DatePicker
              label="Schedule For"
              value={watch('scheduledFor')}
              onChange={(value) => setValue('scheduledFor', value)}
              disablePast
            />
            {error && <Alert severity="error">{error}</Alert>}
          </Stack>
        </FormProvider>
      </Edit>
    </Drawer>
  );
}
