import { CloseOutlined } from '@mui/icons-material';
import { <PERSON><PERSON>, Drawer, Icon<PERSON>utton } from '@mui/material';
import { useGo, useUpdate } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import { FeatureName } from 'hooks/features/types';
import dayjs from '../../utils/dayJsConfig';

export default function StopPaymentDrawer({ visible, close, payment }): JSX.Element {
  const go = useGo();
  const formProps = useForm({ refineCoreProps: { action: 'create' } });
  const { mutate } = useUpdate();

  const { handleSubmit } = formProps;

  const isClaimFundEnabled = payment?.fulfillment?.case?.program?.programFeaturesList?.find(
    (programFeature) => FeatureName.PaymentsClaimFunds === programFeature?.feature?.name,
  )?.enabled;

  const onClose = () => {
    formProps.reset();
    close();
    go({ to: { resource: 'cases', action: 'show', id: payment.fulfillment.case.id } });
  };

  // Future state: we could request a payment stoppage from the payment provider here
  // for Partner-Issued.
  const resetPayment = async (): Promise<void> => {
    const now = dayjs().utc().format();
    mutate(
      {
        resource: 'fulfillments',
        id: payment.fulfillment.id,
        values: {
          deactivatedAt: now,
          payments: { id: payment.id, deactivatedAt: now },
          case: { id: payment.fulfillment.case.id, status: 'READY_FOR_REVIEW' },
        },
      },
      {},
    );
    onClose();
  };

  const onSubmit = async (): Promise<void> => await resetPayment();

  return (
    <Drawer
      open={visible}
      onClose={onClose}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Edit
        resource="cases"
        title="Stop Payment"
        saveButtonProps={{ disabled: !isClaimFundEnabled, onClick: handleSubmit(onSubmit) }}
        isLoading={false}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={onClose} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <Alert severity="info">
          This only works for Claim Funds. It will remove the existing approved payment and set the
          case status back to "Ready for Review", which will prevent the applicant from claiming.
        </Alert>
      </Edit>
    </Drawer>
  );
}
