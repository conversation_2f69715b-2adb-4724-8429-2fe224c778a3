import SelectInput from '@components/forms/SelectInput';
import CloseOutlined from '@mui/icons-material/CloseOutlined';
import {
  <PERSON><PERSON>,
  AlertTitle,
  Drawer,
  IconButton,
  List,
  ListItem,
  ListItemText,
  Stack,
} from '@mui/material';
import { type BaseKey, useList, useNotification, useUpdate } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import { displayCurrency } from '@utils/currency';
import promisify from '@utils/promisify';
import { FormProvider } from 'react-hook-form';

export const PaymentStatuses = {
  SUCCESS: 'Success',
  FAILED: 'Failed',
};

export default function ChangeStatusDrawer({ visible, close, paymentId, onSuccess }) {
  const { open: notify } = useNotification();
  const { mutate: doUpdate } = useUpdate();
  const formProps = useForm({
    refineCoreProps: {
      resource: 'payment',
      action: 'edit',
      redirect: 'show',
      id: paymentId,
      meta: { fields: ['id', 'status', 'amount'] },
      onMutationSuccess: () => {
        if (onSuccess) onSuccess();
      },
    },
  });
  const {
    saveButtonProps,
    reset,
    handleSubmit,
    refineCore: { queryResult },
  } = formProps;

  const existingPayment = queryResult?.data?.data;

  const { data: paymentsData, isLoading: isPaymentLoading } = useList({
    dataProviderName: 'payment',
    resource: 'payments',
    filters: [
      {
        field: 'referenceId',
        operator: 'eq',
        value: existingPayment?.id,
      },
      { field: 'deactivatedAt', operator: 'null', value: true },
    ],
    queryOptions: {
      enabled: !!existingPayment,
    },
    meta: {
      fields: [{ nodes: ['id', 'status', 'amount'] }],
    },
  });

  const paymentDBRecord = paymentsData?.data?.[0];

  const mapPaymentDBStatus = (status) => {
    switch (status) {
      case 'SUCCESS':
        return 'SUCCEEDED';
      default:
        return status;
    }
  };

  const updatePaymentRecord = async (status) => {
    if (!paymentDBRecord || !existingPayment) return;

    const updateFields: { status: string; amount?: number } = {
      status: mapPaymentDBStatus(status),
    };

    if (status === 'SUCCESS' && !paymentDBRecord.amount)
      updateFields.amount = existingPayment.amount;

    await promisify(doUpdate, {
      resource: 'payments',
      dataProviderName: 'payment',
      id: paymentDBRecord.id as BaseKey,
      values: updateFields,
    });
  };

  const onSubmit = (data, e) => {
    const { status } = data;
    updatePaymentRecord(status)
      .then(() => {
        saveButtonProps.onClick(e);
        onClose();
      })
      .catch(() => {
        notify?.({ message: 'Can not update the status', type: 'error' });
      });
  };

  const onClose = () => {
    reset();
    close();
  };

  return (
    <Drawer
      open={visible}
      onClose={onClose}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Edit
        title="Change Payment Status"
        resource="payments"
        saveButtonProps={{
          ...saveButtonProps,
          disabled: isPaymentLoading,
          onClick: handleSubmit(onSubmit),
        }}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={onClose} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <FormProvider {...formProps}>
          <List
            sx={{
              width: '100%',
              bgcolor: 'background.paper',
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
            }}
          >
            <ListItem>
              <ListItemText primary={existingPayment?.status} secondary="Current Status" />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={displayCurrency(existingPayment?.amount ?? 0)}
                secondary="Amount"
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={paymentDBRecord?.status ?? 'N/A'}
                secondary="Provider Payment Status"
              />
            </ListItem>
            <ListItem>
              <ListItemText
                primary={paymentDBRecord?.amount ? displayCurrency(paymentDBRecord.amount) : 'N/A'}
                secondary="Provider Payment Amount"
              />
            </ListItem>
          </List>
          <Stack component="form" autoComplete="off" gap={1}>
            <SelectInput
              name="status"
              label="Status"
              options={Object.entries(PaymentStatuses).map(([id, name]) => ({ id, name }))}
            />
            <Alert severity="warning">
              <AlertTitle>For Special Circumstances Only</AlertTitle>
              This tool is only intended for use in special cases that are not supported according
              to normal rules. Standard changes should be made in the platform. This will update
              corresponding record in payment db as well (if any).
            </Alert>
          </Stack>
        </FormProvider>
      </Edit>
    </Drawer>
  );
}
