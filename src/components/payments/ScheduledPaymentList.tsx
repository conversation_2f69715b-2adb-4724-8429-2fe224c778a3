import { DataGrid } from '@components/data-grid/DataGrid';
import Link from '@components/navigation/Link';
import CancelPaymentsDrawer from '@components/schedules/CancelPaymentsDrawer';
import EditPaymentScheduledDateDrawer from '@components/schedules/EditPaymentScheduledDateDrawer';
import CancelIcon from '@mui/icons-material/Cancel';
import ScheduleIcon from '@mui/icons-material/Schedule';
import { Badge, Button, IconButton, Stack } from '@mui/material';
import type { GridRowParams } from '@mui/x-data-grid';
import {
  DateCalendar,
  DayCalendarSkeleton,
  LocalizationProvider,
  PickersDay,
  type PickersDayProps,
} from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { Paymentmethod } from '@prisma/generated/paymentClient';
import {
  type BaseKey,
  type BaseRecord,
  type CrudFilters,
  useList,
  useModal,
} from '@refinedev/core';
import { List, ShowButton, useDataGrid } from '@refinedev/mui';
import { displayCurrency } from '@utils/currency';
import { displayDate } from '@utils/date';
import { getAccountValue } from '@utils/payments';
import dayjs, { type Dayjs } from 'dayjs';
import usePaymentAccounts from 'hooks/payments/usePaymentAccounts';
import { useEffect, useMemo, useState } from 'react';

const getAccountType = (method: string) => {
  switch (method) {
    case 'ACH':
      return Paymentmethod.ach;
    case 'PHYSICAL_CARD':
      return Paymentmethod.physicalCard;
    case 'VIRTUAL_CARD':
      return Paymentmethod.virtualCard;
    default:
      return '';
  }
};

export default function ScheduledPaymentsList({ partnerId }: { partnerId?: BaseKey }): JSX.Element {
  const [highlightedDays, setHighlightedDays] = useState<Record<string, number>>({});
  const [selectedPayments, setSelectedPayments] = useState<BaseRecord[]>([]);
  const [selectedMonth, setSelectedMonth] = useState(dayjs());
  const [selectedDate, setSelectedDate] = useState<Dayjs | null>(dayjs());
  const initialValue = useMemo(() => dayjs(), []);
  const editScheduleDateDrawer = useModal();
  const cancelPaymentDrawer = useModal();
  const { data, refetch } = useList({
    resource: 'payments',
    meta: {
      fields: [
        {
          nodes: [
            'id',
            'scheduledFor',
            {
              fulfillment: [{ fund: ['id', { partner: ['id', 'name'] }] }],
            },
          ],
        },
      ],
    },
    filters: [
      { field: 'deactivatedAt', operator: 'null', value: true },
      {
        operator: 'and',
        value: [
          {
            field: 'scheduledFor',
            operator: 'gte',
            value: selectedMonth.startOf('month').toISOString(),
          },
          {
            field: 'scheduledFor',
            operator: 'lte',
            value: selectedMonth.endOf('month').toISOString(),
          },
        ],
      },
      partnerId && {
        field: 'fulfillment.fund.partner.id',
        operator: 'eq',
        value: partnerId,
      },
    ].filter(Boolean) as CrudFilters,
    pagination: {
      mode: 'off',
    },
    liveMode: 'auto',
  });

  const daysData = data?.data || [];
  const { dataGridProps, tableQueryResult } = useDataGrid({
    resource: 'payments',
    meta: {
      fields: [
        {
          nodes: [
            'id',
            'displayId',
            'amount',
            'status',
            'method',
            'payeeType',
            'payeeId',
            'initiatedAt',
            'scheduledFor',
            {
              fulfillment: [
                'id',
                'scheduleType',
                { fund: ['id', 'name', { partner: ['id', 'name'] }] },
                { case: ['id', { applicationsList: ['id', { submitter: ['id', 'name'] }] }] },
              ],
            },
          ],
        },
      ],
    },
    sorters: {
      initial: [{ field: 'scheduledFor', order: 'asc' }],
    },
    queryOptions: {
      retry: false,
    },
    filters: {
      defaultBehavior: 'replace',
      permanent: [
        { field: 'deactivatedAt', operator: 'null', value: true },
        {
          operator: 'and',
          value: [
            {
              field: 'scheduledFor',
              operator: 'gte',
              value: (selectedDate || initialValue).startOf('day').toISOString(),
            },
            {
              field: 'scheduledFor',
              operator: 'lte',
              value: (selectedDate || initialValue).endOf('day').toISOString(),
            },
          ],
        },
        partnerId && {
          field: 'fulfillment.fund.partner.id',
          operator: 'eq',
          value: partnerId,
        },
      ].filter(Boolean) as CrudFilters,
    },
  });
  const paymentsWithAccount = dataGridProps?.rows?.filter((payment) =>
    ['PHYSICAL_CARD', 'VIRTUAL_CARD', 'ACH'].includes(payment.method),
  );

  const { paymentAccounts } = usePaymentAccounts({
    filters: [
      {
        field: 'referenceId',
        operator: 'in',
        value: paymentsWithAccount.map(
          ({ payeeId, method }) => `${payeeId}-${getAccountType(method)}`,
        ),
      },
    ],
    enabled: !!paymentsWithAccount?.length,
  });

  useEffect(() => {
    if (selectedMonth.month()) {
      refetch();
    }
  }, [selectedMonth, refetch]);

  useEffect(() => {
    const daysToHighlight = {};
    for (const date of daysData) {
      const day = dayjs(date.scheduledFor).date().toString();
      if (!daysToHighlight[day]) {
        daysToHighlight[day] = 1;
      } else {
        daysToHighlight[day] += 1;
      }
    }
    setHighlightedDays(daysToHighlight);
  }, [daysData]);

  const columns = useMemo(() => {
    return [
      { field: 'id', headerName: 'Id', flex: 1 },
      {
        field: 'fulfillment.fund.partner.name',
        headerName: 'Partner',
        valueGetter: (params) => params.row.fulfillment?.fund?.partner,
        valueFormatter: ({ value }) => value?.name,
        renderCell: ({ value }) => <Link to={`/partners/show/${value?.id}`}>{value?.name}</Link>,
        sortable: false,
      },
      {
        field: 'fulfillment.fund.name',
        headerName: 'Fund',
        valueGetter: (params) => params.row.fulfillment?.fund,
        valueFormatter: ({ value }) => value?.name,
        renderCell: ({ value }) => <Link to={`/funds/show/${value?.id}`}>{value?.name}</Link>,
        sortable: false,
      },
      {
        field: 'fulfillment.case.applications.some.submitter.name',
        headerName: 'Applicant',
        flex: 1,
        valueGetter: (params) => params.row.fulfillment?.case?.applicationsList?.[0]?.submitter,
        valueFormatter: ({ value }) => value?.name,
        renderCell: ({ value }) => (
          <Link to={`/users/coreUsers/show/${value?.id}`}>{value?.name}</Link>
        ),
        sortable: false,
      },
      { field: 'payeeType', headerName: 'Payee Type' },
      { field: 'method', headerName: 'Method' },
      {
        field: 'amount',
        headerName: 'Amount',
        renderCell: ({ value }) => displayCurrency(value),
      },
      { field: 'status', headerName: 'Status' },
      {
        field: 'scheduledFor',
        headerName: 'Payment Date',
        flex: 1,
        renderCell: ({ value }) => displayDate(value),
      },
      {
        field: 'account',
        headerName: 'Account',
        flex: 1,
        sortable: false,
        valueGetter: (params) =>
          paymentAccounts?.find(
            (account) =>
              account.recipient.referenceId === params.row.payeeId &&
              account.type === params.row.method,
          ),
        valueFormatter: ({ value }) => getAccountValue(value),
        renderCell: ({ value }) => getAccountValue(value),
      },
      {
        field: 'actions',
        headerName: 'Actions',
        sortable: false,
        renderCell: ({ row }) => (
          <>
            <ShowButton hideText resource="payments" recordItemId={row.id} />
            <IconButton
              onClick={() => {
                setSelectedPayments([row]);
                editScheduleDateDrawer.show();
              }}
              disabled={row.status !== 'AUTHORIZED' || !!row.initiatedAt}
              color="primary"
            >
              <ScheduleIcon fontSize="small" />
            </IconButton>
            <IconButton
              onClick={() => {
                setSelectedPayments([row]);
                cancelPaymentDrawer.show();
              }}
              disabled={row.status !== 'AUTHORIZED' || !!row.initiatedAt}
              color="primary"
            >
              <CancelIcon fontSize="small" />
            </IconButton>
          </>
        ),
        align: 'center',
        headerAlign: 'center',
      },
    ];
  }, [editScheduleDateDrawer.show, cancelPaymentDrawer.show, paymentAccounts]);

  const handleDateChange = (date: Dayjs | null) => {
    setSelectedDate(date);
  };

  const handleMonthChange = (date: Dayjs) => {
    setSelectedMonth(date);
  };

  const handleRowSelection = (selectedRows) => {
    setSelectedPayments(
      selectedRows.length
        ? dataGridProps.rows.filter((payment) => selectedRows.includes(payment.id))
        : [],
    );
  };

  const handleDrawerClose = () => {
    refetch();
    setSelectedPayments([]);
    setSelectedDate((date) => date);
    tableQueryResult.refetch();
  };

  return (
    <List
      resource="fulfillments"
      title="Scheduled Payments"
      headerButtons={({ defaultButtons }) => (
        <>
          {defaultButtons}
          {selectedPayments.length > 0 && (
            <Button variant="contained" onClick={() => editScheduleDateDrawer.show()}>
              Edit scheduled date of {selectedPayments.length} payment(s)
            </Button>
          )}
        </>
      )}
    >
      <Stack direction="row" gap={2}>
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <DateCalendar
            defaultValue={initialValue}
            loading={false}
            renderLoading={() => <DayCalendarSkeleton />}
            slots={{
              day: PaymentDay,
            }}
            slotProps={{
              day: {
                highlightedDays,
                // biome-ignore lint/suspicious/noExplicitAny: <explanation>
              } as any,
            }}
            onMonthChange={handleMonthChange}
            onYearChange={handleMonthChange}
            value={selectedDate}
            onChange={handleDateChange}
          />
        </LocalizationProvider>
        <DataGrid
          {...dataGridProps}
          columns={columns}
          disableColumnFilter={true}
          checkboxSelection={true}
          onRowSelectionModelChange={handleRowSelection}
          isRowSelectable={(params: GridRowParams) =>
            params.row.status === 'AUTHORIZED' || !params.row.initiatedAt
          }
        />
      </Stack>
      {selectedPayments && editScheduleDateDrawer.visible && (
        <EditPaymentScheduledDateDrawer
          {...editScheduleDateDrawer}
          payments={selectedPayments}
          onDrawerClose={handleDrawerClose}
        />
      )}
      {!!selectedPayments?.length && cancelPaymentDrawer.visible && (
        <CancelPaymentsDrawer
          {...cancelPaymentDrawer}
          fulfillment={{
            ...selectedPayments[0].fulfillment,
            payments: [selectedPayments[0]],
          }}
          singlePayment
          refetch={() => {
            handleDrawerClose();
          }}
        />
      )}
    </List>
  );
}

function PaymentDay(props: PickersDayProps<Dayjs> & { highlightedDays?: Record<string, number> }) {
  const { highlightedDays = {}, day, outsideCurrentMonth, ...other } = props;

  const isSelected = !props.outsideCurrentMonth && highlightedDays[props.day.date()];

  return (
    <Badge
      key={props.day.date().toString()}
      overlap="circular"
      badgeContent={isSelected ? highlightedDays[props.day.date()] : undefined}
      color="secondary"
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'right',
      }}
    >
      <PickersDay {...other} outsideCurrentMonth={outsideCurrentMonth} day={day} />
    </Badge>
  );
}
