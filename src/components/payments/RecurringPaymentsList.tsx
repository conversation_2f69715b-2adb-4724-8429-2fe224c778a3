import { DataGrid } from '@components/data-grid/DataGrid';
import Link from '@components/navigation/Link';
import { List, useDataGrid } from '@refinedev/mui';
import { displayCurrency } from '@utils/currency';
import { displayId } from '@utils/id';
import { useMemo } from 'react';

export default function RecurringPaymentsList(): JSX.Element {
  const { dataGridProps } = useDataGrid({
    resource: 'fulfillments',
    meta: {
      fields: [
        {
          nodes: [
            'id',
            'approvedAmount',
            'displayId',
            'scheduleType',
            { fund: ['id', 'name', { partner: ['id', 'name'] }] },
            {
              case: [
                'id',
                { applicationsList: ['id', { submitter: ['id', 'name'] }] },
                { program: ['id', 'name'] },
              ],
            },
            { payments: [{ nodes: ['id', 'method', 'amount'] }] },
            {
              paymentPatterns: [
                { nodes: ['id', 'fulfillmentId', 'amount', 'pattern', 'count', 'start'] },
              ],
            },
          ],
        },
      ],
    },
    sorters: {
      initial: [{ field: 'createdAt', order: 'asc' }],
    },
    filters: {
      defaultBehavior: 'replace',
      permanent: [
        { field: 'deactivatedAt', operator: 'null', value: true },
        { field: 'scheduleType', operator: 'eq', value: 'RECURRING' },
      ],
    },
  });
  const columns = useMemo(
    () => [
      { field: 'id', headerName: 'Id', flex: 1 },
      {
        field: 'fund.partner.name',
        headerName: 'Partner',
        flex: 1,
        valueGetter: (params) => params.row?.fund?.partner,
        renderCell: ({ value }) => <Link to={`/partners/show/${value?.id}`}>{value?.name}</Link>,
        sortable: false,
      },
      {
        field: 'fund.name',
        headerName: 'Fund',
        flex: 1,
        valueGetter: (params) => params.row?.fund,
        renderCell: ({ value }) => <Link to={`/funds/show/${value?.id}`}>{value?.name}</Link>,
        sortable: false,
      },
      {
        field: 'displayId',
        headerName: 'Display ID',
        sortable: false,
        renderCell: ({ value }) => displayId('F', value),
        flex: 1,
      },
      {
        field: 'case.applications.some.submitter.name',
        headerName: 'Applicant',
        flex: 1,
        valueGetter: (params) => params.row.case?.applicationsList?.[0]?.submitter,
        renderCell: ({ value }) => (
          <Link to={`/users/coreUsers/show/${value?.id}`}>{value?.name}</Link>
        ),
        sortable: false,
      },
      {
        field: 'approvedAmount',
        headerName: 'Approved Amount',
        renderCell: ({ value }) => displayCurrency(value),
        sortable: false,
        flex: 1,
      },
      {
        field: 'method',
        headerName: 'Method',
        valueGetter: (params) => params.row.payments?.[0]?.method,
        sortable: false,
        flex: 1,
      },
      {
        field: 'pattern',
        headerName: 'Pattern',
        valueGetter: (params) =>
          `${displayCurrency(params.row.paymentPatterns?.[0]?.amount ?? 0)} - ${
            params.row.paymentPatterns?.[0]?.pattern ?? 'N/A'
          }`,
        sortable: false,
        flex: 1,
      },
      {
        field: 'count',
        headerName: 'Number of Payments',
        valueGetter: (params) => params.row.paymentPatterns?.[0]?.count,
        sortable: false,
        flex: 1,
      },
      {
        field: 'actions',
        headerName: 'Actions',
        sortable: false,
        renderCell: ({ row }) => (
          <Link to={`/payments/recurringPayments/show/${row?.id}`}>SHOW</Link>
        ),
        align: 'center',
        headerAlign: 'center',
      },
    ],
    [],
  );

  return (
    <List resource="fulfillments" title="Payment Recurring Records">
      <DataGrid {...dataGridProps} columns={columns} />
    </List>
  );
}
