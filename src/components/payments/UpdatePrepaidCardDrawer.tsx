import TextInput from '@components/forms/TextInput';
import { CloseOutlined } from '@mui/icons-material';
import { Drawer, IconButton, Stack } from '@mui/material';
import { type BaseKey, useCreate, useList, useUpdate } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import { isPrepaidCard } from '@utils/payments';
import promisify from '@utils/promisify';
import { useEffect } from 'react';
import { FormProvider } from 'react-hook-form';
import dayjs from '../../utils/dayJsConfig';

export default function UpdatePrepaidCardDrawer({ visible, close, account }): JSX.Element {
  const formProps = useForm();
  const { getValues, handleSubmit, saveButtonProps, setValue, watch } = formProps;
  const { mutate: doUpdate } = useUpdate();
  const { mutate: doCreate } = useCreate();
  const { data: scheduledData } = useList({
    resource: 'schedules',
    dataProviderName: 'payment',
    meta: {
      fields: [
        {
          nodes: ['id', 'accountId'],
        },
      ],
    },
    filters: [
      { field: 'deactivatedAt', operator: 'null', value: true },
      { field: 'accountId', operator: 'eq', value: account?.id },
    ],
    queryOptions: { enabled: !!account },
  });

  const paymentSchedules = scheduledData?.data ?? [];

  const onClose = () => {
    formProps.reset();
    close();
  };

  const deactivatePrepaidAccount = async () => {
    await promisify(doUpdate, {
      resource: 'accounts',
      dataProviderName: 'payment',
      id: account.id,
      values: { deactivatedAt: dayjs().utc().toDate() },
    });
  };

  const prepareAccount = async (): Promise<string | undefined> => {
    const { cardId } = getValues();
    if (
      !account ||
      !isPrepaidCard(account.type) ||
      Number(cardId) === Number(account?.keys?.cardId)
    )
      return;

    await deactivatePrepaidAccount();
    const newAccount = await promisify(doCreate, {
      resource: 'accounts',
      dataProviderName: 'payment',
      values: {
        referenceId: account.referenceId,
        recipientId: account.recipient?.id,
        type: account.type,
        keys: { cardId },
      },
    });
    const { data } = newAccount as { data: { id: string } };
    return data.id as string;
  };

  const onSubmit = async (e): Promise<void> => {
    const accountId = await prepareAccount();

    if (paymentSchedules?.length && accountId) {
      await Promise.all(
        paymentSchedules.map(({ id }) =>
          doUpdate({
            resource: 'schedules',
            dataProviderName: 'payment',
            id: id as BaseKey,
            values: { accountId },
            successNotification: false,
          }),
        ),
      );
    }
    onClose();
  };

  useEffect(() => {
    const cardId = account?.keys?.cardId;
    if (cardId && !getValues().cardId) setValue('cardId', cardId);
  }, [account, getValues, setValue]);

  return (
    <Drawer
      open={visible}
      onClose={onClose}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Edit
        resource="accounts"
        dataProviderName="payment"
        title="Update Prepaid Card ID"
        saveButtonProps={{ disabled: saveButtonProps.disabled, onClick: handleSubmit(onSubmit) }}
        isLoading={false}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <FormProvider {...formProps}>
          <Stack component="form" autoComplete="off" gap={1}>
            <TextInput name="cardId" label="Prepaid Card ID" type="number" required />
          </Stack>
        </FormProvider>
      </Edit>
    </Drawer>
  );
}
