import DatePicker from '@components/forms/DatePicker';
import HiddenInput from '@components/forms/HiddenInput';
import SelectInput from '@components/forms/SelectInput';
import { CloseOutlined } from '@mui/icons-material';
import { <PERSON><PERSON>, Drawer, IconButton, Stack } from '@mui/material';
import { useUpdate } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import { useForm } from '@refinedev/react-hook-form';
import { standardizeScheduleTime } from '@utils/date';
import { checkFeature, checkFeatures } from '@utils/feature';
import { FeatureName } from 'hooks/features/types';
import useAuthorization from 'hooks/identity/useAuthorization';
import useUser from 'hooks/identity/useUser';
import { useJob } from 'hooks/schedules/useJob';
import { type CreateRelationshipRequest, Relation } from 'pages/api/types/identity';
import { useEffect, useState } from 'react';
import { FormProvider } from 'react-hook-form';
import { v4 as uuidv4 } from 'uuid';
import dayjs from '../../utils/dayJsConfig';

export default function ResetPaymentDrawer({ visible, close, payment }): JSX.Element {
  const [error, setError] = useState<string>();

  const formProps = useForm({ refineCoreProps: { action: 'create', redirect: 'show' } });
  const identityUser = useUser({ id: payment?.payeeId, userIdentifier: 'coreUserId' });
  const { createRelationships } = useAuthorization();

  const jobFormProps = useForm({
    refineCoreProps: {
      resource: 'jobs',
      dataProviderName: 'scheduler',
      action: 'create',
      redirect: false,
    },
  });
  const { mutate } = useUpdate();
  const { job: scheduledJob } = useJob(payment.id);

  const { handleSubmit, saveButtonProps, setValue, watch, getValues } = formProps;
  const isRecurring = payment.fulfillment.scheduleType === 'RECURRING';
  const paymentUseCase = watch('paymentsUseCase');

  const programFeatures = payment.fulfillment?.case?.program?.programFeaturesList;

  const getPaymentUseCase = () => {
    if (checkFeature(programFeatures, FeatureName.PaymentsClaimFunds)) return 'CLAIM_FUND';
    if (
      checkFeatures(programFeatures, [
        FeatureName.PaymentsMultiPayment,
        FeatureName.PaymentsPartnerIssued,
      ])
    )
      return 'MULTI_PARTNER_ISSUED';
    if (checkFeature(programFeatures, FeatureName.PaymentsPartnerIssued)) return 'PARTNER_ISSUED';
    return '';
  };

  const onClose = () => {
    setError(undefined);
    formProps.reset();
    jobFormProps.reset();
    close();
  };

  const getPayeeRelation = ({
    payeeId,
    payeeType,
  }: {
    payeeId: string;
    payeeType: string;
  }) => {
    if (payeeType === 'USER' && identityUser)
      return {
        objectType: Relation.USER,
        objectId: identityUser.id,
      };
    if (payeeType === 'VENDOR')
      return {
        objectType: Relation.VENDOR,
        objectId: payeeId,
      };

    return undefined;
  };

  const createPaymentRelationships = async ({
    paymentId,
    fulfillmentId,
    payeeId,
    caseId,
    payeeType,
  }) => {
    const payeeSubject = getPayeeRelation({ payeeType, payeeId });
    await createRelationships({
      relationships: [
        {
          relation: 'case',
          subject: { objectId: caseId, objectType: Relation.CASE },
          object: { objectId: fulfillmentId, objectType: Relation.FULFILLMENT },
        },
        {
          relation: 'fulfillment',
          subject: { objectId: fulfillmentId, objectType: Relation.FULFILLMENT },
          object: { objectId: paymentId, objectType: Relation.PAYMENT },
        },
        ...(payeeSubject
          ? [
              {
                relation: 'payee',
                subject: payeeSubject,
                object: { objectId: fulfillmentId, objectType: Relation.FULFILLMENT },
              },
              {
                relation: 'payee',
                subject: payeeSubject,
                object: { objectId: paymentId, objectType: Relation.PAYMENT },
              },
            ]
          : []),
      ],
    });
  };

  const resetPayment = async (e): Promise<void> => {
    const { id: newPaymentId, payeeType, payeeId } = getValues();
    const caseId = payment.fulfillment.case.id;
    const newFulfillmentId = uuidv4();

    setValue('fulfillment.create.id', newFulfillmentId);
    setValue('paymentsUseCase', undefined);
    if (paymentUseCase === 'CLAIM_FUNDS') setValue('method', undefined);

    const now = dayjs().utc().format();

    // This must run before the update, otherwise values get reset
    saveButtonProps.onClick(e);

    await mutate({
      resource: 'fulfillments',
      id: payment.fulfillment.id,
      values: {
        case: {
          id: caseId,
          status: ['MULTI_PARTNER_ISSUED', 'CLAIM_FUNDS'].includes(paymentUseCase)
            ? 'APPROVED'
            : 'READY_FOR_REVIEW',
        },
        deactivatedAt: now,
        payments: {
          id: payment.id,
          deactivatedAt: now,
        },
      },
    });
    await createPaymentRelationships({
      caseId,
      fulfillmentId: newFulfillmentId,
      paymentId: newPaymentId,
      payeeId,
      payeeType,
    });

    onClose();
  };

  const resetRecurringPayment = async (e): Promise<void> => {
    setError(undefined);
    if (!scheduledJob) {
      setError('Could not find relational data for job - please report to engineering');
      return;
    }
    if (scheduledJob.status === 'SCHEDULED') {
      setError('Previous job has not been processed - please report to engineering');
      return;
    }
    const now = dayjs().utc().format();
    const { id: newPaymentId, payeeType, payeeId } = getValues();
    const { priority, scheduledFor } = jobFormProps.getValues();
    const isImmediate = priority === 'IMMEDIATE';
    const standardizeScheduledFor = standardizeScheduleTime(isImmediate ? now : scheduledFor);

    setValue('scheduledFor', standardizeScheduledFor);
    jobFormProps.setValue('id', newPaymentId);
    jobFormProps.setValue('scheduledFor', standardizeScheduledFor);

    saveButtonProps.onClick(e);

    jobFormProps.saveButtonProps.onClick(e);

    await mutate({
      resource: 'fulfillments',
      id: payment.fulfillment.id,
      values: {
        payments: {
          id: payment.id,
          deactivatedAt: now,
        },
      },
    });

    await createPaymentRelationships({
      caseId: payment.fulfillment.case.id,
      fulfillmentId: payment.fulfillment.id,
      paymentId: newPaymentId,
      payeeId,
      payeeType,
    });

    onClose();
  };

  const onSubmit = async (e): Promise<void> => {
    if (isRecurring) await resetRecurringPayment(e);
    else await resetPayment(e);
  };

  useEffect(() => {
    if (isRecurring && !!scheduledJob) {
      jobFormProps.setValue('priority', scheduledJob.priority);
      jobFormProps.setValue('scheduleId', scheduledJob.scheduleId);
    }
  }, [isRecurring, scheduledJob, jobFormProps.setValue]);

  return (
    <Drawer
      open={visible}
      onClose={onClose}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Edit
        resource="cases"
        title="Reset Payment"
        saveButtonProps={{ disabled: saveButtonProps.disabled, onClick: handleSubmit(onSubmit) }}
        isLoading={false}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={onClose} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <FormProvider {...formProps}>
          {!isRecurring && (
            <>
              <SelectInput
                name="paymentsUseCase"
                label="Payment Use Case"
                options={[
                  { id: 'CLAIM_FUNDS', name: 'Claim Funds' },
                  { id: 'PARTNER_ISSUED', name: 'Partner Issued' },
                  { id: 'MULTI_PARTNER_ISSUED', name: 'Partner Issued - Multi Payment' },
                ]}
                value={getPaymentUseCase()}
              />
              <HiddenInput name="fulfillment.create.fundId" value={payment.fulfillment.fund.id} />
              <HiddenInput name="fulfillment.create.caseId" value={payment.fulfillment.case.id} />
              <HiddenInput
                name="fulfillment.create.approvedAmount"
                value={payment.fulfillment.approvedAmount}
              />
              <HiddenInput
                name="fulfillment.create.scheduleType"
                value={payment.fulfillment.scheduleType}
              />
            </>
          )}
          <HiddenInput name="id" value={uuidv4()} />
          <HiddenInput name="amount" value={payment.amount} />
          <HiddenInput name="payeeType" value={payment.payeeType} />
          <HiddenInput name="payeeId" value={payment.payeeId} />
          <HiddenInput
            name="status"
            value={paymentUseCase === 'CLAIM_FUNDS' || isRecurring ? 'AUTHORIZED' : 'PENDING'}
          />
          <HiddenInput name="method" value={payment.method} />
          <HiddenInput name="note" value={payment.note} />
          <HiddenInput name="mailingAddressType" value={payment.mailingAddressType} />
          <HiddenInput name="mailingAddress" value={payment.mailingAddress} />
          {isRecurring && (
            <>
              <HiddenInput name="fulfillmentId" value={payment.fulfillment.id} />
              <HiddenInput name="scheduledFor" value={jobFormProps.watch('scheduledFor')} />
            </>
          )}
        </FormProvider>
        {isRecurring && (
          <FormProvider {...jobFormProps}>
            <Stack component="form" gap={1} sx={{ mt: 2 }}>
              <SelectInput
                name="priority"
                label="Priority"
                options={[
                  { id: 'REGULAR', name: 'Regular' },
                  { id: 'IMMEDIATE', name: 'Immediate' },
                ]}
              />
              {jobFormProps.watch('priority') === 'REGULAR' && (
                <>
                  <DatePicker
                    label="Schedule For"
                    onChange={(value) => jobFormProps.setValue('scheduledFor', value)}
                    value={jobFormProps.watch('scheduledFor')}
                    disablePast
                  />
                  <Alert severity="info">
                    All scheduled payments will initiate at 15:00 UTC on their scheduled day.
                  </Alert>
                </>
              )}
              <HiddenInput name="status" value="SCHEDULED" />
              <HiddenInput name="id" value="" />
              <HiddenInput name="scheduleId" value={jobFormProps.watch('scheduleId') ?? ''} />

              {/* <HiddenInput name="scheduleId" /> */}
              {error && <Alert severity="error">{error}</Alert>}
            </Stack>
          </FormProvider>
        )}
      </Edit>
    </Drawer>
  );
}
