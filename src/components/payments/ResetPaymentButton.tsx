import WarningAmberIcon from '@mui/icons-material/WarningAmber';
import { Button, Dialog, DialogActions, DialogContent, DialogTitle } from '@mui/material';
import { useModal } from '@refinedev/core';

export default function ResetPaymentButton({ payment, openDrawer }): JSX.Element {
  // If payment is INITIATED or SUCCEEDED, show a confirmation modal first
  const isSuccessful = ['INITIATED', 'SUCCESS'].includes(payment.status);

  if (
    (payment.fulfillment.scheduleType === 'RECURRING' && isSuccessful) ||
    payment.method === 'EXTERNAL' ||
    payment.deactivatedAt ||
    ['PENDING', 'AUTHORIZED'].includes(payment.status)
  )
    return <></>;

  const { visible, close, show } = useModal();
  const onClickButton = () => {
    if (isSuccessful) show();
    else openDrawer();
  };

  const onAcknowledgeModal = () => {
    close();
    openDrawer();
  };

  return (
    <>
      <Button onClick={onClickButton} startIcon={isSuccessful && <WarningAmberIcon />}>
        Reset Payment
      </Button>
      <Dialog
        open={visible}
        onClose={close}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle>Payment Succeeded</DialogTitle>
        <DialogContent>
          This payment is marked as successful. Please be sure nothing actually went through before
          resetting.
        </DialogContent>
        <DialogActions>
          <Button onClick={onAcknowledgeModal}>Acknowledge</Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
