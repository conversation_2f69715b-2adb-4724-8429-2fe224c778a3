import { DataGrid } from '@components/data-grid/DataGrid';
import Link from '@components/navigation/Link';
import EditPaymentScheduledDateDrawer from '@components/schedules/EditPaymentScheduledDateDrawer';
import ScheduleIcon from '@mui/icons-material/Schedule';
import { IconButton } from '@mui/material';
import type { GridColDef } from '@mui/x-data-grid';
import { type BaseKey, type BaseRecord, type CrudFilters, useModal } from '@refinedev/core';
import { List, ShowButton, useDataGrid } from '@refinedev/mui';
import { displayCurrency } from '@utils/currency';
import { displayDate } from '@utils/date';
import { displayId } from '@utils/id';
import { useMemo, useState } from 'react';

interface PaymentsListProps {
  filter?: {
    applicantId?: string;
    caseId?: string;
    fulfillmentId?: string;
    fundId?: string;
    deactivatedAt?: boolean;
    partnerId?: BaseKey;
    status?: string;
    ids?: string[];
  };
  title?: string;
  userColumns?: ({ field: string; userColumnName: string; headerName: string } & GridColDef)[];
}

export default function PaymentsList({
  filter,
  title,
  userColumns = [],
}: PaymentsListProps): JSX.Element {
  // if deactivated filter is available that means we only want to view deactivated records
  const deactivatedAt = filter?.deactivatedAt || false;

  const [selectedPayments, setSelectedPayments] = useState<BaseRecord[]>([]);
  const editScheduleDateDrawer = useModal();

  const { dataGridProps } = useDataGrid({
    resource: 'payments',
    meta: {
      fields: [
        {
          nodes: [
            'id',
            'displayId',
            'amount',
            'status',
            'method',
            'payeeType',
            'payeeId',
            'initiatedAt',
            'scheduledFor',
            {
              fulfillment: [
                'id',
                'scheduleType',
                { fund: ['id', 'name', { partner: ['id', 'name'] }] },
                {
                  case: [
                    'id',
                    'displayId',
                    {
                      applicationsList: [
                        'id',
                        {
                          submitter: [
                            'id',
                            'name',
                            ...userColumns.map((column) => column.userColumnName),
                          ],
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
    sorters: {
      initial: [
        {
          field: filter?.fulfillmentId ? 'scheduledFor' : 'updatedAt',
          order: 'desc',
        },
      ],
    },
    filters: {
      defaultBehavior: 'replace',
      permanent: [
        {
          field: 'deactivatedAt',
          operator: 'null',
          // we have to use the opposite value of boolean as we are comparing the value being null or not. to view deaactivated records, the null comparison should have a false value
          value: !deactivatedAt,
        },
        filter?.applicantId && {
          field: 'fulfillment.case.applications.some.submitterId',
          operator: 'eq',
          value: filter.applicantId,
        },
        filter?.caseId && {
          field: 'fulfillment.caseId',
          operator: 'eq',
          value: filter.caseId,
        },
        filter?.fulfillmentId && {
          field: 'fulfillmentId',
          operator: 'eq',
          value: filter.fulfillmentId,
        },
        filter?.fundId && { field: 'fulfillment.fundId', operator: 'eq', value: filter.fundId },
        filter?.partnerId && {
          field: 'fulfillment.fund.partnerId',
          operator: 'eq',
          value: filter.partnerId,
        },
        filter?.status && { field: 'status', operator: 'eq', value: filter.status },
        filter?.ids?.length && { field: 'id', operator: 'in', value: filter.ids },
      ].filter(Boolean) as CrudFilters,
    },
    ...(filter && { syncWithLocation: false }),
  });
  const columns = useMemo(
    () => [
      { field: 'id', headerName: 'Id', flex: 1 },
      {
        field: 'fulfillment.fund.partner.name',
        headerName: 'Partner',
        flex: 1,
        valueGetter: (params) => params.row.fulfillment?.fund?.partner,
        renderCell: ({ value }) => <Link to={`/partners/show/${value?.id}`}>{value?.name}</Link>,
        valueFormatter: ({ value }) => value?.name,
        sortable: false,
      },
      {
        field: 'fulfillment.fund.name',
        headerName: 'Fund',
        flex: 1,
        valueGetter: (params) => params.row.fulfillment?.fund,
        renderCell: ({ value }) => <Link to={`/funds/show/${value?.id}`}>{value?.name}</Link>,
        valueFormatter: ({ value }) => value?.name,
        sortable: false,
      },
      {
        field: 'fulfillment.case.applications.some.submitter.name',
        headerName: 'Applicant',
        flex: 1,
        valueGetter: (params) => params.row.fulfillment?.case?.applicationsList?.[0]?.submitter,
        renderCell: ({ value }) => (
          <Link to={`/users/coreUsers/show/${value?.id}`}>{value?.name}</Link>
        ),
        valueFormatter: ({ value }) => value?.name,
        sortable: false,
      },
      {
        field: 'fulfillment.case.displayId',
        headerName: 'Case Id',
        flex: 1,
        valueGetter: (params) => displayId('C', params.row.fulfillment?.case?.displayId),
        valueFormatter: ({ value }) => value?.name,
        sortable: false,
      },
      ...userColumns,
      { field: 'payeeType', headerName: 'Payee Type', flex: 1 },
      {
        field: 'amount',
        headerName: 'Amount',
        flex: 1,
        renderCell: ({ value }) => displayCurrency(value),
      },
      { field: 'status', headerName: 'Status', flex: 1 },
      {
        field: filter?.fulfillmentId ? 'scheduledFor' : 'initiatedAt',
        headerName: 'Payment Date',
        flex: 1,
        renderCell: ({ value }) => displayDate(value),
      },
      {
        field: 'actions',
        headerName: 'Actions',
        sortable: false,
        renderCell: ({ row }) => (
          <>
            <ShowButton hideText resource="payments" recordItemId={row.id} />
            {filter?.fulfillmentId && (
              <IconButton
                onClick={() => {
                  setSelectedPayments([row]);
                  editScheduleDateDrawer.show();
                }}
                disabled={row.status !== 'AUTHORIZED' || !!row.initiatedAt}
                color="primary"
              >
                <ScheduleIcon fontSize="small" />
              </IconButton>
            )}
          </>
        ),
        align: 'center',
        headerAlign: 'center',
      },
    ],
    [filter?.fulfillmentId, editScheduleDateDrawer.show, userColumns],
  );

  const getPaymentsTitle = () => {
    if (title) {
      return title;
    }

    if (filter?.fulfillmentId) {
      return 'Payment Schedule';
    }

    return filter?.deactivatedAt ? 'Reset Payments' : 'Payments';
  };
  return (
    <>
      <List resource="payments" title={getPaymentsTitle()} {...(filter && { breadcrumb: false })}>
        <DataGrid {...dataGridProps} columns={columns} />
      </List>
      {selectedPayments && (
        <EditPaymentScheduledDateDrawer {...editScheduleDateDrawer} payments={selectedPayments} />
      )}
    </>
  );
}
