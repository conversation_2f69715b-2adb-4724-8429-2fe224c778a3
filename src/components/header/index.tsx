import { DarkModeOutlined, LightModeOutlined, RateReviewOutlined } from '@mui/icons-material';
import { AppBar, Avatar, IconButton, Stack, Toolbar, Tooltip, Typography } from '@mui/material';
import { useGetIdentity } from '@refinedev/core';
import { HamburgerMenu, type RefineThemedLayoutV2HeaderProps } from '@refinedev/mui';
import { getEnvironment } from '@utils/env';
import type React from 'react';
import { useContext } from 'react';
import { ColorModeContext } from '../../theme/ColorModeContextProvider';

interface IUser {
  name: string;
  avatar: string;
}

export const Header: React.FC<RefineThemedLayoutV2HeaderProps> = () => {
  const env = getEnvironment();
  const { mode, setMode } = useContext(ColorModeContext);
  const { data: user } = useGetIdentity<IUser>();

  return (
    <AppBar position="sticky">
      <Toolbar>
        <Stack direction="row" width="100%" alignItems="center">
          <HamburgerMenu />
          <Stack
            direction="row"
            width="100%"
            justifyContent="flex-end"
            alignItems="center"
            gap="16px"
          >
            <Tooltip title={`You are in the ${env} environment`}>
              <Typography variant="subtitle2">{env}</Typography>
            </Tooltip>
            <Tooltip title="Submit ideas or feedback to product board">
              <IconButton
                color="inherit"
                href="https://portal.productboard.com/vawjaqjvs7ryniwewnm5tdso"
                target={'_blank'}
              >
                <RateReviewOutlined />
              </IconButton>
            </Tooltip>
            <Tooltip title={`Swap to ${mode === 'dark' ? 'light' : 'dark'} mode`}>
              <IconButton
                color="inherit"
                onClick={() => {
                  setMode();
                }}
              >
                {mode === 'dark' ? <LightModeOutlined /> : <DarkModeOutlined />}
              </IconButton>
            </Tooltip>
            {(user?.avatar || user?.name) && (
              <Stack direction="row" gap="16px" alignItems="center" justifyContent="center">
                {user?.name && <Typography variant="subtitle2">{user?.name}</Typography>}
                <Avatar src={user?.avatar} alt={user?.name} />
              </Stack>
            )}
          </Stack>
        </Stack>
      </Toolbar>
    </AppBar>
  );
};
