import { CloseOutlined } from '@mui/icons-material';
import {
  Alert,
  Autocomplete,
  Box,
  Drawer,
  IconButton,
  Switch,
  TextField,
  Tooltip,
} from '@mui/material';
import type { Features, PartnerFeatures, ProgramFeatures } from '@prisma/clients/platform';
import { Create, useAutocomplete } from '@refinedev/mui';
import type { UseModalFormReturnType } from '@refinedev/react-hook-form';
import type { Feature } from 'hooks/features/types';
import useFeatures from 'hooks/features/useFeatures';
import { useMemo, useState } from 'react';
import { Controller } from 'react-hook-form';
import { FeatureDetails } from './FeatureDetails';

type AddFeatureDrawerProps = UseModalFormReturnType & {
  entity: { id: string; resource: string };
  currentFeatures: ((PartnerFeatures | ProgramFeatures) & { feature: Features })[];
};

export const AddFeatureDrawer = (props: AddFeatureDrawerProps) => {
  const {
    saveButtonProps,
    currentFeatures,
    modal: { visible, close },
    register,
    setValue,
    formState: { errors },
    entity,
    control,
  } = props;

  const currentFeatureIds = currentFeatures?.map(({ featureId }) => featureId);

  const { autocompleteProps: featuresAutoCompleteProps } = useAutocomplete({
    resource: 'features',
    sorters: [{ field: 'name', order: 'asc' }],
    meta: {
      fields: [{ nodes: ['id', 'name', 'description'] }],
    },
    pagination: {
      mode: 'off',
    },
    onSearch: (value) => [
      {
        field: 'name',
        operator: 'contains',
        value,
      },
    ],
  });

  const { lookupDeps } = useFeatures();
  const options = useMemo(
    () =>
      featuresAutoCompleteProps.options.map((option) => ({
        ...option,
        deps: lookupDeps(option.name),
      })),
    [featuresAutoCompleteProps, lookupDeps],
  );

  setValue(`${entity.resource}Id`, entity.id);

  const [selected, setSelected] = useState<Feature>();

  return (
    <Drawer
      open={visible}
      onClose={close}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Create
        resource={`${entity}Features`}
        title="Add Feature"
        saveButtonProps={saveButtonProps}
        isLoading={false}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <Box component="form">
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'row',
              flexFlow: 'row',
              alignItems: 'center',
              paddingY: '1rem',
            }}
          >
            <Tooltip title="Enabled">
              <Switch {...register('enabled')} sx={{ marginRight: '1rem' }} />
            </Tooltip>
            <Controller
              control={control}
              name="featureId"
              rules={{ required: 'This field is required' }}
              defaultValue={null}
              render={({ field }) => (
                <Autocomplete
                  {...featuresAutoCompleteProps}
                  {...field}
                  options={options}
                  onChange={(_, value) => {
                    field.onChange(value?.id ?? value);
                    setSelected(value);
                  }}
                  getOptionLabel={(item) => {
                    return (
                      featuresAutoCompleteProps?.options?.find(
                        (p) => p?.id?.toString() === (item?.id ?? item)?.toString(),
                      )?.name ?? ''
                    );
                  }}
                  getOptionDisabled={(option) => currentFeatureIds.includes(option.id)}
                  isOptionEqualToValue={(option, value) =>
                    value === undefined ||
                    option?.id?.toString() === (value?.id ?? value)?.toString()
                  }
                  sx={{ width: '100%' }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Feature"
                      margin="normal"
                      variant="outlined"
                      error={!!errors?.featureId}
                      helperText={errors?.featureId?.message as string}
                      required
                    />
                  )}
                />
              )}
            />
          </Box>
          {selected && (
            <FeatureDetails
              feature={selected}
              enabledFeatures={currentFeatures?.filter(({ enabled }) => enabled)}
              enabled={true}
            />
          )}
        </Box>
      </Create>
    </Drawer>
  );
};
