import { Box, Typography } from '@mui/material';

function DependencyList({ title, deps }: { title: string; deps: string[] }) {
  return (
    <Box>
      <Typography variant="body2">
        <strong>{title}</strong>
      </Typography>
      <Box sx={{ paddingLeft: '1rem' }}>
        {deps.map((item, idx) => (
          <Typography
            key={`title-${
              // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
              idx
            }`}
            variant="caption"
          >
            {item}
          </Typography>
        ))}
      </Box>
    </Box>
  );
}

export { DependencyList };
