import { Alert, Box, Typography } from '@mui/material';
import type { Features, PartnerFeatures, ProgramFeatures } from '@prisma/clients/platform';
import type { Feature } from 'hooks/features/types';
import useFeatures from 'hooks/features/useFeatures';
import { useMemo } from 'react';
import { Conflict } from './Conflict';
import { DependencyList } from './DependencyList';
import { PartnerLevelFeatures } from './PartnerLevelFeatures';

interface FeatureDetailsProps {
  feature: Feature;
  enabledFeatures?: ((PartnerFeatures | ProgramFeatures) & { feature: Features })[];
  enabled?: boolean;
}

function FeatureDetails({ feature, enabledFeatures, enabled }: FeatureDetailsProps) {
  const { getRelatedFeatures, getConflicts } = useFeatures();
  const [description, dependencies, conflicts] =
    useMemo(() => {
      if (feature) {
        const dependencies = getRelatedFeatures(feature.deps);
        const { description } = feature;
        const conflicts = getConflicts(
          enabledFeatures?.map(({ feature }) => feature.name) ?? [],
          feature.deps,
        );
        return [description, dependencies, conflicts];
      }
    }, [enabledFeatures, feature, getConflicts, getRelatedFeatures]) ?? [];

  const proceedAtYourOwnRisk =
    'Editing the feature set of a live partner/program carries some risk! \
    Please ensure that you understand the features being changed and the desired outcome. \
    Make sure to disable any conflicting features before enabling new ones.';

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
      {description && (
        <Box>
          <Typography variant="body1">
            <strong>Feature Description</strong>
          </Typography>
          <Typography variant="body2">{description}</Typography>
        </Box>
      )}
      {dependencies?.requires && <DependencyList title="Requires" deps={dependencies?.requires} />}
      {dependencies?.prohibits && (
        <DependencyList title="Prohibits" deps={dependencies?.prohibits} />
      )}
      <Alert severity="warning">{proceedAtYourOwnRisk}</Alert>
      {enabled && conflicts?.toDisable && conflicts.toDisable.length > 0 && (
        <Conflict name={feature.name} conflicts={conflicts.toDisable} disable />
      )}
      {enabled && conflicts?.toEnable && conflicts.toEnable.length > 0 && (
        <Conflict name={feature.name} conflicts={conflicts.toEnable} />
      )}
      <PartnerLevelFeatures feature={feature} />
    </Box>
  );
}

export { FeatureDetails };
