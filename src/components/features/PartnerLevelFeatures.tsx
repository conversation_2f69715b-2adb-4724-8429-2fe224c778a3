import { Alert, List, ListItem } from '@mui/material';

const PARTNER_FEATURE_NAMES = ['Documents: Classify', 'Analytics: Embed', 'Release: Identity'];

function PartnerLevelFeatures({ feature }) {
  if (PARTNER_FEATURE_NAMES.includes(feature?.name))
    return (
      <Alert severity="info">
        Please note that this feature should be enabled at the partner level, not on individual
        programs:
        <List>
          <ListItem>{feature.name}</ListItem>
        </List>
      </Alert>
    );

  return null;
}

export { PartnerLevelFeatures };
