import { Alert, AlertTitle, Box, List, ListItemText } from '@mui/material';

interface ConflictProps {
  name: string;
  conflicts?: string[];
  disable?: boolean;
}

function Conflict({ name, conflicts, disable = false }: ConflictProps) {
  return (
    <Alert severity="error">
      <AlertTitle>Conflict</AlertTitle>
      <Box>
        <span>
          {name} will not function properly with the current feature set. Please{' '}
          {disable ? 'disable' : 'enable one of'} the following features first:
        </span>
        <List dense>
          {conflicts?.map((item, idx) => (
            <ListItemText
              key={`${item}-${
                // biome-ignore lint/suspicious/noArrayIndexKey: <explanation>
                idx
              }`}
            >
              - {item}
            </ListItemText>
          ))}
        </List>
      </Box>
    </Alert>
  );
}

export { Conflict };
