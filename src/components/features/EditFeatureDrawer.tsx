import { CloseOutlined } from '@mui/icons-material';
import { Box, Drawer, IconButton, Switch, Tooltip, Typography } from '@mui/material';
import type { Features, PartnerFeatures, ProgramFeatures } from '@prisma/clients/platform';
import { Edit } from '@refinedev/mui';
import type { UseModalFormReturnType } from '@refinedev/react-hook-form';
import useFeatures from 'hooks/features/useFeatures';
import { type SyntheticEvent, useEffect, useState } from 'react';
import { FeatureDetails } from './FeatureDetails';

type EditFeatureDrawerProps = UseModalFormReturnType & {
  resource: string;
  currentFeatures: ((ProgramFeatures | PartnerFeatures) & { feature: Features })[];
};

export const EditFeatureDrawer = ({
  currentFeatures,
  modal: { visible, close },
  refineCore: { id, queryResult },
  resource,
  saveButtonProps,
  setValue,
}: EditFeatureDrawerProps) => {
  const { lookupDeps } = useFeatures();
  const { feature, enabled: prevEnabled } = queryResult?.data?.data ?? {};
  if (feature) feature.deps = lookupDeps(feature?.name);

  const [enabled, setEnabled] = useState(!prevEnabled);
  useEffect(() => {
    if (id) setEnabled(!prevEnabled);
  }, [id, prevEnabled]);

  if (!id) return null;
  return (
    <Drawer
      open={visible}
      onClose={close}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Edit
        resource={resource}
        title="Toggle Feature"
        saveButtonProps={{
          ...saveButtonProps,
          onClick: (e: SyntheticEvent) => {
            setValue('enabled', enabled);
            saveButtonProps.onClick(e);
          },
        }}
        isLoading={queryResult?.isLoading}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <Box component="form">
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'row',
              flexFlow: 'row',
              alignItems: 'center',
              paddingY: '1rem',
            }}
          >
            <Tooltip title="Enabled">
              <Switch
                onClick={() => setEnabled(!enabled)}
                checked={enabled}
                sx={{ marginRight: '1rem' }}
              />
            </Tooltip>
            <Typography>{feature?.name}</Typography>
          </Box>
          <FeatureDetails
            feature={feature}
            enabledFeatures={currentFeatures?.filter(({ enabled }) => enabled)}
            enabled={enabled}
          />
        </Box>
      </Edit>
    </Drawer>
  );
};
