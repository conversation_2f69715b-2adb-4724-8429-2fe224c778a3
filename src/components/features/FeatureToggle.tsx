import { <PERSON>I<PERSON>, <PERSON><PERSON>temAvatar, <PERSON><PERSON>temText, Switch, Tooltip } from '@mui/material';

function FeatureToggle({
  featureSetting,
  enabled,
  handleToggle,
}: {
  featureSetting: { feature: { name: string; description: string } };
  enabled: boolean;
  handleToggle: () => void;
}) {
  const { feature } = featureSetting;

  return (
    <Tooltip title={feature.description} placement="right">
      <ListItem sx={{ width: 'fit-content' }}>
        <ListItemAvatar>
          <Switch checked={enabled} onChange={handleToggle} />
        </ListItemAvatar>
        <ListItemText sx={{ whiteSpace: 'pre-line' }} primary={feature.name} />
      </ListItem>
    </Tooltip>
  );
}

export default FeatureToggle;
