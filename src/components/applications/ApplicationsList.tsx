import { DataGrid } from '@components/data-grid/DataGrid';
import Link from '@components/navigation/Link';
import type { CrudFilters } from '@refinedev/core';
import { List, useDataGrid } from '@refinedev/mui';
import { displayDate } from '@utils/date';
import { formatDisplayId } from '@utils/displayId';
import { getApplicantType } from '@utils/multiparty';
import { useMemo } from 'react';

interface ApplicationsListProps {
  filter?: {
    caseId?: string;
  };
}

export default function ApplicationsList({ filter }: ApplicationsListProps): JSX.Element {
  const { dataGridProps } = useDataGrid({
    resource: 'applications',
    meta: {
      fields: [
        {
          nodes: [
            'id',
            'submittedAt',
            'displayId',
            {
              submitter: [
                'id',
                'name',
                { applicantProfile: ['id', { applicantType: ['id', 'name'] }] },
              ],
            },
            {
              case: [
                'id',
                {
                  program: [
                    'id',
                    'name',
                    'config',
                    { programApplicantTypesList: ['id', 'applicantTypeId', 'nameOverride'] },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
    filters: {
      defaultBehavior: 'replace',
      permanent: [
        { field: 'deactivatedAt', operator: 'null', value: true },
        filter?.caseId && {
          field: 'caseId',
          operator: 'eq',
          value: filter.caseId,
        },
      ].filter(Boolean) as CrudFilters,
    },
    ...(filter && { syncWithLocation: false }),
  });

  const columns = useMemo(
    () => [
      { field: 'id', headerName: 'Id', flex: 1 },
      {
        field: 'displayId',
        headerName: 'Display Id',
        flex: 1,
        sortable: true,
        renderCell: ({ value }) => formatDisplayId(value, 'A'),
      },
      {
        field: 'submitter.name',
        headerName: 'Submitter',
        flex: 1,
        valueGetter: (params) => params.row.submitter,
        renderCell: ({ value }) => (
          <Link to={`/users/coreUsers/show/${value?.id}`}>{value?.name}</Link>
        ),
        sortable: false,
      },
      {
        field: 'submitter.applicantType.name',
        headerName: 'Submitter Type',
        flex: 1,
        renderCell: ({ row }) => getApplicantType(row, row.case.program),
        sortable: false,
      },
      {
        field: 'createdAt',
        headerName: 'Started At',
        flex: 1,
        renderCell: ({ value }) => displayDate(value),
      },
      {
        field: 'submittedAt',
        headerName: 'Submitted At',
        flex: 1,
        renderCell: ({ value }) => displayDate(value),
      },
    ],
    [],
  );

  return (
    <>
      <List resource="payments" title="applications" {...(filter && { breadcrumb: false })}>
        <DataGrid {...dataGridProps} columns={columns} />
      </List>
    </>
  );
}
