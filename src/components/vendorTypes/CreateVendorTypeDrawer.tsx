import HiddenInput from '@components/forms/HiddenInput';
import TextInput from '@components/forms/TextInput';
import { CloseOutlined } from '@mui/icons-material';
import { Drawer, IconButton, Stack, Typography } from '@mui/material';
import { useCreate, useOne } from '@refinedev/core';
import { Create } from '@refinedev/mui';
import { FormProvider } from 'react-hook-form';

export const CreateVendorTypeDrawer = (props) => {
  const {
    saveButtonProps,
    modal: { visible, close },
    partnerId,
    handleSubmit,
  } = props;
  const { data } = useOne({
    resource: 'partners',
    id: partnerId,
    meta: { fields: [{ childPartners: [{ nodes: ['id', 'name'] }] }] },
  });
  const hasChildPartners = data?.data.childPartners?.length > 0;

  const { mutate } = useCreate();

  const onSubmit = (formData, e) => {
    if (hasChildPartners) {
      data?.data.childPartners.map((child) => {
        mutate({
          resource: 'vendor_types',
          values: {
            name: formData.name,
            partnerId: child.id,
          },
        });
      });
    } else {
      mutate({
        resource: 'vendor_types',
        values: {
          name: formData.name,
          partnerId: partnerId,
        },
      });
    }
  };

  return (
    <Drawer
      open={visible}
      onClose={close}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Create
        resource="vendor_types"
        saveButtonProps={{ onClick: handleSubmit(onSubmit) }}
        isLoading={false}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <FormProvider {...props}>
          <Stack component="form" direction="column" gap={1}>
            <TextInput name="name" label="Name" required />
            {hasChildPartners && (
              <Typography>Note: VendorType will be added to all the child partners. </Typography>
            )}
          </Stack>
        </FormProvider>
      </Create>
    </Drawer>
  );
};
