import { List, useDataGrid } from '@refinedev/mui';
import { useModalForm } from '@refinedev/react-hook-form';
import { DataGrid } from '../data-grid/DataGrid';
import { CreateVendorTypeDrawer } from './CreateVendorTypeDrawer';

export const PartnerVendorTypeList = ({ partnerId }) => {
  const { dataGridProps } = useDataGrid({
    resource: 'vendor_types',
    meta: {
      fields: [
        {
          nodes: ['id', 'name'],
        },
      ],
    },
    filters: {
      defaultBehavior: 'replace',
      permanent: [{ field: 'partnerId', operator: 'eq', value: partnerId }],
    },
    syncWithLocation: false,
  });

  const createDrawer = useModalForm({
    syncWithLocation: false,
    refineCoreProps: { resource: 'vendor_types', action: 'create', redirect: false },
  });

  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      minWidth: 150,
    },
    {
      field: 'name',
      headerName: 'Name',
      minWidth: 150,
    },
  ];

  return (
    <>
      <List
        resource={'vendor_types'}
        breadcrumb={false}
        createButtonProps={{
          onClick: () => createDrawer.modal.show(),
        }}
      >
        <DataGrid {...dataGridProps} columns={columns} autoHeight syncWithLocation={false} />
      </List>
      <CreateVendorTypeDrawer {...createDrawer} partnerId={partnerId} />
    </>
  );
};
