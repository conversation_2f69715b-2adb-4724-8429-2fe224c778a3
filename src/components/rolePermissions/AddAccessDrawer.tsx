import { CloseOutlined } from '@mui/icons-material';
import {
  Alert,
  Autocomplete,
  Box,
  Drawer,
  FormControl,
  FormHelperText,
  IconButton,
  TextField,
} from '@mui/material';
import { useRefineContext } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import type { UseModalFormReturnType } from '@refinedev/react-hook-form';
import React, { useState } from 'react';
import { CustomOperations, Operations, Resources } from 'types/rolePermission';

export default function AddAccessDrawer({
  modal: { visible, close },
  refineCore: { id, queryResult },
  saveButtonProps,
  setValue,
}: UseModalFormReturnType) {
  const { access } = queryResult?.data?.data ?? {};
  const {
    options: { textTransformers },
  } = useRefineContext();
  const [selectedResource, setSelectedResource] = useState<Resources>();
  const [selectedOperations, setSelectedOperations] = useState<Array<Operations>>();
  const [error, setError] = useState<string>();

  if (!id) return null;

  const onClose = () => {
    setSelectedOperations(undefined);
    setSelectedResource(undefined);
    setError(undefined);
    close();
  };

  const getOptions = (resource?: Resources) => {
    const options = Object.values(Operations);
    const customOperations = resource ? CustomOperations?.[resource] : [];
    if (customOperations?.length) options.push(...customOperations);
    return options;
  };

  return (
    <Drawer
      open={visible}
      onClose={onClose}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Edit
        resource={Resources.RolePermissions}
        title="Toggle Access"
        saveButtonProps={{
          ...saveButtonProps,
          onClick: (e) => {
            if (!selectedOperations?.length || !selectedResource) {
              setError('Resource/Operations is required.');
              return;
            }
            const tempAccess = access ?? {};
            if (selectedOperations?.length && selectedResource) {
              tempAccess[selectedResource] = selectedOperations;
            }
            setValue('access', tempAccess);
            saveButtonProps.onClick(e);
            onClose();
          },
        }}
        isLoading={queryResult?.isLoading}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={onClose} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            flexFlow: 'column',
            paddingY: '1rem',
          }}
        >
          <FormControl>
            <Autocomplete
              value={selectedResource}
              options={Object.values(Resources)}
              onChange={(_, value) => setSelectedResource(value as Resources)}
              getOptionLabel={(item: string) => {
                return textTransformers.humanize(item);
              }}
              getOptionDisabled={(item: string) => !!access?.[item]}
              sx={{ width: '100%' }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Resource"
                  margin="normal"
                  variant="outlined"
                  required
                />
              )}
            />
          </FormControl>
          <FormControl>
            <Autocomplete
              value={selectedOperations ?? []}
              options={getOptions(selectedResource)}
              onChange={(_, value) => setSelectedOperations(value as Array<Operations>)}
              multiple
              getOptionLabel={(item: string) => {
                return textTransformers.humanize(item);
              }}
              sx={{ width: '100%' }}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Operations"
                  margin="normal"
                  variant="outlined"
                  required
                />
              )}
            />
          </FormControl>
        </Box>
        <Alert severity="warning">Adding access of a resource carries some risk!</Alert>
        {!!error && <FormHelperText error>{error}</FormHelperText>}
      </Edit>
    </Drawer>
  );
}
