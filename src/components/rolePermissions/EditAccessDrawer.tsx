import { CloseOutlined } from '@mui/icons-material';
import { <PERSON><PERSON>, Box, Drawer, IconButton, Switch, Tooltip, Typography } from '@mui/material';
import { useRefineContext } from '@refinedev/core';
import { Edit } from '@refinedev/mui';
import type { UseModalFormReturnType } from '@refinedev/react-hook-form';
import React, { useEffect, useState } from 'react';
import { Resources } from 'types/rolePermission';

type EditAccessDrawerProps = UseModalFormReturnType & {
  resource: string;
  operation: string;
};

export default function EditAccessDrawer({
  modal: { visible, close },
  refineCore: { id, queryResult },
  resource,
  operation,
  saveButtonProps,
  setValue,
}: EditAccessDrawerProps) {
  const { access } = queryResult?.data?.data ?? {};
  const [enabled, setEnabled] = useState<boolean>(false);
  const {
    options: { textTransformers },
  } = useRefineContext();

  useEffect(() => {
    if (id) setEnabled(access?.[resource]?.includes(operation));
  }, [id, operation, resource, access]);

  if (!id) return null;
  return (
    <Drawer
      open={visible}
      onClose={close}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Edit
        resource={Resources.RolePermissions}
        title="Toggle Access"
        saveButtonProps={{
          ...saveButtonProps,
          onClick: (e) => {
            const tempAccess = { ...access };
            const operations = tempAccess[resource];
            if (enabled) tempAccess[resource] = [...operations, operation];
            else {
              const tempOperations = operations.filter((grant: string) => grant !== operation);
              if (tempOperations?.length) tempAccess[resource] = tempOperations;
              else delete tempAccess[resource];
            }
            setValue('access', tempAccess);
            saveButtonProps.onClick(e);
          },
        }}
        isLoading={queryResult?.isLoading}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            flexFlow: 'row',
            alignItems: 'center',
            paddingY: '1rem',
          }}
        >
          <Tooltip title="Enabled">
            <Switch
              onClick={() => setEnabled(!enabled)}
              checked={enabled}
              sx={{ marginRight: '1rem' }}
            />
          </Tooltip>
          <Typography>
            {textTransformers.humanize(resource)}: {textTransformers.humanize(operation)}
          </Typography>
        </Box>
        <Alert severity="warning">Editing access of a resource carries some risk!</Alert>
      </Edit>
    </Drawer>
  );
}
