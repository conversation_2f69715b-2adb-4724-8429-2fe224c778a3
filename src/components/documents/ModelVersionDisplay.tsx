import { Box, List, ListItem, ListItemText, ListSubheader, Typography } from '@mui/material';
import dayjs from '@utils/dayJsConfig';
import type { ModelVersion } from './ModelSelection';

export default function ModelVersionDisplay({
  modelVersion,
  grid = false,
}: { modelVersion?: ModelVersion; grid?: boolean }) {
  if (!modelVersion) return null;
  return (
    <Box>
      <ListSubheader>
        <Typography variant="h6">Selected Model Version</Typography>
      </ListSubheader>
      <List
        sx={{
          width: '100%',
          bgcolor: 'background.paper',
          ...(grid
            ? { display: 'grid', gridTemplateColumns: '1fr 1fr' }
            : { display: 'flex', flexWrap: 'wrap' }),
        }}
      >
        <ListItem>
          <ListItemText primary={modelVersion.id} secondary="ID" />
        </ListItem>
        <ListItem>
          <ListItemText primary={modelVersion.type} secondary="Type" />
        </ListItem>
        <ListItem>
          <ListItemText primary={modelVersion.name} secondary="Name" />
        </ListItem>
        <ListItem>
          <ListItemText primary={modelVersion.description} secondary="Description" />
        </ListItem>
        <ListItem>
          <ListItemText
            primary={dayjs(modelVersion.createdAt).format('YYYY-MM-DD HH:mm:ss a')}
            secondary="Created At"
          />
        </ListItem>
      </List>
    </Box>
  );
}
