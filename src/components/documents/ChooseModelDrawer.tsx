import { CloseOutlined } from '@mui/icons-material';
import { Autocomplete, Box, Drawer, IconButton, TextField } from '@mui/material';
import { Edit, useAutocomplete } from '@refinedev/mui';
import type { UseModalFormReturnType } from '@refinedev/react-hook-form';
import { type BaseSyntheticEvent, useState } from 'react';
import { Controller } from 'react-hook-form';
import type { ModelVersion } from './ModelSelection';
import ModelVersionDisplay from './ModelVersionDisplay';

export default function ChooseModelDrawer({
  control,
  currentModel,
  formState: { errors, isDirty },
  modal: { visible, close },
  refineCore: { id, setId, queryResult },
  program,
  saveButtonProps,
  setValue,
  unregister,
}: UseModalFormReturnType & { program; currentModel?: ModelVersion }) {
  const { autocompleteProps } = useAutocomplete({
    resource: 'modelVersions',
    dataProviderName: 'document',
    sorters: [{ field: 'name', order: 'asc' }],
    meta: {
      fields: [{ nodes: ['id', 'type', 'name', 'description', 'createdAt', 'deactivatedAt'] }],
    },
    filters: [
      { field: 'deactivatedAt', operator: 'null', value: true },
      { field: 'type', operator: 'eq', value: 'beam-doc-classifier' },
    ],
    pagination: { mode: 'off' },
    onSearch: (value) => [
      {
        field: 'name',
        operator: 'contains',
        value,
      },
    ],
  });

  const [selected, setSelected] = useState(currentModel);

  const onSave = (e) => {
    unregister('modelId');
    setId(program.id);
    setValue('id', program.id);
    setValue('config', { ...program?.config, documents: { modelId: selected?.id } });
    saveButtonProps.onClick(e);
  };

  return (
    <Drawer
      open={visible}
      onClose={close}
      anchor="right"
      PaperProps={{ sx: { width: { sm: '100%', md: 500 } } }}
    >
      <Edit
        title="Choose New Model Version"
        saveButtonProps={{ ...saveButtonProps, onClick: onSave }}
        isLoading={queryResult?.isLoading}
        breadcrumb={false}
        headerProps={{
          action: (
            <IconButton onClick={() => close()} sx={{ width: '30px', height: '30px' }}>
              <CloseOutlined />
            </IconButton>
          ),
          avatar: null,
        }}
      >
        <Box component="form">
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'row',
              flexFlow: 'row',
              alignItems: 'center',
              paddingY: '1rem',
            }}
          >
            <Controller
              name="modelId"
              control={control}
              rules={{ required: 'This field is required' }}
              defaultValue={id}
              render={({ field }) => (
                <Autocomplete
                  {...autocompleteProps}
                  {...field}
                  onChange={(_, value) => {
                    field.onChange(value?.id ?? value);
                    setSelected(value);
                  }}
                  getOptionLabel={(item) => {
                    return (
                      autocompleteProps?.options?.find(
                        (p) => p?.id?.toString() === (item?.id ?? item)?.toString(),
                      )?.name ?? ''
                    );
                  }}
                  isOptionEqualToValue={(option, value) =>
                    value === undefined ||
                    option?.id?.toString() === (value?.id ?? value)?.toString()
                  }
                  sx={{ width: '100%' }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Model Version"
                      margin="normal"
                      variant="outlined"
                      error={!!errors?.modelId}
                      helperText={errors?.modelId?.message as string}
                      required
                    />
                  )}
                />
              )}
            />
          </Box>
          {selected && isDirty && <ModelVersionDisplay modelVersion={selected} />}
        </Box>
      </Edit>
    </Drawer>
  );
}
