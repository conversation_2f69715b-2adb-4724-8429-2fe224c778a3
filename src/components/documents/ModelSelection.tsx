import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CircularProgress, Typography } from '@mui/material';
import { useSelect } from '@refinedev/core';
import { useModalForm } from '@refinedev/react-hook-form';
import { useMemo } from 'react';
import ChooseModelDrawer from './ChooseModelDrawer';
import ModelVersionDisplay from './ModelVersionDisplay';

export interface ModelVersion {
  id: string;
  type: string;
  name: string;
  description: string;
  createdAt: Date;
  deactivatedAt?: Date;
}

export default function ModelSelection({ program, refetch, isLoading: programLoading }) {
  const {
    queryResult: { isLoading, data },
  } = useSelect<ModelVersion>({
    resource: 'modelVersions',
    dataProviderName: 'document',
    meta: {
      fields: [{ nodes: ['id', 'type', 'name', 'description', 'createdAt', 'deactivatedAt'] }],
    },
    filters: [
      { field: 'deactivatedAt', operator: 'null', value: true },
      { field: 'type', operator: 'eq', value: 'beam-doc-classifier' },
    ],
  });

  function getModelFromProgramConfig(program) {
    return data?.data.find((model) => model.id === program?.config?.documents?.modelId);
  }
  // biome-ignore lint/correctness/useExhaustiveDependencies: we want to change on model options also
  const currentModel = useMemo(() => getModelFromProgramConfig(program), [program, data]);

  const selectModalProps = useModalForm({
    syncWithLocation: false,
    refineCoreProps: {
      resource: 'program',
      meta: { fields: ['id', 'config'] },
      action: 'edit',
      redirect: false,
      onMutationSuccess: refetch,
    },
  });

  return (
    <>
      <ChooseModelDrawer {...selectModalProps} program={program} currentModel={currentModel} />
      <Box boxShadow={1} sx={{ padding: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
        <CardHeader
          title={<Typography variant="h5">Doctopus</Typography>}
          action={
            <Button
              variant="contained"
              onClick={() => {
                selectModalProps.modal.show(program.id);
              }}
            >
              Edit
            </Button>
          }
        />
        {isLoading || programLoading ? (
          <CircularProgress />
        ) : (
          <ModelVersionDisplay modelVersion={currentModel} grid />
        )}
      </Box>
    </>
  );
}
