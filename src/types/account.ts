import type { PaymentAddress, Provider } from './payment';

export enum AccountType {
  Checking = 'checking',
  Savings = 'savings',
}

export interface ACHAccount {
  accountNumber: string;
  routingNumber: string;
  accountType: AccountType;
}

export interface ZelleAccount {
  email: string;
}

export interface CreateACHAccount {
  accountType: 'ach';
  accountInfo: ACHAccount;
}

export interface CreateZelleAccount {
  accountType: 'zelle';
  accountInfo: ZelleAccount;
}

export interface CreateBaseAccount {
  referenceId: string;
  recipientReferenceId: string;
}

export type CreateUSIOAccount = CreateBaseAccount & {
  provider: Provider.USIO;
  payer: string;
  address: PaymentAddress;
  email: string;
  firstName: string;
  lastName: string;
  account: { accountType: 'physicalCard' | 'virtualCard' };
};

export type CreateJPMCAccount = CreateBaseAccount & {
  provider: Provider.JPMC;
  account: CreateACHAccount | CreateZelleAccount;
};

export type CreateAccount = CreateJPMCAccount | CreateUSIOAccount;

export interface AccountResponse {
  recipientId: string;
  referenceId: string;
  type: string;
}
