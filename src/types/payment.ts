import type {
  Fulfillments as Fulfillment,
  Funds as Fund,
  Partners as Partner,
  Payments as Payment,
  PaymentPatterns as PaymentPattern,
} from '@prisma/clients/platform';
import type { Payee } from './payee';

export type PopulatedPayment = Payment & {
  payee: Payee;
  fulfillment: Fulfillment & { fund: PopulatedFund; paymentPattern?: PaymentPattern };
};
export type PopulatedFund = Fund & { partner: Partner };

export enum Provider {
  JPMC = 'jpmc',
  USIO = 'usio',
}

export interface PaymentAddress {
  street: string;
  unit?: string;
  city: string;
  state: string;
  zip: string;
  country?: string;
}

export type PaymentError = {
  retryable: boolean;
  errorCode?: string;
  message: string;
};

export enum PaymentServiceResponseStatus {
  Success = 'success',
  Failed = 'failed',
}

export interface FailedPaymentServiceResponse {
  status: PaymentServiceResponseStatus.Failed;
  error: PaymentError;
}

export interface SuccessfulPaymentServiceResponse<T> {
  status: PaymentServiceResponseStatus.Success;
  payload: T;
}

export type PaymentServiceResponse<T> =
  | SuccessfulPaymentServiceResponse<T>
  | FailedPaymentServiceResponse;

export interface ScheduledPayment {
  id: string;
  scheduledFor: Date;
}
