export interface Recipient {
  salesForceId: string;
  firstName: string;
  lastName: string;
  email: string;
  participantAddress: string;
  participantCity: string;
  participantState: string;
  participantZipCode: string;

  // payment info
  paymentMethod: 'ach' | 'physicalCard' | 'check';
  paymentAmount: string;
  paymentDate: string;

  // required field for ach
  accountNumber: string;
  routingNumber: string;
  accountType: 'savings' | 'checking' | '';

  // application answers
  stipendType: string;
  cohort: string;
  organization: string;
  program: string;

  // program address
  programAddress: string;
  programCity: string;
  programState: string;
  programZipCode: string;
}

export interface SchedulePaymentsRequest {
  adminId: string;
  programId: string;
  recipients: [Recipient];
  action: 'import' | 'schedule';
  withAccountCreation?: boolean;
}

export interface SchedulePaymentsResponse {
  status: string;
  errors?: { id: string; message: string }[];
}
