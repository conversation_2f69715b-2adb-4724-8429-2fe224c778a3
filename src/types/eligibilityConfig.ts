import type { DefaultRootProps } from '@measured/puck';
import type { ReactNode } from 'react';

export type RootProps = {
  children: ReactNode;
} & DefaultRootProps;

type TitleProps = {
  title?: string;
};
type CopyProps = {
  copy?: string;
};
type MessageProps = {
  eligible?: string;
  ineligible?: string;
};
type TextInputProps = {
  label?: string;
  copy?: string;
  answer?: string;
};
type ContentProps = {
  content?: string;
};
type DropdownProps = {
  items?: { label?: string; value?: string }[];
  label?: string;
  copy?: string;
  answer?: string;
};
type YesOrNoProps = {
  answer?: string;
  copy?: string;
};
type AMIComponentProps = {
  label?: string;
  state?: string;
  county?: string[];
};
export type Props = {
  title: TitleProps;
  copy: CopyProps;
  message: MessageProps;
} & EligibilityQuestionType;

export type EligibilityQuestionType = {
  amicomponent: AMIComponentProps;
  content: ContentProps;
  dropdown: DropdownProps;
  text_input: TextInputProps;
  yes_or_no: YesOrNoProps;
};

export const eligibilityQuestionDescriptions: Record<keyof EligibilityQuestionType, string> = {
  amicomponent: 'AMI Section',
  content: 'CAP Finder Text',
  dropdown: 'Dropdown',
  text_input: 'Open Text Area',
  yes_or_no: 'Simple Yes or No',
};

export const eligibilityQuestionDBTypes: Record<keyof EligibilityQuestionType, string> = {
  amicomponent: 'AMIComponent',
  content: 'Content',
  dropdown: 'Dropdown',
  text_input: 'TextInput',
  yes_or_no: 'YesOrNo',
};
export const dropdownDefaultValues = {
  copy: 'Choose one of the options',
  items: [
    { label: 'One', value: 'one' },
    { label: 'Two', value: 'two' },
  ],
  label: 'Pick One',
  answer: 'one',
};

export const amiDefaultValues = {
  copy: 'Complete the income questions',
  label: 'Search by town or county',
  limitFilter: { state: 'PA' },
};

export const textDefaultValues = {
  copy: 'Enter your favorite food',
  label: 'e.g. cookies',
  answer: 'cookies',
};

export const radioDefaultValues = {
  copy: 'Are you eligible for this?',
  answer: 'true',
};

export const DEFAULT_MESSAGE_CONTENT = {
  eligible:
    '**You are eligible to apply!**\n\nSelect the button below to create your account. Please note that this is not a guarantee you will receive assistance.',
  ineligible:
    "**You're ineligible to apply for assistance.**\n\nBased on your answers, you probably will not qualify for {PARTNER_NAME}'s programs. If you think this assessment is wrong, review your answers and re-check your eligibility. If you have any questions, contact [{PARTNER_EMAIL}]({PARTNER_EMAIL})",
};
