import type { Addresses as Address, Funds as Fund, Users as User } from '@prisma/clients/platform';
import type { AccountType } from './account';

export interface BankAccount {
  routingNumber: string;
  accountType: AccountType;
  accountNumber: string;
}

export enum PayeeType {
  User = 'User',
}

export type Payee = User & {
  payeeType: PayeeType.User;
  mailingAddress: Address;
  bankAccount?: BankAccount;
};

export type PayeeInput = Payee & { fund: Fund };
