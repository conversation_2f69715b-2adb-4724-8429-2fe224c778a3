export enum NotificationType {
  UnknownType = 'UnknownType',
  AuthenticationChangePrimaryEmail = 'AuthenticationChangePrimaryEmail',
  AuthenticationResetPassword = 'AuthenticationResetPassword',
  AuthenticationVerifyEmail = 'AuthenticationVerifyEmail',
  AuthenticationMagicLink = 'AuthenticationMagicLink',
  ApplicationDenied = 'ApplicationDenied',
  ApplicationIncomplete = 'ApplicationIncomplete',
  ApplicationSubmitted = 'ApplicationSubmitted',
  ApplicationWithdrawn = 'ApplicationWithdrawn',
  ApplicationApproved = 'ApplicationApproved',
  PaymentsClaimFunds = 'PaymentsClaimFunds',
  PaymentsFundsClaimed = 'PaymentsFundsClaimed',
  PaymentsFundsIssued = 'PaymentsFundsIssued',
  PaymentSentToOtherParty = 'PaymentSentToOtherParty',
  PaymentsApplicantPaymentReset = 'PaymentsApplicantPaymentReset',
  PaymentsAdvocatePaymentReset = 'PaymentsAdvocatePaymentReset',
  PaymentsRecurringPaymentInitiated = 'PaymentsRecurringPaymentInitiated',
  PaymentsRecurringPaymentsScheduled = 'PaymentsRecurringPaymentsScheduled',
  PaymentsRecurringPaymentsScheduledOtherParty = 'PaymentsRecurringPaymentsScheduledOtherParty',
  MultipartyInvitation = 'MultipartyInvitation',
  ProgramReferral = 'ProgramReferral',
  CaseComment = 'CaseComment',
  ParticipantCaseCommentAssignee = 'ParticipantCaseCommentAssignee',
  ParticipantCaseCommentSupport = 'ParticipantCaseCommentSupport',
}

export type PlatformNotificationType =
  | typeof NotificationType.ApplicationApproved
  | typeof NotificationType.PaymentsAdvocatePaymentReset
  | typeof NotificationType.PaymentsApplicantPaymentReset
  | typeof NotificationType.ApplicationDenied
  | typeof NotificationType.ApplicationIncomplete
  | typeof NotificationType.ApplicationSubmitted
  | typeof NotificationType.ApplicationWithdrawn
  | typeof NotificationType.AuthenticationChangePrimaryEmail
  | typeof NotificationType.PaymentsClaimFunds
  | typeof NotificationType.PaymentsFundsClaimed
  | typeof NotificationType.PaymentsFundsIssued
  | typeof NotificationType.PaymentSentToOtherParty
  | typeof NotificationType.ProgramReferral
  | typeof NotificationType.PaymentsRecurringPaymentInitiated
  | typeof NotificationType.PaymentsRecurringPaymentsScheduled
  | typeof NotificationType.PaymentsRecurringPaymentsScheduledOtherParty
  | typeof NotificationType.AuthenticationResetPassword
  | typeof NotificationType.AuthenticationVerifyEmail
  | typeof NotificationType.CaseComment
  | typeof NotificationType.ParticipantCaseCommentAssignee
  | typeof NotificationType.ParticipantCaseCommentSupport;

export enum PlatformNotificationEnum {
  ApplicationApproved = 'ApplicationApproved',
  PaymentsAdvocatePaymentReset = 'PaymentsAdvocatePaymentReset',
  PaymentsApplicantPaymentReset = 'PaymentsApplicantPaymentReset',
  ApplicationDenied = 'ApplicationDenied',
  ApplicationIncomplete = 'ApplicationIncomplete',
  ApplicationSubmitted = 'ApplicationSubmitted',
  ApplicationWithdrawn = 'ApplicationWithdrawn',
  AuthenticationChangePrimaryEmail = 'AuthenticationChangePrimaryEmail',
  PaymentsClaimFunds = 'PaymentsClaimFunds',
  PaymentsFundsClaimed = 'PaymentsFundsClaimed',
  PaymentsFundsIssued = 'PaymentsFundsIssued',
  PaymentSentToOtherParty = 'PaymentSentToOtherParty',
  ProgramReferral = 'ProgramReferral',
  PaymentsRecurringPaymentInitiated = 'PaymentsRecurringPaymentInitiated',
  PaymentsRecurringPaymentsScheduled = 'PaymentsRecurringPaymentsScheduled',
  PaymentsRecurringPaymentsScheduledOtherParty = 'PaymentsRecurringPaymentsScheduledOtherParty',
  AuthenticationResetPassword = 'AuthenticationResetPassword',
  AuthenticationVerifyEmail = 'AuthenticationVerifyEmail',
  CaseComment = 'CaseComment',
  ParticipantCaseCommentAssignee = 'ParticipantCaseCommentAssignee',
  ParticipantCaseCommentSupport = 'ParticipantCaseCommentSupport',
}

export enum CommunicationChannels {
  EMAIL = 'email',
  SMS = 'sms',
}

export interface CommunicationChannelsConfig {
  channels?: Record<Partial<CommunicationChannels>, boolean>;
  [CommunicationChannels.EMAIL]?: Record<Partial<NotificationType>, boolean>;
  [CommunicationChannels.SMS]?: Record<Partial<NotificationType>, boolean>;
}
