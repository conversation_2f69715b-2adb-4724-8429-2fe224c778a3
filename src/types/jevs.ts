export enum JEVSRecipientColumns {
  SALESFORCE_ID = 'Salesforce ID',
  FIRST_NAME = 'Participant First Name',
  LAST_NAME = 'Participant Last Name',
  EMAIL = 'Participant email address',
  PARTICIPANT_ADDRESS = 'Participant Full Address',
  PARTICIPANT_CITY = 'Participant City',
  PARTICIPANT_STATE = 'Participant State',
  PARTICIPANT_ZIP_CODE = 'Participant ZIP Code',

  PAYMENT_METHOD = 'Payment Method',
  ACCOUNT_NUMBER = 'Bank Account Number',
  ROUTING_NUMBER = 'Bank Routing Number',
  ACCOUNT_TYPE = 'Bank Account Type',
  STIPEND_TYPE = 'Stipend Type',
  COHORT = 'Cohort',
  ORGANIZATION = 'Organization',
  PROGRAM = 'Program',

  PROGRAM_ADDRESS = 'Program Shipping Address',
  PROGRAM_CITY = 'Program City',
  PROGRAM_STATE = 'Program State',
  PROGRAM_ZIP_CODE = 'Program ZIP Code',

  PAYMENT_DATE = 'Payment Date',
  PAYMENT_AMOUNT = 'Payment Amount',
}

export const JEVSFileMapper: Record<JEVSRecipientColumns, string> = {
  [JEVSRecipientColumns.SALESFORCE_ID]: 'salesForceId',
  [JEVSRecipientColumns.FIRST_NAME]: 'firstName',
  [JEVSRecipientColumns.LAST_NAME]: 'lastName',
  [JEVSRecipientColumns.EMAIL]: 'email',
  [JEVSRecipientColumns.PARTICIPANT_ADDRESS]: 'participantAddress',
  [JEVSRecipientColumns.PARTICIPANT_CITY]: 'participantCity',
  [JEVSRecipientColumns.PARTICIPANT_STATE]: 'participantState',
  [JEVSRecipientColumns.PARTICIPANT_ZIP_CODE]: 'participantZipCode',

  [JEVSRecipientColumns.PAYMENT_METHOD]: 'paymentMethod',
  [JEVSRecipientColumns.ACCOUNT_NUMBER]: 'accountNumber',
  [JEVSRecipientColumns.ROUTING_NUMBER]: 'routingNumber',
  [JEVSRecipientColumns.ACCOUNT_TYPE]: 'accountType',
  [JEVSRecipientColumns.STIPEND_TYPE]: 'stipendType',
  [JEVSRecipientColumns.COHORT]: 'cohort',
  [JEVSRecipientColumns.ORGANIZATION]: 'organization',
  [JEVSRecipientColumns.PROGRAM]: 'program',

  [JEVSRecipientColumns.PROGRAM_ADDRESS]: 'programAddress',
  [JEVSRecipientColumns.PROGRAM_CITY]: 'programCity',
  [JEVSRecipientColumns.PROGRAM_STATE]: 'programState',
  [JEVSRecipientColumns.PROGRAM_ZIP_CODE]: 'programZipCode',

  [JEVSRecipientColumns.PAYMENT_DATE]: 'paymentDate',
  [JEVSRecipientColumns.PAYMENT_AMOUNT]: 'paymentAmount',
};

export enum USIOCSVColumns {
  cardType = 'cardType',
  cardCount = 'cardCount',
  shippingDestination = 'shippingDestination',
  firstName = 'firstName',
  lastName = 'lastName',
  programName = 'programName',
  address = 'address',
  address2 = 'address2',
  city = 'city',
  state = 'state',
  zipCode = 'zipCode',
  phone = 'phone',
  email = 'email',
  loadAmount = 'loadAmount',
  loadNow = 'loadNow',
  distributorId = 'distributorId',
  cardDesignId = 'cardDesignId',
  giftMessage = 'giftMessage',
  giftSenderFirstName = 'giftSenderFirstName',
  giftSenderLastName = 'giftSenderLastName',
  giftVirtualDeliveryMethod = 'giftVirtualDeliveryMethod',
  expiresIn = 'expiresIn',
  reportData1 = 'reportData1',
  reportData2 = 'reportData2',
  reportData3 = 'reportData3',
  source = 'Source',
  shippingMethod = 'ShippingMethod',
}

export enum USIOImportColumns {
  Program = 'program',
  Email_Address = 'email',
  Card_Id = 'cardId',
  CH_Name = 'name',
  Card_Status = 'status',
  Card_Type = 'cardType',
  Address_1 = 'address',
  Address_2 = 'address2',
  City = 'city',
  State = 'state',
  Zip = 'zipCode',
  Create_Date = 'createdAt',
  Distributor = 'distributer',
  Enrollment_Score = 'score',
  Mail_To = 'shippingAddress',
  Order_Id = 'orderId',
  Order_Status = 'orderStatus',
  Phone_Number = 'phone',
  Purchaser = 'purchaser',
  type = 'type',
  Initial_Amount = 'initialAmount',
  Current_Balance = 'balance',
}
