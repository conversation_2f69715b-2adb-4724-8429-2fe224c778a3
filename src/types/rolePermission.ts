export enum Roles {
  PST = 'pst',
  ADMIN = 'admin',
}

export enum Resources {
  Admins = 'admins',
  AnalyticsResources = 'analytics_resources',
  ApplicantProfileConfigurations = 'applicant_profile_configurations',
  ApplicantTypes = 'applicant_types',
  ApplicationConfigurations = 'application_configurations',
  ApplicationConfigurationsCourses = 'application_configurations_courses',
  AuditLogs = 'audit_logs',
  Cases = 'cases',
  Changelogs = 'changelogs',
  Configurations = 'configurations',
  EligibilityConfig = 'eligibility_config',
  NotificationTemplates = 'notification_templates',
  ExternalApiUsers = 'external_api_users',
  Features = 'features',
  Fulfillments = 'fulfillments',
  Funds = 'funds',
  IamAdmin = 'iam-admin',
  Banners = 'incident_messages',
  JEVS = 'jevs',
  Migrations = 'migrations',
  PartnerAdmins = 'partner-admins',
  PartnerCommunicationChannelsConfigurations = 'partner_communication_channels_configurations',
  PartnerFeatures = 'partner_features',
  PartnerIncidents = 'partner_incidents',
  Partners = 'partners',
  PartnersSection = 'partners-section',
  Payments = 'payments',
  PaymentSchedules = 'payments-schedules',
  PaymentsRecurring = 'recurring-payments',
  TransferPayments = 'transfer-payments',
  PaymentsSection = 'payments-section',
  PaymentsSettings = 'payments-settings',
  Principals = 'principals',
  ProgramApplicantType = 'program_applicant_types',
  ProgramFeatures = 'program_features',
  ProgramFunds = 'program_funds',
  Programs = 'programs',
  RolePermissions = 'role_permissions',
  Schedules = 'schedules',
  Settings = 'settings',
  Users = 'users',
  CoreUsers = 'core_users',
  VendorTypes = 'vendor_types',
  VerificationConfigurations = 'verification_configurations',
  WorkflowEvents = 'workflow_events',
}

export enum Operations {
  List = 'list',
  Show = 'show',
  Create = 'create',
  Edit = 'edit',
  Delete = 'delete',
}

export const CustomOperations = {
  [Resources.Partners]: ['reports', 'identity'],
  [Resources.CoreUsers]: ['deactivate'],
};
