import type { AuditLog } from '@prisma/clients/sorcery';
import type { BaseKey } from '@refinedev/core';
import type { CourseKeys } from './ApplicationAnswer';
import type { DeleteTypes } from './requests';
import type { SchedulePaymentsRequest, SchedulePaymentsResponse } from './schedulePayments';

export interface Operation<Input, Output> {
  run(input: Input): Promise<Output>;
}

// --- users ---
export type DeleteUserOperation = Operation<
  {
    entityId: string;
    entityType: string;
    partnerId: string;
    requestType: 'USER_SUBMITTED' | 'PARTNER_SUBMITTED' | 'PARTNER_SCHEDULED';
    type: DeleteTypes;
    author?: { id: string };
  },
  Record<string, { count: number }>
>;

// --- audits ---
export type CreateAuditOperation = Operation<Partial<AuditLog>, AuditLog>;

// --- application answers ---
export type UpdateCoursesOperation = Operation<
  {
    programId: string;
    courses: Array<{
      course: string;
      action: 'Update' | 'Remove';
      name?: string;
      duration?: string;
    }>;
    key: CourseKeys;
  },
  string[]
>;

export type SaveEligibilityQuestionsOperation = Operation<
  {
    creations: { value: Record<string, unknown> }[];
    deletes: string[];
    updates: { value: Record<string, unknown>; id: string }[];
    configId: string;
  },
  { success: boolean }
>;

// external api user
export type SaveExternalApiUserOperation = Operation<
  {
    appId: string;
    partnerId: string;
    email: string;
  },
  { success: boolean }
>;

type UpdateScheduledOperation = Operation<
  { ids: BaseKey[]; scheduledFor: string },
  { count: number } | never[]
>;

type ScheduleRecurringPaymentsOperation = Operation<
  SchedulePaymentsRequest,
  SchedulePaymentsResponse
>;

type CreateUsioAccountsOperation = Operation<
  {
    partnerId: string;
    accounts: { email: string; cardId: number; type?: 'physicalCard' | 'virtualCard' }[];
  },
  {
    status: string;
    errors?: { id: string; message: string }[];
  }
>;

export interface Operations {
  audits: {
    create: CreateAuditOperation;
  };
  scheduler: {
    updateJobs: UpdateScheduledOperation;
  };
  compliance: {
    deleteUser: DeleteUserOperation;
  };
  platform: {
    updatePayments: UpdateScheduledOperation;
    updateCourses: UpdateCoursesOperation;
    scheduleRecurringPayments: ScheduleRecurringPaymentsOperation;
    saveEligibilityQuestions: SaveEligibilityQuestionsOperation;
  };
  payment: {
    createUsioAccounts: CreateUsioAccountsOperation;
  };
  identity: {
    saveExternalApiUser: SaveExternalApiUserOperation;
  };
}
