import type { Field } from '@measured/puck';
import type { ContentConfig } from '@utils/appConfig';
import type { IntroPageProps } from '@utils/appConfig/blocks/IntroPage';
import type { ConfigOverridesProps } from '@utils/appConfig/blocks/Override';
import type { SectionProps } from '@utils/appConfig/blocks/Section';
import { z } from 'zod';

export enum CONFIG_SECTIONS {
  INTRO_PAGES = 'introPages',
  OVERRIDES = 'overrides',
  SECTIONS = 'sections',
}

export enum FIELD_TYPES {
  ADDRESS = 'address',
  CALCULATED = 'calculated',
  CHECKBOX = 'checkbox',
  CHECKBOX_GROUP = 'checkboxGroup',
  COMPLEX = 'complex',
  DATE = 'date',
  DOCUMENT = 'document',
  DROPDOWN = 'dropdown',
  RADIO_LIST = 'radioList',
  TEXT = 'text',
  TYPOGRAPHY = 'typography',
}

export enum PartnerReportingKeys {
  Email = 'email',
  EmployerAddress = 'employerAddress',
  MailingAddress = 'address.mailingAddress',
  DateOfBirth = 'dateOfBirth',
  GraduationDate = 'graduationDate',
  PhoneNumber = 'phoneNumber',
  SSN = 'ssn',
  Name = 'name',
  Race = 'race',
  Gender = 'gender',
  Ethnicity = 'ethnicity',
  ContactPhone = 'contactPhone',
  ContactEmail = 'contactEmail',
  Signature = 'signature',
  Identification = 'identification',
  StudentId = 'studentId',
  Dependents = 'dependents',
  HouseholdSize = 'householdSize',
  IncomeSource = 'incomeSource',
  HouseHold = 'houseHold',
}

export const appConfigSections: Record<CONFIG_SECTIONS, { label: string; value: string }> = {
  [CONFIG_SECTIONS.INTRO_PAGES]: { label: 'Intro Pages', value: 'introPages' },
  [CONFIG_SECTIONS.OVERRIDES]: { label: 'Overrides', value: 'overrides' },
  [CONFIG_SECTIONS.SECTIONS]: { label: 'Sections', value: 'sections' },
};

export const FieldToBlockType: Record<FIELD_TYPES, Partial<keyof ContentConfig>> = {
  [FIELD_TYPES.ADDRESS]: 'GenericAddressField',
  [FIELD_TYPES.CALCULATED]: 'CalculatedField',
  [FIELD_TYPES.CHECKBOX]: 'CheckboxField',
  [FIELD_TYPES.CHECKBOX_GROUP]: 'CheckboxGroupField',
  [FIELD_TYPES.COMPLEX]: 'ComplexField',
  [FIELD_TYPES.DATE]: 'GenericDateField',
  [FIELD_TYPES.DOCUMENT]: 'GenericDocumentField',
  [FIELD_TYPES.DROPDOWN]: 'GenericDropdownField',
  [FIELD_TYPES.RADIO_LIST]: 'RadioListField',
  [FIELD_TYPES.TEXT]: 'GenericTextField',
  [FIELD_TYPES.TYPOGRAPHY]: 'TypographyComponent',
};

export const QUESTION_GROUP_REGEX = /^(?!.*\bQuestion\b(?!-))(?=.*\bQuestion-Group\b).*/g;
export const QUESTION_REGEX = /^.*\bQuestion\b$/g;

export interface Course {
  label: string;
  value: string;
  disabled: boolean;
}

export interface CourseQuestion {
  key: string;
  courses: Course[];
  formula: ConditionExpression;
}

export enum ExpressionType {
  Answer = 'answer',
  Boolean = 'boolean',
  Compare = 'compare',
  Condition = 'condition',
  Format = 'format',
  List = 'list',
  Literal = 'literal',
  Math = 'math',
}

export enum CompareOperator {
  Eq = 'eq',
  Gt = 'gt',
  Gte = 'gte',
  In = 'in',
  Lt = 'lt',
  Lte = 'lte',
}
export enum BoolOperator {
  And = 'and',
  Or = 'or',
  Not = 'not',
}
export enum ListOperator {
  LengthOperator = 'length',
  Map = 'map',
  Sum = 'sum',
}
export enum MathOperator {
  Add = 'add',
  Divide = 'divide',
  Multiply = 'multiply',
  Subtract = 'subtract',
}

export type AstValue = string | number | boolean;

export interface AnswerExpression {
  type: ExpressionType.Answer;
  key: string;
}

export interface LiteralExpression {
  type: ExpressionType.Literal;
  value: AstValue | AstValue[];
}

export interface CompareExpression {
  type: ExpressionType.Compare;
  op: CompareOperator;
  comparators: [AnswerExpression, LiteralExpression];
}

export interface ConditionExpression {
  type: ExpressionType.Condition;
  conditions: {
    if: CompareExpression;
    then: LiteralExpression;
  }[];
  else?: LiteralExpression;
}

export enum LOGIC_TYPES {
  NONE = 'none',
  ANSWER = 'answer',
  BOOLEAN = 'boolean',
  COMPARE = 'compare',
  CONDITION = 'condition',
  FORMAT = 'format',
  LIST = 'list',
  LITERAL = 'literal',
  MATH = 'math',
}

export const DynamicLogicBaseValues: Record<string, Field<unknown>> = {
  type: {
    type: 'select',
    label: 'Type',
    options: [
      { label: 'none', value: LOGIC_TYPES.NONE },
      { label: 'Answer Expression', value: LOGIC_TYPES.ANSWER },
      { label: 'Boolean Expression', value: LOGIC_TYPES.BOOLEAN },
      { label: 'Compare Expression', value: LOGIC_TYPES.COMPARE },
      { label: 'Condition Expression', value: LOGIC_TYPES.CONDITION },
      { label: 'Format Expression', value: LOGIC_TYPES.FORMAT },
      { label: 'List Expression', value: LOGIC_TYPES.LIST },
      { label: 'Literal Expression', value: LOGIC_TYPES.LITERAL },
      { label: 'Math Expression', value: LOGIC_TYPES.MATH },
    ],
  },
  op: {
    type: 'select',
    label: 'Operator',
    options: [
      { label: '', value: '' },
      { label: 'And', value: 'and' },
      { label: 'Or', value: 'or' },
      { label: 'Not', value: 'not' },
      { label: 'Eq', value: 'eq' },
      { label: 'Gt', value: 'gt' },
      { label: 'Gte', value: 'gte' },
      { label: 'In', value: 'in' },
      { label: 'Lt', value: 'lt' },
      { label: 'Lte', value: 'lte' },
      { label: 'Length', value: 'length' },
      { label: 'Map', value: 'map' },
      { label: 'Sum', value: 'sum' },
      { label: 'Add', value: 'add' },
      { label: 'Divide', value: 'divide' },
      { label: 'Multiply', value: 'multiply' },
      { label: 'Subtract', value: 'subtract' },
    ],
  },
  key: {
    type: 'text',
    label: 'Key',
  },
  valueAsText: {
    type: 'text',
    label: 'Value as text (single or comma separated list)',
  },
  format: {
    type: 'select',
    label: 'Format',
    options: [
      { label: '', value: '' },
      { label: 'Pattern', value: 'pattern' },
      { label: 'Phone', value: 'phone' },
    ],
  },
  pattern: {
    type: 'text',
    label: 'Pattern',
  },
};

const createDynamicLogicLevel = (
  data: Record<string, Field<unknown>>,
  next?: Record<string, Field<unknown>>,
): Record<string, Field<unknown>> => ({
  ...data,
  comparators: {
    type: 'array',
    label: 'comparators: use 2 expressions',
    getItemSummary: (_, index) => `Expression #${(index ?? 0) + 1}`,
    arrayFields: next || DynamicLogicBaseValues,
  },
  expressions: {
    type: 'array',
    arrayFields: next || DynamicLogicBaseValues,
    getItemSummary: (_, index) => `Expression #${(index ?? 0) + 1}`,
    defaultItemProps: {
      type: '',
      op: '',
      pattern: '',
      key: 'Reference to another field key',
      value: '',
    },
  },
  expression: {
    type: 'object',
    objectFields: next || DynamicLogicBaseValues,
  },
  conditions: {
    type: 'array',
    arrayFields: {
      if: { type: 'object', objectFields: next || DynamicLogicBaseValues },
      // biome-ignore lint/suspicious/noThenProperty: <explanation>
      then: { type: 'object', objectFields: next || DynamicLogicBaseValues },
    },
    getItemSummary: (_, index) => `Condition #${(index ?? 0) + 1}`,
  },
  else: { type: 'object', objectFields: next || DynamicLogicBaseValues },
  valueAsExpression: {
    type: 'object',
    label: 'Value as Expression',
    objectFields: next || DynamicLogicBaseValues,
  },
  list: {
    type: 'object',
    label: 'List',
    objectFields: next || DynamicLogicBaseValues,
  },
  mapper: {
    type: 'object',
    label: 'Mapper',
    objectFields: next || DynamicLogicBaseValues,
  },
});

const constructDeepObject = (
  dataArray: Record<string, Field<unknown>>[],
): Record<string, Field<unknown>> | undefined => {
  if (dataArray.length === 0) return;
  const [firstData, ...restData] = dataArray;
  return createDynamicLogicLevel(firstData, constructDeepObject(restData));
};

export const DynamicLogicValues: Record<string, Field<unknown>> = {
  ...DynamicLogicBaseValues,
  comparators: {
    type: 'array',
    label: 'comparators: use 2 expressions',
    getItemSummary: (_, index) => `Expression #${(index ?? 0) + 1}`,
    arrayFields: DynamicLogicBaseValues,
  },
  expression: {
    type: 'object',
    objectFields: DynamicLogicBaseValues,
  },
  expressions: {
    type: 'array',
    arrayFields: {
      ...DynamicLogicBaseValues,
    },
    getItemSummary: (_, index) => `Expression #${(index ?? 0) + 1}`,
    defaultItemProps: {
      type: '',
      op: '',
      pattern: '',
      key: 'Reference to another field key',
      value: '',
    },
  },
  conditions: {
    type: 'object',
    objectFields: {
      if: { type: 'object', objectFields: DynamicLogicBaseValues },
      // biome-ignore lint/suspicious/noThenProperty: <explanation>
      then: { type: 'object', objectFields: DynamicLogicBaseValues },
    },
  },
  else: { type: 'object', objectFields: DynamicLogicBaseValues },
  valueAsExpression: {
    type: 'object',
    label: 'Value as Expression',
    objectFields: DynamicLogicBaseValues,
  },
  list: {
    type: 'object',
    label: 'List',
    objectFields: DynamicLogicBaseValues,
  },
  mapper: {
    type: 'object',
    label: 'Mapper',
    objectFields: DynamicLogicBaseValues,
  },
};

export const DeepDynamicLogicValues = (() => {
  const MAX_DEPTH = 1;
  return (
    constructDeepObject([
      DynamicLogicValues,
      ...Array.from({ length: MAX_DEPTH }, () => DynamicLogicValues),
    ]) || DynamicLogicValues
  );
})();

export const FieldValidationValues: Record<string, Field<unknown>> = {
  required: {
    type: 'radio',
    options: [
      { label: 'true', value: true },
      { label: 'false', value: false },
    ],
  },
  rules: {
    type: 'array',
    arrayFields: {
      rule: { type: 'object', objectFields: DynamicLogicValues },
      message: { type: 'text' },
    },
    getItemSummary: (_, index) => `rule #${(index ?? 0) + 1}`,
  },
  condition: { type: 'object', objectFields: DynamicLogicValues },
};

export const DefaultValidationValues = {
  required: true,
};

export const DocumentEligibilityValidationValues: Record<string, Field<unknown>> = {
  type: {
    type: 'select',
    options: [
      { label: 'Error', value: 'Error' },
      { label: 'Warning', value: 'Warning' },
    ],
  },
  logicalOperator: {
    type: 'select',
    options: [
      { label: 'AND', value: 'AND' },
      { label: 'OR', value: 'OR' },
    ],
  },
  ruleset: {
    type: 'array',
    arrayFields: {
      type: {
        type: 'select',
        options: [
          { label: 'Error', value: 'Error' },
          { label: 'Warning', value: 'Warning' },
        ],
      },
      message: { type: 'text' },
      exists: {
        type: 'radio',
        options: [
          { label: 'true', value: true },
          { label: 'false', value: false },
        ],
      },
    },
    getItemSummary: (_, index) => `Rule #${(index ?? 0) + 1}`,
  },
};

export const EligibilityValidationValues: Record<string, Field<unknown>> = {
  type: {
    type: 'select',
    options: [
      { label: 'Error', value: 'Error' },
      { label: 'Warning', value: 'Warning' },
      { label: 'Info', value: 'Info' },
    ],
  },
  message: { type: 'text' },
  logicalOperator: {
    type: 'select',
    options: [
      { label: 'AND', value: 'AND' },
      { label: 'OR', value: 'OR' },
    ],
  },
  ruleset: {
    type: 'array',
    arrayFields: {
      type: {
        type: 'select',
        options: [
          { label: 'Boolean Validation', value: 'BooleanValidation' },
          { label: 'Range Validation', value: 'RangeValidation' },
        ],
      },
      message: { type: 'text' },
      condition: {
        type: 'select',
        options: [
          { label: 'Equals', value: 'equals' },
          { label: 'Range', value: 'range' },
        ],
      },
      value: { type: 'text' },
      minValue: { type: 'number' },
      maxValue: { type: 'number' },
      unit: {
        type: 'select',
        options: [
          { label: 'Years', value: 'years' },
          { label: 'Months', value: 'months' },
          { label: 'Days', value: 'days' },
        ],
      },
      reference: {
        type: 'select',
        options: [{ label: 'Current Date', value: 'currentDate' }],
      },
    },
    getItemSummary: (_, index) => `Rule #${(index ?? 0) + 1}`,
  },
};
export interface FieldValidation {
  required: boolean;

  // If any of these rules fail, the field fails validation
  rules?: ValidationRule[];

  // If this expression resolves to true, the field *is* required
  // NOTE: this is in addition to the automated condition logic added
  // when a field has dynamic logic
  condition?: Expression;
}

interface ValidationRule {
  // If the rule expression evaluates to false, that means validation has failed
  rule: Expression;
  // If validation fails, returns this message
  message: string;
}

interface BaseDocumentEligibilityRule {
  type: 'Error' | 'Warning';
  message: string;
  exists: boolean;
}

type DocumentEligibilityRule = BaseDocumentEligibilityRule;

export type DocumentEligibilityValidation = {
  type: 'Error' | 'Warning';
  logicalOperator: 'AND' | 'OR';
  ruleset: DocumentEligibilityRule[];
};

interface BaseEligibilityRule {
  type: 'Error' | 'Warning';
  message: string;
}

type BooleanValidation = BaseEligibilityRule & {
  condition: 'equals';
  value: string | boolean;
};

type RangeValidation = BaseEligibilityRule & {
  condition: 'range';
  minValue: number;
  maxValue: number;
  unit: 'years' | 'months' | 'days';
  reference?: 'currentDate';
};

type EligibilityRule = BooleanValidation | RangeValidation;

export type EligibilityValidation = {
  type: 'Error' | 'Warning' | 'Info';
  message?: string;
  logicalOperator: 'AND' | 'OR';
  ruleset?: EligibilityRule[];
};

export type Expression = {
  comparators?: [Expression, Expression];
  conditions?: {
    if: Expression;
    then: Expression;
  }[];
  else?: Expression;
  expression?: Expression;
  expressions?: Expression[];
  format?: string;
  list?: Expression;
  mapper?: Expression;
  op?: string;
  pattern?: string;
  type?: string;
  value?: Expression | string;
  key?: string;
};

export enum TextInputType {
  Currency = 'currency',
  Date = 'date',
  Email = 'email',
  Number = 'number',
  Password = 'password',
  Phone = 'tel',
  Signature = 'signature',
  Text = 'text',
}

export interface LabelValue {
  label: string;
  description?: string;
  value: string;
  disabled?: boolean;
  children?: Omit<LabelValue, 'children'>[];
}

export type BaseFieldProps = {
  type: string;
  key: string;
  displayName?: string;
  dynamicLogic?: Expression;
};

export const IconSelectValues = [
  { label: 'add', value: 'add' },
  { label: 'adminPanelOutline', value: 'adminPanelOutline' },
  { label: 'arrowForward', value: 'arrowForward' },
  { label: 'arrowDownward', value: 'arrowDownward' },
  { label: 'assignment', value: 'assignment' },
  { label: 'bulkAction', value: 'bulkAction' },
  { label: 'calendar', value: 'calendar' },
  { label: 'cancel', value: 'cancel' },
  { label: 'checkBox', value: 'checkBox' },
  { label: 'checkBoxOutlineBlank', value: 'checkBoxOutlineBlank' },
  { label: 'checkCircle', value: 'checkCircle' },
  { label: 'checkCircleOutline', value: 'checkCircleOutline' },
  { label: 'circle', value: 'circle' },
  { label: 'close', value: 'close' },
  { label: 'description', value: 'description' },
  { label: 'dollarSign', value: 'dollarSign' },
  { label: 'edit', value: 'edit' },
  { label: 'errorOutline', value: 'errorOutline' },
  { label: 'error', value: 'error' },
  { label: 'fastForward', value: 'fastForward' },
  { label: 'flag', value: 'flag' },
  { label: 'filterList', value: 'filterList' },
  { label: 'gppGoodOutlined', value: 'gppGoodOutlined' },
  { label: 'help', value: 'help' },
  { label: 'home', value: 'home' },
  { label: 'info', value: 'info' },
  { label: 'infoOutline', value: 'infoOutline' },
  { label: 'keyboardArrowDown', value: 'keyboardArrowDown' },
  { label: 'keyboardArrowUp', value: 'keyboardArrowUp' },
  { label: 'keyboardArrowLeft', value: 'keyboardArrowLeft' },
  { label: 'keyboardArrowRight', value: 'keyboardArrowRight' },
  { label: 'language', value: 'language' },
  { label: 'logout', value: 'logout' },
  { label: 'mail', value: 'mail' },
  { label: 'markEmailRead', value: 'markEmailRead' },
  { label: 'menu', value: 'menu' },
  { label: 'money', value: 'money' },
  { label: 'moreDots', value: 'moreDots' },
  { label: 'newApplicant', value: 'newApplicant' },
  { label: 'pin', value: 'pin' },
  { label: 'pinOutlined', value: 'pinOutlined' },
  { label: 'profile', value: 'profile' },
  { label: 'raisedHandOutlined', value: 'raisedHandOutlined' },
  { label: 'radioButtonUnchecked', value: 'radioButtonUnchecked' },
  { label: 'referral', value: 'referral' },
  { label: 'reportGmailErrorRed', value: 'reportGmailErrorRed' },
  { label: 'save', value: 'save' },
  { label: 'search', value: 'search' },
  { label: 'syncAlt', value: 'syncAlt' },
  { label: 'task', value: 'task' },
  { label: 'time', value: 'time' },
  { label: 'trash', value: 'trash' },
  { label: 'upload', value: 'upload' },
  { label: 'UploadDoneOutline', value: 'UploadDoneOutline' },
  { label: 'user', value: 'user' },
  { label: 'unverified', value: 'unverified' },
  { label: 'verifiedOutlined', value: 'verifiedOutlined' },
  { label: 'verified', value: 'verified' },
  { label: 'view', value: 'view' },
  { label: 'warning', value: 'warning' },
  { label: 'warningOutline', value: 'warningOutline' },
];

export interface Participant {
  name: string;
  email: string;
  applicantTypeId: string;
}
export interface ApplicationConfig {
  introPages: IntroPageProps[];
  overrides?: ConfigOverridesProps;
  sections: SectionProps[];
  participants?: Participant[];
}

export interface ApplicationConfiguration {
  id: string;
  name: string;
  description: string;
  config: ApplicationConfig;
  partnerId?: string;
  programApplicationConfigurationsByConfigurationId: Array<{
    programId: string;
  }>;
}

const IntroPageContentBlockSchema = z.object({
  title: z.string().min(1),
  description: z.string().min(1).optional(),
  icon: z.string().min(1),
});

const IntroPageSchema = z.object({
  key: z.string().min(1),
  title: z.string().min(1),
  layout: z.enum(['welcome', 'steps', 'default']),
  body: z.string().min(1).optional(),
  copy: z.string().optional(),
  steps: z.array(IntroPageContentBlockSchema).optional(),
});

const StatusOverrideSchema = z.object({
  status: z.enum([
    'Application',
    'Approved',
    'AwaitingReview',
    'Denied',
    'InReview',
    'InProgress',
    'Incomplete',
    'WithdrawnOpen',
    'WithdrawnClosed',
    'Unknown',
  ]),
  label: z.string().optional(),
  message: z.string().min(1).optional(),
  action: z.string().min(1).optional(),
  referralMessage: z.string().min(1).optional(),
});

const SubmissionBulletSchema = z.object({
  key: z.string().min(1),
  icon: z.string().min(1),
  title: z.string().min(1),
  description: z.string().min(1).optional(),
});

const BaseDynamicLogicSchema = z.object({
  key: z.string().optional(),
  type: z.string().optional(),
  op: z.string().optional(),
  pattern: z.string().optional(),
  format: z.string().optional(),
});

const EligibilityRuleSchema = z.union([
  z.object({
    type: z.literal('Error').or(z.literal('Warning')),
    message: z.string(),
    condition: z.literal('equals'),
    value: z.union([z.string(), z.boolean()]),
  }),
  z.object({
    type: z.literal('Error').or(z.literal('Warning')),
    message: z.string(),
    condition: z.literal('range'),
    minValue: z.number(),
    maxValue: z.number(),
    unit: z.literal('years').or(z.literal('months')).or(z.literal('days')),
    reference: z.optional(z.literal('currentDate')),
  }),
]);

const EligibilityValidationSchema = z.object({
  type: z.literal('Error').or(z.literal('Warning')).or(z.literal('Info')),
  message: z.string().optional(),
  logicalOperator: z.literal('AND').or(z.literal('OR')),
  ruleset: z.array(EligibilityRuleSchema),
});

const DocumentEligibilityRuleSchema = z.object({
  type: z.literal('Error').or(z.literal('Warning')),
  message: z.string(),
  exists: z.boolean(),
});

const DocumentEligibilityValidationSchema = z.object({
  type: z.literal('Error').or(z.literal('Warning')),
  logicalOperator: z.literal('AND').or(z.literal('OR')),
  ruleset: z.array(DocumentEligibilityRuleSchema),
});

type ExpressionSchema = z.infer<typeof BaseDynamicLogicSchema> & {
  comparators?: ExpressionSchema[];
  conditions?: {
    if: ExpressionSchema;
    then: ExpressionSchema;
  }[];
  else?: ExpressionSchema;
  expression?: ExpressionSchema;
  expressions?: ExpressionSchema[];
  list?: ExpressionSchema;
  mapper?: ExpressionSchema;
  value?: ExpressionSchema | string | number;
};

const DynamicLogicSchema: z.ZodType<ExpressionSchema> = BaseDynamicLogicSchema.extend({
  comparators: z.lazy(() => DynamicLogicSchema.array().length(2).optional()),
  conditions: z.lazy(() => {
    const ifThenObjectSchema = z.object({
      if: DynamicLogicSchema,
      // biome-ignore lint/suspicious/noThenProperty: <explanation>
      then: DynamicLogicSchema,
    });
    return z.array(ifThenObjectSchema).optional();
  }),
  else: z.lazy(() => DynamicLogicSchema.optional()),
  expression: z.lazy(() => DynamicLogicSchema.optional()),
  expressions: z.lazy(() => DynamicLogicSchema.array().optional()),
  list: z.lazy(() => DynamicLogicSchema.optional()),
  mapper: z.lazy(() => DynamicLogicSchema.optional()),
  value: z.lazy(() =>
    z.union([DynamicLogicSchema.optional(), z.string().optional(), z.number().optional()]),
  ),
});

const ValidationRuleSchema = z.object({
  rule: DynamicLogicSchema,
  message: z.string().min(1),
});

const ValidationSchema = z.object({
  required: z.boolean(),
  rules: z.array(ValidationRuleSchema).optional(),
  condition: DynamicLogicSchema.optional(),
});

const BaseFieldSchema = z.object({
  key: z.string().min(1),
  displayName: z.string().optional(),
  dynamicLogic: DynamicLogicSchema.optional(),
});

const AddressFieldSchema = BaseFieldSchema.extend({
  type: z.literal(FIELD_TYPES.ADDRESS),
  copy: z.string().optional(),
  validation: ValidationSchema.optional(),
  copyAddressKey: z.string().optional(),
  filter: z.object({ state: z.string(), city: z.string().optional() }).optional(),
  allowUnhoused: z.boolean().optional(),
});

const CalculatedFieldSchema = BaseFieldSchema.extend({
  type: z.literal(FIELD_TYPES.CALCULATED),
  formula: DynamicLogicSchema,
  display: z.object({
    copy: z.string(),
    inputType: z.enum(['calculated', ...Object.values(TextInputType)]).optional(),
    validation: ValidationSchema.optional(),
  }),
});

const CheckboxFieldSchema = BaseFieldSchema.extend({
  type: z.literal(FIELD_TYPES.CHECKBOX),
  copy: z.string(),
  validation: ValidationSchema.optional(),
  eligibilityValidation: EligibilityValidationSchema.optional(),
});

const BaseLabelValueSchema = z.object({
  label: z.string().min(1),
  description: z.string().optional(),
  value: z.string().min(1),
  disabled: z.boolean().optional(),
});

const LabelValueSchema = BaseLabelValueSchema.extend({
  children: z.lazy(() => BaseLabelValueSchema.array()).optional(),
});

const CheckboxFieldGroupSchema = BaseFieldSchema.extend({
  type: z.literal(FIELD_TYPES.CHECKBOX_GROUP),
  options: z.array(LabelValueSchema).nonempty(),
  validation: ValidationSchema.optional(),
});

const ComplexFieldBaseSchema = BaseFieldSchema.extend({
  copy: z.string(),
  validation: ValidationSchema.optional(),
  displayName: z.string().optional(),
  useLabelPrefix: z.boolean().optional(),
});

const ComplexFieldSchema = ComplexFieldBaseSchema.extend({
  type: z.literal(FIELD_TYPES.COMPLEX),
  subFields: z.lazy(() => SubFieldSchema.array().min(1)),
});

const DateFieldSchema = BaseFieldSchema.extend({
  type: z.literal(FIELD_TYPES.DATE),
  copy: z.string().optional(),
  props: z
    .object({
      helperText: z.string().optional(),
    })
    .optional(),
  validation: ValidationSchema.extend({
    allowedRange: z.enum(['today', 'past', 'future']).optional(),
    minimumDate: z.string().optional(),
    maximumDate: z.string().optional(),
  }).optional(),
  eligibilityValidation: EligibilityValidationSchema.optional(),
});

const DocumentFieldSchema = BaseFieldSchema.extend({
  type: z.literal(FIELD_TYPES.DOCUMENT),
  validation: ValidationSchema.optional(),
  copy: z
    .object({
      title: z.string().optional(),
      description: z.string().optional(),
    })
    .optional(),
  documentEligibilityValidation: DocumentEligibilityValidationSchema.optional(),
});

const DropdownFieldSchema = BaseFieldSchema.extend({
  type: z.literal('dropdown'),
  copy: z.string().optional(),
  validation: ValidationSchema.optional(),
  options: z.array(LabelValueSchema).min(1),
  props: z
    .union([
      z.object({
        searchable: z.boolean().optional(),
      }),
      z.object({
        childMode: z.enum(['OnParentSelect', 'AlwaysAvailable']).optional(),
        multiple: z.literal(true),
        selectAll: z.boolean().optional(),
      }),
    ])
    .optional(),
});

const RadioListFieldSchema = BaseFieldSchema.extend({
  type: z.literal(FIELD_TYPES.RADIO_LIST),
  copy: z.string().optional(),
  options: z.array(LabelValueSchema).nonempty(),
  validation: ValidationSchema.optional(),
});

const TextFieldSchema = BaseFieldSchema.extend({
  type: z.literal(FIELD_TYPES.TEXT),
  copy: z.string(),
  isSensitive: z.boolean().optional(),
  inputType: z.enum(['text', ...Object.values(TextInputType)]),
  validation: ValidationSchema.optional(),
  props: z
    .object({
      characterCounter: z
        .object({
          max: z.number().optional(),
          exact: z.number().optional(),
          min: z.number().optional(),
        })
        .optional(),
      helperText: z.string().optional(),
      inputProps: z.object({ maxlength: z.number().optional() }).optional(),
      multiline: z.boolean().optional(),
      rows: z.number().optional(),
      tooltip: z.string().optional(),
    })
    .optional(),
});

const TypographySchema = BaseFieldSchema.extend({
  type: z.literal(FIELD_TYPES.TYPOGRAPHY),
  copy: z.string(),
  props: z.object({
    alignment: z.enum(['left', 'center', 'right']).optional(),
    variant: z.enum([
      'h1',
      'h2',
      'h3',
      'h4',
      'body',
      'fieldLabel',
      'label',
      'largeBody',
      'stat',
      'statusBlockHeader',
    ]),
  }),
});

const ApplicationFieldSchema = z.discriminatedUnion('type', [
  AddressFieldSchema,
  CalculatedFieldSchema,
  CheckboxFieldSchema,
  CheckboxFieldGroupSchema,
  ComplexFieldSchema,
  DateFieldSchema,
  DocumentFieldSchema,
  DropdownFieldSchema,
  RadioListFieldSchema,
  TextFieldSchema,
  TypographySchema,
]);

const SubFieldSchema = z.discriminatedUnion('type', [
  CalculatedFieldSchema,
  CheckboxFieldSchema,
  DateFieldSchema,
  DocumentFieldSchema,
  DropdownFieldSchema,
  TextFieldSchema,
  TypographySchema,
]);

const ApplicationQuestionSchema = z.object({
  key: z.string().min(1),
  copy: z.object({
    title: z.string().min(1),
    intro: z.string().min(1).optional(),
  }),
  dynamicLogic: DynamicLogicSchema.optional(),
  displayName: z.string().optional(),
  fields: z.array(ApplicationFieldSchema),
  layout: z.enum(['default', 'panel']).optional(),
  skippable: z.boolean().optional(),
});

const ApplicationQuestionGroupSchema = z.object({
  key: z.string().min(1),
  name: z.string().min(1),
  overview: z
    .object({
      title: z.string(),
      description: z.string(),
    })
    .optional(),
  questions: z.array(ApplicationQuestionSchema).nonempty(),
  dynamicLogic: DynamicLogicSchema.optional(),
});

const ApplicationSectionSchema = z.object({
  key: z.string().min(1),
  name: z.string().min(1),
  overview: z.object({
    title: z.string().min(1),
    description: z.string(),
  }),
  questionGroups: z.array(ApplicationQuestionGroupSchema).nonempty(),
});

export const AppConfigSchema = z.object({
  introPages: z.array(IntroPageSchema),
  overrides: z
    .object({
      statuses: z.array(StatusOverrideSchema).optional(),
      submission: z
        .object({
          title: z.string().min(1),
          bullets: z.array(SubmissionBulletSchema),
        })
        .optional(),
    })
    .optional(),
  sections: z.array(ApplicationSectionSchema).nonempty(),
});
