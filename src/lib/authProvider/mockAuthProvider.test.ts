import { describe, expect, it, jest } from '@jest/globals';
import { mockAuthProvider } from './mockAuthProvider';

jest.mock('nookies', () => {
  const cookies: { name?: string } = {};
  return {
    set: jest.fn().mockImplementation((_, name, data) => {
      cookies[name as string] = data;
    }),
    get: jest.fn().mockImplementation((ctx) => {
      return cookies;
    }),
    destroy: jest.fn().mockImplementation((_, name) => {
      delete cookies[name as string];
    }),
  };
});

describe('mockAuthProvider', () => {
  it('should not login with an invalid user', async () => {
    const result = await mockAuthProvider.login({ mockUserRole: 'invalid' });
    expect(result.success).toBe(false);
  });

  it('should login with a valid user', async () => {
    const result = await mockAuthProvider.login({ mockUserRole: 'admin' });
    expect(result.success).toBe(true);
    expect(result.redirectTo).toBe('/');
  });

  it('should check if user is authenticated', async () => {
    const result = await mockAuthProvider.check({});
    expect(result.authenticated).toBe(true);
  });

  it('should get user permissions', async () => {
    const result = await mockAuthProvider?.getPermissions?.();
    expect(result).toEqual(['admin']);
  });

  it('should get user identity', async () => {
    const result = await mockAuthProvider?.getIdentity?.();
    expect(result).toEqual({
      name: 'Admin User',
      email: '<EMAIL>',
      roles: ['admin'],
      avatar: 'https://i.pravatar.cc/150',
    });
  });

  it('should logout successfully', async () => {
    const result = await mockAuthProvider.logout({});
    expect(result.success).toBe(true);
    expect(result.redirectTo).toBe('/login');
  });

  it('should check if user is unauthenticated', async () => {
    const result = await mockAuthProvider.check({});
    expect(result.authenticated).toBe(false);
    expect(result.logout).toBe(true);
    expect(result.redirectTo).toBe('/login');
  });

  it('should handle errors', async () => {
    const error = new Error('Test error');
    const result = await mockAuthProvider.onError(error);
    expect(result.error).toBe(error);
  });
});
