import type { GetServerSidePropsContext } from 'next';
import type { IAPAuthResponse } from '../../pages/api/iapauth';

let fetchInProgress: Promise<Response> | undefined;

/**
 * Custom hook that fetches IAP authentication status from the API endpoint.
 * @returns An object containing the authentication status and user details
 */
export const fetchIAPAuth = async (
  ctx?: GetServerSidePropsContext,
): Promise<IAPAuthResponse | null> => {
  let urlBase = '';

  // TODO: just use the logic in the endpoint directly when SSR
  if (ctx?.req) {
    const req = ctx.req;
    // the value of x-forwarded-proto header ends up being a list: 'https,http'
    // but its presence alone indicates that the request was made over https
    const proto = req?.headers?.['x-forwarded-proto'] ? 'https' : 'http';

    const host = req?.headers?.['x-forwarded-host'] ?? req?.headers?.host;
    urlBase = `${proto}://${host}`;
  }
  const getAuth = async () => {
    if (!fetchInProgress) {
      fetchInProgress = fetch(`${urlBase}/api/iapauth`);
    }
    const fetchResponse = await fetchInProgress;
    fetchInProgress = undefined;
    return fetchResponse;
  };

  const data = (await getAuth()).clone().json();

  return data;
};
