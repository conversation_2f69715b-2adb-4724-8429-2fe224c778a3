import type { AuthBindings } from '@refinedev/core';
import type { NextPageContext } from 'next';
import nookies from 'nookies';

export const mockUsers = {
  admin: {
    name: 'Admin User',
    email: '<EMAIL>',
    roles: ['admin'],
    avatar: 'https://i.pravatar.cc/150',
  },
  pst: {
    name: 'PST User',
    email: '<EMAIL>',
    roles: ['pst'],
    avatar: 'https://i.pravatar.cc/150?img=1',
  },
};

export const mockAuthProvider: AuthBindings = {
  login: async ({ mockUserRole }) => {
    const user = mockUsers[mockUserRole];

    if (user) {
      nookies.set(null, 'devauth', JSON.stringify(user), {
        maxAge: 30 * 24 * 60 * 60,
        path: '/',
      });

      return {
        success: true,
        redirectTo: '/',
      };
    }
    return {
      success: false,
    };
  },
  logout: async () => {
    nookies.destroy(null, 'devauth');

    return {
      success: true,
      redirectTo: '/login',
    };
  },
  check: async (ctx: NextPageContext) => {
    const authCookie = nookies.get(ctx).devauth;
    if (authCookie) {
      return {
        authenticated: true,
      };
    }

    return {
      authenticated: false,
      logout: true,
      redirectTo: '/login',
    };
  },
  getPermissions: async () => {
    const auth = nookies.get().devauth;

    if (auth) {
      const parsedUser = JSON.parse(auth);
      return parsedUser.roles;
    }

    return null;
  },
  getIdentity: async () => {
    const auth = nookies.get().devauth;

    if (auth) {
      const parsedUser = JSON.parse(auth);
      return parsedUser;
    }

    return null;
  },
  onError: async (error) => {
    return { error };
  },
};
