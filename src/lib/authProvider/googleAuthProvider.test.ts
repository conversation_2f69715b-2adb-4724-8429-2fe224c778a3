import { afterAll, beforeEach, describe, expect, it, jest } from '@jest/globals';
import { googleAuthProvider } from './googleAuthProvider';

jest.mock('nookies', () => {
  const cookies: { name?: string } = {};
  return {
    set: jest.fn().mockImplementation((_, name, data) => {
      cookies[name as string] = data;
    }),
    get: jest.fn().mockImplementation((ctx) => {
      return cookies;
    }),
    destroy: jest.fn().mockImplementation((_, name) => {
      delete cookies[name as string];
    }),
  };
});

const AUTHENTICATED_IAP_RESPONSE = {
  authenticated: true,
  tokenPayload: {
    exp: Math.floor(Date.now() / 1000) + 360,
    iss: '1234',
    sub: '1234',
    aud: '1234',
    iat: 1234,
  },
  user: {
    id: '123',
    name: 'first last',
    email: '<EMAIL>',
    roles: ['admin'],
  },
};

describe('googleAuthProvider', () => {
  let mockFetch: jest.Mock;

  beforeEach(() => {
    mockFetch = jest.fn();
    // @ts-ignore
    global.fetch = jest.fn(() =>
      Promise.resolve({
        clone: () => ({
          json: mockFetch,
        }),
      }),
    ) as jest.Mock;
  });

  afterAll(() => {
    jest.resetAllMocks();
  });

  describe('login', () => {
    it('should return authenticated true and redirectTo / when user is authenticated and logs in', async () => {
      mockFetch.mockReturnValueOnce(AUTHENTICATED_IAP_RESPONSE);

      const result = await googleAuthProvider.login({});

      expect(result.success).toBe(true);
      expect(result.redirectTo).toBe('/');
    });

    it('should return authenticated false when user is not authenticated', async () => {
      mockFetch.mockReturnValueOnce({ authenticated: false });

      googleAuthProvider.logout({});
      const result = await googleAuthProvider.login({});

      expect(result.success).toBe(false);
      expect(result.error).toEqual({
        name: 'LoginError',
        message: 'You may not have an account on this application',
      });
    });
  });
  describe('check', () => {
    it('should return authenticated true when user is authenticated', async () => {
      mockFetch.mockReturnValueOnce(AUTHENTICATED_IAP_RESPONSE);

      const result = await googleAuthProvider.check({});

      expect(result).toStrictEqual({
        authenticated: true,
      });
    });

    it('should return authenticated false and redirect to /login when user is not logged in', async () => {
      googleAuthProvider.logout({});
      mockFetch.mockReturnValueOnce({ authenticated: false });

      const result = await googleAuthProvider.check({});

      expect(result).toStrictEqual({
        authenticated: false,
        logout: true,
        redirectTo: '/login',
      });
    });
  });
  describe('getPermissions', () => {
    it('should return roles if user is authenticated', async () => {
      mockFetch.mockReturnValueOnce(AUTHENTICATED_IAP_RESPONSE);

      await googleAuthProvider.login({});
      const result = await googleAuthProvider.getPermissions?.();

      expect(result).toStrictEqual(['admin']);
    });

    it('should return empty roles list if user record is not found', async () => {
      await googleAuthProvider.logout({});
      mockFetch.mockReturnValueOnce({
        ...AUTHENTICATED_IAP_RESPONSE,
        user: undefined,
      });

      await googleAuthProvider.login({});
      const result = await googleAuthProvider.getPermissions?.();

      expect(result).toStrictEqual([]);
    });
  });

  describe('getIdentity', () => {
    it('should return the user object when authenticated', async () => {
      await googleAuthProvider.logout({});
      mockFetch.mockReturnValueOnce(AUTHENTICATED_IAP_RESPONSE);

      await googleAuthProvider.login({});
      const result = await googleAuthProvider.getIdentity?.();

      expect(result).toStrictEqual({
        id: '123',
        name: 'first last',
        email: '<EMAIL>',
        roles: ['admin'],
      });
    });
    it('should return null if authenticated but there is no user object in the response', async () => {
      await googleAuthProvider.logout({});
      const iapResponse = {
        ...AUTHENTICATED_IAP_RESPONSE,
        user: undefined,
      };
      mockFetch.mockReturnValueOnce(iapResponse);

      const result = await googleAuthProvider.getIdentity?.();

      expect(result).toStrictEqual(null);
    });

    it('should return null if authenticated is false', async () => {
      await googleAuthProvider.logout({});
      const iapResponse = {
        authenticated: false,
      };
      mockFetch.mockReturnValueOnce(iapResponse);

      const result = await googleAuthProvider.getIdentity?.();

      expect(result).toStrictEqual(null);
    });
  });
});
