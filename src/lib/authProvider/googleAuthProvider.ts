import type { AuthBindings } from '@refinedev/core';
import * as Sentry from '@sentry/browser';
import { fetchIAPAuth } from 'lib/authProvider/fetchIAPAuth';
import type { GetServerSidePropsContext } from 'next';

import nookies from 'nookies';
import type { IAPAuthResponse } from 'pages/api/iapauth';

const getLoggedInUserOrReAuth = async (): Promise<IAPAuthResponse['user'] | null> => {
  const user = nookies.get().auth;

  if (user) {
    return JSON.parse(user);
  }

  const iapResponse = await fetchIAPAuth();

  if (iapResponse?.authenticated && iapResponse.user) {
    const cookieSetSuccess = setAuthCookieFromIAPAuthResponse(iapResponse);
    return cookieSetSuccess ? iapResponse.user : null;
  }

  return null;
};

const setAuthCookieFromIAPAuthResponse = (iapResponse: IAPAuthResponse): boolean => {
  if (iapResponse.authenticated && iapResponse.tokenPayload && iapResponse.user) {
    const maxAge = iapResponse.tokenPayload.exp - Math.floor(Date.now() / 1000);

    nookies.set(null, 'auth', JSON.stringify(iapResponse.user), {
      maxAge,
      sameSite: 'lax',
    });

    return maxAge > 0;
  }
  return false;
};

export const googleAuthProvider: AuthBindings = {
  login: async () => {
    const iapResponse = await fetchIAPAuth();
    const user = iapResponse?.user;

    if (iapResponse?.authenticated === true && user && iapResponse?.tokenPayload) {
      Sentry.setUser({ email: user.email });

      setAuthCookieFromIAPAuthResponse(iapResponse);

      return {
        success: true,
        redirectTo: '/',
      };
    }

    Sentry.setUser(null);
    // console.warn(iapResponse?.error || iapResponse);
    return {
      success: false,
      error: {
        name: 'LoginError',
        message: 'You may not have an account on this application',
      },
    };
  },
  logout: async () => {
    nookies.destroy(null, 'auth');
    Sentry.setUser(null);

    return {
      success: true,
      redirectTo: '/login',
    };
  },
  check: async (ctx: GetServerSidePropsContext) => {
    const user = await getLoggedInUserOrReAuth();
    const authenticated = user !== null;

    if (authenticated) {
      return {
        authenticated: true,
      };
    }

    return {
      authenticated: false,
      logout: true,
      redirectTo: '/login',
    };
  },
  getPermissions: async (): Promise<string[]> => {
    const user = await getLoggedInUserOrReAuth();

    if (user?.roles) {
      return user.roles;
    }

    return [];
  },
  getIdentity: async (ctx?: GetServerSidePropsContext): Promise<IAPAuthResponse['user'] | null> => {
    const user = await getLoggedInUserOrReAuth();

    if (user) {
      return user;
    }

    return null;
  },
  onError: async (error) => {
    return { error };
  },
};
