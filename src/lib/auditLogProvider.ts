import type { AuditLogProvider } from '@refinedev/core';
import graphqlDataProvider, { GraphQLClient } from 'lib/graphileDataProvider';

const dataProvider = graphqlDataProvider(new GraphQLClient('/api/graphql/sorcery'));

const auditLogProvider: AuditLogProvider = {
  get: async () => {
    return Promise.reject();
  },
  create: async (params) => {
    return dataProvider.create({
      resource: 'auditLog',
      variables: params,
    });
  },
  update: async () => {
    return Promise.reject();
  },
};

export { auditLogProvider };
