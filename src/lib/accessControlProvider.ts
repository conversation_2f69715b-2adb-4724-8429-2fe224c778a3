import type { AccessControlProvider } from '@refinedev/core';
import { authProvider } from './authProvider';

export const accessControlProvider: AccessControlProvider = {
  can: async ({ resource, action }) => {
    const roles = (await authProvider.getPermissions?.()) || [];
    const role = roles[0]; // use first role for now

    const response = await fetch('/api/access', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ role }),
      next: { revalidate: 3600 },
    });
    const access = await response.json();
    if (resource && access?.[resource.toLocaleLowerCase()]?.includes(action)) {
      return { can: true };
    }

    return {
      can: false,
      reason: 'Unauthorized',
    };
  },
};
