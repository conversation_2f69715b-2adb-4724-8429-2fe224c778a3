import type { BaseRecord, DataProvider } from '@refinedev/core';

import * as gql from 'gql-query-builder';
import { GraphQLClient } from 'graphql-request';
import { camelCase, startCase } from 'lodash';
import { generateFilter, generatePatch, generateSort } from '../utils';
const pluralize = require('pluralize');

const camelCaseToPascalCase = (str: string) => str.charAt(0).toUpperCase() + str.slice(1);

/* Custom DataProvider for Postgraphile */
const dataProvider = (client: GraphQLClient): Required<DataProvider> => {
  /*
   * This function is used to flatten the response from the GraphQL API.
   * It removes the edges and nodes properties from the response and flattens the data.
   * This makes it easier to use the data in the UI.
   * Example input: { edges: [{ node: { id: 1, name: 'Test' } }] }
   * Example output: [{ id: 1, name: 'Test' }]
   */

  // biome-ignore lint/suspicious/noExplicitAny: can be any type
  const getFlatResponse = (originalResponse: any) => {
    let flatResponse = originalResponse;

    if (flatResponse === undefined || flatResponse === null) return flatResponse;

    // biome-ignore lint/suspicious/noPrototypeBuiltins: IGNORE THIS
    if (originalResponse?.hasOwnProperty('edges')) {
      flatResponse = originalResponse.edges.flatMap((edge: BaseRecord) => edge.node);
    }
    // biome-ignore lint/suspicious/noPrototypeBuiltins: IGNORE THIS
    if (originalResponse?.hasOwnProperty('nodes')) {
      flatResponse = originalResponse.nodes;
    }

    // for lists, loop through all arrays and flatten them
    for (let i = 0; i < flatResponse?.length; i++) {
      if (typeof flatResponse[i] === 'object' && !Array.isArray(flatResponse[i])) {
        for (const key in flatResponse[i]) {
          if (
            // biome-ignore lint/suspicious/noPrototypeBuiltins: IGNORE THIS
            flatResponse[i][key]?.hasOwnProperty('edges') ||
            // biome-ignore lint/suspicious/noPrototypeBuiltins: IGNORE THIS
            flatResponse[i][key]?.hasOwnProperty('nodes')
          ) {
            flatResponse[i][key] = getFlatResponse(flatResponse[i][key]);
          }
        }
      }
    }

    // for non-lists, loop through all objects and flatten them
    if (flatResponse)
      for (const key in flatResponse) {
        if (
          // biome-ignore lint/suspicious/noPrototypeBuiltins: IGNORE THIS
          flatResponse[key]?.hasOwnProperty('edges') ||
          // biome-ignore lint/suspicious/noPrototypeBuiltins: IGNORE THIS
          flatResponse[key]?.hasOwnProperty('nodes')
        ) {
          flatResponse[key] = getFlatResponse(flatResponse[key]);
        }
      }

    return flatResponse;
  };

  return {
    getList: async ({ resource, pagination, sorters, filters, meta }) => {
      const { current = 1, pageSize = 10, mode = 'server' } = pagination ?? {};

      const fields = meta?.fields || [];
      const sortBy = generateSort(sorters);
      const filterBy = generateFilter(filters);

      const camelResource = camelCase(resource);
      const pascalResource = camelCaseToPascalCase(camelResource);
      const orderByType = `[${pascalResource.replace(/^(.*)List$/, '$1')}OrderBy!]`;

      const operation = meta?.operation ?? camelResource;

      // biome-ignore lint/suspicious/noPrototypeBuiltins: IGNORE THIS
      if (!pascalResource.endsWith('List') && !fields?.hasOwnProperty('totalCount')) {
        fields?.push('totalCount');
      }

      const { query, variables } = gql.query({
        operation,
        variables: {
          ...meta?.variables,
          orderBy: { value: sortBy, type: orderByType },
          ...(Object.keys(filterBy).length
            ? {
                filter: {
                  value: filterBy,
                  type: `${pluralize.singular(pascalResource.replace(/^(.*)List$/, '$1'))}Filter`,
                },
              }
            : {}),
          ...(mode === 'server'
            ? {
                first: pageSize,
                offset: (current - 1) * pageSize,
              }
            : {}),
        },
        fields: [...fields],
      });

      const response = await client.request<BaseRecord>(query, variables);

      return {
        data: getFlatResponse(response[operation]),
        total: response[operation].totalCount,
      };
    },

    getMany: async ({ resource, ids, meta }) => {
      const camelResource = camelCase(resource);
      const pascalResource = camelCaseToPascalCase(camelResource);

      const operation = meta?.operation ?? camelResource;
      const filterType = `${pluralize.singular(pascalResource.replace(/^(.*)List$/, '$1'))}Filter`;

      const { query, variables } = gql.query({
        operation,
        variables: {
          filter: {
            value: {
              id: { in: ids },
            },
            type: filterType,
          },
        },

        fields: meta?.fields,
      });

      const response = await client.request<BaseRecord>(query, variables);

      return {
        data: response[operation],
      };
    },

    create: async ({ resource, variables, meta }) => {
      const singularResource = pluralize.singular(camelCase(resource));
      const camelCreateName = camelCase(`create-${singularResource}`);

      const operation = meta?.operation ?? camelCreateName;
      const inputType = meta?.inputType ?? `${startCase(camelCreateName).replace(/ /g, '')}Input!`;

      const { query, variables: gqlVariables } = gql.mutation({
        operation,
        variables: {
          input: {
            value: { [`${singularResource}`]: variables },
            type: inputType,
          },
        },
        fields: [
          {
            operation: singularResource,
            fields: [...(meta?.fields ?? ['id'])],
            variables: {},
          },
        ],
      });
      const response = await client.request<BaseRecord>(query, gqlVariables);

      return {
        data: getFlatResponse(response[operation][singularResource]),
      };
    },

    // TODO: Implement me? https://www.graphile.org/postgraphile/v4-new-features/#graphql-query-batching
    createMany: async ({ resource, variables, meta }) => {
      throw new Error('createMany not implemented');
      // const singularResource = pluralize.singular(resource);
      // const camelCreateName = camelCase(`create-${singularResource}`);

      // const operation = meta?.operation ?? camelCreateName;
      // const inputType = meta?.operation ?? `${camelCreateName}Input`;

      // const response = await Promise.all(
      //   variables.map(async (param) => {
      //     const { query, variables: gqlVariables } = gql.mutation({
      //       operation,
      //       variables: {
      //         input: {
      //           value: { data: param },
      //           type: inputType,
      //         },
      //       },
      //       fields: meta?.fields ?? [
      //         {
      //           operation: singularResource,
      //           fields: ["id"],
      //           variables: {},
      //         },
      //       ],
      //     });
      //     const result = await client.request<BaseRecord>(query, gqlVariables);

      //     return result[operation][singularResource];
      //   })
      // );
      // return {
      //   data: response,
      // };
    },

    update: async ({ resource, id, variables, meta }) => {
      const singularResource = pluralize.singular(camelCase(resource));
      const camelUpdateName = camelCase(`update-${singularResource}`);

      const operation = meta?.operation ?? camelUpdateName;
      const inputType = meta?.operation ?? `${startCase(camelUpdateName).replace(/ /g, '')}Input!`;

      const patch = generatePatch(variables);

      const { query, variables: gqlVariables } = gql.mutation({
        operation,
        variables: {
          input: {
            value: { id, patch },
            type: inputType,
          },
        },
        fields: [
          {
            operation: singularResource,
            fields: [...(meta?.fields ?? ['id'])],
            variables: {},
          },
        ],
      });
      const response = await client.request<BaseRecord>(query, gqlVariables);

      return {
        data: getFlatResponse(response[operation][singularResource]),
      };
    },

    updateMany: async () => {
      throw new Error('updateMany not implemented');
    },

    getOne: async ({ resource, id, meta }) => {
      const singularResource = pluralize.singular(resource);
      const camelResource = camelCase(singularResource);

      const operation = meta?.operation ?? camelResource;

      const { query, variables } = gql.query({
        operation,
        variables: {
          id: { value: id, type: 'UUID', required: true },
        },
        fields: meta?.fields,
      });

      const response = await client.request<BaseRecord>(query, variables);

      return {
        data: getFlatResponse(response[operation]),
      };
    },

    deleteOne: async ({ resource, id, meta }) => {
      const singularResource = pluralize.singular(resource);
      const camelDeleteName = camelCase(`delete-${singularResource}`);

      const operation = meta?.operation ?? camelDeleteName;
      const inputType = meta?.operation ?? `${startCase(camelDeleteName).replace(/ /g, '')}Input!`;

      const { query, variables } = gql.mutation({
        operation,
        variables: {
          input: {
            value: { id },
            type: inputType,
          },
        },
        fields: meta?.fields ?? [
          {
            operation: camelCase(singularResource),
            fields: ['id'],
            variables: {},
          },
        ],
      });

      const response = await client.request<BaseRecord>(query, variables);

      return {
        data: getFlatResponse(response[operation][singularResource]),
      };
    },

    deleteMany: async () => {
      throw new Error('deleteMany not implemented');
    },

    getApiUrl: () => {
      throw new Error('Not implemented on refine-graphql data provider.');
    },

    custom: async ({ url, method, headers, meta }) => {
      let gqlClient = client;

      if (url) {
        gqlClient = new GraphQLClient(url, { headers });
      }

      if (meta) {
        if (meta.operation) {
          if (method === 'get') {
            const { query, variables } = gql.query({
              operation: meta.operation,
              fields: meta.fields,
              variables: meta.variables,
            });

            const response = await gqlClient.request<BaseRecord>(query, variables);

            return {
              data: response[meta.operation],
            };
          }
          const { query, variables } = gql.mutation({
            operation: meta.operation,
            fields: meta.fields,
            variables: meta.variables,
          });

          const response = await gqlClient.request<BaseRecord>(query, variables);

          return {
            data: response[meta.operation],
          };
        }
        throw new Error('GraphQL operation name required.');
      }
      throw new Error('GraphQL need to operation, fields and variables values in meta object.');
    },
  };
};

export default dataProvider;
