import { describe, expect, it, jest } from '@jest/globals';
import type { GraphQLClient } from 'graphql-request';
import dataProvider from '.';

// biome-ignore lint/suspicious/noExplicitAny: any records
type RequestFnType = () => Promise<any>;

describe('dataProvider', () => {
  describe('createMany', () => {
    it('expect to throw an error', async () => {
      await expect(
        dataProvider({} as unknown as GraphQLClient).createMany({
          resource: 'posts',
          variables: [
            {
              title: 'foo',
              content: 'bar',
              category: '2',
            },
            {
              title: 'foo-2',
              content: 'bar-2',
              category: '3',
            },
          ],
          meta: {
            fields: [
              {
                operation: 'post',
                fields: ['id', 'title', 'content', { category: ['id'] }],
                variables: {},
              },
            ],
          },
        }),
      ).rejects.toThrow('createMany not implemented');
    });
  });

  describe('deleteMany', () => {
    it('expect to throw an error', async () => {
      await expect(
        dataProvider({} as unknown as GraphQLClient).deleteMany({
          resource: 'posts',
          ids: ['37', '38'],
          meta: {
            fields: [
              {
                operation: 'post',
                fields: ['id', 'title'],
                variables: {},
              },
            ],
          },
        }),
      ).rejects.toThrow('deleteMany not implemented');
    });
  });

  describe('updateMany', () => {
    it('expect to throw an error', async () => {
      await expect(
        dataProvider({} as unknown as GraphQLClient).updateMany({
          resource: 'posts',
          ids: ['24', '25'],
          variables: {
            title: 'updated-foo',
            content: 'updated-bar',
            category: '2',
          },

          meta: {
            fields: [
              {
                operation: 'post',
                fields: ['id', 'title', 'content', { category: ['id'] }],
                variables: {},
              },
            ],
          },
        }),
      ).rejects.toThrow('updateMany not implemented');
    });
  });

  describe('getMany', () => {
    it('correct response with meta', async () => {
      const mockRequest = jest.fn<RequestFnType>().mockResolvedValueOnce({
        posts: [
          {
            id: 'mockId1',
            title: 'mockTitle',
            content: 'mockContent',
            category: { id: 'mockCategoryId1' },
          },
          {
            id: 'mockId2',
            title: 'mockTitle',
            content: 'mockContent',
            category: { id: 'mockCategoryId2' },
          },
        ],
      });
      const provider = dataProvider({
        request: mockRequest,
      } as unknown as GraphQLClient);
      const { data } = await provider.getMany({
        resource: 'posts',
        ids: ['mockId1', 'mockId2'],
        meta: {
          fields: ['id', 'title', 'content', { category: ['id'] }],
        },
      });

      expect(mockRequest).toHaveBeenCalledWith(expect.stringMatching('query'), {
        filter: { id: { in: ['mockId1', 'mockId2'] } },
      });

      expect(data).toHaveLength(2);
      expect(Object.keys(data[0])).toEqual(['id', 'title', 'content', 'category']);
    });
  });

  describe('create', () => {
    it('correct response with meta', async () => {
      const mockRequest = jest.fn<RequestFnType>().mockResolvedValueOnce({
        createPost: {
          post: {
            id: 'mockCreatedId',
            title: 'mockTitle',
            content: 'mockContent',
            category: { id: 'mockCategoryId1' },
          },
        },
      });
      const provider = dataProvider({
        request: mockRequest,
      } as unknown as GraphQLClient);
      const { data } = await provider.create({
        resource: 'posts',
        variables: {
          title: 'mockTitle',
          content: 'mockContent',
        },
        meta: {
          fields: ['id', 'title', 'content', { category: ['id'] }],
        },
      });

      expect(mockRequest).toHaveBeenCalledWith(
        expect.stringContaining('mutation ($input: CreatePostInput!)'),
        {
          input: { post: { content: 'mockContent', title: 'mockTitle' } },
        },
      );
      expect(data.id).toEqual('mockCreatedId');
    });

    it('correct response without meta', async () => {
      const mockRequest = jest.fn<RequestFnType>().mockResolvedValueOnce({
        createPost: {
          post: {
            id: 'mockCreatedId',
            title: 'mockTitle',
            content: 'mockContent',
            category: { id: 'mockCategoryId1' },
          },
        },
      });
      const provider = dataProvider({
        request: mockRequest,
      } as unknown as GraphQLClient);
      const { data } = await provider.create({
        resource: 'posts',
        variables: {
          title: 'mockTitle',
          content: 'mockContent',
        },
      });
      expect(mockRequest).toHaveBeenCalledWith(expect.stringContaining('post  { id }'), {
        input: { post: { content: 'mockContent', title: 'mockTitle' } },
      });
      expect(data.id).toEqual('mockCreatedId');
    });
  });

  describe('custom', () => {
    it('correct query response', async () => {
      const mockRequest = jest.fn<RequestFnType>().mockResolvedValueOnce({
        posts: [
          {
            id: 'mockId',
            title: 'mockTitle',
          },
        ],
      });
      const provider = dataProvider({
        request: mockRequest,
      } as unknown as GraphQLClient);
      const response = await provider.custom({
        url: '',
        method: 'get',
        meta: {
          operation: 'posts',
          variables: {
            orderBy: 'TITLE_ASC',
            filter: { value: { title: { includes: 'title' } }, type: 'JSON' },
          },
          fields: ['id', 'title', { category: ['id'] }],
        },
      });

      expect(mockRequest).toHaveBeenCalledWith(
        expect.stringContaining(
          'query ($orderBy: String, $filter: JSON) { posts (orderBy: $orderBy, filter: $filter) { id, title, category { id } } }',
        ),
        {
          orderBy: 'TITLE_ASC',
          filter: { title: { includes: 'title' } },
        },
      );
      expect(response?.data).toMatchObject([
        expect.objectContaining({
          id: 'mockId',
          title: 'mockTitle',
        }),
      ]);
    });

    it('correct mutation response', async () => {
      const mockRequest = jest.fn<RequestFnType>().mockResolvedValueOnce({
        updatePost: {
          post: {
            id: 'mockId',
            title: 'updatedTitle',
          },
        },
      });
      const provider = dataProvider({
        request: mockRequest,
      } as unknown as GraphQLClient);
      const response = await provider.custom({
        url: '',
        method: 'post',
        meta: {
          operation: 'updatePost',
          variables: {
            id: 'mockId',
            input: {
              value: {
                patch: { title: 'updatedTitle' },
              },
              type: 'updatePostInput',
            },
          },
          fields: [
            {
              operation: 'post',
              fields: ['id', 'title'],
              variables: {},
            },
          ],
        },
      });

      expect(mockRequest).toHaveBeenCalledWith(
        expect.stringContaining('updatePost (id: $id, input: $input)'),
        {
          id: 'mockId',
          input: {
            patch: { title: 'updatedTitle' },
          },
        },
      );
      expect(response).toEqual({ data: { post: { id: 'mockId', title: 'updatedTitle' } } });
    });

    it('to throw if meta is not defined', async () => {
      const mockRequest = jest.fn<RequestFnType>();
      const provider = dataProvider({ request: mockRequest } as unknown as GraphQLClient);

      await expect(
        provider.custom({
          url: '',
          method: 'post',
        }),
      ).rejects.toThrow('GraphQL need to operation, fields and variables values in meta object.');

      expect(mockRequest).not.toHaveBeenCalled();
    });

    it('to throw if operation name is not defined', async () => {
      const mockRequest = jest.fn<RequestFnType>();
      const provider = dataProvider({ request: mockRequest } as unknown as GraphQLClient);
      await expect(
        provider.custom({
          url: '',
          method: 'post',
          meta: {
            operation: '',
          },
        }),
      ).rejects.toThrow('GraphQL operation name required.');

      expect(mockRequest).not.toHaveBeenCalled();
    });
  });

  describe('deleteOne', () => {
    it('correct response with meta', async () => {
      const mockRequest = jest.fn<RequestFnType>().mockResolvedValueOnce({
        deletePost: {
          post: {
            id: 'mockId',
          },
        },
      });
      const provider = dataProvider({ request: mockRequest } as unknown as GraphQLClient);

      const { data } = await provider.deleteOne({
        resource: 'posts',
        id: 'mockId',
        meta: {
          fields: [
            {
              operation: 'post',
              fields: ['id', 'title'],
              variables: {},
            },
          ],
        },
      });

      expect(mockRequest).toHaveBeenCalledWith(
        expect.stringContaining('deletePost (input: $input)'),
        { input: { id: 'mockId' } },
      );
      expect(data.id).toEqual('mockId');
    });
  });

  describe('getList', () => {
    it('correct response', async () => {
      const mockRequest = jest.fn<RequestFnType>().mockResolvedValueOnce({
        posts: [
          {
            id: 'mockId',
          },
        ],
      });
      const provider = dataProvider({ request: mockRequest } as unknown as GraphQLClient);

      const { data } = await provider.getList({
        resource: 'posts',
        meta: {
          fields: ['id', 'title'],
        },
      });

      expect(mockRequest).toHaveBeenCalledWith(
        'query ($orderBy: [PostsOrderBy!], $first: Int, $offset: Int) { posts (orderBy: $orderBy, first: $first, offset: $offset) { id, title, totalCount } }',
        { first: 10, offset: 0, orderBy: [] },
      );
      expect(data).toHaveLength(1);
    });

    it('correct sorting response', async () => {
      const mockRequest = jest.fn<RequestFnType>().mockResolvedValueOnce({
        posts: [
          {
            id: 'mockId',
          },
        ],
      });
      const provider = dataProvider({ request: mockRequest } as unknown as GraphQLClient);

      const { data } = await provider.getList({
        resource: 'posts',
        sorters: [
          {
            field: 'title',
            order: 'asc',
          },
        ],
        meta: {
          fields: ['id', 'title'],
        },
      });

      expect(mockRequest).toHaveBeenCalledWith(
        'query ($orderBy: [PostsOrderBy!], $first: Int, $offset: Int) { posts (orderBy: $orderBy, first: $first, offset: $offset) { id, title, totalCount } }',
        { first: 10, offset: 0, orderBy: ['TITLE_ASC'] },
      );
      expect(data).toHaveLength(1);
    });

    it('correct filter response', async () => {
      const mockRequest = jest.fn<RequestFnType>().mockResolvedValueOnce({
        posts: [
          {
            id: 'mockId',
          },
        ],
      });
      const provider = dataProvider({ request: mockRequest } as unknown as GraphQLClient);

      const { data } = await provider.getList({
        resource: 'posts',
        filters: [
          {
            field: 'id',
            operator: 'eq',
            value: 'mockId',
          },
        ],
        meta: {
          fields: ['id', 'title'],
        },
      });

      expect(mockRequest).toHaveBeenCalledWith(
        'query ($orderBy: [PostsOrderBy!], $filter: PostFilter, $first: Int, $offset: Int) { posts (orderBy: $orderBy, filter: $filter, first: $first, offset: $offset) { id, title, totalCount } }',
        {
          filter: {
            id: {
              equalTo: 'mockId',
            },
          },
          first: 10,
          offset: 0,
          orderBy: [],
        },
      );
      expect(data).toHaveLength(1);
    });

    it('correct filter and sort response', async () => {
      const mockRequest = jest.fn<RequestFnType>().mockResolvedValueOnce({
        posts: [
          {
            id: 'mockId',
          },
        ],
      });
      const provider = dataProvider({ request: mockRequest } as unknown as GraphQLClient);

      const { data } = await provider.getList({
        resource: 'posts',
        filters: [
          {
            field: 'title',
            operator: 'contains',
            value: 'title',
          },
        ],
        sorters: [
          {
            field: 'title',
            order: 'asc',
          },
        ],
        meta: {
          fields: ['id', 'title', { category: ['id', 'title'] }],
        },
      });

      expect(mockRequest).toHaveBeenCalledWith(
        'query ($orderBy: [PostsOrderBy!], $filter: PostFilter, $first: Int, $offset: Int) { posts (orderBy: $orderBy, filter: $filter, first: $first, offset: $offset) { id, title, category { id, title }, totalCount } }',
        {
          filter: {
            title: {
              includesInsensitive: 'title',
            },
          },
          first: 10,
          offset: 0,
          orderBy: ['TITLE_ASC'],
        },
      );
      expect(data).toHaveLength(1);
    });
  });

  describe('useOne', () => {
    it('correct response with meta', async () => {
      const mockRequest = jest.fn<RequestFnType>().mockResolvedValueOnce({
        post: {
          id: 'mockId',
        },
      });
      const provider = dataProvider({ request: mockRequest } as unknown as GraphQLClient);

      const { data } = await provider.getOne({
        resource: 'posts',
        id: 'mockId',
        meta: {
          fields: ['id'],
        },
      });

      expect(mockRequest).toHaveBeenCalledWith(
        expect.stringContaining('query ($id: UUID!) { post (id: $id) { id } }'),
        { id: 'mockId' },
      );
      expect(data.id).toEqual('mockId');
    });
  });

  describe('update', () => {
    it('correct response with meta', async () => {
      const mockRequest = jest.fn<RequestFnType>().mockResolvedValueOnce({
        updatePost: {
          post: {
            id: 'mockId',
          },
        },
      });
      const provider = dataProvider({ request: mockRequest } as unknown as GraphQLClient);

      const { data } = await provider.update({
        resource: 'posts',
        id: 'mockId',
        variables: {
          title: 'updatedTitle',
        },
        meta: {
          fields: [
            {
              operation: 'post',
              fields: ['id'],
              variables: {},
            },
          ],
        },
      });

      expect(mockRequest).toHaveBeenCalledWith(
        expect.stringContaining('mutation ($input: UpdatePostInput!)'),
        { input: { id: 'mockId', patch: { title: 'updatedTitle' } } },
      );
      expect(data.id).toEqual('mockId');
    });
  });
});
