import { describe, expect, it } from '@jest/globals';
import type { CrudFilters } from '@refinedev/core';
import { generateFilter } from '.';

describe('generateFilter', () => {
  it('should return an empty object if no filters are provided', () => {
    expect(generateFilter()).toEqual({});
  });

  it('should return correct queryFilters for given filters', () => {
    const filters: CrudFilters = [
      { field: 'name', operator: 'eq', value: '<PERSON>' },
      { field: 'age', operator: 'gte', value: 18 },
      { field: 'city', operator: 'ne', value: 'New York' },
      {
        operator: 'or',
        value: [
          { field: 'status', operator: 'eq', value: 'active' },
          { field: 'status', operator: 'eq', value: 'pending' },
        ],
      },
    ];

    const expectedResult = {
      name: { equalTo: 'John' },
      age: { greaterThanOrEqualTo: 18 },
      city: { notEqualTo: 'New York' },
      or: [{ status: { equalTo: 'active' } }, { status: { equalTo: 'pending' } }],
    };

    expect(generateFilter(filters)).toEqual(expectedResult);
  });
});
