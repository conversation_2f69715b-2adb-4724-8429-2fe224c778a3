import type { <PERSON><PERSON><PERSON>, Meta<PERSON>uery } from '@refinedev/core';
import camelCase from 'camelcase';
import * as gql from 'gql-query-builder';

type GenerateUseManySubscriptionParams = {
  resource: string;
  meta: MetaQuery;
  ids?: BaseKey[];
};

type GenerateUseManySubscriptionReturnValues = {
  // biome-ignore lint/suspicious/noExplicitAny: IGNORE THIS
  variables: any;
  query: string;
  operation: string;
};

export const generateUseManySubscription = ({
  resource,
  meta,
  ids,
}: GenerateUseManySubscriptionParams): GenerateUseManySubscriptionReturnValues => {
  if (!ids) {
    console.error('[useSubscription]: `ids` is required in `params` for graphql subscriptions');
  }

  const camelResource = camelCase(resource);

  const operation = meta.operation ?? camelResource;

  const { query, variables } = gql.subscription({
    operation,
    variables: {
      where: {
        value: { id_in: ids },
        type: 'JSO<PERSON>',
      },
    },
    fields: meta.fields,
  });

  return { query, variables, operation };
};
