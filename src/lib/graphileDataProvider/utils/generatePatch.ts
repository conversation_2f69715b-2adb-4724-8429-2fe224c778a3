export const generatePatch = (variables) => {
  const updateVariables = { ...variables }; // don't mutate the original variables, it breaks "New Data" in the audit logs

  const keys = Object.keys(updateVariables ?? {});

  const childUpdates = {};

  for (const key of keys) {
    if (updateVariables[key] instanceof Object && !Array.isArray(updateVariables[key])) {
      const { id, ...patchData } = updateVariables[key];

      if (id) {
        childUpdates[key] = {
          updateById: {
            id: id,
            patch: generatePatch(patchData),
          },
        };
        updateVariables[key] = undefined;
      }
    } else if (Array.isArray(variables[key]) && variables[key].some(({ id }) => !!id)) {
      childUpdates[key] = { updateById: [] };

      for (let i = 0; i < updateVariables[key].length; i++) {
        const { id, ...patchData } = variables[key][i];

        childUpdates[key].updateById.push({
          id: id,
          patch: generatePatch(patchData),
        });
      }
    }
  }

  const patch = {
    ...updateVariables,
    ...childUpdates,
  };

  return patch;
};
