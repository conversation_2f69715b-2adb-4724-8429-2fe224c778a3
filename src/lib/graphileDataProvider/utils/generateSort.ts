import type { CrudSorting } from '@refinedev/core';

export const generateSort = (sorters?: CrudSorting) => {
  if (sorters && sorters.length > 0) {
    const sortQuery = sorters.map((i) => {
      const field = i.field
        .replace(/\.?([A-Z])/g, (x, y) => `_${y.toLowerCase()}`)
        .replace(/^_/, '');

      return `${field.toUpperCase()}_${i.order.toUpperCase()}`;
    });

    return sortQuery;
  }

  return [];
};
