import type { CrudFilters } from '@refinedev/core';

/*
Operators to convert:
"eq" |
    "ne" |
    "lt" |
    "gt" |
    "lte" |
    "gte" |
    "in" |
    "nin" |
    "contains" |
    "ncontains" |
    "containss" |
    "ncontainss" |
    "between" |
    "nbetween" |
    "null" |
    "nnull" |
    "startswith" |
    "nstartswith" |
    "startswiths" |
    "nstartswiths" |
    "endswith" |
    "nendswith" |
    "endswiths" |
    "nendswiths";
    */

const operatorMap = {
  eq: 'equalTo',
  ne: 'notEqualTo',
  lt: 'lessThan',
  gt: 'greaterThan',
  lte: 'lessThanOrEqualTo',
  gte: 'greaterThanOrEqualTo',
  in: 'in',
  nin: 'notInInsensitive',
  contains: 'includesInsensitive',
  ncontains: 'notIncludesInsensitive',
  containss: 'includes',
  ncontainss: 'notIncludes',
  //    "between"
  //    "nbetween"
  null: 'isNull',
  //    "nnull": ""
  startswith: 'startsWithInsensitive',
  nstartswith: 'notStartsWithInsensitive',
  startswiths: 'startsWith',
  nstartswiths: 'notStartsWith',
  endswith: 'endsWithInsensitive',
  nendswith: 'notEndsWithInsensitive',
  endswiths: 'endsWith',
  nendswiths: 'notEndsWith',
};

const getFilterObj = (fieldParts, operator, value) => {
  if (fieldParts.length === 0) {
    return {
      [`${operatorMap[operator]}`]: value,
    };
  }

  return {
    [`${fieldParts[0]}`]: getFilterObj(fieldParts.slice(1), operator, value),
  };
};

export const generateFilter = (filters?: CrudFilters) => {
  // biome-ignore lint/suspicious/noExplicitAny: IGNORE THIS
  const queryFilters: { [key: string]: any } = {};

  if (filters) {
    filters.map((filter) => {
      if (filter.operator !== 'or' && filter.operator !== 'and' && 'field' in filter) {
        const { field, operator, value } = filter;
        const fieldParts = field.split('.');

        if (field.includes('Exist') && fieldParts.length === 1) {
          queryFilters[`${fieldParts[0]}`] = value;
        } else if (operator in operatorMap) {
          queryFilters[`${fieldParts[0]}`] = getFilterObj(fieldParts.slice(1), operator, value);
        }
      } else {
        const value = filter.value as CrudFilters;

        // biome-ignore lint/suspicious/noExplicitAny: IGNORE THIS
        const filters: any[] = [];
        value.map((val) => {
          filters.push(generateFilter([val]));
        });

        queryFilters[filter.operator] = filters;
      }
    });
  }

  return queryFilters;
};
