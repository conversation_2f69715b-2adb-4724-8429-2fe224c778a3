import { describe, expect, it } from '@jest/globals';
import type { CrudSorting } from '@refinedev/core';
import { generateSort } from '.';

describe('generateSort', () => {
  it.each([undefined, null, []])('should return empty array when sorters is %p', (sorters) => {
    expect(generateSort(sorters as CrudSorting)).toEqual([]);
  });

  it('should return correct sort query for single sorter', () => {
    const sorters: CrudSorting = [
      {
        field: 'name',
        order: 'asc',
      },
    ];
    expect(generateSort(sorters)).toEqual(['NAME_ASC']);
  });

  it('should return correct sort query for multiple sorters', () => {
    const sorters: CrudSorting = [
      {
        field: 'name',
        order: 'asc',
      },
      {
        field: 'age',
        order: 'desc',
      },
    ];
    expect(generateSort(sorters)).toEqual(['NAME_ASC', 'AGE_DESC']);
  });
});
