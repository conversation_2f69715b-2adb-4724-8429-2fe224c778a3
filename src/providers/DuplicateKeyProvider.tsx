import { type ReactNode, createContext, useCallback, useContext, useState } from 'react';

interface DuplicateKeysContextType {
  duplicates: DuplicateMap;
  setDuplicates: (keyDuplicates: DuplicateMap) => void;
}

export type DuplicateMap = Record<string, { count: number; trace: string[][] }>;

export const DuplicateKeyContext = createContext<DuplicateKeysContextType>({
  duplicates: {},
  setDuplicates: () => {},
});

export function useDuplicateKeys() {
  const context = useContext(DuplicateKeyContext);
  if (!context) {
    throw new Error('useDuplicateKeys must be used within a DuplicateKeysProvider');
  }

  return context;
}

export const DuplicateKeysProvider = ({ children }: { children: ReactNode }) => {
  const [duplicates, setDuplicates] = useState<DuplicateMap>({});

  const setDuplicatesCallback = useCallback((newState: DuplicateMap) => {
    setDuplicates(newState);
  }, []);

  return (
    <DuplicateKeyContext.Provider value={{ duplicates, setDuplicates: setDuplicatesCallback }}>
      {children}
    </DuplicateKeyContext.Provider>
  );
};
