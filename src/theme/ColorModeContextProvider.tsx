import { useMediaQuery } from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import { RefineThemes, type Theme } from '@refinedev/mui';
import { getEnvironment } from '@utils/env';
import { parseCookies, setCookie } from 'nookies';
import type React from 'react';
import { type PropsWithChildren, createContext, useEffect, useMemo, useState } from 'react';
import { customOverrides } from 'theme/mui';

type ColorModeContextType = {
  mode: string;
  setMode: (targetMode?: 'light' | 'dark') => void;
  theme: Theme;
};

export const ColorModeContext = createContext<ColorModeContextType>({} as ColorModeContextType);

export const ColorModeContextProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const [isMounted, setIsMounted] = useState(false);
  const [env, setEnv] = useState<ReturnType<typeof getEnvironment>>(getEnvironment());
  const [mode, setMode] = useState('light');

  useEffect(() => setIsMounted(true), []);

  const systemTheme = useMediaQuery('(prefers-color-scheme: dark)');

  useEffect(() => {
    if (isMounted) {
      setMode(parseCookies().theme || (systemTheme ? 'dark' : 'light'));
      setEnv(getEnvironment());
    }
  }, [isMounted, systemTheme]);

  const theme = useMemo(() => {
    if (mode === 'light') {
      switch (env) {
        case 'PROD':
          return customOverrides(RefineThemes.Red);
        case 'DEV':
          return customOverrides(RefineThemes.Orange);
        case 'LOCAL':
          return customOverrides(RefineThemes.Green);
        case 'UNKNOWN':
          return customOverrides(RefineThemes.Magenta);
      }
    }
    switch (env) {
      case 'PROD':
        return customOverrides(RefineThemes.RedDark);
      case 'DEV':
        return customOverrides(RefineThemes.OrangeDark);
      case 'LOCAL':
        return customOverrides(RefineThemes.GreenDark);
      case 'UNKNOWN':
        return customOverrides(RefineThemes.MagentaDark);
    }
  }, [mode, env]);

  const toggleMode = (targetMode?: 'light' | 'dark') => {
    const nextMode = mode === 'light' ? 'dark' : 'light';
    setMode(targetMode || nextMode);
    setCookie(null, 'theme', targetMode || nextMode);
  };

  return (
    <ColorModeContext.Provider
      value={{
        setMode: toggleMode,
        mode,
        theme,
      }}
    >
      <ThemeProvider theme={theme}>{children}</ThemeProvider>
    </ColorModeContext.Provider>
  );
};
