import { GlobalStyles, type Theme } from '@mui/material';

const customOverrides = (theme: Theme): Theme => ({
  ...theme,
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
        },
      },
    },
    MuiCssBaseline: {
      styleOverrides: {
        '.MuiBox-root': {
          width: '100%', // Set the width to 100% as can break responsiveness of the layout
          overflow: 'auto !important',
        },
        // resetting global styles for app config editor
        '#app-config-editor .MuiBox-root': {
          width: 'initial',
          overflow: 'initial',
        },
        'nav.MuiBox-root .MuiDrawer-root .MuiPaper-root .MuiPaper-root': {
          backgroundColor: theme.palette.primary.main,
          boxShadow:
            '0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12)',
        },
      },
    },
  },
});

const globalStyles = (
  <GlobalStyles
    styles={{
      html: { WebkitFontSmoothing: 'auto' },
    }}
  />
);

export { customOverrides, globalStyles };
