version: '3'
services:
  # This image is for production, do not use in local dev yet 
  app:
    container_name: sorcery-app
    build: 
      context: ./
      dockerfile: Dockerfile
      args: 
        - NODE_ENV=development
    env_file:
      - docker.env
    ports: 
      - 8081:8080
  db:
    container_name: sorcery-db
    build:
      context: ./
      dockerfile: prisma/db.Dockerfile
    ports:
      - 5433:5432
