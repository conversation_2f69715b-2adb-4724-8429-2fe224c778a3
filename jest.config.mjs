// jest.config.mjs
import nextJest from 'next/jest.js';
import { pathsToModuleNameMapper } from 'ts-jest';
import tsconfig from './tsconfig.json' assert { type: 'json' };

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files in your test environment
  dir: './',
});

// Add any custom config to be passed to Jest
/** @type {import('jest').Config} */
const config = {
  preset: 'ts-jest',
  testEnvironment: 'jest-environment-jsdom',

  // this enables us to use tsconfig-paths with jest
  modulePaths: [tsconfig.compilerOptions.baseUrl],
  moduleNameMapper: pathsToModuleNameMapper(tsconfig.compilerOptions.paths),
  collectCoverageFrom: ['./src/hooks/**/*.ts', './src/lib/**/*.ts', './src/pages/api/**/*.ts', './src/utils/**/*.ts'],
  coverageReporters: ['text', 'text-summary', 'cobertura'],
  coverageDirectory: './coverage',
  setupFiles: ['./test/setup/env.ts'],
  setupFilesAfterEnv: ['./test/setup/globals.ts'],
  coverageThreshold: {
    // Temporary set this too low
    global: {
      branches: 15,
      functions: 10,
      lines: 15,
      statements: 15,
    }
  },
};
// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
export default createJestConfig(config);
