PUBLIC_ENVIRONMENT="local"

PLATFORM_DATABASE_URL="postgresql://main:<EMAIL>:9012/core"
SORCERY_DATABASE_URL="postgresql://main:<EMAIL>:5433/sorcery"
SCHEDULER_DATABASE_URL="postgresql://main:<EMAIL>:9014/scheduler"
CONFIG_DATABASE_URL="postgresql://main:<EMAIL>:9021/config"

# Payment
PAYMENT_DATABASE_URL="postgresql://main:<EMAIL>:9013/payments"
PUBLIC_PAYMENT_CLIENT_ID="079d80fd-3f03-4e72-932b-2aac51b332ed"
PAYMENTS_URL=http://localhost:10012
PAYMENTS_AUTH_ID=e5ff6820-7a57-484a-9119-dd3997a22289
PAYMENTS_AUTH_SECRET=ZGQ2ZGZjOTQ2ZGYwOWY5YzMzMTVmODE4YmM1MDBmM2JkMjQxOTY0MGU0ZGJiNTQ0YzQ2ZTEzZTJlZmI4ZTkwNw==
# payments service when the api is running on docker
WEBHOOK_BASE_URL=http://localhost:9052
PUBLIC_USIO_DESIGN_ID="399"

# identity
IDENTITY_DATABASE_URL="postgresql://main:<EMAIL>:9022/identity"

# ImageKit
PUBLIC_BRANDING_UPLOAD_FOLDER="branding-dev"
PUBLIC_IMAGEKIT_URL_ENDPOINT="https://ik.imagekit.io/bybeam"
PUBLIC_IMAGEKIT_PUBLIC_KEY="<REPLACE_ME>"
IMAGEKIT_PRIVATE_KEY="<REPLACE_ME>"

CLOUD_STORAGE_BUCKET="beam-platform-docs-dev"
PLATFORM_ENCRYPTION_KEY="<REPLACE_ME>"

# PW PUSH CRED
PW_PUSH_URL="https://pwpush.int.bybeam.co"
PW_PUSH_USER_EMAIL="<REPLACE_ME>"
PW_PUSH_TOKEN="<REPLACE_ME>"

# Scheduler
PUBLIC_SCHEDULER_ACTIVE_WINDOW=120
PUBLIC_SCHEDULER_CONCURRENCY=250
PUBLIC_SCHEDULER_FREQUENCY=30