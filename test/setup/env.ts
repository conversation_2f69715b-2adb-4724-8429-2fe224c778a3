process.env.JWT_SECRET = 'mockJWTSecretString';
process.env.PAYMENTS_URL = 'mockPaymentsUrl';
process.env.PAYMENTS_AUTH_ID = 'mockPaymentsAuthId';
process.env.PAYMENTS_AUTH_SECRET = 'mockPaymentsAuthSecret';
process.env.PAYMENTS_WEBHOOK_URL = 'http://mockWebhookUrl';
process.env.IDENTITY_SERVER_URL = 'mockIdentityServer';
process.env.PW_PUSH_URL = 'mockPushUrl';
process.env.PW_PUSH_USER_EMAIL = 'mockEmail';
process.env.PW_PUSH_TOKEN = 'mockPushToken';
process.env.PLATFORM_ENCRYPTION_KEY = 'mockApiEncryptionKey';
