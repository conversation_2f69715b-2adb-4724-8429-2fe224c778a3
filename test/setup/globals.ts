import { jest } from '@jest/globals';
import axios from 'axios';
import { logger } from 'utils/logger';

global.afterEach(() => {
  jest.clearAllMocks();
});

export const mockLogger = {
  debug: jest.spyOn(logger, 'debug').mockImplementation(() => {}),
  error: jest.spyOn(logger, 'error').mockImplementation(() => {}),
  info: jest.spyOn(logger, 'info').mockImplementation(() => {}),
  warn: jest.spyOn(logger, 'warn').mockImplementation(() => {}),
};

export const mockAxios = {
  isAxiosError: jest.spyOn(axios, 'isAxiosError').mockReturnThis(),
  post: jest.spyOn(axios, 'post').mockReturnThis(),
  get: jest.spyOn(axios, 'post').mockReturnThis(),
};
