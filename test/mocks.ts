import { randomUUID } from 'node:crypto';
import type { Recipient } from 'types/schedulePayments';

export function makeFakeRecipient(overrides: Partial<Recipient> = {}): Recipient {
  const randId = randomUUID();
  return {
    salesForceId: `mockSalesForceId-${randId}`,
    firstName: 'Test',
    lastName: 'User',
    email: `test.user+${randId}@bybeam.co`,
    participantAddress: '1 Main St',
    participantCity: 'Hanover',
    participantState: 'NH',
    participantZipCode: '03755',

    paymentMethod: 'ach',
    accountNumber: '8524694',
    routingNumber: '*********',
    accountType: 'savings',
    paymentAmount: '10000',
    paymentDate: '',

    programAddress: '1 Main St',
    programCity: 'Hanover',
    programState: 'NH',
    programZipCode: '03755',

    organization: 'JEVS',
    program: 'Jevs Program',
    stipendType: '2',
    cohort: '5',

    ...overrides,
  };
}
