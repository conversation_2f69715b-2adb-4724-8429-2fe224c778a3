name: Coverage

on:
  pull_request:
jobs:
  coverage:
    name: report
    runs-on: github-xlarge
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PNPM
        uses: pnpm/action-setup@v4

      - name: Setup
        uses: actions/setup-node@v4
        with:
          node-version: 20.10.0
          cache: "pnpm"

      - name: Install modules
        run: pnpm install

      - name: Generate prisma
        run: pnpm prisma:generate

      - name: Build dependencies
        run: pnpm build

      - name: Run tests
        run: pnpm test:coverage

      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          files: >
            ./coverage/cobertura-coverage.xml,
          fail_ci_if_error: true
