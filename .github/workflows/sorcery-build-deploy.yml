name: Build and Deploy sorcery to Cloud Run

on:
  push:
    branches:
      - main

jobs:
  build:    
    permissions:
      contents: 'read'
      id-token: 'write'
      actions: 'write'

    uses: edquity/devops-cicd/.github/workflows/gcp-cloudrun-build.yml@main
    with:
      container-project: 'shared-infra-beam'
      container-repository: 'sorcery'
      service-name: 'sorcery-app'
      dockerfile-path: './Dockerfile'
    secrets:
      GCP_WI_PROVIDER: ${{ secrets.GCP_WI_PROVIDER }}
      GCP_WI_SA: ${{ secrets.GCP_WI_SA }}
      GH_TOKEN: ${{ secrets.GH_TOKEN }}

  migrate-db-dev:
    runs-on: gcp-runners-dev
    steps:
      - name: Checkout caller repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Setup PNPM
        uses: pnpm/action-setup@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with: 
          node-version: 18.17.0
          cache: 'pnpm'
      - name: Install modules
        run: pnpm install 
      - name: Download SQL Auth Proxy
        run: "wget https://storage.googleapis.com/cloud-sql-connectors/cloud-sql-proxy/v2.11.0/cloud-sql-proxy.linux.amd64 -O cloud-sql-proxy"
      - name: Make the Cloud SQL Auth proxy executable
        run: "chmod +x cloud-sql-proxy"
      - name: Start the Cloud SQL proxy
        run: "./cloud-sql-proxy --private-ip --port 5432 --run-connection-test sorcery-dev-6265616d:us-central1:sorcery &"      
      - name: Apply all pending migrations to the database
        run: pnpm run prisma:migrate:deploy
        env:
          SORCERY_DATABASE_URL: ${{ secrets.SORCERY_DATABASE_URL_DEV }}

  migrate-db-prod:
    needs: [migrate-db-dev]
    runs-on: gcp-runners-prod
    steps:
      - name: Checkout caller repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Setup PNPM
        uses: pnpm/action-setup@v4
      - name: Setup Node
        uses: actions/setup-node@v4
        with: 
          node-version: 18.17.0
          cache: 'pnpm'
      - name: Install modules
        run: pnpm install 
      - name: Download SQL Auth Proxy
        run: "wget https://storage.googleapis.com/cloud-sql-connectors/cloud-sql-proxy/v2.11.0/cloud-sql-proxy.linux.amd64 -O cloud-sql-proxy"
      - name: Make the Cloud SQL Auth proxy executable
        run: "chmod +x cloud-sql-proxy"
      - name: Start the Cloud SQL proxy
        run: "./cloud-sql-proxy --private-ip --port 5432 --run-connection-test sorcery-prod-beam:us-central1:sorcery &"      
      - name: Apply all pending migrations to the database
        run: pnpm run prisma:migrate:deploy
        env:
          SORCERY_DATABASE_URL: ${{ secrets.SORCERY_DATABASE_URL_PROD }}

  deploy-dev:
    needs: [build, migrate-db-dev]
    permissions:
      contents: 'read'
      id-token: 'write'
    uses: edquity/devops-cicd/.github/workflows/gcp-cloudrun-deploy.yml@main
    with:
      environment: 'development'
      service-project: 'sorcery-dev-6265616d'
      service-name: 'sorcery-app'
      cloudrun-template: 'cloudrun'
      allow-unauthenticated: true
      docker-image: ${{ needs.build.outputs.docker-image-latest }}
    secrets:
      GCP_WI_PROVIDER: ${{ secrets.GCP_WI_PROVIDER }}
      GCP_WI_SA: ${{ secrets.GCP_WI_SA }}
      GH_TOKEN: ${{ secrets.GH_TOKEN }}

  deploy-prod:
    needs: [build, migrate-db-prod]
    permissions:
      contents: 'read'
      id-token: 'write'
    uses: edquity/devops-cicd/.github/workflows/gcp-cloudrun-deploy.yml@main
    with:
      environment: 'production'
      service-project: 'sorcery-prod-beam'
      service-name: 'sorcery-app'
      cloudrun-template: 'cloudrun'
      allow-unauthenticated: true
      docker-image: ${{ needs.build.outputs.docker-image-latest }}
    secrets:
      GCP_WI_PROVIDER: ${{ secrets.GCP_WI_PROVIDER }}
      GCP_WI_SA: ${{ secrets.GCP_WI_SA }}
      GH_TOKEN: ${{ secrets.GH_TOKEN }}
