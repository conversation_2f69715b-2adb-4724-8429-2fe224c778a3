PUBLIC_ENVIRONMENT="local"

PLATFORM_DATABASE_URL="postgresql://main:password@localhost:9012/core"
SORCERY_DATABASE_URL="postgresql://main:password@localhost:5433/sorcery"
SCHEDULER_DATABASE_URL="postgresql://main:password@localhost:9014/scheduler"
CONFIG_DATABASE_URL="postgresql://main:password@localhost:9021/config"
COMPLIANCE_DB_URL="postgresql://main:password@localhost:9023/compliance"
VERIFICATION_DATABASE_URL="postgresql://main:password@localhost:9020/verification"
DOCUMENT_DATABASE_URL="postgresql://main:password@localhost:9024/document_ai"
IDENTITY_DATABASE_URL="postgresql://main:password@localhost:9022/identity"

# Payment
PAYMENT_DATABASE_URL="postgresql://main:password@localhost:9013/payments"
PUBLIC_PAYMENT_CLIENT_ID="079d80fd-3f03-4e72-932b-2aac51b332ed"
PAYMENTS_URL=http://localhost:10012
PAYMENTS_AUTH_ID=e5ff6820-7a57-484a-9119-dd3997a22289
PAYMENTS_AUTH_SECRET=ZGQ2ZGZjOTQ2ZGYwOWY5YzMzMTVmODE4YmM1MDBmM2JkMjQxOTY0MGU0ZGJiNTQ0YzQ2ZTEzZTJlZmI4ZTkwNw==
PAYMENTS_WEBHOOK_URL=http://localhost:10090
PUBLIC_SCHEDULER_ACTIVE_WINDOW=120
PUBLIC_USIO_DESIGN_ID="FAKE_DESIGN_ID"

# ImageKit
PUBLIC_BRANDING_UPLOAD_FOLDER="branding-dev"
PUBLIC_IMAGEKIT_URL_ENDPOINT="https://ik.imagekit.io/bybeam"
PUBLIC_IMAGEKIT_PUBLIC_KEY="<REPLACE ME>"
IMAGEKIT_PRIVATE_KEY="<REPLACE ME>"

CLOUD_STORAGE_BUCKET="beam-platform-docs-dev"
# PLATFORM_ENCRYPTION_KEY="<REPLACE ME>"
# PW PUSH CREDS
PW_PUSH_URL="https://pwpush.int.bybeam.co"
PW_PUSH_USER_EMAIL="<REPLACE ME>"
PW_PUSH_TOKEN="<REPLACE ME>"

# Microservices
IDENTITY_SERVER_URL=localhost:10059
NOTIFICATION_SERVER_URL=localhost:10057

# Tenant Defaults
PUBLIC_APPLICANT_TENANT_ID=app-local-c5615
PUBLIC_ADVOCATE_TENANT_ID=adv-local-v4dd0

# Posthog
NEXT_PUBLIC_POSTHOG_KEY=phc_1Ol4OTVD5rNTIpzsV7ZhzsxtXKsO5ElxtgWpumqx3Wd
NEXT_PUBLIC_POSTHOG_HOST=https://us.i.posthog.com